import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-list-item",
  styleUrl: "c-list-item.css",
})
export class CListItem {
  @Prop() type: string;
  @Prop() isClickable: boolean = false;

  private styleClasses: string;

  componentWillLoad() {
    this.generateStyles();
  }

  generateStyles() {
    if (this.type === "dashboardPrimaryInfoListItem") {
      this.styleClasses = "dashboard-primary-info-list-item";
    }

    if (this.isClickable) {
      this.styleClasses = this.styleClasses + " clickable";
    }
  }

  render() {
    return (
      <li class={this.styleClasses}>
        <slot />
      </li>
    );
  }
}
