import { Component, Listen, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-overview-2",
  styleUrl: "c-rm-overview-2.css",
  shadow: true,
})
export class CRmOverview2 {
  @State() isFetching: boolean = true;
  @State() accountDetails: any;
  @State() salesDetails: any;
  @State() ticketSales: any = [];

  @State()
  isModalActive: boolean = false;
  @State() modalName: string = "";
  @State() modalData: any;

  @Listen("buttonClick") handleButtonClick(e) {
    if (e.detail.name === "showSessionInfo") {
      this.openModal(e.detail.name);
      let value = e.detail.value.split("---");
      this.modalData = {
        ticketId: value[0],
        sessionId: value[1],
      };
    }
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.isModalActive = false;
    this.modalName = "";
  }

  componentDidLoad() {
    this.getOverviewData();
  }

  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  getOverviewData() {
    let payload = {
      eventCode: state.eventCodeForMonitoring,
      filterType: "eventCode",
      startDate: "",
      endDate: "",
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/overview-v3`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.accountDetails = response.data.payload.accounts;
          this.salesDetails = response.data.payload.sales;
          this.ticketSales = this.salesDetails.tickets;
          state.notificationCount = response.data.payload.notifications.count;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        <c-modal
          data={this.modalData}
          name={this.modalName}
          is-active={this.isModalActive}
        ></c-modal>
        {this.isFetching ? (
          <div class="account-overview">
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : (
          <div class="account-overview">
            <c-text type="sectionDivider">
              Overview{" "}
              {state.eventNameForMonitoring &&
                `of ${state.eventNameForMonitoring}`}
            </c-text>{" "}
            <c-card>
              <div class="overview-header hide-on-mobile">
                <c-row>
                  <div class="row__primary-container">
                    <c-text type="subtext">
                      <ion-icon name="cash-outline"></ion-icon>
                      &nbsp;&nbsp;TICKET REVENUE
                      <br />
                      <span class="overview-number">
                        ₹
                        {Math.floor(this.salesDetails.revenue).toLocaleString(
                          "hi"
                        )}
                      </span>
                    </c-text>
                  </div>
                  <div class="vertical-divider"></div>
                  <div class="row__green-container">
                    <c-text type="subtext">
                      <ion-icon name="ticket-outline"></ion-icon>
                      &nbsp;&nbsp;ATTENDEES
                      <br />
                      <span class="overview-number">
                        {" "}
                        {this.accountDetails.withTicket}
                      </span>
                    </c-text>
                  </div>
                  <div class="row__red-container">
                    <c-text type="subtext">
                      <ion-icon name="people-outline"></ion-icon>
                      &nbsp;&nbsp;ACCOUNTS WITHOUT TICKETS
                      <br />
                      <span class="overview-number">
                        {" "}
                        {this.accountDetails.withoutTicket}
                      </span>
                    </c-text>
                  </div>
                </c-row>
              </div>
              <div class="overview-header show-on-mobile">
                <div class="overview-header_row row__primary-container">
                  <c-row>
                    <c-text>
                      <ion-icon name="cash-outline"></ion-icon>
                      &nbsp;&nbsp;Ticket revenue
                    </c-text>
                    <c-text>
                      ₹
                      {Math.floor(this.salesDetails.revenue).toLocaleString(
                        "hi"
                      )}
                    </c-text>
                  </c-row>
                </div>
                <div class="overview-header_row row__green-container">
                  <c-row>
                    <c-text>
                      <ion-icon name="ticket-outline"></ion-icon>
                      &nbsp;&nbsp;Attendees
                    </c-text>
                    <c-text>{this.accountDetails.withTicket}</c-text>
                  </c-row>
                </div>
                <div class="overview-header_row row__red-container">
                  <c-row>
                    <c-text>
                      <ion-icon name="people-outline"></ion-icon>
                      &nbsp;&nbsp;Accounts without tickets
                    </c-text>
                    <c-text> {this.accountDetails.withoutTicket}</c-text>
                  </c-row>
                </div>
              </div>
            </c-card>
            <br />
            <c-card>
              <c-text type="subtext">CONFERENCE TICKETS</c-text>
              {this.ticketSales.map(
                (ticket: any) =>
                  ticket.ticketType === "fullTicket" && (
                    <c-row>
                      <div class="row__item__1">
                        <c-text>{ticket.ticketTitle}</c-text>
                      </div>
                      <div class="row__item__2">
                        <c-text>{ticket.saleCount}</c-text>
                      </div>
                    </c-row>
                  )
              )}
              {this.ticketSales.map(
                (ticket: any) =>
                  ticket.ticketType === "basicTicket" && (
                    <c-row>
                      <div class="row__item__1">
                        <c-text>{ticket.ticketTitle}</c-text>
                      </div>
                      <div class="row__item__2">
                        <c-text>{ticket.saleCount}</c-text>
                      </div>
                    </c-row>
                  )
              )}
              <div class="seperator__horizontal seperator__horizontal--regular"></div>
              <c-text type="subtext">WORKSHOPS & COURSES</c-text>
              <div class="spacing"></div>
              {this.ticketSales.map(
                (ticket: any) =>
                  ticket.ticketType === "trackTicket" &&
                  ticket.sessions.map((session: any) => (
                    <div class="track__container">
                      <c-row>
                        <div class="row__item__1">
                          {" "}
                          <c-button
                            name="showSessionInfo"
                            type="link_Small"
                            value={`${ticket.ticketId}---${session.sessionId}`}
                          >
                            {session.ticketTitle}
                          </c-button>
                        </div>
                        <div class="row__item__2">
                          <c-text>{session.saleCount}</c-text>
                        </div>
                      </c-row>
                    </div>
                  ))
              )}
            </c-card>
          </div>
        )}
      </div>
    );
  }
}
