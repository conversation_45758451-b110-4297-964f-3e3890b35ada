.container {
  width: 110%;
  margin-bottom: 5em;
  box-sizing: border-box;
}

.overview-section-header {
  color: rgba(0, 0, 0, 0.4);
  font-size: 1.5em;
  margin-bottom: 0.25em;
  font-weight: 400;
}

.vseperator {
  margin-left: 0.75em;
  margin-right: 0.75em;
}

.row {
  display: flex;
}

.card-header {
  background: rgba(238, 234, 245, 0.5);
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.75em 0.75em 0 0;
  font-size: 0.9em;
  font-weight: 700;
  color: var(--accent-color);
  display: flex;
  justify-content: space-between;
}

.card-header p {
  margin-top: 0;
  margin-bottom: 0;
}

.card-content {
  display: flex;
  justify-content: space-between;
  padding: 0em 1em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em;
  font-size: 0.9em;
}

.overview-section {
  margin-bottom: 2.5em;
}

.label-total-row {
  width: 100%;
  justify-content: space-between;
}

.elem-2 {
  width: 60%;
  text-align: right;
}

.elem-3 {
  width: 20%;
  text-align: left;
}

.total-count-row {
  display: flex;
  justify-content: space-between;
}

.wide-card {
  width: 350px;
}

.card-footer {
  background: white;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em 0em 0.75em 0.75em;
  font-size: 0.9em;
  display: flex;
  justify-content: space-between;
}

.card-footer p {
  margin: 0;
  padding: 0;
}

.round-bottom {
  border-radius: 0 0 0.75em 0.75em;
}

.card-subheading p {
  font-size: 0.7em;
  margin: 0;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
}

.sale-count {
  color: rgba(0, 0, 0, 0.4);
}

.no-footer-card {
  border-radius: 0 0 0.75em 0.75em;
}

.overview-section:first-child c-text .section-divider {
  margin-top: 0;
}

.row__primary-container .subtext {
  /* background: rgba(238, 234, 245, 0.5); */
  color: var(--accent-color);
  /* font-weight: 700; */
  /* padding: 1em;
  border-radius: 0.75em 0 0 0.75em;
  border: 1px solid rgba(0, 0, 0, 0.1); */
}

.row__green-container .subtext {
  color: var(--accent-green-darker);
}

.row__red-container .subtext {
  color: var(--red-600);
}

.vertical-divider {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.overview-header {
  text-align: center;
}

.overview-number {
  font-size: 1.5em;
}

.revenue-container {
  text-align: center;
}

.seperator__horizontal {
  margin: 1em 0em;
  height: 1px;
  width: 100%;
}
.seperator__horizontal--regular {
  background: rgba(0, 0, 0, 0.1);
}
.seperator__horizontal--light {
  background: rgba(0, 0, 0, 0.05);
}

c-text .section-divider {
  border-bottom: 0;
  margin-bottom: 0.5em;
}

c-card .basic-card-container {
  padding: 1em;
  border-radius: 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-card .basic-card-container c-row .row-container {
  margin: 1em 0;
}

c-card .basic-card-container c-row:first-child .row-container {
  margin: 0;
}

.sales-overview-header {
  width: 50%;
  padding: 1em;
  border-radius: 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-left: 4%;
}

.revenue-container {
  padding: 0 0.5em;
}

.sales-over-header-container {
  width: 100%;
  display: flex;
}

.row__item__1 {
  width: 80%;
}
.row__item__2 {
  width: 15%;
  text-align: right;
}
/* .row__item__3 {
  width: 10%;
  text-align: right;
} */

.track__container button {
  text-align: left;
}

.track__container c-button .link-small {
  color: var(--violet-400);
}

.track__container c-button .link-small:hover {
  background: var(--violet-50);
}

.spacing {
  margin-top: 0.5em;
}

.hide-on-mobile {
  display: block;
}

.show-on-mobile {
  display: none;
}

@media only screen and (max-width: 768px) {
  .container {
    box-sizing: border-box;
    width: 100%;
    margin: 0;
  }

  .overview-header c-row .row-container .vertical-divider {
    display: none;
  }

  .row__primary-container {
    padding-bottom: 0.75em;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .row__green-container {
    padding: 0.75em 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .row__red-container {
    padding-top: 0.5em;
  }

  .hide-on-mobile {
    display: none;
  }

  .show-on-mobile {
    display: block;
  }

  .show-on-mobile .row__primary-container {
    color: var(--accent-color);
  }

  .show-on-mobile .row__green-container {
    color: var(--accent-green-darker);
  }

  .show-on-mobile .row__red-container {
    color: var(--red-600);
  }

  .overview-header_row {
    padding: 0.75em 0;
  }

  .track__container c-button .link-small {
    padding: 0;
  }
  .track__container {
    padding: 1em 0;
  }
}
