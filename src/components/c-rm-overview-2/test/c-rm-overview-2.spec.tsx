import { newSpecPage } from '@stencil/core/testing';
import { CRmOverview2 } from '../c-rm-overview-2';

describe('c-rm-overview-2', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CRmOverview2],
      html: `<c-rm-overview-2></c-rm-overview-2>`,
    });
    expect(page.root).toEqualHtml(`
      <c-rm-overview-2>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-rm-overview-2>
    `);
  });
});
