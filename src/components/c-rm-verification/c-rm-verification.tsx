import { Component, State, Listen, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-verification",
  styleUrl: "c-rm-verification.css",
})
export class CRmVerification {
  @State() isFetching: boolean = true;
  @State() isToastActive: boolean = false;
  private userVerificationArr: any;
  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;

  @Listen("verify-bank-transaction")
  verifyBankTransactionHandler(event) {
    event.preventDefault();
    let payload = event.detail;
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/banktxverification`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.isFetching = true;
          this.showToast({
            type: response.data.status,
            label: response.data.msg,
          });
          this.getAllVerifications();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  componentDidLoad() {
    this.getAllVerifications();
  }

  getAllVerifications() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/getverifications`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.isFetching = false;
          this.userVerificationArr = response.data.payload;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  render() {
    return (
      <div>
        {this.isToastActive ? (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        ) : (
          ""
        )}
        <div class="verification-container">
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            ""
          )}

          {this.isFetching === false && this.userVerificationArr.length > 0 ? (
            this.userVerificationArr.map((userVerification) => (
              <c-user-verification-card
                firstName={userVerification.firstName}
                lastName={userVerification.lastName}
                email={userVerification.email}
                verificationType={userVerification.verificationType}
                orderID={userVerification.orderID}
                bankTxCode={userVerification.bankTxCode}
                transferredAmount={userVerification.transferredAmount}
              ></c-user-verification-card>
            ))
          ) : this.isFetching === false &&
            this.userVerificationArr.length === 0 ? (
            <p>
              There are <strong>no issues</strong> at the moment
            </p>
          ) : (
            ""
          )}
        </div>
      </div>
    );
  }
}
