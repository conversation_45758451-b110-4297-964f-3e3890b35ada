import { newSpecPage } from '@stencil/core/testing';
import { CRmSales } from '../c-rm-sales';

describe('c-rm-sales', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CRmSales],
      html: `<c-rm-sales></c-rm-sales>`,
    });
    expect(page.root).toEqualHtml(`
      <c-rm-sales>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-rm-sales>
    `);
  });
});
