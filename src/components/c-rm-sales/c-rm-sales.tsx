import { Component, State, Listen, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-sales",
  styleUrl: "c-rm-sales.css",
  shadow: true,
})
export class CRmSales {
  @State() isFetching: boolean = true;
  @State() purchasesArr: any;
  private monthName: string = "";
  private totalPurchases: number = 0;

  private salesStartDate: any = "-";
  private salesEndDate: any = "-";

  @Listen("dropdown-input-event")
  filterHandler(event) {
    this.monthName = event.detail.value;
    this.isFetching = true;
    this.fetchData();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "salesStartDate") {
      this.salesStartDate = e.detail.value;
    } else if (e.detail.name === "salesEndDate") {
      this.salesEndDate = e.detail.value;
    }
  }

  @Listen("download-purchase-data")
  downloadPurchaseDataHandler() {
    window.open(
      `${state.baseUrl}/download-sales-data-v5/${state.eventCodeForMonitoring}/${this.salesStartDate}/${this.salesEndDate}`
    );
  }

  componentWillLoad() {
    this.fetchData();
    state.isMobileDashboardOptionsVisible = false;
  }

  fetchData() {
    let payload = {
      monthName: this.monthName,
      eventCode: state.eventCodeForMonitoring,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/purchases-summary-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.purchasesArr = "";
          this.purchasesArr = response.data.payload;
          this.purchasesArr = [...this.purchasesArr];
          this.totalPurchases = response.data.payload.length;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div>
        {state.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile">
            <c-control-bar
              count={!this.isFetching && this.totalPurchases}
              name="purchases"
            ></c-control-bar>
          </div>
        )}

        <div class="show-on-desktop">
          <c-control-bar
            count={!this.isFetching && this.totalPurchases}
            name="purchases"
          ></c-control-bar>
        </div>

        <div class="purchase-list-container">
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div>
              <c-text type="sectionDivider">
                All sales{" "}
                {state.eventNameForMonitoring &&
                  `of ${state.eventNameForMonitoring}`}
              </c-text>{" "}
              {this.purchasesArr.length > 0 ? (
                <c-list
                  type="dashboardSalesInfoList"
                  listItemsAsString={JSON.stringify(this.purchasesArr)}
                ></c-list>
              ) : (
                <c-text>No sales found</c-text>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
}
