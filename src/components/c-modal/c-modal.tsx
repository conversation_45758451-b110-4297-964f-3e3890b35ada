import {
  Component,
  Event,
  EventEmitter,
  Prop,
  State,
  Host,
  Watch,
  h,
} from "@stencil/core";
import { gsap } from "gsap";

@Component({
  tag: "c-modal",
  styleUrl: "c-modal.css",
})
export class CModal {
  modalBg!: HTMLDivElement;

  @Event({
    eventName: "modalClosed",
    bubbles: true,
  })
  modalClosedEvent: EventEmitter;

  @Prop()
  isActive: boolean = false;
  @Prop() name: string = "";
  @Prop() data: any;

  @State() activeModal: string = "";
  @State() modalData: any;

  componentDidLoad() {
    if (this.name) {
      this.activeModal = this.name;
      this.showModal();
    }

    if (this.data) {
      this.modalData = this.data;
    }
  }

  @Watch("data")
  modalDataWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      this.modalData = this.data;
    }
  }

  @Watch("isActive")
  isActiveWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      if (newVal === true) {
        this.showModal();
      } else {
        this.hideModal();
      }
    }
  }

  @Watch("name")
  nameWatcher(newVal: string, oldVal: string) {
    if (newVal != oldVal) {
      this.activeModal = newVal;
    }
  }

  showModal() {
    let tl = gsap.timeline();
    tl.to(this.modalBg, {
      display: "block",
      duration: 0,
    });
    tl.to(this.modalBg, {
      opacity: 0.8,
      duration: 0.15,
    });
  }

  hideModal() {
    let tl = gsap.timeline();
    tl.to(this.modalBg, {
      opacity: 0,
      duration: 0.15,
    });
    tl.to(this.modalBg, {
      display: "none",
      duration: 0,
    });
  }

  render() {
    return (
      <Host>
        {this.activeModal === "openCreateEventModal" && (
          <c-wizard-create-event></c-wizard-create-event>
        )}
        {this.activeModal === "openEditEventModal" && (
          <c-wizard-edit-event></c-wizard-edit-event>
        )}

        {this.activeModal === "openAccountSetupModal" && (
          <c-wizard-account-setup></c-wizard-account-setup>
        )}

        {this.activeModal === "openAddPageModal" && (
          <c-wizard-add-page></c-wizard-add-page>
        )}

        {this.activeModal === "openAddTicketModal" && (
          <c-wizard-add-ticket></c-wizard-add-ticket>
        )}

        {this.activeModal === "getId" && <c-wizard-get-id></c-wizard-get-id>}

        {this.activeModal === "showSessionInfo" && (
          <c-session-info data={this.modalData}></c-session-info>
        )}

        {this.activeModal === "createCoupon" && (
          <c-create-coupon-modal></c-create-coupon-modal>
        )}
        {this.activeModal === "showCouponDetails" && (
          <c-coupon-details-modal></c-coupon-details-modal>
        )}

        {this.activeModal === "createInvoice" && (
          <c-create-invoice-modal></c-create-invoice-modal>
        )}
        {this.activeModal === "showInvoiceDetails" && (
          <c-invoice-details-modal
            invoiceGroup={this.data}
          ></c-invoice-details-modal>
        )}

        {this.activeModal === "expandUserDetails" && (
          <c-account-details-modal></c-account-details-modal>
        )}

        <div
          class="modal-background"
          ref={(el) => (this.modalBg = el as HTMLDivElement)}
        ></div>
      </Host>
    );
  }
}
