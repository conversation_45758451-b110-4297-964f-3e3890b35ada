p-free-purchase-thanks .free-purchase-thanks-container {
  background: white;
  width: 40%;
  padding: 2em 3em;
  border-radius: 0.25em;
  /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05); */
  border: 1px solid rgba(0, 0, 0, 0.1);

  margin: 0 auto;
  margin-top: 25vh;
}

p-free-purchase-thanks .free-purchase-thanks-container p {
  line-height: 1.5;
  font-size: 0.9em;
}

p-free-purchase-thanks .footer-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3em;
}

p-free-purchase-thanks .heading-failed {
  color: var(--danger-color);
  font-size: 1.5em;
}

p-free-purchase-thanks .heading-success {
  color: var(--accent-color);
  font-size: 1.5em;
}

p-free-purchase-thanks .heading-success .tick {
  color: white;
  padding: 0.1em 0.4em;
  font-size: 0.7em;
  background: var(--accent-green);
  border-radius: 100%;
}

p-free-purchase-thanks c-text-link a {
  font-size: 0.9em;
}

@media only screen and (max-width: 768px) {
  p-free-purchase-thanks .free-purchase-thanks-container {
    width: 80%;
    margin-top: 2em;
  }
}
