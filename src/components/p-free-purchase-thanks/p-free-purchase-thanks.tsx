import { Component, Listen, Prop, State, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-free-purchase-thanks",
  styleUrl: "p-free-purchase-thanks.css",
})
export class PFreePurchaseThanks {
  @Prop() history: RouterHistory;
  @State() compState = "init";

  @Listen("back-to-account")
  backToAccount(event) {
    event.preventDefault();
    this.backToAccountHandler();
  }

  backToAccountHandler() {
    this.history.push("/", {});
  }

  componentWillLoad() {
    if (!this.history.location.state) {
      this.history.push("/", {});
    }
  }

  componentDidLoad() {
    let payload = this.history.location.state;

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/free-purchase-confirm-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.changeComponentState("free-purchase-payment-failed");
        } else if (response.data.status === "Success") {
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
          state.isCouponsAvailable = false;
          state.paymentGateway = "razorpay";
          this.changeComponentState("free-purchase-payment-success");
        }
        state.isPaymentBtnDisabled = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  changeComponentState(newState) {
    this.compState = newState;
  }

  render() {
    return (
      <div class="free-purchase-thanks-container">
        {this.compState === "free-purchase-payment-success" ? (
          <div>
            <h1 class="heading-success">
              <span class="tick">✓</span> Congrats
            </h1>
            <p>
              Your desired tickets are confirmed. A confirmation mail has been
              sent to your email.
            </p>
            <div class="footer-controls">
              <div></div>
              <c-btn
                name="backtoaccount"
                label="My account"
                action-label=".."
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            </div>
          </div>
        ) : (
          ""
        )}

        {this.compState === "free-purchase-payment-failed" ? (
          <div>
            <h1 class="heading-failed">Failed</h1>
            <p>
              We were not able to save your desired tickets. Please get in touch
              with the organising team.
            </p>

            <div class="footer-controls">
              <div></div>
              <c-btn
                name="backtoaccount"
                label="My account"
                action-label=".."
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            </div>
          </div>
        ) : (
          ""
        )}

        {this.compState === "init" ? (
          <div>
            <c-skel-line color="gray" width={75}></c-skel-line>
            <br />
            <c-skel-line color="gray" width={75}></c-skel-line>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          ""
        )}
      </div>
    );
  }
}

injectHistory(PFreePurchaseThanks);
