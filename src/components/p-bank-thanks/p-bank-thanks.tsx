import { Component, Listen, Prop, State, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-bank-thanks",
  styleUrl: "p-bank-thanks.css",
})
export class PBankThanks {
  /*----
  Props
  ----*/
  @Prop() history: RouterHistory;

  @State() compState = "init";

  @Listen("back-to-account")
  backToAccount(event) {
    event.preventDefault();
    this.backToAccountHandler();
  }

  backToAccountHandler() {
    this.history.push("/", {});
  }

  componentWillLoad() {
    if (!this.history.location.state) {
      this.history.push("/", {});
    }
  }

  componentDidLoad() {
    setTimeout(() => {
      this.checkPaymentStatus();
    }, 2500);
  }

  checkPaymentStatus() {
    let payload = this.history.location.state;
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/bank-payment-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.changeComponentState("bank-payment-failed");
        } else if (response.data.status === "Success") {
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
          state.isCouponsAvailable = false;
          state.paymentGateway = "razorpay";
          this.changeComponentState("bank-payment-success");
        }
        state.isPaying = false;
        state.isPaymentBtnDisabled = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  changeComponentState(newState) {
    this.compState = newState;
  }

  render() {
    return (
      <div class="bank-thanks-container">
        {this.compState === "bank-payment-success" ? (
          <div>
            <h1 class="success-header">
              <span class="tick">✓</span> Payment recorded
            </h1>
            <p>
              We have recorded your purchases and bank transaction code. We will
              send you a confirmation once we verify your transaction. This
              process might take 2-7 working days.
            </p>
            <div class="footer-controls">
              <div></div>
              <c-btn
                name="backtoaccount"
                label="My account"
                action-label=".."
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            </div>
          </div>
        ) : (
          ""
        )}

        {this.compState === "bank-payment-failed" ? (
          <div>
            <h1>Payment failed</h1>
            <p>
              Something went wrong and we could not save your purchases. Please
              contact HCIPAI.
            </p>

            <div class="footer-controls">
              <div></div>
              <c-btn
                name="backtoaccount"
                label="My account"
                action-label=".."
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            </div>
          </div>
        ) : (
          ""
        )}

        {this.compState === "init" ? (
          <div class="init-container">
            <c-spinner-dark></c-spinner-dark>
            &nbsp;&nbsp;
            <p class="processing-message">Processing payment..</p>
          </div>
        ) : (
          ""
        )}
      </div>
    );
  }
}

injectHistory(PBankThanks);
