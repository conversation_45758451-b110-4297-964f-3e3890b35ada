p-bank-thanks .bank-thanks-container {
  background: white;
  width: 40%;
  padding: 1em;
  border-radius: 0.25em;
  /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05); */
  border: 1px solid rgba(0, 0, 0, 0.1);

  margin: 0 auto;
  margin-top: 25vh;
}

p-bank-thanks .bank-thanks-container p {
  line-height: 1.5;
  font-size: 0.9em;
}

p-bank-thanks .footer-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3em;
}

p-bank-thanks h1 {
  margin: 0;
}

p-bank-thanks .success-header {
  color: var(--accent-color);
  font-size: 1.5em;
}

p-bank-thanks .tick {
  color: white;
  padding: 0.1em 0.4em;
  font-size: 0.7em;
  background: var(--accent-green);
  border-radius: 100%;
}

p-bank-thanks c-text-link a {
  font-size: 0.9em;
}

p-bank-thanks .init-container {
  display: flex;
  align-items: center;
}

p-bank-thanks .processing-message {
  font-size: 4em;
}

@media only screen and (max-width: 768px) {
  p-bank-thanks .bank-thanks-container {
    width: 80%;
    margin-top: 2em;
  }
}
