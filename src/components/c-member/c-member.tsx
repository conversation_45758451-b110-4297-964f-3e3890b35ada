import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-member",
  styleUrl: "c-member.css",
})
export class CMember {
  @Prop() name: string;
  @Prop() email: string;
  @Prop() memberId: string;
  @Prop() membershipType: string;
  @Prop() membershipStartDate: string;
  @Prop() membershipEndDate: string;
  @Prop() expiresIn: string;
  render() {
    return (
      <div class="card">
        <div class="row-items row-item-1">
          <p class="name">{this.name}</p>
          <c-text-link
            url={`mailto:${this.email}`}
            label={this.email}
          ></c-text-link>
          <div class="hspacer"></div>
          {this.membershipType === "annual" ? (
            <c-badge label="Annual" color="green"></c-badge>
          ) : (
            ""
          )}
          {this.membershipType === "lifetime" ? (
            <c-badge label="Lifetime" color="blue"></c-badge>
          ) : (
            ""
          )}
        </div>
        <p>
          <span class="label">MEMBERSHIP ID</span>
          <br />
          <span class="member-info">{this.memberId}</span>
        </p>
        <p>
          <span class="label">START DATE</span>
          <br />
          <span class="member-info">
            {new Date(this.membershipStartDate).toString().substring(4, 15)}
          </span>
        </p>
        <p>
          {" "}
          <span class="label">END DATE</span>
          <br />
          <span class="member-info">
            {" "}
            {this.membershipType === "annual"
              ? new Date(this.membershipEndDate).toString().substring(4, 15)
              : ""}
            {this.membershipType === "lifetime" ? "-" : ""}
          </span>
        </p>
        <p>
          {" "}
          <span class="label">EXPIRES IN</span>
          <br />
          {this.membershipType === "annual" ? (
            parseInt(this.expiresIn) > 0 ? (
              <span class="member-info">{this.expiresIn} days</span>
            ) : (
              <span class="expired-text">expired</span>
            )
          ) : (
            ""
          )}
          {this.membershipType === "lifetime" ? "-" : ""}
        </p>
      </div>
    );
  }
}
