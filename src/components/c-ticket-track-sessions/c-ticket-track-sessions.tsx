import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  Prop,
  State,
  Watch,
  Host,
  h,
} from "@stencil/core";

@Component({
  tag: "c-ticket-track-sessions",
  styleUrl: "c-ticket-track-sessions.css",
})
export class CTicketTrackSessions {
  /*-----------
  Event Emitter
  -----------*/
  @Event({
    eventName: "addAccessDates_partialTicket",
    bubbles: true,
  })
  addAccessDates_partialTicket: EventEmitter;

  /*-------------
  Event Listeners  
  -------------*/
  @Listen("accessDateClicked") handleTicketTierClick(e) {
    this.accessName_Old = e.detail.name;
    this.accessDateId_Old = e.detail.id;
    this.accessIsMultiDay_Old = e.detail.isMultiDay;
    this.accessStartDate_Old = e.detail.startDate;
    this.accessEndDate_Old = e.detail.endDate;
    this.mode = "editTrackSession";
    console.log(this.accessDateId_Old);
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "newTrackSession") {
      this.mode = "newTrackSession";
    } else if (e.detail.name === "cancelTrackSession") {
      this.resetComponent();
      this.mode = "viewTrackSession";
    } else if (e.detail.name === "createTrackSession") {
      this.createTrackSession();
    } else if (e.detail.name === "deleteTrackSession") {
      this.deleteTrackSession();
    } else if (e.detail.name === "editTrackSession") {
      this.editTrackSession();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "accessName") {
      this.accessName = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("radio-change-event")
  radioChangeEventHandler(e) {
    if (e.detail.name === "trackDays") {
      this.currentTrackDay = e.detail.value;
    }
    console.log(`this.currentTrackDay`);
    console.log(this.currentTrackDay);
    // this.validateInputs();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "accessStartDate") {
      this.accessStartDate = e.detail.value;
    } else if (e.detail.name === "accessEndDate") {
      this.accessEndDate = e.detail.value;
    }
    this.validateInputs();
  }

  /*---
  Props
  ---*/
  @Prop() trackDaysString: string;
  @Prop() subTicketsString: string;
  @Prop() accessDatesString: string = "";

  /*----
  States  
  ----*/
  @State()
  mode: string = "viewTrackSession";
  @State() isSaveAccessDateDisabled: boolean = true;
  @State() accessDates: any = [];
  @State() isMultiDayAccess: boolean = false;
  @State() trackDays_Array: any = [];
  @State() trackDays_RadioData: any = [];
  @State() currentTrackDay: string = "";

  /*------
  Watchers  
  ------*/
  @Watch("accessDatesString") accessDatesStringWatcher(
    newVal: string,
    oldVal: string
  ) {
    if (newVal != oldVal) {
      this.generateAccessDateList();
    }
  }

  /*-------
  Variables  
  -------*/
  private accessName: string = "";
  private accessStartDate: string = "";
  private accessEndDate: string = "";

  private accessDateId_Old: string = "";
  private accessName_Old: string = "";
  private accessIsMultiDay_Old: boolean = false;
  private accessStartDate_Old: string = "";
  private accessEndDate_Old: string = "";

  // private sessionName_New: string = "";
  // private sessionUrl_New: string = "";
  // private sessionTime_New: string = "";
  // private sessionIsDependent_New: boolean = true;

  // private sessionName_Old: string = "";
  // private sessionUrl_Old: string = "";
  // private sessionTime_Old: string = "";
  // private sessionIsDependent_Old: boolean = true;

  /*---------------
  Lifecycle Methods  
  ---------------*/
  componentWillLoad() {
    // console.log("");
    // console.log("trackDaysString");
    // console.log(this.trackDaysString);
    // console.log("subTicketsString");
    // console.log(this.subTicketsString);
    // console.log("");
    this.generateTrackDays_Array();
    this.generateTrackDays_RadioData();
  }

  /*-------
  Functions  
  -------*/
  createTrackSession() {
    let obj = {
      id: Math.random().toString(),
      accessName: this.accessName,
      isMultiDayAccess: this.isMultiDayAccess,
      accessStartDate: this.accessStartDate,
      accessEndDate: this.accessEndDate,
    };
    this.addAccessDates_partialTicket.emit({
      accessDateObj: obj,
    });
    this.isSaveAccessDateDisabled = true;
    this.mode = "viewTrackSession";
    this.resetComponent();
  }

  deleteTrackSession() {
    console.log("delete access date");
  }

  editTrackSession() {
    console.log("edit access date");
  }

  generateAccessDateList() {
    this.accessDates = JSON.parse(this.accessDatesString);
    this.accessDates = [...this.accessDates];
  }

  resetComponent() {
    this.accessName = "";
    this.isMultiDayAccess = false;
    this.accessStartDate = "";
    this.accessEndDate = "";

    this.accessName_Old = "";
    this.accessIsMultiDay_Old = false;
    this.accessStartDate_Old = "";
    this.accessEndDate_Old = "";
  }

  validateInputs() {
    if (this.mode === "newTrackSession") {
      this.validateInputs_NewAccessDate();
    } else if (this.mode === "editTrackSession") {
      this.validateInputs_EditAccessDate();
    }
  }

  validateInputs_NewAccessDate() {
    if (this.isMultiDayAccess) {
      if (
        this.accessName.length > 0 &&
        this.accessStartDate.length > 0 &&
        this.accessEndDate.length > 0
      ) {
        this.isSaveAccessDateDisabled = false;
      } else {
        this.isSaveAccessDateDisabled = true;
      }
    } else {
      if (this.accessName.length > 0 && this.accessStartDate.length > 0) {
        this.isSaveAccessDateDisabled = false;
      } else {
        this.isSaveAccessDateDisabled = true;
      }
    }
  }

  validateInputs_EditAccessDate() {
    let hasAccessNameChanged: boolean = false;
    let hasAccessStartDateChanged: boolean = false;
    let hasAccessEndDateChanged: boolean = false;

    if (this.accessName != this.accessName_Old) {
      hasAccessNameChanged = true;
    } else {
      hasAccessNameChanged = false;
    }

    if (this.accessStartDate != this.accessStartDate_Old) {
      hasAccessStartDateChanged = true;
    } else {
      hasAccessStartDateChanged = false;
    }

    if (this.accessEndDate != this.accessEndDate_Old) {
      hasAccessEndDateChanged = true;
    } else {
      hasAccessEndDateChanged = false;
    }

    if (
      hasAccessNameChanged ||
      hasAccessStartDateChanged ||
      hasAccessEndDateChanged
    ) {
      this.isSaveAccessDateDisabled = false;
    } else {
      this.isSaveAccessDateDisabled = true;
    }
  }

  generateTrackDays_Array() {
    this.trackDays_Array = JSON.parse(this.trackDaysString);
    this.trackDays_Array = [...this.trackDays_Array];
  }

  generateTrackDays_RadioData() {
    let isLabelChecked: boolean = true;

    this.trackDays_Array.map((item: any) => {
      let obj = {
        val: item.trackDay,
        label: item.trackDay,
        isChecked: isLabelChecked,
      };

      this.trackDays_RadioData.push(obj);

      if (isLabelChecked) {
        this.currentTrackDay = item.trackDay;
        isLabelChecked = false;
      }
    });

    this.trackDays_RadioData = [...this.trackDays_RadioData];
  }

  /*-------------------
  Functional Components
  ------------------ */
  RadioButtonGroup: FunctionalComponent = () => (
    <div class="radio-button-group">
      {this.trackDays_RadioData.map((item) => (
        <c-radio
          variant="button"
          name="trackDays"
          label1={item.val}
          label2="0 sessions"
          val={item.val}
          isChecked={item.isChecked}
        ></c-radio>
      ))}
    </div>
  );

  /*-------------------
  Functional Components 
  -------------------*/
  ViewTrackSession: FunctionalComponent = () =>
    this.accessDates.length > 0 ? (
      this.accessDates.map((date: any) => (
        <c-ticket-partial-access-date-item
          name={date.accessName}
          dateId={date.id}
          isMultiDay={date.isMultiDayAccess}
          startDate={date.accessStartDate}
          endDate={date.accessEndDate}
        ></c-ticket-partial-access-date-item>
      ))
    ) : (
      <div class="no-access-dates-container">
        <c-text>Found 0 sessions on {this.currentTrackDay}</c-text>
      </div>
    );

  InputTrackSession: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          SESSION NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="sessionName"
          placeholder="e.g. Humour in UX, Design Systems etc"
          isDisabled={false}
          value={this.mode === "editTrackSession" ? this.accessName : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          SESSION URL
        </c-text>
        <c-textbox
          input-type="text"
          name="sessionUrl"
          placeholder="Link to session description"
          isDisabled={false}
          value={this.mode === "editTrackSession" ? this.accessName : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        {/* <c-text>Do you want to create a multi-day access?</c-text> */}
        <c-text type="modalLabel" isMandatory={true}>
          ALLOW MULTI-DAY ACCESS?
        </c-text>
        <div class="radio-group">
          <c-radio
            name="accessType"
            label1="Yes"
            label2=""
            label3=""
            val="multiDay"
            isChecked={this.isMultiDayAccess}
          ></c-radio>
          <c-radio
            name="accessType"
            label1="No"
            label2=""
            label3=""
            val="singleDay"
            isChecked={!this.isMultiDayAccess}
          ></c-radio>
        </div>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {this.isMultiDayAccess
            ? "SELECT ACCESS START & END DATES"
            : "SELECT ACCESS DATE"}
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="accessStartDate"
            pickTime={false}
            date={this.accessStartDate}
          ></c-date-picker>
          {this.isMultiDayAccess && (
            <c-date-picker
              name="accessEndDate"
              pickTime={false}
              date={this.accessEndDate}
            ></c-date-picker>
          )}
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelTrackSession">
            Cancel
          </c-button>
          <div class="button-group">
            <c-button
              type="ghost_Small"
              name="createTrackSession"
              isDisabled={this.isSaveAccessDateDisabled}
            >
              Create
            </c-button>
          </div>
        </c-row>
      </div>
    </div>
  );

  EditTrackSession: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          ACCESS NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="accessName"
          placeholder="e.g. 1 Day conference ticket, Workshop/Course pass"
          isDisabled={false}
          value={this.mode === "editTrackSession" ? this.accessName_Old : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {this.isMultiDayAccess
            ? "SELECT ACCESS START & END DATES"
            : "SELECT ACCESS DATE"}
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="accessStartDate"
            pickTime={false}
            date={this.accessStartDate_Old}
          ></c-date-picker>
          {this.accessIsMultiDay_Old && (
            <c-date-picker
              name="accessEndDate"
              pickTime={false}
              date={this.accessEndDate_Old}
            ></c-date-picker>
          )}
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelTrackSession">
            Cancel
          </c-button>
          <c-row>
            <c-button type="ghost_Danger_Small" name="deleteTrackSession">
              Delete
            </c-button>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <c-button
              type="ghost_Small"
              name="editTrackSession"
              isDisabled={this.isSaveAccessDateDisabled}
            >
              Save
            </c-button>
          </c-row>
        </c-row>
      </div>
    </div>
  );

  CreateTrackSession: FunctionalComponent = () => (
    <div class="ticket-price-table__footer">
      <c-button
        name="newTrackSession"
        type="newTrackSession"
        isDisabled={false}
        isInAction={false}
      ></c-button>
    </div>
  );

  render() {
    return (
      <Host>
        <div class="sessions__top-row">
          <c-text type="modalLabel" isMandatory={true}>
            ADD SESSIONS TO EACH TRACK DAY
          </c-text>
        </div>
        <this.RadioButtonGroup></this.RadioButtonGroup>
        <div class="sessions-table__header">
          <div class="sessions-table__header--item sessions-table__header--item--1">
            <c-text>Session name</c-text>
          </div>
          <div class="sessions-table__header--item sessions-table__header--item--2">
            <c-text>Session type</c-text>
          </div>
          <div class="sessions-table__header--item sessions-table__header--item--3">
            <c-text>Session price</c-text>
          </div>
        </div>
        <div class="sessions-table__body">
          {this.mode === "viewTrackSession" && (
            <this.ViewTrackSession></this.ViewTrackSession>
          )}
          {this.mode === "newTrackSession" && (
            <this.InputTrackSession></this.InputTrackSession>
          )}
          {this.mode === "editTrackSession" && (
            <this.EditTrackSession></this.EditTrackSession>
          )}
          {this.mode === "viewTrackSession" && (
            <this.CreateTrackSession></this.CreateTrackSession>
          )}
        </div>
      </Host>
    );
  }
}
