import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "c-inputbox",
  styleUrl: "c-inputbox.css",
})
export class CInputbox {
  inputBox!: HTMLInputElement;

  @Prop() type: string;
  @Prop() name: string;
  @Prop() placeholder: string;
  @Prop() isDisabled: boolean;
  @Prop() isAutofocus: boolean;
  @Prop() value: string;

  @Event({
    eventName: "input-event",
    bubbles: true,
  })
  inputEvent: EventEmitter;

  handleInput = (event) => {
    if (this.name === "email") {
      Store.setEmail(event.target.value.toLowerCase().trim());
    } else if (this.name === "firstName") {
      Store.setFirstName(event.target.value.trim());
    } else if (this.name === "lastName") {
      Store.setLastName(event.target.value.trim());
    } else if (this.name === "emailVerificationCode") {
      Store.setEmailVerificationCode(event.target.value.trim());
    } else if (this.name === "jobDegree") {
      Store.setJobDegree(event.target.value.trim());
    } else if (this.name === "orgInsti") {
      Store.setOrgInsti(event.target.value.trim());
    } else if (this.name === "membershipid") {
      let memberID = event.target.value.trim();
      memberID = memberID.toUpperCase();
      state.submittedID = memberID;
    } else if (this.name === "passwordResetCode") {
      Store.setPasswordResetCode(event.target.value.trim());
    } else if (this.name === "password") {
      Store.setPassword(event.target.value.trim());
    } else if (this.name === "confirmpassword") {
      Store.setConfirmPassword(event.target.value.trim());
    } else if (this.name === "rmRegistrantSearch") {
      state.registrantSearchString = event.target.value.trim();
    } else if (this.name === "businessName") {
      Store.setBusinessName(event.target.value.trim());
    } else if (this.name === "taxID") {
      Store.setTaxID(event.target.value.trim());
    } else if (this.name === "billingAddressLine1") {
      Store.setBillingAddressLine1(event.target.value.trim());
    } else if (this.name === "billingAddressLine2") {
      Store.setBillingAddressLine2(event.target.value.trim());
    } else if (this.name === "billingAddressLine3") {
      Store.setBillingAddressLine3(event.target.value.trim());
    }
    this.inputEvent.emit({
      name: this.name,
      value: event.target.value.trim(),
    });
  };

  render() {
    return (
      <input
        class={this.isDisabled ? "inputbox disabled" : "inputbox"}
        type={this.type}
        name={this.name}
        placeholder={this.placeholder}
        onInput={(event) => this.handleInput(event)}
        disabled={this.isDisabled}
        value={this.value}
        ref={(el) => (this.inputBox = el as HTMLInputElement)}
      />
    );
  }
}
