:host {
  position: fixed;
  display: none;
  justify-content: space-between;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 0.75em 1em;
  background: white;
  box-sizing: border-box;
  align-items: center;
  -webkit-box-shadow: -1px -2px 4px -1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: -1px -2px 4px -1px rgba(0, 0, 0, 0.1);
  box-shadow: -1px -2px 4px -1px rgba(0, 0, 0, 0.1);
}

label {
  display: flex;
  align-items: center;
}

label ion-icon {
  font-size: 1.2em;
  margin-right: 0.25em;
}

@media only screen and (max-width: 768px) {
  :host {
    display: flex;
  }
}
