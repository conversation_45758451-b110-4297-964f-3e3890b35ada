import { Component, Host, h } from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-mobile-cart",
  styleUrl: "c-mobile-cart.css",
  shadow: true,
})
export class CMobileCart {
  componentDidLoad() {
    this.getCart();
  }

  getCart() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/cart`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.cartItems = response.data.payload.cartItems;
          state.cartTotal = response.data.payload.cartTotal;
          if (state.cartItems.length > 0) {
            state.isCheckoutBtnDisabled = false;
          } else {
            state.isCheckoutBtnDisabled = true;
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <Host>
        <label>
          <ion-icon name="cart-outline"></ion-icon>
          <e-text>Cart: ₹{state.cartTotal}</e-text>
        </label>
        <c-btn
          name="checkout"
          label="Checkout"
          action-label="Checking.."
          is-in-action={false}
          is-disabled={state.isCheckoutBtnDisabled}
        ></c-btn>
      </Host>
    );
  }
}
