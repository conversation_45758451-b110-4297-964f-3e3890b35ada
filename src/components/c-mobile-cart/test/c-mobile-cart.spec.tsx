import { newSpecPage } from '@stencil/core/testing';
import { CMobileCart } from '../c-mobile-cart';

describe('c-mobile-cart', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CMobileCart],
      html: `<c-mobile-cart></c-mobile-cart>`,
    });
    expect(page.root).toEqualHtml(`
      <c-mobile-cart>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-mobile-cart>
    `);
  });
});
