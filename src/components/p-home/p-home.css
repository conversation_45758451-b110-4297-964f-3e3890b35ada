p-home main {
  width: 97.5%;
  max-width: 1250px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  padding-top: 4em;
}

p-home .checkout-btn-container c-btn button {
  margin: 0 auto;
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 1em;
  padding-bottom: 1em;
}

p-home c-section-buy-membership .container {
  margin-bottom: 3em;
}

p-home .event-card {
  width: 250px;
  padding: 1em;
  border-radius: 0.4em;
  /* background: #0863c8; */
  /* background: -webkit-linear-gradient(top left, #0863c8, #539ae7);
  background: -moz-linear-gradient(top left, #0863c8, #539ae7);
  background: linear-gradient(to bottom right, #0863c8, #539ae7); */
  background: -webkit-linear-gradient(top left, #0f4143, #52806b);
  background: -moz-linear-gradient(top left, #0f4143, #52806b);
  background: linear-gradient(to bottom right, #0f4143, #52806b);
  box-shadow: 0 10px 20px rgb(0 0 0 / 5%), 0 6px 6px rgb(0 0 0 / 5%);
  color: #e7ecec;
}

p-home .event-card h1 {
  font-size: 1.5em;
  margin: 0;
  color: #cfd9d9;
}
/* p-home .event-card p {
  margin-top: 0;
  font-size: 0.9em;
  color: #e7ecec;
} */

p-home .event-card a {
  /* display: block; */
  text-decoration: none;
  color: #e7ecec;
  /* font-weight: 700; */
  /* padding: 0.7em 1em;
  border-radius: 0.25em; */
  transition: all 0.15s ease-in;
  /* outline: none; */
  /* background: #688789; */
  /* font-size: 0.9em;
  border: 1px solid rgba(255, 255, 255, 0.5);
  text-align: center; */
}
p-home .event-card a:hover {
  /* background: #1f4345; */
  background: rgba(255, 255, 255, 0.2);
}

p-home .card-footer {
  width: 300px;
  padding: 1.25em 1.5em 1em 1.5em;
  border-radius: 0em 0em 0.4em 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

p-home .card-footer p {
  margin: 0;
}

p-home .card-header h1 {
  margin: 0;
}

p-home .event-message {
  margin: 0;
  font-size: 0.9em;
  margin-top: 1.5em;
}

p-home .highlight {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25em 1em;
  border-radius: 0.25em;
  margin: 0 0 1em 0;
}

p-home .event-card-blue {
  background: -webkit-linear-gradient(top left, #053b78, #3982d3);
  background: -moz-linear-gradient(top left, #053b78, #3982d3);
  background: linear-gradient(to bottom right, #053b78, #3982d3);
}

p-home .event-card-blue h1 {
  color: #cee0f4;
}

p-home .event-card-blue p {
  color: #cee0f4;
}

p-home .subheading {
  margin-top: 0;
  font-size: 0.8em;
}

p-home .archived-events-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

p-home .year-container c-text .section-divider:first-child {
  margin-top: 0;
}

p-home .skel-event-card {
  width: 100%;
  display: flex;
  background: white;
  border-radius: var(--site-border-radius);
  border: var(--site-border);
  margin-bottom: 2em;
}

p-home .skel-card-container {
  display: block;
  justify-content: space-between;
  flex-wrap: wrap;
}

p-home .skel-event-card-img {
  width: 330px;
  height: 250px;
  background: #ccc;
  background-image: linear-gradient(
    90deg,
    #f4f4f4 0px,
    rgba(229, 229, 229, 0.8) 40%,
    #f4f4f4 100%
  );
  background-size: 200%;
  animation: shine-line 1s infinite ease-out;
}

p-home .skel-event-card-content {
  padding: 1em;
  width: 300px;
}

p-home .skel-event-card-content-line {
  width: 100%;
  height: 10px;
  /* background: rgba(0, 0, 0, 0.1); */
  background: #ccc;
  background-image: linear-gradient(
    90deg,
    #f4f4f4 0px,
    rgba(229, 229, 229, 0.8) 40%,
    #f4f4f4 100%
  );
  background-size: 200%;
  animation: shine-line 1s infinite ease-out;
  border-radius: 1em;
}

p-home .skel-line-1 {
  margin-bottom: 1em;
  width: 60%;
}

p-home .skel-line-2 {
  margin-bottom: 5em;
}

p-home .skel-line-3 {
  margin-bottom: 5em;
  width: 50%;
}

p-home .no-events-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 400px;
  text-align: center;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: var(--site-border-radius);
}

p-home .no-events-container ion-icon {
  font-size: 3em;
  color: rgba(0, 0, 0, 0.2);
}

@keyframes shine-line {
  0% {
    background-position: -100px;
  }
  40%,
  100% {
    background-position: 140px;
  }
}

p-home c-page .default-page {
  margin-top: 6em;
  padding-bottom: 6em;
}

p-home .show-on-mobile {
  display: none;
}

p-home .show-on-desktop {
  display: block;
}

p-home .membership-banner-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1em;
}


@media only screen and (max-width: 768px) {
  p-home c-page .default-page {
    margin-top: 1em;
  }

  p-home c-control-bar .control-bar-container {
    display: block;
    position: static;
    margin: 0 0 1.5em 0;
    width: 90vw;
    box-sizing: border-box;
    margin-bottom: 0;
  }

  p-home .skel-event-card {
    display: block;
    width: 100%;
  }

  p-home .skel-event-card-img {
    width: 100%;
  }

  p-home .skel-event-card-content {
    width: 100%;
    box-sizing: border-box;
  }

  p-home .dropdown-menu-container {
    display: flex;
    flex-direction: row-reverse;
    margin-bottom: 1.5em;
  }

  p-home .show-on-mobile {
    display: flex;
  }

  p-home .show-on-desktop {
    display: none;
  }

  p-home .membership-banner-row {
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-start;
  }
}
