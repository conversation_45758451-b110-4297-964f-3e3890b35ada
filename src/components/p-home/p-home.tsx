import {
  Component,
  Event,
  EventEmitter,
  Prop,
  FunctionalComponent,
  State,
  Listen,
  h,
} from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";

import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-home",
  styleUrl: "p-home.css",
})
export class PHome {
  @Prop() history: RouterHistory;

  @State() isBannerActive: boolean = false;
  @State() isToastActive: boolean = false;
  @State() isFetchingData: boolean = true;
  @State() isUpcomingEventsFetched: boolean = false;
  @State() isArchivedEventsFetched: boolean = false;
  @State() activeSection: string = "upcomingEvents";
  @State()
  isModalActive: boolean = false;
  @State() modalName: string = "";
  @State() upcomingEventsArray: any = [];
  @State() archivedEventsArray: any = [];
  @State() isMobileDashboardOptionsVisible: boolean = false;

  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;
  private navOpts: any = [
    {
      type: "label",
      label: "Events",
      isFirstLabel: true,
    },
    {
      type: "navItem",
      name: "upcomingEvents",
      label: "Upcoming",
      state: "active",
      icon: "calendar-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "archivedEvents",
      label: "Archived",
      state: "",
      icon: "archive-outline",
      subText: "",
      route: "",
    },
    // {
    //   type: "label",
    //   label: "HCIPAI Membership",
    //   isFirstLabel: false,
    // },
    // {
    //   type: "navItem",
    //   name: "membership",
    //   label: "Get Membership",
    //   state: "",
    //   icon: "people-outline",
    //   subText: "",
    //   route: "",
    // },
  ];

  private eventType: string = "";
  private upcomingPublishingFilter: string = "";
  private archivedPublishingFilter: string = "";

  @Event({
    eventName: "getAccountInfoEvent",
    bubbles: true,
  })
  getAccountInfoEvent: EventEmitter;

  @Listen("checkout-btn-click-event")
  buyNowHandler() {
    this.history.push("/confirm", {});
  }

  // @Listen("membership-selected")
  // membershipSelectedHandler() {
  //   this.history.push("/confirm", {});
  // }

  @Listen("goToMembershipCheckout")
  membershipCheckoutHandler() {
    this.history.push("/confirm", {});
  }

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(e) {
    if (e.detail.navName === "homePageNav") {
      this.activeSection = e.detail.cNavItemName;
      if (this.activeSection === "archivedEvents") {
        this.fetchArchivedEvents();
      } else if (this.activeSection === "upcomingEvents") {
        this.fetchUpcomingEvents();
      }
    }
  }

  @Listen("membership-removed")
  membershipRemovedHandler() {
    this.isToastActive = false;
    this.showToast({
      type: "Success",
      label: "Membership removed!",
    });
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  @Listen("button-click")
  buttonClickHandlerOld(e) {
    if (e.detail.name === "openCreateEventModal") {
      this.openModal(e.detail.name);
    } else if (e.detail.name === "goToDashboard") {
      this.history.push(`/dashboard/${e.detail.value}`, {});
    } else if (e.detail.name === "goToRegistration") {
      this.history.push(`/registration/${e.detail.value}`, {});
    } else if (e.detail.name === "goToMembership") {
      this.activeSection = "membership";
    }
  }

  @Listen("buttonClick") handleButtonClick(e) {
    if (e.detail.name === "openEditEventModal") {
      this.openModal(e.detail.name);
    } else if (e.detail.name === "goToEventConfiguration") {
      this.history.push(`/configure/${e.detail.value}`, {});
    } else if (e.detail.name === "showDashboardViewOptions") {
      this.isMobileDashboardOptionsVisible = true;
    } else if (e.detail.name === "hideDashboardViewOptions") {
      this.isMobileDashboardOptionsVisible = false;
    }
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.isModalActive = false;
    this.modalName = "";
    this.getAccountInfoEvent.emit();
  }

  @Listen("fetchNewData")
  fetchNewDataHandler() {
    this.fetchUpcomingEvents();
  }

  @Listen("eventEdited")
  eventEditedHandler() {
    if (this.activeSection === "upcomingEvents") {
      this.fetchUpcomingEvents();
    } else if (this.activeSection === "archivedEvents") {
      this.fetchArchivedEvents();
    }
  }

  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  componentDidLoad() {
    this.getAccountInfoEvent.emit();
    this.fetchUpcomingEvents();
  }

  fetchArchivedEvents() {
    this.eventType = "archived";
    this.isArchivedEventsFetched = false;

    let payload = {
      eventType: this.eventType,
      publishingFilter: this.archivedPublishingFilter,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getevents`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Failed to fetch data");
        } else if (response.data.status === "Success") {
          this.archivedEventsArray = response.data.payload;
          this.archivedEventsArray = [...this.archivedEventsArray];
          this.isArchivedEventsFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  fetchUpcomingEvents() {
    this.eventType = "upcoming";
    this.isUpcomingEventsFetched = false;

    let payload = {
      eventType: this.eventType,
      publishingFilter: this.upcomingPublishingFilter,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getevents`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Failed to fetch data");
        } else if (response.data.status === "Success") {
          this.upcomingEventsArray = response.data.payload;
          this.upcomingEventsArray = [...this.upcomingEventsArray];
          this.isUpcomingEventsFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  SkelEventCard: FunctionalComponent = () => (
    <div class="skel-event-card">
      <div class="skel-event-card-img"></div>
      <div class="skel-event-card-content">
        <div class="skel-event-card-content-line skel-line-1"></div>
        <div class="skel-event-card-content-line skel-line-2"></div>
        <div class="skel-event-card-content-line skel-line-3"></div>
      </div>
    </div>
  );

  SkelEvents: FunctionalComponent = () => (
    <c-section>
      <div class="skel-card-container">
        <this.SkelEventCard></this.SkelEventCard>
        {/* <this.SkelEventCard></this.SkelEventCard>
        <this.SkelEventCard></this.SkelEventCard> */}
      </div>
    </c-section>
  );

  UpcomingEvents: FunctionalComponent = () => (
    <c-section>
      <c-banner>
        <c-text>
          <strong>Become a HCIPAI member</strong>
          <br />
          Get exclusive content, networking opportunities & event discounts
        </c-text>
        <div class="membership-banner-row">
          <c-text type="subtext">
            <c-link url="https://www.indiahci.org/hcipai-memberships/" target="_blank">Read membership benefits</c-link>
          </c-text>
          <c-btn
            name="goToMembership"
            label="Get HCIPAI Membership"
          ></c-btn>
        </div>
      </c-banner>
      <br/>
      {state.occupation === "student" &&
      state.isIdCardExists &&
      !state.isIdCardVerified ? (
        <div>
          <c-banner>
            Your student ID is under verification but you can still buy tickets
          </c-banner>
          <br />
        </div>
      ) : (
        ""
      )}

      {/* Insert HCIPAI membership banner */}
      {this.upcomingEventsArray.length > 0 ? (
        this.upcomingEventsArray.map((event: any) => (
          <c-card
            eventId={event.id}
            eventCode={event.code}
            eventName={event.name}
            eventTagline={event.tagline}
            eventVenueLabel={event.venueLabel}
            eventVenueUrl={event.venueUrl}
            eventWebsiteUrl={event.website}
            eventLogoUrl={event.logoUrl}
            eventBannerUrl={event.bannerUrl}
            eventPosterUrl={event.posterUrl}
            eventStartsOn={event.startsOn}
            eventEndsOn={event.endsOn}
            eventRegStartsOn={event.registrationStartsOn}
            eventRegEndsOn={event.registrationEndsOn}
            eventIsPublished={event.isPublished}
            eventIsRegistrationOpen={event.isRegistrationOpen}
            eventIsActive={event.isActive}
            eventIsManager={event.isUserManager}
            type="event"
          ></c-card>
        ))
      ) : (
        <this.NoEvents></this.NoEvents>
      )}
    </c-section>
  );

  ArchivedEvents: FunctionalComponent = () => (
    <c-section>
      <c-banner>
        <c-text>
          <strong>Become a HCIPAI member</strong>
          <br />
          Get exclusive content, networking opportunities & event discounts
        </c-text>
        <div class="membership-banner-row">
          <c-text type="subtext">
            <c-link url="https://www.indiahci.org/hcipai-memberships/" target="_blank">Read membership benefits</c-link>
          </c-text>
          <c-btn
            name="goToMembership"
            label="Get HCIPAI Membership"
          ></c-btn>
        </div>
      </c-banner>
      <br/>
      {/* Insert HCIPAI membership banner */}
      {this.archivedEventsArray.length > 0 ? (
        this.archivedEventsArray.map((event: any) => (
          <c-card
            eventId={event.id}
            eventCode={event.code}
            eventName={event.name}
            eventTagline={event.tagline}
            eventVenueLabel={event.venueLabel}
            eventVenueUrl={event.venueUrl}
            eventWebsiteUrl={event.website}
            eventLogoUrl={event.logoUrl}
            eventBannerUrl={event.bannerUrl}
            eventPosterUrl={event.posterUrl}
            eventStartsOn={event.startsOn}
            eventEndsOn={event.endsOn}
            eventRegStartsOn={event.registrationStartsOn}
            eventRegEndsOn={event.registrationEndsOn}
            eventIsArchived={event.isArchived}
            eventIsPublished={event.isPublished}
            eventIsRegistrationOpen={event.isRegistrationOpen}
            eventIsActive={event.isActive}
            eventIsManager={event.isUserManager}
            type="event"
          ></c-card>
        ))
      ) : (
        <this.NoEvents></this.NoEvents>
      )}
    </c-section>
  );

  Membership: FunctionalComponent = () => (
    <c-section>
      {state.isMember ? (
        <c-banner>
          <c-text>You are already a HCIPAI member</c-text>
          <br />
          <c-text>
            Membership type:{" "}
            {state.membershipType === "annual" && "Annual member"}
            {state.membershipType === "lifetime" && "Lifetime member"}
          </c-text>
          <c-text>Membership id: {state.membershipId}</c-text>
          {state.membershipType === "annual" && (
            <c-text>
              Membership ends on:{" "}
              {new Date(state.membershipEndDate).toString().substring(4, 15)}
            </c-text>
          )}
        </c-banner>
      ) : (
        <c-section-buy-membership></c-section-buy-membership>
      )}
    </c-section>
  );

  NoEvents: FunctionalComponent = () => (
    <div class="no-events-container">
      <div>
        <ion-icon name="calendar-clear-outline"></ion-icon>
        <c-text>No events found</c-text>
      </div>
    </div>
  );

  render() {
    return (
      <c-page>
        {this.isToastActive && (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        )}

        <c-modal name={this.modalName} is-active={this.isModalActive}></c-modal>

        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}

        <c-topbar></c-topbar>
        <c-sidebar type="left">
          <c-vnav
            nav-opts-str={JSON.stringify(this.navOpts)}
            name="homePageNav"
            activeOption={this.activeSection}
          ></c-vnav>
        </c-sidebar>
        <div class="show-on-mobile dropdown-menu-container">
          <div></div>
          <c-button
            type="toggleMobileDashboardOptions"
            name={
              this.isMobileDashboardOptionsVisible
                ? "hideDashboardViewOptions"
                : "showDashboardViewOptions"
            }
          ></c-button>
        </div>
        {this.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile">
            {state.isAdmin && <c-control-bar name="home"></c-control-bar>}
          </div>
        )}
        <div class="show-on-desktop">
          {state.isAdmin && <c-control-bar name="home"></c-control-bar>}
        </div>


        {this.activeSection === "upcomingEvents" &&
          (this.isUpcomingEventsFetched ? (
            <this.UpcomingEvents></this.UpcomingEvents>
          ) : (
            <this.SkelEvents></this.SkelEvents>
          ))}

        {this.activeSection === "archivedEvents" &&
          (this.isArchivedEventsFetched ? (
            <this.ArchivedEvents></this.ArchivedEvents>
          ) : (
            <this.SkelEvents></this.SkelEvents>
          ))}

        {this.activeSection === "membership" && (
          <this.Membership></this.Membership>
        )}
      </c-page>
    );
  }
}

injectHistory(PHome);
