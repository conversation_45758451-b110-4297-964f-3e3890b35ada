import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-radio",
  styleUrl: "c-radio.css",
})
export class CRadio {
  @Prop() variant: string = "default";
  @Prop() name: string;
  @Prop() label1: string;
  @Prop() label2: string;
  @Prop() label3: string;
  @Prop() val: string;
  @Prop() url: string;
  @Prop() isLabel2Link: boolean = false;
  @Prop() isChecked: boolean;

  @Event({
    eventName: "radio-change-event",
    bubbles: true,
  })
  radioChangeEvent: EventEmitter;

  handleRadioChange() {
    this.radioChangeEvent.emit({
      name: this.name,
      value: this.val,
    });
  }

  render() {
    if (this.variant === "button") {
      return (
        <div class="radio-button-container">
          <input
            type="radio"
            class="radio-button-input"
            id={`${this.name}-${this.val}`}
            name={this.name}
            value={this.val}
            checked={this.isChecked ? true : false}
            onChange={() => this.handleRadioChange()}
          ></input>
          <label
            class="radio-button-label"
            htmlFor={`${this.name}-${this.val}`}
          >
            <div class="radio-button-label__text-container">
              <p>{this.label1}</p>
              {this.label2 && <p>{this.label2}</p>}
            </div>
          </label>
        </div>
      );
    } else if (this.variant === "default") {
      return (
        <div class="radio-btn-container">
          <input
            id={`${this.name}-${this.val}`}
            class="radio-btn"
            type="radio"
            name={this.name}
            value={this.val}
            checked={this.isChecked ? true : false}
            onChange={() => this.handleRadioChange()}
          />
          <label class="radio-btn-label" htmlFor={`${this.name}-${this.val}`}>
            <span class="radio-btn-label-1">{this.label1}</span>{" "}
            <span class="radio-btn-label-2">
              {this.isLabel2Link ? (
                <c-text-link url={this.url} label={this.label2}></c-text-link>
              ) : (
                this.label2
              )}
            </span>
            <p class="radio-btn-label-3">{this.label3}</p>
          </label>
        </div>
      );
    }
  }
}
