c-radio .radio-btn-container {
  margin-bottom: 1em;
  margin-top: 1em;
}

c-radio .radio-btn-label {
  display: block;
  padding-bottom: 0;
  margin-bottom: 0;
}
c-radio .radio-btn-label:hover {
  cursor: pointer;
}

c-radio .radio-btn-label-2 {
  font-size: 0.8em;
}

c-radio .radio-btn-label-3 {
  margin: 0;
  padding-top: 0;
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

/* Radio button styling */
c-radio .radio-btn-container input[type="radio"] {
  position: absolute;
  opacity: 0;
}

c-radio .radio-btn-container input[type="radio"] + .radio-btn-label:before {
  content: "";
  background: #f4f4f4;
  border-radius: 100%;
  border: 1px solid #b4b4b4;
  display: inline-block;
  top: 0.1em;
  width: 1em;
  height: 1em;
  position: relative;
  margin-right: 0.5em;
  vertical-align: top;
  cursor: pointer;
  text-align: center;
  transition: var(--site-transition);
}

c-radio
  .radio-btn-container
  input[type="radio"]:checked
  + .radio-btn-label:before {
  background-color: var(--violet-400);
  box-shadow: inset 0 0 0 4px #f4f4f4;
}

c-radio
  .radio-btn-container
  input[type="radio"]:focus
  + .radio-btn-label:before {
  outline: none;
  border-color: var(--violet-400);
}

c-radio
  .radio-btn-container
  input[type="radio"]:disabled
  + .radio-btn-label:before {
  box-shadow: inset 0 0 0 4px #f4f4f4;
  border-color: #b4b4b4;
  background: #b4b4b4;
}

c-radio
  .radio-btn-container
  input[type="radio"]
  + .radio-btn-label:empty:before {
  margin-right: 0;
}

/*----------
Radio Button
----------*/

c-radio .radio-button-container {
  display: flex;
}

c-radio .radio-button-input {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: white;
  color: black;
}

c-radio .radio-button-label {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 0.5em;
  -webkit-transition: all 100ms ease-in;
  -moz-transition: all 100ms ease-in;
  transition: all 100ms ease-in;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--accent-color);
}

c-radio .radio-button-label:hover {
  border: 1px solid var(--accent-color);
}

c-radio .radio-button-input:active + .radio-button-label {
  border: 1px solid var(--accent-color);
  background: var(--accent-color-bg-lightest);
}

c-radio .radio-button-input:checked + .radio-button-label {
  border: 1px solid var(--accent-color);
  background: var(--accent-color-bg-lightest);
}

c-radio .radio-button-label__text-container p:last-child {
  font-size: 0.8em;
}
