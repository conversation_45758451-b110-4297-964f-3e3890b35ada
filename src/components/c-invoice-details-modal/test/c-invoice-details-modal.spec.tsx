import { newSpecPage } from '@stencil/core/testing';
import { CInvoiceDetailsModal } from '../c-invoice-details-modal';

describe('c-invoice-details-modal', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CInvoiceDetailsModal],
      html: `<c-invoice-details-modal></c-invoice-details-modal>`,
    });
    expect(page.root).toEqualHtml(`
      <c-invoice-details-modal>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-invoice-details-modal>
    `);
  });
});
