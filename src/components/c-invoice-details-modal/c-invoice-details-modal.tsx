import {
  Component,
  Event,
  EventEmitter,
  Prop,
  State,
  Listen,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-invoice-details-modal",
  styleUrl: "c-invoice-details-modal.css",
})
export class CInvoiceDetailsModal {
  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "downloadSingleInvoice") {
      this.initiateSingleInvoiceDownload(e.detail.value);
    } else if (e.detail.name === "sendAllInvoices") {
      this.sendAllInvoices();
    } else if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    }
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  componentWillLoad() {
    this.processInvoiceGroup();
  }

  processInvoiceGroup() {
    this.invoiceGroup_Processed = JSON.parse(this.invoiceGroup);
    this.isInvoiceGroupDataProcessed = true;
    this.invoiceGroupId = this.invoiceGroup_Processed.id;
  }

  initiateSingleInvoiceDownload(invoiceNo: string) {
    let downloadLink: string = "";
    let eventCode: string = "";

    this.invoiceGroup_Processed.invoices.map((invoice: any) => {
      if (invoice.invoiceNo === invoiceNo) {
        eventCode = invoice.eventCode;
      }
    });

    if (eventCode === "membership") {
      let urlEncodedInvoiceNo: string = encodeURIComponent(invoiceNo);
      downloadLink = `${state.baseUrl}/download-invoice/${urlEncodedInvoiceNo}`;
    } else {
      downloadLink = `${state.baseUrl}/download-invoice/${invoiceNo}`;
    }

    window.open(downloadLink);
  }

  sendAllInvoices() {
    this.isSendingInvoices = true;
    let payload = {
      invoiceGroupId: this.invoiceGroupId,
    };
    axios({
      method: "POST",
      data: payload,
      baseURL: `${state.baseUrl}/send-all-invoices`,
      responseType: "json",
    })
      .then((response) => {
        this.showToast({
          type: response.data.status,
          label: response.data.msg,
        });
        this.isSendingInvoices = false;
      })
      .catch((error) => {
        alert(`${error} : Please try again`);
        this.isSendingInvoices = false;
      });
  }

  @Prop() invoiceGroup: string;

  @State() isToastActive: boolean = false;
  @State() isInvoiceGroupDataProcessed: boolean = false;
  @State() invoiceGroup_Processed: any;
  @State() isSendingInvoices: boolean = false;

  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;
  private invoiceGroupId: string = "";

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  render() {
    return (
      <div class="modal-container">
        {this.isToastActive && (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        )}

        {this.isInvoiceGroupDataProcessed ? (
          <div>
            <div class="row card">
              <div>
                {" "}
                <h2>{this.invoiceGroup_Processed.name}</h2>
                <c-text type="subtext">
                  Created on{" "}
                  {new Date(this.invoiceGroup_Processed.createdOn)
                    .toString()
                    .substring(4, 15)}
                </c-text>
              </div>
              <c-button
                type="modalClose"
                name="closeModal"
                icon-name=""
                label=""
              ></c-button>
            </div>
            <c-row>
              <p class="invoice-details-container__header__email">
                {" "}
                Invoices:{" "}
                <strong>{this.invoiceGroup_Processed.invoiceCount}</strong>
              </p>
              <p class="invoice-details-container__header__email">
                {" "}
                Mail attempts: {this.invoiceGroup_Processed.bulkSendCount}
              </p>
            </c-row>
            <div class="invoice-details-container">
              <div class="invoice-details-container__header">
                <c-button
                  name="sendAllInvoices"
                  isInAction={this.isSendingInvoices}
                >
                  Send all invoices
                </c-button>
              </div>
              <div class="invoice-details-container__info">
                <div class="invoice-details-container__info__header">
                  <p class="row-item-1">
                    <strong>Buyer</strong>
                  </p>
                  <p class="row-item-2">
                    <strong>Invoice No</strong>
                  </p>
                  <p class="row-item-3">
                    <strong>Download</strong>
                  </p>
                </div>
              </div>
              <div class="invoice-details-container__list">
                {this.invoiceGroup_Processed.invoices.map((invoice: any) => (
                  <div>
                    <div class="invoice-details-container__list__item desktop-only-ui">
                      <p class="row-item-1">
                        {invoice.buyerName}
                        <br />
                        <c-text-link
                          url={`mailto:${invoice.buyerEmail}`}
                          label={invoice.buyerEmail}
                        ></c-text-link>
                      </p>
                      <p class="row-item-2">{invoice.invoiceNo}</p>
                      <p class="row-item-3">
                        <c-button
                          name="downloadSingleInvoice"
                          type="downloadButton"
                          value={invoice.invoiceNo}
                        ></c-button>
                      </p>
                    </div>
                    <div class="invoice-details-container__list__item  mobile-only-ui">
                      <div class="list-item-row">
                        <ion-icon name="person-outline"></ion-icon>{" "}
                        &nbsp;&nbsp;&nbsp;
                        <p class="invoice-total"> {invoice.buyerName}</p>
                      </div>
                      <div class="list-item-row">
                        <ion-icon name="mail-outline"></ion-icon>{" "}
                        &nbsp;&nbsp;&nbsp;
                        <p class="invoice-total">
                          <c-text-link
                            url={`mailto:${invoice.buyerEmail}`}
                            label={invoice.buyerEmail}
                          ></c-text-link>
                        </p>
                      </div>
                      <div class="list-item-row">
                        <ion-icon name="document-text-outline"></ion-icon>{" "}
                        &nbsp;&nbsp;&nbsp;
                        <p class="invoice-total">{invoice.invoiceNo}</p>
                      </div>
                      <div class="list-item-row">
                        <ion-icon name="download-outline"></ion-icon>{" "}
                        &nbsp;&nbsp;&nbsp;
                        <c-button
                          name="downloadSingleInvoice"
                          type="downloadButton"
                          value={invoice.invoiceNo}
                        ></c-button>{" "}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        )}
      </div>
    );
  }
}
