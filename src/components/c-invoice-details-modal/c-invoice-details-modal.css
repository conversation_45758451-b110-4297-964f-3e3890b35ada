c-invoice-details-modal .modal-container {
  background: var(--bg-color);
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
  position: fixed;
  top: 1em;
  box-sizing: border-box;
  width: 60vw;
  max-height: 80vh;
  overflow: auto;
  margin: 4em auto 0 auto;
  left: 0;
  right: 0;
  padding: 1em;
}

c-invoice-details-modal p {
  font-size: 0.9em;
  margin: 0;
}

c-invoice-details-modal h2 {
  font-size: 1.25em;
  margin: 0;
  color: var(--accent-color);
}

c-invoice-details-modal .row {
  display: flex;
  justify-content: space-between;
}

c-invoice-details-modal label {
  font-size: 0.75em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.5);
}

c-invoice-details-modal .card {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
}

c-invoice-details-modal .invoice-details-container {
  margin-top: 0.5em;
}

c-invoice-details-modal .email-label {
  margin-bottom: 0.5em;
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.5);
}

c-invoice-details-modal .invoice-details-container__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em 0.25em 0 0;
  flex-direction: row-reverse;
}

c-invoice-details-modal ul {
  display: flex;
  align-items: center;
  justify-content: space-between;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

c-invoice-details-modal ul li {
  font-size: 0.75em;
}

c-invoice-details-modal .row-item-1 {
  width: 48%;
  /* background: pink; */
}
c-invoice-details-modal .row-item-2 {
  width: 32.5%;
  /* background: yellow; */
}
c-invoice-details-modal .row-item-3 {
  width: 15%;
  /* background: pink; */
}

c-invoice-details-modal .invoice-details-container__list__item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1em 0;
  margin: 1em 0;
}

c-invoice-details-modal .invoice-details-container__list__item .row-item-1 {
  width: 49%;
}
c-invoice-details-modal .invoice-details-container__list__item .row-item-2 {
  width: 31%;
}
c-invoice-details-modal .invoice-details-container__list__item .row-item-3 {
  width: 13%;
}

c-invoice-details-modal .invoice-details-container__info__header {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.5);
  text-transform: uppercase;
  font-size: 0.8em;
}

c-invoice-details-modal .invoice-details-container__list__item {
  display: flex;
  align-items: center;
}

c-invoice-details-modal .invoice-details-container__info__header li {
  color: rgba(0, 0, 0, 0.5);
  text-transform: uppercase;
}

c-invoice-details-modal .invoice-details-container__info {
  background: rgba(0, 0, 0, 0.05);
  padding: 0.5em 0.2em 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
}

c-invoice-details-modal c-button button {
  font-size: 0.8em;
}

c-invoice-details-modal .invoice-details-container__header__email {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 1.5em;
}

c-invoice-details-modal .invoice-details-container__list {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0 0 0.25em 0.25em;
}

c-invoice-details-modal c-button .modal-close {
  padding: 0;
}

c-invoice-details-modal .desktop-only-ui {
  display: flex;
}

c-invoice-details-modal .mobile-only-ui {
  display: none;
}

@media only screen and (max-width: 768px) {
  c-invoice-details-modal .modal-container {
    width: 90vw;
    top: 2em;
    margin: 0 auto;
  }

  c-invoice-details-modal .invoice-details-container__info {
    display: none;
  }

  c-invoice-details-modal .invoice-details-container__list__item {
    padding: 0.5em 0;
    margin: 0.5em 0;
  }

  c-invoice-details-modal .invoice-details-container__list__item .row-item-1 {
    width: 100%;
  }
  c-invoice-details-modal .invoice-details-container__list__item .row-item-2 {
    width: 100%;
  }
  c-invoice-details-modal .invoice-details-container__list__item .row-item-3 {
    width: 100%;
  }

  c-invoice-details-modal .desktop-only-ui {
    display: none;
  }

  c-invoice-details-modal .mobile-only-ui {
    display: block;
  }

  c-invoice-details-modal .list-item-row {
    display: flex;
    margin: 0.5em 0;
    flex-wrap: wrap;
  }

  c-invoice-details-modal .download-button {
    padding: 0;
  }

  c-invoice-details-modal .list-item-seperator {
    margin: 1em 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  c-invoice-details-modal .invoice-details-container__header {
    flex-direction: row;
    justify-content: space-around;
  }

  c-invoice-details-modal
    .invoice-details-container__list
    div:first-child
    .invoice-details-container__list__item {
    margin-top: 0;
    padding-top: 0;
  }

  c-invoice-details-modal
    .invoice-details-container__list
    div:last-child
    .invoice-details-container__list__item {
    border-bottom: 0;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}
