import { Component, Event, EventEmitter, Listen, h } from "@stencil/core";
import { isBankTxCodeValid } from "../../utils/";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "c-payment-options",
  styleUrl: "c-payment-options.css",
})
export class CPaymentOptions {
  @Event({
    eventName: "calculateMembershipCheckoutTotal",
    bubbles: true,
  })
  calculateMembershipCheckoutTotal: EventEmitter;

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "paymentMode") {
      state.paymentGateway = event.detail.value;
      if (state.paymentGateway === "razorpay") {
        state.isPaymentBtnDisabled = false;
      } else if (state.paymentGateway === "bank") {
        state.isPaymentBtnDisabled = true;
      }
    }
    this.calculateMembershipCheckoutTotal.emit();
  }

  @Listen("input-event")
  inputEventHandler(event) {
    if (event.detail.name === "bankTxCode") {
      Store.setBankTxCode(event.detail.value.trim());
      let payload = {
        bankTxCode: event.detail.value.trim(),
      };
      let { error } = isBankTxCodeValid(payload);
      if (error) {
        state.isPaymentBtnDisabled = true;
      } else {
        state.isPaymentBtnDisabled = false;
      }
    }
  }

  @Listen("pay-btn-click-event")
  payBtnClickHandler(event) {
    event.preventDefault();
    state.isPaymentBtnDisabled = true;
    state.isPaying = true;
  }

  @Listen("get-cart")
  getCartHandler(event) {
    event.preventDefault();
  }

  render() {
    return (
      <div class="card" style={{ width: `280px` }}>
        <div class={`pricebands-container pricebands-container-4 `}>
          <p class="payment-method-heading">CHOOSE PAYMENT METHOD</p>
          <c-radio
            name="paymentMode"
            label1="Card/Netbanking/UPI/Wallet"
            label2=""
            label3="Gateway fee: 4% of cart total"
            val="razorpay"
            isChecked={state.paymentGateway === "razorpay" ? true : false}
          ></c-radio>
          <div class="hspace"></div>
          <c-radio
            name="paymentMode"
            label1="Bank Transfer"
            label2=": How to transfer?"
            isLabel2Link={true}
            url="https://www.indiahci.org/hcipai-bank-details.html"
            label3="No gateway fee"
            val="bank"
            isChecked={state.paymentGateway === "bank" ? true : false}
          ></c-radio>
          <div
            class={
              state.paymentGateway === "bank"
                ? "bank-tx-inputbox"
                : "hide-inputbox-container"
            }
          >
            <c-inputbox
              class="hide-inputbox"
              type="text"
              name="bankTxCode"
              placeholder="Enter Transaction Code"
            ></c-inputbox>
          </div>
        </div>

        {!state.isMembershipSaved ? (
          <div class="pricebands-container">
            <c-skel-line color="accent-light" width={100}></c-skel-line>
            <br />
            <c-skel-line color="accent-light" width={100}></c-skel-line>
            <br />
            <c-skel-line color="accent-light" width={100}></c-skel-line>
          </div>
        ) : (
          <div class="pricebands-container pricebands-container-1">
            <c-priceband
              heading="Cart total"
              subheading={state.isCouponApplied ? "Before discount" : ""}
              currency={state.currency}
              price={state.cartTotal}
            ></c-priceband>

            {state.paymentGateway === "razorpay" && (
              <c-priceband
                heading="Gateway fee"
                subheading={`4% of ${
                  state.isCouponApplied ? "new" : ""
                } cart total`}
                currency={state.currency}
                price={state.gatewayFee}
              ></c-priceband>
            )}

            <strong>
              <c-priceband
                heading="Grand total"
                subheading=""
                currency={state.currency}
                price={state.grandTotal}
              ></c-priceband>
            </strong>
          </div>
        )}

        <div class="pricebands-container pricebands-container-3">
          <c-btn
            name="pay"
            label="Proceed to Pay"
            action-label="Paying.."
            is-in-action={state.isPaying}
            is-disabled={state.isPaymentBtnDisabled}
          ></c-btn>
        </div>
      </div>
    );
  }
}
