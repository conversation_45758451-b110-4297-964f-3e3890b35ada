import {
  Component,
  FunctionalComponent,
  h,
  Prop,
  State,
  Listen,
} from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import state from "../../global/state";

@Component({
  tag: "p-profile",
  styleUrl: "p-profile.css",
})
export class PProfile {
  @Prop() history: RouterHistory;
  @State() isToastActive: boolean = false;
  @State() isOverlayVisible: boolean = false;
  @State() activeSection: string = "profile";

  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;
  private navOpts = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "profile",
      label: "Profile",
      state: "active",
      icon: "person-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "purchases",
      label: "Purchases",
      state: "",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
  ];

  @Listen("profile-edit-btn-click")
  profileEditHandler() {
    this.isOverlayVisible = true;
  }

  @Listen("goToMembershipCheckout")
  membershipCheckoutHandler() {
    this.history.push("/confirm", {});
  }

  @Listen("profile-edit-close")
  profileEditClose() {
    this.isOverlayVisible = false;
  }

  @Listen("profile-item-saved")
  profileItemSavedHandler(event) {
    this.isOverlayVisible = false;
    this.isToastActive = false;

    let label: string = "";
    if (event.detail.profileItem === "name") {
      label = "Name has been changed";
    } else if (event.detail.profileItem === "email") {
      label = "Email has been changed";
    } else if (event.detail.profileItem === "password") {
      label = "Password has been changed";
    } else if (event.detail.profileItem === "phone") {
      label = "Mobile No. has been changed";
    } else if (event.detail.profileItem === "country") {
      label = "Country has been changed";
    } else if (event.detail.profileItem === "orginsti") {
      label = "Organisation/Institution has been changed";
    } else if (event.detail.profileItem === "jobdegree") {
      label = "Job/Degree has been changed";
    }

    this.showToast({
      type: "Success",
      label: label,
    });
  }

  @Listen("membership-selected")
  membershipSelectedHandler() {
    this.history.push("/confirm", {});
  }

  @Listen("membership-removed")
  membershipRemovedHandler() {
    this.isToastActive = false;
    this.showToast({
      type: "Success",
      label: "Membership removed!",
    });
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    this.activeSection = event.detail.cNavItemName;
    if (this.activeSection === "back") this.history.goBack();
  }

  Profile: FunctionalComponent = () => (
    <c-section>
      <c-profile-items-list></c-profile-items-list>
    </c-section>
  );
  Purchases: FunctionalComponent = () => (
    <c-section>
      <c-paid-items-list own-purchases={true}></c-paid-items-list>
    </c-section>
  );

  componenWillLoad() {
    state.isMobileMenuOpen = false;
  }

  render() {
    return (
      <c-page>
        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}
        <c-sidebar type="left">
          <c-vnav nav-opts-str={JSON.stringify(this.navOpts)}></c-vnav>
        </c-sidebar>
        {this.isOverlayVisible && <div class="dark-overlay"></div>}
        {this.isToastActive && (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        )}
        <c-topbar></c-topbar>
        {/* <c-profile-items-list></c-profile-items-list> */}
        {this.activeSection === "profile" && <this.Profile></this.Profile>}
        {this.activeSection === "purchases" && (
          <this.Purchases></this.Purchases>
        )}
      </c-page>
    );
  }
}

injectHistory(PProfile);
