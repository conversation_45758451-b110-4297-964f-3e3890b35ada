c-button button {
  /* display: flex;
  align-items: center;
  justify-content: space-around; */
  display: flex;
  box-sizing: border-box;
  width: 100%;
  outline: none;
  border: 0;
  border-radius: var(--site-border-radius);
  font-size: var(--site-font-size);
  padding: var(--site-padding);
  transition: var(--site-transition);
}

c-button button:hover {
  cursor: pointer;
}

c-button .settings-button {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.45em;
  background: none;
  color: rgba(0, 0, 0, 0.6);
}

c-button .default-button {
  justify-content: space-around;
  background: var(--violet-400);
  color: var(--violet-50);
}

c-button .default-button:hover {
  background: var(--violet-500);
}

c-button .back {
  background: none;
  color: var(--accent-color);
}

c-button .back:hover {
  background: rgba(0, 0, 0, 0.05);
}

c-button .modal-close {
  color: rgba(0, 0, 0, 0.3);
}

c-button .modal-close:hover {
  color: rgba(0, 0, 0, 1);
}

/* --- Disabled Button --- */
c-button button:disabled,
c-button button[disabled] {
  pointer-events: none;
  opacity: 0.5;
}

/* --- OAuth Button --- */

c-button .linkedinOauth {
  display: flex;
  align-items: center;
  background: #0077b5;
  color: white;
}

c-button .linkedinOauth:hover {
  background: #006097;
}

c-button .googleOauth {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.025);
  border: var(--site-border);
}

c-button .googleOauth:hover {
  background: rgba(0, 0, 0, 0.05);
}

c-button .label-icon-container {
  display: inline-block;
  background: orange;
}

/* --- Spinner --- */
c-button c-spinner .spinner-icon {
  margin-right: 0.5em;
}

c-button ion-icon {
  font-size: var(--icon-size);
  margin-left: var(--icon-left-margin);
  vertical-align: middle;
}

c-button .account-control-button {
  display: flex;
  align-items: center;
  background: none;
  font-size: 0.9em;
  color: rgba(0, 0, 0, 0.5);
  height: 25px;
  padding: 0 1em;
}

c-button .account-control-button:hover {
  /* background: rgba(0, 0, 0, 0.05); */
  color: rgba(0, 0, 0, 0.8);
}

c-button .dp-name-button {
  display: flex;
  align-items: center;
}

c-button .dp-name-button .dp {
  /* display: flex;
  justify-content: space-around;
  align-items: center;
  background: var(--accent-color-bg-lightest);
  color: white;
  height: 32px;
  width: 32px;
  border: 5px solid white;
  border-radius: 100%;
  margin-right: 0.5em;
  color: var(--accent-color); */
  padding: 0.5em 1em;
  border-radius: 0.5em;
  background: var(--accent-color-bg-lightest);
}

c-button .admin-control-dd {
  display: flex;
  align-items: center;
  background: none;
  font-size: 0.8em;
  padding-right: 0;
  opacity: 0.6;
  padding: 0.1em;
  padding: 0.5em 0.5em 0.5em 0.75em;
}

c-button .admin-control-dd:hover {
  opacity: 0.8;
  background: var(--accent-color-bg-lightest);
}

c-button .admin-control-dd ion-icon {
  font-size: 0.9em;
}

/* --------------
CONTROL LIST ITEM
-------------- */
c-button .admin-control-list-item {
  width: 150px;
  display: flex;
  align-items: center;
  background: none;
  padding-right: 0;
  padding: 0.1em;
  padding: 0.5em 0.5em 0.5em 0.75em;
  color: var(--accent-color);
  font-size: 0.95em;
}

c-button .admin-control-list-item:hover {
  background: var(--accent-color-bg-lightest);
}

c-button .admin-control-list-item ion-icon {
  font-size: 1em;
  margin-right: 0.25em;
}

c-button .cancel-file-input {
  padding: 0.25em;
  color: rgba(0, 0, 0, 0.3);
}

c-button .cancel-file-input ion-icon {
  font-size: 1em;
}

c-button .cancel-file-input:hover {
  color: rgba(0, 0, 0, 1);
}

/* --- Danger --- */
c-button .danger-ghost {
  color: var(--accent-pink-darker);
  border: 1px solid var(--accent-pink-darker);
  background: #ffebf4;
}

c-button .danger-ghost:hover {
  background: rgba(255, 255, 255, 0.5);
  /* color: var(--accent-pink-darker);
  border: 1px solid var(--accent-pink-darker); */
}

c-button .is-in-action {
  pointer-events: none;
  border: none;
}

c-button .add-page-button--active {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0.5em 0.5em 0 0;
}

c-button .add-page-button--inactive {
  background: none;
}

c-button .edit-page-button {
  background: none;
  color: var(--violet-400);
  border: 1px solid var(--violet-200);
}

c-button .edit-page-button:hover {
  color: var(--violet-400);
  background: var(--violet-50);
}

c-button .delete-page-button {
  background: none;
  color: #cc3333;
  border: 1px solid #ebadad;
}

c-button .delete-page-button:hover {
  color: #cc3333;
  background: #faebeb;
}

c-button .add-tier-button {
  background: none;
  padding: 0;
  color: var(--violet-400);
  border: 1px solid var(--violet-200);
  font-size: 0.9em;
  padding: 0.25em 0.5em;
  margin-bottom: 0.5em;
}

c-button .add-tier-button:hover {
  color: var(--violet-400);
}

c-button .ghost-small {
  background: none;
  padding: 0.5em 0.75em;
  border: 1px solid var(--violet-200);
  font-size: 0.9em;
  color: var(--violet-400);
}

c-button .ghost-small:hover {
  background: var(--violet-50);
  color: var(--violet-400);
}

c-button .ghost-small--dark {
  background: none;
  padding: 0.5em 0.75em;
  font-size: 0.9em;
  border: 1px solid var(--accent-color-bg-light);
  color: var(--accent-color-bg-light);
}

c-button .ghost-small--dark:hover {
  border: 1px solid var(--accent-color-bg-light);
  color: var(--accent-color-bg-light);
  background: rgba(255, 255, 255, 0.1);
}

c-button .link-small {
  background: none;
  padding: 0.5em 0.75em;
  font-size: 0.9em;
  color: rgba(0, 0, 0, 0.8);
}

c-button .link-small:hover {
  background: rgba(0, 0, 0, 0.05);
}

c-button .ghost-danger-small {
  background: none;
  color: #cc3333;
  border: 1px solid #ebadad;
  font-size: 0.9em;
  padding: 0.5em 0.75em;
}

c-button .ghost-danger-small--dark {
  background: none;
  color: #ebadad;
  border: 1px solid #ebadad;
  font-size: 0.9em;
  padding: 0.5em 0.75em;
}

c-button .ghost-danger-small:hover {
  background: var(--red-50);
}

c-button .ghost-danger-small--dark:hover {
  background: rgba(255, 255, 255, 0.1);
}

c-button .access-days-button {
  display: flex;
  justify-content: space-around;
  width: 115px;
  color: var(--accent-color);
  background: var(--accent-color-bg-lightest);
  border-radius: 0.25em 0.25em;
  padding: 0.5em;
  border: 1px solid var(--accent-color-bg);
  font-size: 0.9em;
}

c-button .access-days-button:hover {
  background: var(--accent-color-bg-lighter);
}

c-button .download-button {
  padding: 0;
  display: block;
  align-items: center;
  font-size: 0.9em;
  background: white;
  color: var(--accent-color);
  padding: 0.5em;
}

c-button .download-button:hover {
  background: var(--accent-color-bg-lightest);
}
