import {
  Component,
  Event,
  EventEmitter,
  Prop,
  State,
  Watch,
  h,
} from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-button",
  styleUrl: "c-button.css",
})
export class CButton {
  @Event({
    eventName: "buttonClick",
    bubbles: true,
  })
  buttonClick: EventEmitter;

  @Prop() type: string;
  @Prop() name: string;
  @Prop() label: string;
  @Prop() value: string;
  @Prop() isDisabled: boolean = false;
  @Prop() isInAction: boolean;
  @Prop() isInActionLabel: string;
  @Prop() iconUrl: string;
  @Prop() iconName: string;
  @Prop() theme: string;
  @Prop() isInActiveState: boolean = false;

  @State() isActive: boolean = false;
  @State() activeButtonIcon: string = "";

  @Watch("isInActiveState") activeStateWatcher(
    newVal: boolean,
    oldVal: boolean
  ) {
    if (newVal != oldVal) {
      this.changeActiveState();
    }
  }

  @Watch("name") nameWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      this.renderButtonIcon();
    }
  }

  componentWillLoad() {
    this.changeActiveState();
    this.renderButtonIcon();
  }

  renderButtonIcon() {
    if (this.type === "toggleMobileDashboardOptions") {
      this.activeButtonIcon = this.name;
    }
  }

  changeActiveState() {
    this.isActive = this.isInActiveState;
  }

  handleButtonClick() {
    this.buttonClick.emit({
      name: this.name,
      value: this.value,
    });
  }

  render() {
    if (this.type === "ghost") {
      return (
        <button
          class={`ghost ${this.theme === "danger" && "danger-ghost"} ${
            this.isInAction && "is-in-action"
          }`}
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? this.isInActionLabel : this.label}
          <slot />
        </button>
      );
    } else if (this.type === "back") {
      return (
        <button
          class="back"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          {this.iconName && <ion-icon name={this.iconName}></ion-icon>}
          <slot />
        </button>
      );
    } else if (this.type === "modalClose") {
      return (
        <button
          class="back modal-close"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <ion-icon name="close-outline"></ion-icon>
        </button>
      );
    } else if (this.type === "toggleMobileDashboardOptions") {
      return (
        <button
          class="settings-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          {this.activeButtonIcon === "showDashboardViewOptions" && (
            <ion-icon name="options-outline"></ion-icon>
          )}
          {this.activeButtonIcon === "hideDashboardViewOptions" && (
            <ion-icon name="close-outline"></ion-icon>
          )}
        </button>
      );
    } else if (this.type === "openAddPageForm") {
      return (
        <button
          class={`back modal-close ${
            this.isActive && "add-page-button--active"
          }`}
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          {this.isActive ? (
            <ion-icon name="close-outline"></ion-icon>
          ) : (
            <ion-icon name="add-outline"></ion-icon>
          )}
        </button>
      );
    } else if (this.type === "openPageSettingsForm") {
      return (
        <button
          class={`back modal-close ${
            this.isActive && "add-page-button--active"
          }`}
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          {this.isActive ? (
            <ion-icon name="close-outline"></ion-icon>
          ) : (
            <ion-icon name="ellipsis-vertical-outline"></ion-icon>
          )}
        </button>
      );
    } else if (this.type === "editPage") {
      return (
        <button
          class="back modal-close edit-page-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            {/* <ion-icon name="pencil-outline"></ion-icon> */}
            <c-text type="buttonLabel">Save edits</c-text>
          </div>
        </button>
      );
    } else if (this.type === "deletePage") {
      return (
        <button
          class="back modal-close delete-page-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            {/* <ion-icon name="trash-outline"></ion-icon>{" "} */}
            <c-text type="buttonLabel">Delete page</c-text>
          </div>
        </button>
      );
    } else if (this.type === "newPricingTier") {
      return (
        <button
          class="back modal-close add-tier-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            <c-text type="buttonLabel">+ Add tier</c-text>
          </div>
        </button>
      );
    } else if (this.type === "newSubTickets") {
      return (
        <button
          class="back modal-close add-tier-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            <c-text type="buttonLabel">+ Create sub-ticket</c-text>
          </div>
        </button>
      );
    } else if (this.type === "newAccessDate") {
      return (
        <button
          class="back modal-close add-tier-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            <c-text type="buttonLabel">+ Add access type</c-text>
          </div>
        </button>
      );
    } else if (this.type === "newTrackSession") {
      return (
        <button
          class="back modal-close add-tier-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            <c-text type="buttonLabel">+ Add track session</c-text>
          </div>
        </button>
      );
    } else if (this.type === "newTrackDay") {
      return (
        <button
          class="back modal-close add-tier-button"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <div class="edit-delete-page-container">
            <c-text type="buttonLabel">+ Add track day</c-text>
          </div>
        </button>
      );
    } else if (this.type === "cancelFileInput") {
      return (
        <button
          class="back cancel-file-input"
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <ion-icon name="close-outline"></ion-icon>
        </button>
      );
    } else if (this.type === "oauth") {
      return (
        <button
          class={this.name}
          disabled={this.isDisabled}
          onClick={() => this.handleButtonClick()}
        >
          <c-img type="oauthIcon" src={this.iconUrl}></c-img>
          <slot />
        </button>
      );
    } else if (this.type === "solidWithIcon") {
      return (
        <button
          class="default-button"
          disabled={this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? (
            <c-spinner></c-spinner>
          ) : (
            <div class="icon-label-container">
              <c-text type="buttonLabel">{this.label}</c-text>
              <ion-icon name={this.iconName}></ion-icon>
            </div>
          )}
        </button>
      );
    } else if (this.type === "accountControl") {
      if (this.name === "logoutUser") {
        return (
          <button
            class="account-control-button"
            onClick={() => this.handleButtonClick()}
          >
            Logout
          </button>
        );
      } else {
        return (
          <button
            class="account-control-button dp-name-button"
            onClick={() => this.handleButtonClick()}
          >
            {/* {state.dpUrl.length > 0 && (
              <c-img type="menuDp" src={state.dpUrl}></c-img>
            )} */}
            <div class="dp">{state.firstName}</div>
            {/* {state.firstName} */}
          </button>
        );
      }
    } else if (this.type === "adminControlDD") {
      return (
        <button
          class="admin-control-dd"
          onClick={() => this.handleButtonClick()}
        >
          <c-text type="buttonLabel">{this.label}</c-text>
          &nbsp;
          <ion-icon name={this.iconName}></ion-icon>
        </button>
      );
    } else if (this.type === "adminControlListItem") {
      return (
        <button
          class="admin-control-list-item"
          onClick={() => this.handleButtonClick()}
          disabled={this.isInAction}
        >
          {this.isInAction ? (
            <div>
              <c-spinner-dark></c-spinner-dark>
              {/* &nbsp; &nbsp;
              <c-text type="buttonLabel">{this.isInActionLabel}</c-text> */}
            </div>
          ) : (
            <div>
              <ion-icon name={this.iconName}></ion-icon>
              &nbsp;
              <c-text type="buttonLabel">{this.label}</c-text>
            </div>
          )}
        </button>
      );
    } else if (this.type === "ghost_Small") {
      return (
        <button
          class="ghost-small"
          disabled={this.isDisabled || this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? <c-spinner></c-spinner> : <slot />}
        </button>
      );
    } else if (this.type === "ghost_Small_onDark") {
      return (
        <button
          class="ghost-small--dark"
          disabled={this.isDisabled || this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? <c-spinner></c-spinner> : <slot />}
        </button>
      );
    } else if (this.type === "ghost_Danger_Small") {
      return (
        <button
          class="ghost-danger-small"
          disabled={this.isDisabled || this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? <c-spinner></c-spinner> : <slot />}
        </button>
      );
    } else if (this.type === "ghost_Danger_Small_onDark") {
      return (
        <button
          class="ghost-danger-small--dark"
          disabled={this.isDisabled || this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? <c-spinner></c-spinner> : <slot />}
        </button>
      );
    } else if (this.type === "link_Small") {
      return (
        <button
          class="link-small"
          disabled={this.isDisabled || this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? <c-spinner></c-spinner> : <slot />}
        </button>
      );
    } else if (this.type === "accessDays") {
      return (
        <button
          class="access-days-button"
          onClick={() => this.handleButtonClick()}
        >
          <slot></slot>
        </button>
      );
    } else if (this.type === "downloadButton") {
      return (
        <button
          onClick={() => this.handleButtonClick()}
          class="download-button"
        >
          Download
        </button>
      );
    } else {
      return (
        <button
          class="default-button"
          disabled={this.isDisabled || this.isInAction}
          onClick={() => this.handleButtonClick()}
        >
          {this.isInAction ? <c-spinner></c-spinner> : <slot />}
        </button>
      );
    }
  }
}
