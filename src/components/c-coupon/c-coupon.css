c-coupon .card {
  display: flex;
  justify-content: space-between;
  background: white;
  padding: 1em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5em;
  transition: all 0.15s ease-in;
}

c-coupon .row-items {
  margin: 0;
}

c-coupon .row-item-1 {
  width: 35%;
  /* background: pink; */
}

c-coupon .row-item-2 {
  width: 25%;
  /* background: yellow; */
}

c-coupon .row-item-3 {
  width: 25%;
  /* background: pink; */
}

c-coupon .row-item-4 {
  width: 10%;
  /* background: yellow; */
}

c-coupon .name {
  font-size: 0.9em;
  margin: 0em;
  margin-bottom: 0.5em;
}

c-coupon .hspacer {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

c-coupon .member-info {
  font-size: 0.8em;
}

c-coupon c-text-link a {
  font-size: 1.2em;
}

c-coupon c-text-link a {
  font-size: 0.8em;
}

c-coupon .expired-text {
  font-size: 0.8em;
  color: red;
  font-weight: 700;
}

c-coupon .subtext {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.8);
}

c-coupon .label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
}

c-coupon .value {
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.25em 0.5em;
  margin: 1em 1.5em 0.5em 0;
  border-radius: 0.25em;
  text-align: center;
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}

c-coupon c-btn button {
  font-size: 0.8em;
  outline: none;
}

c-coupon .bubble {
  margin: 0;
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.3em 1em;
  border-radius: 0.25em;
  margin-right: 0.75em;
}

c-coupon .gold-bubble {
  background: var(--accent-golden-bg-lighter);
  color: var(--accent-golden-darker);
}

c-coupon .blue-bubble {
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}

c-coupon .bubble-container {
  margin-top: 0.25em;
}

c-coupon .subtext {
  font-size: 0.9em;
  color: rgba(0, 0, 0, 0.6);
}

c-coupon .access {
  margin-top: 0;
  margin-bottom: 0.75em;
}
