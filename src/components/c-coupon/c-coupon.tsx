import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-coupon",
  styleUrl: "c-coupon.css",
})
export class CCoupon {
  @Prop() couponId: string;
  @Prop() name: string;
  @Prop() access: string;
  @Prop() openCode: string;
  @Prop() deductionType: string;
  @Prop() deductionValue: string;
  @Prop() issuedOn: string;
  @Prop() issuedBy: string;
  @Prop() issuedCount: string;
  @Prop() usedCount: string;

  // expandCouponDetails;
  render() {
    return (
      <div class="card">
        <div class="row-items row-item-1">
          <p class="name">{this.name}</p>
          {/* <p class="access">
            {this.access === "open" ? (
              <span class="member-info">Anyone can use this coupon</span>
            ) : (
              ""
            )}
            {this.access === "emaillist" ? (
              <span class="member-info">
                A restricted email list can use this coupon
              </span>
            ) : (
              ""
            )}
          </p> */}

          <div class="bubble-container">
            <span class="bubble blue-bubble">
              {this.deductionType === "fixed"
                ? `₹${this.deductionValue} discount`
                : ""}
              {this.deductionType === "percentage"
                ? `${this.deductionValue}% discount`
                : ""}
              {this.deductionType === "ticketType" ? `${this.name}` : ""}
            </span>
            {this.access === "open" ? (
              <span class="bubble gold-bubble">{this.openCode}</span>
            ) : (
              ""
            )}
          </div>
        </div>
        <p class="row-items row-item-2">
          <span class="label">ACCESS</span>
          <br />
          {this.access === "open" ? (
            <span class="member-info">Open to all</span>
          ) : (
            ""
          )}
          {this.access === "emaillist" ? (
            <span class="member-info">Restricted (email list)</span>
          ) : (
            ""
          )}
        </p>
        <p class="row-items row-item-3">
          <span class="label">USAGE</span>
          <br />
          {this.access === "open" ? (
            <span class="member-info">{this.usedCount} coupons used</span>
          ) : (
            ""
          )}
          {this.access === "emaillist" ? (
            <span class="member-info">
              {this.usedCount} of {this.issuedCount} coupons used
            </span>
          ) : (
            ""
          )}
        </p>
        <div class="row-items row-item-4">
          <c-btn
            name="expandCouponDetails"
            value={this.couponId}
            type="ghost"
            label="Expand"
            action-label=""
            is-in-action={false}
            is-disabled={false}
          ></c-btn>
        </div>
      </div>
    );
  }
}
