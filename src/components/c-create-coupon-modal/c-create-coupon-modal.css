c-create-coupon-modal .modal-container {
  position: fixed;
  width: 60vw;
  background: var(--bg-color);
  margin: 4em auto 0 auto;
  padding: 1em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
  box-sizing: border-box;
}

c-create-coupon-modal .section-header {
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin: 0;
}

c-create-coupon-modal .card {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  /* height: 50vh; */
}

c-create-coupon-modal .row {
  display: flex;
  justify-content: space-between;
  /* align-items: baseline; */
}

c-create-coupon-modal .label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
  margin: 0;
}

c-create-coupon-modal c-radio .radio-btn-container {
  margin: 0;
}

c-create-coupon-modal .subtitle {
  margin-top: 0;
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 2em;
}

c-create-coupon-modal c-radio .radio-btn-label {
  margin-left: 0.1em;
  font-size: 0.9em;
  margin-bottom: 0;
}

c-create-coupon-modal .wizard-controls {
  display: flex;
  justify-content: space-between;
  margin-top: 2em;
}

c-create-coupon-modal .mandatory-field-star {
  color: red;
}

c-create-coupon-modal .wizard-controls-container {
  display: flex;
  flex-direction: row-reverse;
}

c-create-coupon-modal .show {
  display: block;
}

c-create-coupon-modal .hide {
  display: none;
}

c-create-coupon-modal c-radio .radio-btn-label-3 {
  margin-bottom: 0;
}

c-create-coupon-modal c-inputbox .inputbox {
  margin-bottom: 0;
  margin-top: 0.25em;
}

c-create-coupon-modal .subtext {
  font-size: 0.8em;
  margin-top: 0.25em;
  color: rgba(0, 0, 0, 0.5);
}

c-create-coupon-modal .coupon-access-desc {
  width: 92.5%;
  font-size: 0.9em;
  margin: 0;
}

c-create-coupon-modal .coupon-access-sub-desc {
  font-size: 0.9em;
  color: rgba(0, 0, 0, 0.6);
}

c-create-coupon-modal .error {
  color: red;
}

c-create-coupon-modal .or-seperator {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.3);
  margin: 1em auto 1em auto;
  font-size: 0.9em;
}

c-create-coupon-modal .line {
  width: 35%;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.3);
}

c-create-coupon-modal .copy {
  font-size: 0.9em;
  margin-top: 0;
}

c-create-coupon-modal c-textarea textarea {
  margin-top: 0.25em;
}

c-create-coupon-modal .no-margin {
  margin: 0;
}

c-create-coupon-modal .field-container-2 {
  margin-bottom: 1.75em;
}

c-create-coupon-modal .coupon-summary {
  width: 100%;
  margin-bottom: 0;
}

c-create-coupon-modal c-checkbox label {
  font-size: 0.9em;
}

c-create-coupon-modal c-checkbox input[type="checkbox"] {
  outline: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  margin-right: 0.5em;
}

c-create-coupon-modal c-checkbox input[type="checkbox"]:hover {
  cursor: pointer;
  border: 1px solid red;
  outline: 1px solid rgba(0, 0, 0, 0.3);
}

c-create-coupon-modal .summary-ticket-container {
  display: flex;
  flex-wrap: wrap;
}

c-create-coupon-modal .summary-ticket {
  background: rgba(0, 0, 0, 0.05);
  padding: 0.25em 0.5em 0.4em 0.5em;
  border-radius: 0.25em;
  margin: 0;
  margin-top: 0.5em;
  margin-right: 0.5em;
}

c-create-coupon-modal .radio-group {
  margin-top: 0.5em;
}

c-create-coupon-modal .radio-label-container {
  margin-top: 0.5em;
}

c-create-coupon-modal c-button .modal-close {
  padding: 0;
}

@media only screen and (max-width: 768px) {
  c-create-coupon-modal .modal-container {
    width: 90vw;
    top: 2em;
    margin: 0 auto;
  }
}
