import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

interface wizardConfigObject {
  name: string;
}

@Component({
  tag: "c-create-coupon-modal",
  styleUrl: "c-create-coupon-modal.css",
})
export class CCreateCouponModal {
  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Event({
    eventName: "coupon-creation-response",
    bubbles: true,
  })
  couponCreationResponseEvent: EventEmitter;

  @Listen("button-click")
  buttonClickHandler(event) {
    if (event.detail.name === "create-coupon-next") {
      if (
        this.wizardStep === 2 &&
        state.couponAccessType === "emaillist" &&
        this.wizardStep1Sub === 1
      ) {
        this.wizardStep1Sub = 2;
        if (this.textAreaEmailCount > 0) {
          this.isStep2NextButtonDisabled = false;
        } else {
          this.isStep2NextButtonDisabled = true;
        }
      } else {
        if (this.wizardStep < this.wizardConfig.length - 1) {
          this.wizardStep = this.wizardStep + 1;
        }
      }
    } else if (event.detail.name === "create-coupon-prev") {
      if (
        this.wizardStep === 2 &&
        state.couponAccessType === "emaillist" &&
        this.wizardStep1Sub === 2
      ) {
        this.wizardStep1Sub = 1;
        this.isStep2NextButtonDisabled = false;
      } else {
        if (this.wizardStep > 0) {
          this.wizardStep = this.wizardStep - 1;
        }
      }
    } else if (event.detail.name === "confirm-create-coupon") {
      this.submitNewCouponDetails();
    }
    this.wizardStepControl();
  }

  @Listen("buttonClick") buttonClickHandlerNew(e) {
    if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    }
  }

  @Listen("input-event") inputHandler(event) {
    if (event.detail.name === "couponName") {
      state.couponName = event.detail.value.trim();
    } else if (event.detail.name === "couponValue") {
      if (
        !isNaN(event.detail.value) &&
        !isNaN(parseFloat(event.detail.value))
      ) {
        state.couponDeductionValue = parseFloat(event.detail.value);
        if (state.couponDeductionType === "percentage") {
          if (state.couponDeductionValue > 100) {
            state.couponPercentageDeductionValue = 100;
          } else {
            state.couponPercentageDeductionValue = state.couponDeductionValue;
          }
        } else if (state.couponDeductionType === "fixed") {
          state.couponFixedDeductionValue = state.couponDeductionValue;
        }
        this.isCouponDeductionTypeValid = true;
      } else {
        this.isCouponDeductionTypeValid = false;
      }
    } else if (event.detail.name === "ticketDeductionValue") {
      state.couponTicketsDeductionValue = parseFloat(event.detail.value);
      if (state.couponTicketDeductionLogic === "deductByPercentage") {
        if (state.couponTicketsDeductionValue > 100) {
          state.couponTicketsDeductionValue = 100;
        }
      }
    }
    this.wizardStepControl();
  }

  @Listen("textarea-input-event") textAreaInputHandler(event) {
    let textBlock = event.detail.value;
    this.emailArray = this.extractEmails(textBlock);
    if (this.emailArray) {
      this.textAreaEmailCount = this.emailArray.length;
      this.isStep2NextButtonDisabled = false;
    } else {
      this.textAreaEmailCount = 0;
      this.isStep2NextButtonDisabled = true;
    }
  }

  extractEmails = (textBlock) => {
    return textBlock.match(
      /([a-zA-Z0-9._+-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi
    );
  };

  @Listen("radio-change-event")
  radioChangeHandler(event) {
    if (event.detail.name === "couponType") {
      state.couponDeductionType = event.detail.value;
      if (state.couponDeductionType === "percentage") {
        if (state.couponDeductionValue > 100) {
          state.couponPercentageDeductionValue = 100;
        } else {
          state.couponPercentageDeductionValue = state.couponDeductionValue;
        }
        state.couponFixedDeductionValue = 0;
        this.couponTicketsForDeduction = [];
      } else if (state.couponDeductionType === "fixed") {
        state.couponFixedDeductionValue = state.couponDeductionValue;
        state.couponPercentageDeductionValue = 0;
        this.couponTicketsForDeduction = [];
      } else if (state.couponDeductionType === "ticketType") {
        state.couponPercentageDeductionValue = 0;
        state.couponFixedDeductionValue = 0;
      }
    } else if (event.detail.name === "couponAccess") {
      state.couponAccessType = event.detail.value;
    } else if (event.detail.name === "couponTicketDeductionLogic") {
      state.couponTicketDeductionLogic = event.detail.value;
    }
    this.wizardStepControl();
  }

  @Listen("checkbox-input-event")
  checkboxEventHandler(event) {
    if (event.detail.name === "couponTicketType") {
      if (event.detail.value) {
        // this.fetchedTicketTypes.map((ticketType: any) => {
        //   if (event.detail.item === ticketType.subType) {
        //     this.couponTicketsForDeduction.push(ticketType);
        //   }
        // });
        let obj = {
          ticketId: event.detail.item,
        };
        this.couponTicketsForDeduction.push(obj);
      } else {
        if (this.couponTicketsForDeduction.length > 0) {
          let buff: any = [];
          this.couponTicketsForDeduction.forEach((ticket: any) => {
            if (ticket.ticketId != event.detail.item) {
              buff.push(ticket);
            }
          });
          this.couponTicketsForDeduction = buff;
        } else {
          this.couponTicketsForDeduction = [];
        }
      }
      this.couponTicketsForDeduction = [...this.couponTicketsForDeduction];
    }
    this.wizardStepControl();
  }

  @State() wizardStep: number = 0;
  @State() wizardStep1Sub: number = 1;
  @State() couponEmailList: Array<string> = [];
  @State() isStep0NextButtonDisabled: boolean = true;
  @State() isStep1NextButtonDisabled: boolean = true;
  @State() isStep2NextButtonDisabled: boolean = false;
  @State() isStep3NextButtonDisabled: boolean = true;
  @State() isCouponDeductionTypeValid: boolean = true;
  @State() textAreaEmailCount: number = 0;
  @State() emailArray: any;
  @State() isDataFetched: boolean = false;
  @State() fetchedTicketTypes: any;
  @State() couponTicketsForDeduction: any = [];

  private wizardConfig: Array<wizardConfigObject> = [
    { name: "Basic Details" },
    { name: "Deduction Details" },
    { name: "Access Details" },
    { name: "Confirm Details" },
  ];

  submitNewCouponDetails() {
    let newCouponPayload = {
      couponName: state.couponName,
      couponDeductionType: state.couponDeductionType,
      couponFixedDeductionValue: state.couponFixedDeductionValue,
      couponPercentageDeductionValue: state.couponPercentageDeductionValue,
      couponTicketsForDeduction: this.couponTicketsForDeduction,
      couponTicketDeductionLogic: state.couponTicketDeductionLogic,
      couponTicketsDeductionValue: state.couponTicketsDeductionValue,
      couponAccessType: state.couponAccessType,
      eventCode: `${state.eventCodeForMonitoring}`,
      emailList: this.emailArray,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/createcoupon`,
      data: newCouponPayload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        state.couponName = "";
        state.couponDeductionType = "fixed";
        state.couponDeductionValue = 0;
        state.couponFixedDeductionValue = 0;
        state.couponPercentageDeductionValue = 0;
        state.couponAccessType = "open";
        this.emailArray = [];
        this.couponCreationResponseEvent.emit({
          status: response.data.status,
          msg: response.data.msg,
        });
      })
      .catch((error) => {
        alert(error);
      });
  }

  wizardStepControl() {
    if (this.wizardStep === 0) {
      if (state.couponName.length > 0 && state.couponDeductionType.length > 0) {
        this.isStep0NextButtonDisabled = false;
      } else {
        this.isStep0NextButtonDisabled = true;
      }
    } else if (this.wizardStep === 1) {
      if (state.couponDeductionType === "ticketType") {
        if (
          this.couponTicketsForDeduction.length > 0 &&
          state.couponTicketDeductionLogic.length > 0 &&
          state.couponTicketsDeductionValue > 0
        ) {
          this.isStep1NextButtonDisabled = false;
        } else {
          this.isStep1NextButtonDisabled = true;
        }
      } else if (
        state.couponDeductionType === "fixed" &&
        state.couponFixedDeductionValue > 0
      ) {
        this.isStep1NextButtonDisabled = false;
      } else if (
        state.couponDeductionType === "percentage" &&
        state.couponPercentageDeductionValue > 0
      ) {
        this.isStep1NextButtonDisabled = false;
      }
    }
  }

  getTicketTypes() {
    this.isDataFetched = false;
    let payload: any = {
      eventCode: state.eventCodeForMonitoring,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-ticket-types-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchedTicketTypes = response.data.payload;
          this.isDataFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  componentWillLoad() {
    this.getTicketTypes();
  }

  render() {
    return (
      <div class="modal-container">
        <c-row>
          <div>
            <h3 class="section-header">
              {this.wizardConfig[this.wizardStep].name}
            </h3>
            <p class="subtitle">
              STEP {this.wizardStep + 1} of {this.wizardConfig.length}
            </p>
          </div>
          <c-button
            type="modalClose"
            name="closeModal"
            icon-name=""
            label=""
          ></c-button>
        </c-row>

        <div class="card">
          <div class={this.wizardStep === 0 ? "show" : "hide"}>
            <div class="field-container-2">
              <p class="label">
                COUPON NAME <span class="mandatory-field-star">*</span>
              </p>
              <c-inputbox
                type="text"
                name="couponName"
                placeholder="e.g. Free Conference Ticket for Gold Sponsor"
                is-disabled={false}
              ></c-inputbox>
            </div>
            <div class="field-container-2">
              <p class="label label-top">
                DEDUCTION TYPE <span class="mandatory-field-star">*</span>
              </p>
              <div class="row field-container-1 radio-group">
                <c-radio
                  name="couponType"
                  label1=""
                  label2=""
                  label3=""
                  val="fixed"
                  isChecked={true}
                ></c-radio>
                <p class="coupon-access-desc">
                  Deduct a fixed amount from cart total
                  {/* <br />
                  <span class="coupon-access-sub-desc">
                    Deducts a fixed amount from cart total
                  </span> */}
                </p>
              </div>
              <div class="row field-container-1 radio-group">
                <c-radio
                  name="couponType"
                  label1=""
                  label2=""
                  label3=""
                  val="percentage"
                  isChecked={false}
                ></c-radio>
                <p class="coupon-access-desc">
                  Deduct a percentage from cart total
                  {/* <br />
                  <span class="coupon-access-sub-desc">
                    Deducts a percentage from cart total
                  </span> */}
                </p>
              </div>
              <div class="row field-container-1 radio-group">
                <c-radio
                  name="couponType"
                  label1=""
                  label2=""
                  label3=""
                  val="ticketType"
                  isChecked={false}
                ></c-radio>
                <p class="coupon-access-desc">
                  Deduct price of specific tickets from cart total
                  {/* <br />
                  <span class="coupon-access-sub-desc">
                    Deducts price of specific tickets from cart total
                  </span> */}
                </p>
              </div>
            </div>
          </div>
          <div class={this.wizardStep === 1 ? "show" : "hide"}>
            {state.couponDeductionType === "fixed" ||
            state.couponDeductionType === "percentage" ? (
              <div>
                <p class="label label-top">
                  COUPON DEDUCTION VALUE{" "}
                  <span class="mandatory-field-star">*</span>
                </p>
                <c-inputbox
                  type="number"
                  name="couponValue"
                  placeholder={
                    state.couponDeductionType === "fixed"
                      ? "e.g. 500"
                      : "e.g. 30"
                  }
                  is-disabled={false}
                ></c-inputbox>
                <p class="subtext coupon-summary">
                  {this.isCouponDeductionTypeValid ? (
                    <span>
                      This coupon will deduct{" "}
                      <strong>
                        {state.couponDeductionType === "fixed"
                          ? `₹${state.couponFixedDeductionValue}`
                          : ""}

                        {state.couponDeductionType === "percentage"
                          ? `${state.couponPercentageDeductionValue}%`
                          : ""}
                      </strong>{" "}
                      {state.couponDeductionType === "fixed" ? `from` : ""}
                      {state.couponDeductionType === "percentage"
                        ? `of`
                        : ""}{" "}
                      cart total
                    </span>
                  ) : (
                    <span class="error">Enter a valid numerical value</span>
                  )}
                </p>
              </div>
            ) : (
              ""
            )}

            {state.couponDeductionType === "ticketType" && (
              <div>
                <p class="label label-top">
                  CHOOSE TICKET TYPES{" "}
                  <span class="mandatory-field-star">*</span>
                </p>
                {this.isDataFetched ? (
                  this.fetchedTicketTypes.map((ticket: any) => (
                    <c-checkbox
                      label={ticket.title}
                      name="couponTicketType"
                      item={ticket.id}
                    ></c-checkbox>
                  ))
                ) : (
                  <div>
                    <c-skel-line color="gray" width={100}></c-skel-line>
                    <br />
                    <c-skel-line color="gray" width={75}></c-skel-line>
                    <br />
                    <c-skel-line color="gray" width={50}></c-skel-line>
                  </div>
                )}
                <br />
                <p class="label label-top">
                  CHOOSE DEDUCTION LOGIC{" "}
                  <span class="mandatory-field-star">*</span>
                </p>
                {/* <div class="row field-container-1 radio-label-container">
                  <c-radio
                    name="couponTicketDeductionLogic"
                    label1=""
                    label2=""
                    label3=""
                    val="deductOneTicket"
                    isChecked={true}
                  ></c-radio>
                  <p class="coupon-access-desc">
                    100% deduction on the costliest selected tickets
                    <br />
                    <span class="coupon-access-sub-desc">
                      Highest ticket price will be deducted from cart total
                    </span>
                  </p>
                </div> */}
                {/* <div class="row field-container-1 radio-label-container">
                  <c-radio
                    name="couponTicketDeductionLogic"
                    label1=""
                    label2=""
                    label3=""
                    val="deductAllTickets"
                    isChecked={false}
                  ></c-radio>
                  <p class="coupon-access-desc">
                    100% deduction on all selected tickets
                    <br />
                    <span class="coupon-access-sub-desc">
                      Sum of all the ticket prices will be deducted from cart
                    </span>
                  </p>
                </div> */}
                <div class="row field-container-1 radio-label-container">
                  <c-radio
                    name="couponTicketDeductionLogic"
                    label1=""
                    label2=""
                    label3=""
                    val="deductByValue"
                    isChecked={false}
                  ></c-radio>
                  <p class="coupon-access-desc">
                    Fixed amount deduction on selected tickets
                    {/* <br />
                    <span class="coupon-access-sub-desc">
                      Selected tickets will be deducted by a certain value
                    </span> */}
                  </p>
                </div>
                <div class="row field-container-1 radio-label-container">
                  <c-radio
                    name="couponTicketDeductionLogic"
                    label1=""
                    label2=""
                    label3=""
                    val="deductByPercentage"
                    isChecked={false}
                  ></c-radio>
                  <p class="coupon-access-desc">
                    Percentage deduction on selected tickets
                    {/* <br />
                    <span class="coupon-access-sub-desc">
                      Selected tickets will be deducted by a certain percentage
                    </span> */}
                  </p>
                </div>
                <br />
                <p class="label label-top">
                  CHOOSE DEDUCTION VALUE{" "}
                  <span class="mandatory-field-star">*</span>
                </p>
                <c-inputbox
                  type="number"
                  name="ticketDeductionValue"
                  placeholder="e.g. 50"
                  is-disabled={false}
                ></c-inputbox>
              </div>
            )}
          </div>
          <div class={this.wizardStep === 2 ? "show" : "hide"}>
            <div class={this.wizardStep1Sub === 1 ? "show" : "hide"}>
              <p class="label">
                COUPON ACCESS TYPE <span class="mandatory-field-star">*</span>
              </p>
              <div class="row field-container-1">
                <c-radio
                  name="couponAccess"
                  label1=""
                  label2=""
                  label3=""
                  val="open"
                  isChecked={true}
                ></c-radio>
                <p class="coupon-access-desc">
                  <strong>Open to all</strong>
                  <br />
                  <span class="coupon-access-sub-desc">
                    A single coupon code will be generated and anyone with the
                    code will be able to use it
                  </span>
                </p>
              </div>
              <div class="row field-container-1">
                <c-radio
                  name="couponAccess"
                  label1=""
                  label2=""
                  label3=""
                  val="emaillist"
                  isChecked={false}
                ></c-radio>
                <p class="coupon-access-desc">
                  <strong>Restricted by email</strong>
                  <br />
                  <span class="coupon-access-sub-desc">
                    Only specific emails will be able to use this coupon. A code
                    will be generated for each email
                  </span>
                </p>
              </div>
            </div>
            <div class={this.wizardStep1Sub === 2 ? "show" : "hide"}>
              <p class="label">ENTER ONE EMAIL ON EACH LINE</p>
              <c-textarea
                rows={7}
                cols={0}
                placeholder="e.g.&#10;<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                name="emailListInput"
              ></c-textarea>
              <p class="subtext no-margin">
                {this.textAreaEmailCount === 0 && "No emails detected"}

                {this.textAreaEmailCount === 1 && (
                  <span>
                    <strong>{this.textAreaEmailCount} email</strong> detected
                  </span>
                )}

                {this.textAreaEmailCount > 1 && (
                  <span>
                    <strong>{this.textAreaEmailCount} emails</strong> detected
                  </span>
                )}
              </p>
            </div>
          </div>
          <div class={this.wizardStep === 3 ? "show" : "hide"}>
            <div class="field-container-2">
              <p class="label">COUPON NAME</p>
              <p class="copy">{state.couponName}</p>
            </div>

            <div class="field-container-2">
              {state.couponDeductionType === "ticketType" ? (
                <p class="label">COUPON DEDUCTION LOGIC</p>
              ) : (
                <p class="label">COUPON DEDUCTION</p>
              )}

              {state.couponDeductionType === "fixed" && (
                <p class="copy">
                  <strong>₹{state.couponFixedDeductionValue}</strong> from cart
                  total
                </p>
              )}

              {state.couponDeductionType === "percentage" && (
                <p class="copy">
                  <strong>{state.couponPercentageDeductionValue}%</strong> of
                  cart total
                </p>
              )}

              {state.couponDeductionType === "ticketType" &&
                state.couponTicketDeductionLogic === "deductOneTicket" && (
                  <p class="copy">
                    Highest ticket price will be deducted from cart total
                  </p>
                )}

              {state.couponDeductionType === "ticketType" &&
                state.couponTicketDeductionLogic === "deductAllTickets" && (
                  <p class="copy">
                    Sum of all the ticket prices will be deducted from cart
                  </p>
                )}

              {state.couponDeductionType === "ticketType" &&
                state.couponTicketDeductionLogic === "deductByValue" && (
                  <p class="copy">
                    Fixed ticket prices will be deducted from cart total
                  </p>
                )}

              {state.couponDeductionType === "ticketType" &&
                state.couponTicketDeductionLogic === "deductByPercentage" && (
                  <p class="copy">
                    Percentage of ticket prices will be deducted from cart total
                  </p>
                )}
            </div>

            {state.couponDeductionType === "ticketType" && (
              <div class="field-container-2">
                <p class="label">TICKETS FOR DEDUCTION</p>
                <div class="summary-ticket-container">
                  {this.couponTicketsForDeduction.map((ticket) => (
                    <p class="copy summary-ticket">{ticket.title}</p>
                  ))}
                </div>
              </div>
            )}

            <div class="field-container-2">
              <p class="label">COUPON ACCESS</p>
              {state.couponAccessType === "open" && (
                <p class="copy">Open to all</p>
              )}
              {state.couponAccessType === "emaillist" && (
                <p class="copy">
                  Email list ({this.textAreaEmailCount} emails)
                </p>
              )}
            </div>
          </div>
          <div class="wizard-controls">
            {this.wizardStep === 0 ? (
              <div></div>
            ) : (
              <c-btn
                name="create-coupon-prev"
                label="Back"
                action-label=""
                value=""
                type="transparent"
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            )}

            {this.wizardStep === 0 && (
              <c-btn
                name="create-coupon-next"
                label="Next"
                action-label=""
                value=""
                is-in-action={false}
                is-disabled={this.isStep0NextButtonDisabled}
              ></c-btn>
            )}

            {this.wizardStep === 1 && (
              <c-btn
                name="create-coupon-next"
                label="Next"
                action-label=""
                value=""
                is-in-action={false}
                is-disabled={this.isStep1NextButtonDisabled}
              ></c-btn>
            )}

            {this.wizardStep === 2 && (
              <c-btn
                name="create-coupon-next"
                label="Next"
                action-label=""
                value=""
                is-in-action={false}
                is-disabled={this.isStep2NextButtonDisabled}
              ></c-btn>
            )}

            {this.wizardStep === 3 && (
              <c-btn
                name="confirm-create-coupon"
                label="Create coupon"
                action-label=""
                value=""
                is-in-action={false}
                is-disabled={this.isStep2NextButtonDisabled}
              ></c-btn>
            )}
          </div>
        </div>
      </div>
    );
  }
}
