import {
  Component,
  Listen,
  Prop,
  State,
  FunctionalComponent,
  h,
} from "@stencil/core";
import { RouterHistory, MatchResults, injectHistory } from "@stencil/router";
import state from "../../global/state";
import {
  textInput<PERSON>elper,
  localLogin<PERSON>elper,
  localSignupHelper,
  getEventAuthHelper,
} from "../../global/helpers";
import {
  localLoginPayloadGenerator,
  localSignupPayloadGenerator,
} from "../../global/generators";
import { isLoginInputValid, isSignUpInputValid } from "../../utils/";
import { appLabel } from "../../global/app";
import axios from "axios";

@Component({
  tag: "p-auth",
  styleUrl: "p-auth.css",
})
export class PAuth {
  @Prop() history: RouterHistory;
  @Prop() match: MatchResults;

  @State() primaryView: string = "login";
  @State() secondaryView: string = "sendVerificationCode";
  @State() isDataFetched: boolean = false;
  @State() isDisabled_Button_sendVerificationCode: boolean = true;
  @State() isDisabled_Button_verifyCode: boolean = true;
  @State() isDisabled_Button_saveNewPassword: boolean = true;
  @State() isInAction_Button_sendVerificationCode: boolean = false;
  @State() isInAction_Button_verifyCode: boolean = false;
  @State() isInAction_Button_saveNewPassword: boolean = false;

  @Listen("radio-change-event")
  radioChangeEventHandler(e) {
    if (e.detail.name === "occupation") {
      state.occupation = e.detail.value;
    }
  }

  private emailForResetCode: string = "";
  private emailVerificationCode: string = "";
  private newPassword: string = "";
  private newPasswordAgain: string = "";

  private eventObj: any;
  private eventDateString: string = "";
  private registrationStartString: string = "";
  private registrationEndString: string = "";
  private authType: string = "genericAuth";
  private isAuthEnabled: boolean = false;

  @Listen("linkClick")
  linkClickHandler(event) {
    if (event.detail.name === "restartPasswordReset") {
      this.secondaryView = "sendVerificationCode";
      this.resetPasswordResetVars();
    } else if (event.detail.name === "goToLogin") {
      this.primaryView = "login";
      this.resetPasswordResetVars();
    } else if (event.detail.name === "resendVerificationCode") {
      this.handleResendVerificationCode();
    } else {
      this.primaryView = event.detail.name;
    }
    state.isNotificationActive = false;
    state.isLoginError = false;
    state.isSignupError = false;
  }

  @Listen("textInput")
  textInputHandler(event) {
    if (event.detail.name === "emailForResetCode") {
      this.emailForResetCode = event.detail.value;
      this.verifyEmailForResetCode();
    } else if (event.detail.name === "emailVerificationCode") {
      this.emailVerificationCode = event.detail.value;
      this.verifyCode();
    } else if (event.detail.name === "newPassword") {
      this.newPassword = event.detail.value;
      this.verifyNewPassword();
    } else if (event.detail.name === "newPasswordAgain") {
      this.newPasswordAgain = event.detail.value;
      this.verifyNewPassword();
    } else {
      textInputHelper(event.detail.name, event.detail.value);
    }
  }

  verifyEmailForResetCode() {
    let emailRegex: any =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    let isEmailValid: any = emailRegex.test(
      this.emailForResetCode.toLowerCase()
    );
    if (isEmailValid) {
      this.isDisabled_Button_sendVerificationCode = false;
    } else {
      this.isDisabled_Button_sendVerificationCode = true;
    }
  }

  verifyCode() {
    if (this.emailVerificationCode.length > 3) {
      this.isDisabled_Button_verifyCode = false;
    } else {
      this.isDisabled_Button_verifyCode = true;
    }
  }

  verifyNewPassword() {
    let isPasswordLong: boolean = false;
    let isPasswordSame: boolean = false;

    if (this.newPassword.length > 7) {
      isPasswordLong = true;
    } else {
      isPasswordLong = false;
    }

    if (this.newPassword === this.newPasswordAgain) {
      isPasswordSame = true;
    } else {
      isPasswordSame = false;
    }

    if (isPasswordLong && isPasswordSame) {
      this.isDisabled_Button_saveNewPassword = false;
    } else {
      this.isDisabled_Button_saveNewPassword = true;
    }
  }

  resetPasswordResetVars() {
    this.emailForResetCode = "";
    this.emailVerificationCode = "";
    this.newPassword = "";
    this.newPasswordAgain = "";
    this.secondaryView = "sendVerificationCode";
    this.isDisabled_Button_sendVerificationCode = true;
    this.isDisabled_Button_verifyCode = true;
    this.isDisabled_Button_saveNewPassword = true;
  }

  @Listen("buttonClick")
  async buttonClickHandler(event) {
    if (event.detail.name === "localLogin") {
      let localLoginPayload = localLoginPayloadGenerator();
      let { error } = isLoginInputValid(localLoginPayload);
      if (error) {
        state.isLoginError = true;
        state.notificationType = "error";
        state.notificationMessage = appLabel.login.error;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      let { isUserLoggedIn, loginMessage } = await localLoginHelper(
        localLoginPayload
      );
      if (!isUserLoggedIn) {
        state.isLoginError = true;
        state.notificationType = "error";
        state.notificationMessage = loginMessage;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      if (state.eventCode.length > 0) {
        state.eventCodeForRegistration = state.eventCode;
        this.history.push(
          `/registration/${state.eventCodeForRegistration}`,
          {}
        );
      } else {
        this.history.push("/events", {});
      }
      state.password = "";
    } else if (event.detail.name === "localSignup") {
      let localSignupPayload = localSignupPayloadGenerator();
      let { error } = isSignUpInputValid(localSignupPayload);
      if (error) {
        state.isSignupError = true;
        state.notificationType = "error";
        state.notificationMessage = appLabel.signup.error;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      let { isUserSignedUp, signupMessage } = await localSignupHelper(
        localSignupPayload
      );
      if (!isUserSignedUp) {
        state.isSignupError = true;
        state.notificationType = "error";
        state.notificationMessage = signupMessage;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      if (state.eventCode.length > 0) {
        state.eventCodeForRegistration = state.eventCode;
        this.history.push(
          `/registration/${state.eventCodeForRegistration}`,
          {}
        );
      } else {
        state.isEmailVerified = false;
        // state.isAccountSetup = false;
        this.history.push("/events", {});
      }
      console.log(`p-auth: ${state.isAccountSetup}`);
      state.password = "";
    } else if (event.detail.name === "sendVerificationCode") {
      this.handleSendVerificationCode();
    } else if (event.detail.name === "submitVerifyCode") {
      this.handleVerifyCode();
    } else if (event.detail.name === "saveNewPassword") {
      this.handleSaveNewPassword();
    }
  }

  componentWillLoad() {
    if (this.match.params.eventCode) {
      state.eventCode = this.match.params.eventCode.trim();
    }

    if (state.eventCode) {
      this.authType = "eventAuth";
    }
  }

  componentDidLoad() {
    /*  Next 5 lines: Causing multiple renders */
    if (this.authType === "genericAuth") {
      this.isDataFetched = true;
    } else if (this.authType === "eventAuth") {
      this.fetchData();
    }
  }

  async fetchData() {
    let { eventObj, error } = await getEventAuthHelper(state.eventCode);
    if (error) alert(error);
    this.eventObj = eventObj;
    this.prepareEventDateString();
    this.prepareRegistrationStrings();
    this.isAuthEnabled = eventObj.isAuthEnabled;
    if (eventObj) this.isDataFetched = true;
  }

  handleSendVerificationCode() {
    this.isInAction_Button_sendVerificationCode = true;

    let payload: any = {
      email: this.emailForResetCode.toLowerCase(),
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/sendpasswordresetcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Could not send password reset code");
        } else if (response.data.status === "Success") {
          this.secondaryView = "enterVerificationCode";
        }
        this.isInAction_Button_sendVerificationCode = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  handleResendVerificationCode() {
    let payload: any = {
      email: this.emailForResetCode.toLowerCase(),
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/resendpasswordresetcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Could not re-send password reset code");
        } else if (response.data.status === "Success") {
          alert("Password reset code sent");
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  handleVerifyCode() {
    this.isInAction_Button_verifyCode = true;

    let payload: any = {
      email: this.emailForResetCode.toLowerCase(),
      passwordResetCode: this.emailVerificationCode,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/verifypasswordresetcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Could not verify reset code");
          this.secondaryView = "passwordResetFailed";
        } else if (response.data.status === "Success") {
          this.secondaryView = "resetPassword";
        }
        this.isInAction_Button_verifyCode = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  handleSaveNewPassword() {
    this.isInAction_Button_saveNewPassword = true;

    let payload: any = {
      email: this.emailForResetCode.toLowerCase(),
      passwordResetCode: this.emailVerificationCode,
      newPassword: this.newPassword,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/resetpassword`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Could not verify reset code");
          this.secondaryView = "passwordResetFailed";
        } else if (response.data.status === "Success") {
          this.secondaryView = "passwordResetSuccessful";
        }
        this.isInAction_Button_saveNewPassword = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  prepareEventDateString() {
    let startsOn: string = new Date(this.eventObj.startsOn).toDateString();
    let endsOn: string = new Date(this.eventObj.endsOn).toDateString();
    let startDate: string = startsOn.substring(8, 10);
    let startMonth: string = startsOn.substring(4, 7);
    let startYear: string = startsOn.substring(11, 15);
    let endDate: string = endsOn.substring(8, 10);
    let endMonth: string = endsOn.substring(4, 7);
    let endYear: string = endsOn.substring(11, 15);
    let isDateSame: boolean = false;
    let isMonthSame: boolean = false;
    let isYearSame: boolean = false;
    if (startDate === endDate) {
      isDateSame = true;
    }
    if (startMonth === endMonth) {
      isMonthSame = true;
    }
    if (startYear === endYear) {
      isYearSame = true;
    }
    if (isDateSame && isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear}`;
    } else if (!isDateSame && isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} - ${endDate} ${startMonth}, ${startYear}`;
    } else if (isDateSame && !isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} - ${endDate} ${endMonth}, ${startYear}`;
    } else if (isDateSame && isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    } else if (!isDateSame && !isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} - ${endDate} ${endMonth}, ${startYear}`;
    } else if (isDateSame && !isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    } else if (!isDateSame && isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    } else if (!isDateSame && !isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    }
  }

  prepareRegistrationStrings() {
    let startsOn: string = new Date(
      this.eventObj.registrationStartsOn
    ).toDateString();
    let endsOn: string = new Date(
      this.eventObj.registrationEndsOn
    ).toDateString();
    let startDate: string = startsOn.substring(8, 10);
    let startMonth: string = startsOn.substring(4, 7);
    let startYear: string = startsOn.substring(11, 15);
    let startTime: string = new Date(
      this.eventObj.registrationStartsOn
    ).toLocaleString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
    this.registrationStartString = `${startDate} ${startMonth} ${startYear}, ${startTime.toLowerCase()} (IST)`;

    let endDate: string = endsOn.substring(8, 10);
    let endMonth: string = endsOn.substring(4, 7);
    let endYear: string = endsOn.substring(11, 15);
    let endTime: string = new Date(
      this.eventObj.registrationEndsOn
    ).toLocaleString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
    this.registrationEndString = `${endDate} ${endMonth} ${endYear}, ${endTime.toLowerCase()} (IST)`;
  }

  AuthEnabled: FunctionalComponent = () => (
    <c-page type="auth">
      <div class="auth-sidebar">
        {this.primaryView === "login" && <this.Login></this.Login>}
        {this.primaryView === "signup" && <this.SignUp></this.SignUp>}
        {this.primaryView === "forgotPassword" && (
          <this.ForgotPassword></this.ForgotPassword>
        )}
      </div>
    </c-page>
  );

  EventLogo: FunctionalComponent = () => (
    <div>
      <c-img type="sidebarLogo" src={this.eventObj.logoUrl}></c-img>
      <div class="divider"></div>
    </div>
  );

  AuthDisabled: FunctionalComponent = () => (
    <div class="auth-container">
      {/* <c-text type="heading">{this.eventObj.name}</c-text> */}
      {/* <c-img type="sidebarLogo" src={this.eventObj.logoUrl}></c-img>
      <br />
      <br /> */}
      <this.EventLogo></this.EventLogo>
      <c-link type="textWithIcon" url={""} iconName="calendar-outline">
        {this.eventDateString}
      </c-link>
      <c-link
        type="textWithIcon"
        url={this.eventObj.venueUrl}
        iconName="location-outline"
      >
        {this.eventObj.venueLabel}
      </c-link>
      <c-link
        type="textWithIcon"
        url={this.eventObj.website}
        iconName="link-outline"
      >
        {this.eventObj.website}
      </c-link>
      <br />
      {this.eventObj.isArchived ? (
        <c-text type="danger">
          Registration closed on
          <br />
          {this.registrationEndString}
        </c-text>
      ) : this.eventObj.isPublished ? (
        <c-text type="warning">
          Registration will open on <br />
          {this.registrationStartString}
        </c-text>
      ) : (
        <c-text type="warning">Event is not yet published</c-text>
      )}
    </div>
  );

  SkelAuth: FunctionalComponent = () => (
    <c-page type="auth">
      <div class="poster-container">
        <div class="skel-poster"></div>
      </div>
      <div class="auth-sidebar">
        <div class="auth-container">
          <br />
          <c-skel-line color="gray" width={100}></c-skel-line>
          <br />
          <c-skel-line color="gray" width={100}></c-skel-line>
          <br />
          <br />
          <br />
          <c-skel-line color="gray" width={100}></c-skel-line>
          <br />
          <c-skel-line color="gray" width={75}></c-skel-line>
          <br />
          <br />
          <c-skel-line color="gray" width={100}></c-skel-line>
          <br />
          <c-skel-line color="gray" width={75}></c-skel-line>
          <br />
          <br />
          <c-skel-line color="gray" width={100}></c-skel-line>
          <br />
          <c-skel-line color="gray" width={75}></c-skel-line>
        </div>
      </div>
    </c-page>
  );

  Login: FunctionalComponent = () => (
    <div class="auth-container">
      <div class="auth-container-header">
        {this.authType === "eventAuth" && <this.EventLogo></this.EventLogo>}
        <c-text type="heading">Login</c-text>
        <c-text>
          Not registered yet?{" "}
          <c-link name="signup" type="textWithEvent">
            Sign up
          </c-link>
        </c-text>
      </div>
      <c-oauth></c-oauth>
      <c-textbox
        type="floatingLabel"
        input-type="text"
        name="email"
        placeholder="Email"
        isDisabled={false}
        isError={state.isLoginError}
        value=""
      ></c-textbox>
      <c-textbox
        type="floatingLabel"
        input-type="password"
        name="password"
        placeholder="Password"
        isDisabled={false}
        isError={state.isLoginError}
        value=""
      ></c-textbox>
      <c-button
        type="solidWithIcon"
        name="localLogin"
        icon-name="log-in-outline"
        is-in-action={state.isLoginButtonInAction}
        label="Login"
      ></c-button>
      <c-text type="small">
        <c-link name="forgotPassword" type="textWithEvent">
          Forgot Password
        </c-link>
      </c-text>
    </div>
  );

  SignUp: FunctionalComponent = () => (
    <div class="auth-container">
      <div class="auth-container-header">
        {this.authType === "eventAuth" && <this.EventLogo></this.EventLogo>}
        <c-text type="heading">Sign Up</c-text>
        <c-text>
          Already have an account?{" "}
          <c-link name="login" type="textWithEvent">
            Login
          </c-link>
        </c-text>
      </div>
      <c-oauth></c-oauth>
      <div class="adjacent-inputboxes">
        <c-row>
          <c-textbox
            type="floatingLabel"
            input-type="text"
            name="firstName"
            placeholder="First Name"
            isDisabled={false}
            isError={state.isSignupError}
            value=""
          ></c-textbox>
          <c-textbox
            type="floatingLabel"
            input-type="text"
            name="lastName"
            placeholder="Last Name"
            isDisabled={false}
            isError={state.isSignupError}
            value=""
          ></c-textbox>
        </c-row>
      </div>
      <c-textbox
        type="floatingLabel"
        input-type="text"
        name="email"
        placeholder="Email"
        isDisabled={false}
        isError={state.isSignupError}
        value=""
      ></c-textbox>
      <c-textbox
        type="floatingLabel"
        input-type="password"
        name="password"
        placeholder="Password"
        isDisabled={false}
        isError={state.isSignupError}
        value=""
      ></c-textbox>
      <div class="occupation-container">
        <c-text>Your occupation?</c-text>
        <c-row>
          <c-radio
            name="occupation"
            label1="Student"
            label2=""
            label3=""
            val="student"
            isChecked={false}
          ></c-radio>
          <c-radio
            name="occupation"
            label1="Professional"
            label2=""
            label3=""
            val="professional"
            isChecked={true}
          ></c-radio>
        </c-row>
      </div>
      {/* <c-inputphone
        is-disabled={false}
        numpad-placeholder="Mobile No (optional)"
      ></c-inputphone> */}
      <c-button
        type="solidWithIcon"
        name="localSignup"
        icon-name="log-in-outline"
        is-in-action={state.isSignupButtonInAction}
        label="Sign up"
      ></c-button>
      {/* <c-text type="small">
        <c-link name="mobileEmailRequirement" type="textWithEvent">
          Learn why we need
        </c-link>{" "}
        your mobile no
      </c-text>
      <br /> */}
      <c-text type="small">
        By signing up, you agree to our{" "}
        <c-link url="https://www.indiahci.org/terms-of-service.html">
          terms of service
        </c-link>{" "}
        &{" "}
        <c-link url="https://www.indiahci.org/privacy-policy.html">
          privacy policy
        </c-link>
      </c-text>
    </div>
  );

  ForgotPassword: FunctionalComponent = () => (
    <div class="auth-container">
      <div class="auth-container-header">
        {this.authType === "eventAuth" && <this.EventLogo></this.EventLogo>}
        <c-text type="heading">Forgot Password</c-text>
      </div>
      {this.secondaryView === "sendVerificationCode" && (
        <this.ForgotPassword_sendVerificationCode></this.ForgotPassword_sendVerificationCode>
      )}
      {this.secondaryView === "enterVerificationCode" && (
        <this.ForgotPassword_submitVerificationCode></this.ForgotPassword_submitVerificationCode>
      )}
      {this.secondaryView === "resetPassword" && (
        <this.ForgotPassword_resetPassword></this.ForgotPassword_resetPassword>
      )}
      <this.ForgotPassword_Footer></this.ForgotPassword_Footer>
    </div>
  );

  ForgotPassword_sendVerificationCode: FunctionalComponent = () => (
    <div class="forgot-password-container">
      <div class="forgot-password-container__step-header">
        <c-text type="subtext">STEP 1 OF 3 &mdash; VERIFY EMAIL</c-text>
      </div>
      <div class="forgot-password-container__step-content">
        {" "}
        <c-text>Enter your email & we will send you a verification code</c-text>
      </div>
      <c-textbox
        type="floatingLabel"
        input-type="text"
        name="emailForResetCode"
        placeholder="Your email"
        isDisabled={false}
        value={this.emailForResetCode}
      ></c-textbox>

      <c-button
        name="sendVerificationCode"
        isDisabled={this.isDisabled_Button_sendVerificationCode}
        isInAction={this.isInAction_Button_sendVerificationCode}
      >
        Send
      </c-button>
    </div>
  );

  ForgotPassword_submitVerificationCode: FunctionalComponent = () => (
    <div class="forgot-password-container">
      <div class="forgot-password-container__step-header">
        <c-text type="subtext">STEP 2 OF 3 &mdash; VERIFY RESET CODE</c-text>
      </div>
      <div class="forgot-password-container__step-content">
        <c-text>
          Enter the reset code sent to <strong>{this.emailForResetCode}</strong>{" "}
          <br />
          <c-text type="subtext">
            <c-link name="resendVerificationCode" type="textWithEvent">
              Re-send code
            </c-link>
          </c-text>
        </c-text>
      </div>

      <c-textbox
        type="floatingLabel"
        input-type="text"
        name="emailVerificationCode"
        placeholder="Verification Code"
        isDisabled={false}
        value={this.emailVerificationCode}
      ></c-textbox>
      <c-button
        name="submitVerifyCode"
        isDisabled={this.isDisabled_Button_verifyCode}
        isInAction={this.isInAction_Button_verifyCode}
      >
        Submit
      </c-button>
    </div>
  );

  ForgotPassword_resetPassword: FunctionalComponent = () => (
    <div class="forgot-password-container">
      <div class="forgot-password-container__step-header">
        <c-text type="subtext">STEP 3 OF 3 &mdash; RESET PASSWORD</c-text>
      </div>
      <c-text>
        Enter new password
        <br />
        (min. 8 chars)
      </c-text>
      <c-textbox
        type="floatingLabel"
        input-type="password"
        name="newPassword"
        placeholder="New password"
        isDisabled={false}
        value={this.newPassword}
      ></c-textbox>
      <c-textbox
        type="floatingLabel"
        input-type="password"
        name="newPasswordAgain"
        placeholder="Re-type password"
        isDisabled={false}
        value={this.newPasswordAgain}
      ></c-textbox>
      <c-button
        name="saveNewPassword"
        isDisabled={this.isDisabled_Button_saveNewPassword}
        isInAction={this.isInAction_Button_saveNewPassword}
      >
        Save Password
      </c-button>
    </div>
  );

  ForgotPassword_Footer: FunctionalComponent = () => (
    <div>
      {this.secondaryView === "passwordResetSuccessful" && (
        <div class="password-reset-container password-reset-container--success">
          Password reset successful
        </div>
      )}

      {this.secondaryView === "passwordResetFailed" && (
        <div class="password-reset-container password-reset-container--failure">
          Password reset failed
        </div>
      )}
      {this.secondaryView != "passwordResetFailed" ? (
        <c-link name="goToLogin" type="textWithEvent">
          Go back to login
        </c-link>
      ) : (
        <c-link name="restartPasswordReset" type="textWithEvent">
          Try reset again
        </c-link>
      )}
    </div>
  );

  render() {
    if (this.isDataFetched) {
      if (this.authType === "genericAuth") {
        return <this.AuthEnabled></this.AuthEnabled>;
      } else if (this.authType === "eventAuth") {
        if (this.isAuthEnabled) {
          return <this.AuthEnabled></this.AuthEnabled>;
        } else {
          return <this.AuthDisabled></this.AuthDisabled>;
        }
      }
    } else {
      return <this.SkelAuth></this.SkelAuth>;
    }
  }
}

injectHistory(PAuth);
