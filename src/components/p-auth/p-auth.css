/* p-auth .poster-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 72.5%;
  height: 100vh;
  background: white;
  background: rgba(0, 0, 0, 0.05);
}

p-auth .poster {
  width: 90%;
  height: 90vh;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
} */

/* p-auth .poster-container {
  position: "relative";
  width: 72.5%;
  height: 100vh;
  overflow: "hidden";
  background-color: rgba(0, 0, 0, 0.05);
  border-right: rgba(0, 0, 0, 0.1);
} */

p-auth .skel-poster {
  width: 90%;
  height: 90vh;
  border-radius: var(--site-border-radius);
  background: #ccc;
  background-image: linear-gradient(
    90deg,
    #f4f4f4 0px,
    rgba(229, 229, 229, 0.8) 40%,
    #f4f4f4 100%
  );
  background-size: 200%;
  animation: shine-line 1s infinite ease-out;
}

@keyframes shine-line {
  0% {
    background-position: -100px;
  }
  40%,
  100% {
    background-position: 140px;
  }
}

p-auth c-text .heading {
  font-weight: 400;
}

p-auth .auth-sidebar {
  width: 27.5%;
  height: 100vh;
  font-size: 0.9em;
}

p-auth .auth-sidebar-disabled {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 30%;
}

p-auth .auth-sidebar-skel {
  background: yellow;
}

p-auth .auth-container {
  /* width: 75%; */
  background: white;
  padding: 2em;
  border-radius: 0.5em;
  border: var(--site-border);
  margin: 4em auto 0 auto;
}

p-auth .auth-container c-row .floating-label-container {
  width: 140px;
  margin: 0;
  margin-top: 0.75em;
}
/* p-auth .auth-container c-row c-textbox:first-child input {
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

p-auth .auth-container c-row c-textbox:last-child input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
} */

p-auth .auth-container-header {
  margin-bottom: 1.5em;
}

p-auth .oauth-container c-button button {
  margin-bottom: 0;
}

p-auth .oauth-container c-button .linkedin {
  margin-bottom: 1em;
}

p-auth .forgot-password-container {
  /* background: rgba(0, 0, 0, 0.05);
  padding: 1.25em 1.25em 0.1em 1.25em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.05); */
  margin-bottom: 1.75em;
}

p-auth .forgot-password-container__step-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 1em;
}

p-auth .forgot-password-container c-text p {
  margin-bottom: 1em;
}

p-auth c-inputphone .inputphone-container {
  margin-bottom: 1em;
}

p-auth c-radio .radio-btn-container {
  margin-top: 0;
  margin-bottom: 0;
}

p-auth .occupation-container {
  padding: var(--site-padding);
  border: var(--site-border);
  border-radius: var(--site-border-radius);
  margin-bottom: 1em;
  /* background: rgba(0, 0, 0, 0.03); */
  /* background: var(--accent-color-bg-lightest); */
}

p-auth .occupation-container c-text p {
  margin-bottom: 0.75em;
  /* font-size: 0.8em; */
  color: rgba(0, 0, 0, 0.5);
}

p-auth c-textbox input {
  margin-bottom: 1em;
}

p-auth c-button button {
  margin-bottom: 1.5em;
}

p-auth .hidden {
  visibility: hidden;
}

p-auth .event-description-middle {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
  padding: 1em 0;
  margin-top: 0.5em;
  font-size: 1.1em;
}

p-auth .event-description-middle .text-with-icon {
  margin-bottom: 1em;
}

p-auth .password-reset-container {
  padding: 1em;
  border-radius: 0.25em;
  margin-bottom: 1em;
}

p-auth .password-reset-container--success {
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
}

p-auth .password-reset-container--failure {
  background: var(--accent-pink-bg-lighter);
  color: var(--accent-pink-darker);
}

/* New additions, removes poster from auth page*/
p-auth .poster-container {
  display: none;
}

p-auth .auth-sidebar {
  width: 100%;
}

p-auth .auth-container {
  max-width: 280px;
}

p-auth .adjacent-inputboxes c-row c-textbox:first-child input {
  border-right: none;
  border-radius: var(--site-border-radius) 0 0 var(--site-border-radius);
}

p-auth .adjacent-inputboxes c-row c-textbox:last-child input {
  border-radius: 0 var(--site-border-radius) var(--site-border-radius) 0;
}

p-auth .forgot-password-container__step-content {
  margin-bottom: 1.5em;
}

p-auth .divider {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 1em;
  margin-bottom: 1.5em;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  p-auth .auth-container {
    margin: 2em auto 2em auto;
    padding: 1em;
  }
}
