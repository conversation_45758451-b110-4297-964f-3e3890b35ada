import { newSpecPage } from '@stencil/core/testing';
import { CMobileMenu } from '../c-mobile-menu';

describe('c-mobile-menu', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CMobileMenu],
      html: `<c-mobile-menu></c-mobile-menu>`,
    });
    expect(page.root).toEqualHtml(`
      <c-mobile-menu>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-mobile-menu>
    `);
  });
});
