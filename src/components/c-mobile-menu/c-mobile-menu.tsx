import { Component, Listen, Host, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-mobile-menu",
  styleUrl: "c-mobile-menu.css",
  shadow: true,
})
export class CMobileMenu {
  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "closeMobileMenu") {
      state.isMobileMenuOpen = false;
    }
  }

  private navOpts: any = [
    {
      type: "label",
      label: "Events",
      isFirstLabel: true,
    },
    {
      type: "navItem",
      name: "upcomingEvents",
      label: "Upcoming",
      state: "",
      icon: "calendar-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "archivedEvents",
      label: "Archived",
      state: "",
      icon: "archive-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "HCIPAI Membership",
      isFirstLabel: false,
    },
    {
      type: "navItem",
      name: "membership",
      label: "Get HCIPAI Membership",
      state: "",
      icon: "people-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "ACCOUNT",
      isFirstLabel: false,
    },
    {
      type: "navItem",
      name: "mobileProfile",
      label: "Profile",
      state: "",
      icon: "person-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "mobilePurchases",
      label: "Purchases",
      state: "",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
  ];

  private navName: string = "homePageNav";

  render() {
    return (
      <Host>
        <header>
          <c-row>
            <c-site-logo
              link="https://indiahci.org"
              src="https://res.cloudinary.com/layerpark/image/upload/v1619012555/hcipai-transparent-logo.png"
            ></c-site-logo>
            <c-button type="ghost_Small" name="closeMobileMenu">
              <ion-icon name="close"></ion-icon>
            </c-button>
          </c-row>
          <br />
          <c-vnav
            nav-opts-str={JSON.stringify(this.navOpts)}
            name={this.navName}
          ></c-vnav>
        </header>
        <c-account-control></c-account-control>
        <div></div>
      </Host>
    );
  }
}
