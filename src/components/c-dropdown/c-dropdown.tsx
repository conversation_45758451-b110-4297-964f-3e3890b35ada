import { Component, Prop, State, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-dropdown",
  styleUrl: "c-dropdown.css",
})
export class CDropdown {
  @Prop() optionStr: string;
  @Prop() name: string;
  @Prop() isDisabled: boolean = false;
  @Prop() value: string;
  @State() isOptionSelected: boolean = false;

  private optionObj: any;

  selectEl!: HTMLSelectElement;

  @Event({
    eventName: "dropdown-input-event",
    bubbles: true,
  })
  dropDownInputEvent: EventEmitter;

  componentWillLoad() {
    this.parseOptionStr();
  }

  componentDidLoad() {
    this.assignInitValue();
  }

  assignInitValue() {
    if (this.value) this.selectEl.value = this.value;
  }

  handleDropDown(event) {
    if (this.name === "singleSelectorTicket") {
      let label: string = "";
      let value: string = event.target.value;
      this.optionObj.forEach((option) => {
        if (option.value === event.target.value) label = option.label;
      });
      this.dropDownInputEvent.emit({
        label: label,
        value: value,
      });
    } else {
      this.dropDownInputEvent.emit({
        filter: this.name,
        value: event.target.value,
      });
    }
    this.generateStyle();
  }

  generateStyle() {
    if (this.selectEl.value.length > 0) {
      this.isOptionSelected = true;
    } else {
      this.isOptionSelected = false;
    }
  }

  parseOptionStr() {
    this.optionObj = JSON.parse(this.optionStr);
  }

  render() {
    return (
      <select
        ref={(el) => (this.selectEl = el as HTMLSelectElement)}
        class={this.isOptionSelected && "selected"}
        onInput={(event) => this.handleDropDown(event)}
        disabled={this.isDisabled}
      >
        {this.optionObj.map((option) => (
          <option
            value={
              this.name === "changeMobilePage"
                ? `${option.name}---${option.label}`
                : option.value
            }
          >
            {option.label}
          </option>
        ))}
      </select>
    );
  }
}
