c-dropdown select {
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.7em 0.5em;
  padding-left: 1em;
  width: 180px;
  outline: none;
  border-radius: 0.25em;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 0.8em;
  margin-bottom: 0;
  margin-right: 1.5em;
  background-color: rgba(238, 234, 245, 0.5);
  background-image: url("data:image/svg+xml;utf8,<svg fill='%23593196' fill-opacity='0.4' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: 98%;
  background-position-y: 2px;
  transition: all 0.15s ease-in;
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.8);
  background-color: transparent;
}

c-dropdown select:focus {
  /* outline: var(--accent-color) solid 1px; */
  border: 1px solid var(--accent-color-bg);
}

c-dropdown select::-ms-expand {
  display: none;
}

c-dropdown select:hover {
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.3);
}

c-dropdown select:disabled {
  pointer-events: none;
}

c-dropdown .selected {
  background-color: var(--accent-color-bg-lightest);
}
