c-divider .single {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 1.5em;
  margin-bottom: 1em;
}

c-divider .dual {
  /* border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
  margin: 0.5em 0 0 0;
  padding: 0.75em 0 1em 0;
}

c-divider .oauth {
  display: flex;
  justify-content: space-around;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  opacity: 0.6;
}

c-divider .oauth-divider-container {
  width: 50%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-divider .line {
  width: 35%;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
}

c-divider .or {
  font-size: 0.8em;
  margin: 0;
  color: rgba(0, 0, 0, 0.5);
}

c-divider .vertical-sepeartor {
  margin-right: 1px solid black;
}
