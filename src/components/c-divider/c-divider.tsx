import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-divider",
  styleUrl: "c-divider.css",
})
export class CDivider {
  @Prop() type: string;
  render() {
    if (this.type === "single") {
      return (
        <div class="single">
          <slot />
        </div>
      );
    } else if (this.type === "dual") {
      return (
        <div class="dual">
          <slot />
        </div>
      );
    } else if (this.type === "vertical") {
      <div class="vertical-sepeartor"></div>;
    } else if (this.type === "oauth") {
      return (
        <div class="oauth">
          <div class="oauth-divider-container">
            <div class="line"></div>
            <p class="or">OR</p>
            <div class="line"></div>
          </div>
        </div>
      );
    }
  }
}
