import {
  Component,
  Listen,
  Prop,
  FunctionalComponent,
  State,
  h,
} from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-membership-thanks",
  styleUrl: "p-membership-thanks.css",
})
export class PMembershipThanks {
  /*----
  Props
  ----*/
  @Prop() history: RouterHistory;

  /*----
  States
  ----*/
  @State() compState = "init";

  /*------------
  Event Listener
  ------------*/
  @Listen("back-to-account")
  backToAccount(event) {
    event.preventDefault();
    this.backToAccountHandler();
  }
  /*------------
  Event Handlers
  ------------*/
  backToAccountHandler() {
    this.history.push("/", {});
  }

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    if (!this.history.location.state) {
      this.goToHome();
    }
  }

  componentDidLoad() {
    setTimeout(() => {
      this.checkPaymentStatus();
    }, 3000);
  }

  /*-----
  Methods
  -----*/
  goToHome() {
    this.history.push("/", {});
  }

  changeComponentState(newState) {
    this.compState = newState;
  }

  checkPaymentStatus() {
    let payload = this.history.location.state;
    if (payload.paymentGateway === "BankTransfer") {
      this.processBankPayment(payload);
    } else if (payload.paymentGateway === "Razorpay") {
      this.processRazorpayPayment(payload);
    }
  }

  processBankPayment(payload: any) {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/membership-bank-payment`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.changeComponentState("bank-payment-failed");
        } else if (response.data.status === "Success") {
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
          state.isCouponsAvailable = false;
          state.paymentGateway = "razorpay";
          this.changeComponentState("bank-payment-success");
        }
        state.isPaying = false;
        state.isPaymentBtnDisabled = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  processRazorpayPayment(payload: any) {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/membership-razorpay-payment`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.changeComponentState("razorpay-failed");
        } else if (response.data.status === "Success") {
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
          state.isCouponsAvailable = false;
          state.paymentGateway = "razorpay";
          this.changeComponentState("razorpay-success");
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  /*------------------
  Functional Component
  ------------------*/
  Init: FunctionalComponent = () => (
    <div class="init-container">
      <c-spinner-dark></c-spinner-dark>
      &nbsp;&nbsp;
      <p class="processing-message">Processing payment..</p>
    </div>
  );

  BankSuccess: FunctionalComponent = () => (
    <div>
      <h1 class="success-header">
        <span class="tick">✓</span> Payment recorded
      </h1>
      <p>
        We have recorded your purchases and bank transaction code. We will send
        you a confirmation once we verify your transaction. This process might
        take 2-7 working days.
      </p>
      <div class="footer-controls">
        <div></div>
        <c-btn
          name="backtoaccount"
          label="My account"
          action-label=".."
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
    </div>
  );

  BankFailure: FunctionalComponent = () => (
    <div>
      <h1>Payment failed</h1>
      <p>
        Something went wrong and we could not save your purchases. Please
        contact HCIPAI.
      </p>

      <div class="footer-controls">
        <div></div>
        <c-btn
          name="backtoaccount"
          label="My account"
          action-label=".."
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
    </div>
  );

  RazorpaySuccess: FunctionalComponent = () => (
    <div>
      <h1 class="heading-success">
        <span class="tick">✓</span> Payment successful
      </h1>
      <p>
        We have recorded your purchases. A confirmation mail has been sent to
        your email.
      </p>
      <div class="footer-controls">
        <div></div>
        <c-btn
          name="backtoaccount"
          label="My account"
          action-label=".."
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
    </div>
  );

  RazorpayFailure: FunctionalComponent = () => (
    <div>
      <h1 class="heading-failed">Something went wrong</h1>
      <p>
        Our payment gateway (Razorpay) was unable to process your payment.
        Either the transaction failed entirely or there were processing delays.
      </p>
      <p>
        <strong>In case of payment failure</strong>, money should not be
        deducted from your account/card. You may go back to your account and try
        paying once again.
      </p>
      <p>
        <strong>But in the rare case of processing delays</strong>, money is
        probably deducted from your account/card. But don't worry, it usually
        takes a few minutes to hours for this processing to complete. Kindly
        refrain from purchasing further items and keep checking the "Purchases"
        page of your IndiaHCI account. Once the processing is complete, the
        items will be marked as "Paid" and will show up in the "Purchases" page.
        In case the processing is not successful within 5 days, the deducted
        amount will be refunded to your account/card.
      </p>

      <div class="footer-controls">
        <div></div>
        <c-btn
          name="backtoaccount"
          label="My account"
          action-label=".."
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
    </div>
  );

  render() {
    return (
      <div class="bank-thanks-container">
        {this.compState === "init" && <this.Init></this.Init>}
        {this.compState === "bank-payment-success" && (
          <this.BankSuccess></this.BankSuccess>
        )}
        {this.compState === "bank-payment-failed" && (
          <this.BankFailure></this.BankFailure>
        )}
        {this.compState === "razorpay-success" && (
          <this.RazorpaySuccess></this.RazorpaySuccess>
        )}
        {this.compState === "razorpay-failed" && (
          <this.RazorpayFailure></this.RazorpayFailure>
        )}
      </div>
    );
  }
}

injectHistory(PMembershipThanks);
