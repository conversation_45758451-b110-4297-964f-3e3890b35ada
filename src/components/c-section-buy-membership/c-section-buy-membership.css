c-section-buy-membership {
  width: 100%;
}

c-section-buy-membership .container {
  background: rgb(89, 49, 150);
  background: linear-gradient(
    135deg,
    rgba(89, 49, 150, 1) 0%,
    rgba(106, 70, 161, 1) 35%,
    rgba(122, 90, 171, 1) 100%
  );
  padding: 1em;
  border-radius: 0.4em;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05);
}

c-section-buy-membership .disable-membership-tab-left {
  pointer-events: none;
  opacity: 0.2;
}

c-section-buy-membership .disable-membership-tab-right {
  pointer-events: none;
  border: 2px solid rgba(255, 255, 255, 0);
  border-left: 2px solid rgba(255, 255, 255, 0.15);
}

c-section-buy-membership .disable-membership-tab-right p {
  opacity: 0.2;
  pointer-events: none;
}

c-section-buy-membership .disable-membership-tab-right .membership-price-band {
  opacity: 0.2;
  pointer-events: none;
}

c-section-buy-membership .seperator {
  margin: 2em 0 2em 0;
}

c-section-buy-membership .membership-heading {
  color: var(--accent-color-bg-lighter);
  font-weight: 400;
  margin-bottom: 0em;
  font-size: 0.9em;
  margin-top: 0;
  width: 80%;
}

c-section-buy-membership .membership-heading-small {
  font-size: 0.5em;
}

c-section-buy-membership .membership-name {
  color: var(--accent-color-bg-lighter);
  font-size: 0.9em;
  /* font-weight: 700; */
  line-height: 1.2;
  margin-bottom: 1.5em;
  margin-top: 0;
}

c-section-buy-membership .membership-band {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5em;
}

c-section-buy-membership .membership-card {
  width: 50%;
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 1em;
  border-radius: 0.25em;
  /* background: var(--accent-color); */
}

c-section-buy-membership .membership-card-left {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}

c-section-buy-membership .membership-card-right {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* c-section-buy-membership .membership-card-show-border-right {
  border-right: 2px solid rgba(255, 255, 255, 0.15);
} */

c-section-buy-membership .membership-price {
  color: var(--accent-color-bg-lightest);
  font-weight: 700;
  font-size: 0.9em;
}

c-section-buy-membership .membership-subheading {
  font-size: 0.8em;
  font-weight: 400;
  color: var(--accent-color-bg);
}

c-section-buy-membership .membership-benefits-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  margin-bottom: 2em;
}

c-section-buy-membership .membership-benefits {
  margin-bottom: 0.5em;
  color: var(--accent-color-bg-light);
}

c-section-buy-membership .green-tick {
  color: var(--accent-green);
}

c-section-buy-membership .membership-price-subscription {
  font-size: 0.8em;
  font-weight: 400;
  color: var(--accent-color-bg);
}

c-section-buy-membership .membership-price-band {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

c-section-buy-membership .hide {
  display: none;
}

c-section-buy-membership .membership-header-container {
  display: flex;
  justify-content: space-between;
}

c-section-buy-membership .expand-btn {
  font-weight: 700;
  border: 0;
  background: none;
  background: var(--accent-color-lightest);
  color: var(--accent-color-bg-lightest);
  padding: 1em;
  border-radius: 0.25em;
  transition: all 0.15s ease-in;
  outline: none;
  display: none;
}

c-section-buy-membership .expand-btn:hover {
  cursor: pointer;
  background: var(--accent-color-light);
  color: var(--accent-color-bg-lightest);
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  p-home c-section-buy-membership .container {
    display: block;
    width: 100%;
    box-sizing: border-box;
  }
  c-section-buy-membership .membership-heading {
    width: 100%;
  }
  c-section-buy-membership .membership-band {
    display: block;
  }
  c-section-buy-membership .membership-card {
    width: 87.5%;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 0.25em;
  }

  c-section-buy-membership .membership-card-left {
    border-bottom: 0;
    border-radius: 0.25em 0.25em 0 0;
  }

  c-section-buy-membership .membership-card-right {
    border-radius: 0em 0em 0.25em 0.25em;
  }
}
