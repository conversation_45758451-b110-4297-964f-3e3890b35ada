import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import Store from "../../global/store";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-section-buy-membership",
  styleUrl: "c-section-buy-membership.css",
})
export class CSectionBuyMembership {
  // @Listen("membership-selected")
  // membershipSelectedHandler(event) {
  //   this.activeMembershipBtn = event.detail.type;
  // }
  // @Listen("membership-removed")
  // membershipRemovedHandler(event) {
  //   event.preventDefault();
  //   this.activeMembershipBtn = "annual-lifetime";
  // }

  @Event({
    eventName: "goToMembershipCheckout",
    bubbles: true,
  })
  goToMembershipCheckout: EventEmitter;

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "annualMembershipCheckout") {
      state.membershipInCart = "annualMembership";
    } else if (e.detail.name === "lifetimeMembershipCheckout") {
      state.membershipInCart = "lifetimeMembership";
    }
    this.goToMembershipCheckout.emit();
  }

  @State() isFetching: boolean = true;
  @State() activeMembershipBtn: string = "annual-lifetime";
  @State() isEnlarged: boolean = true;

  @State() isButtonSelected_AnnualMembership: boolean = false;
  @State() isButtonSelected_LifetimeMembership: boolean = false;

  private lifetimeTicketID: string;
  private lifetimeTitle: string;
  private lifetimeSubTitle: string;
  private lifetimePrice: number;
  private lifetimeMembershipBtnState: string;

  private annualTicketID: string;
  private annualTitle: string;
  private annualSubTitle: string;
  private annualPrice: number;
  private annualMembershipBtnState: string;

  private expandBtnLabel: string = "Know More";

  componentDidLoad() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/getmemberticketdetails`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
        } else if (response.data.status === "Success") {
          this.annualTicketID = response.data.payload[0].ticketID;
          this.annualTitle = response.data.payload[0].title;
          this.annualSubTitle = response.data.payload[0].subTitle;
          this.annualPrice = response.data.payload[0].price;
          this.annualMembershipBtnState =
            response.data.payload[0].ticketBtnStatus;
          this.lifetimeTicketID = response.data.payload[1].ticketID;
          this.lifetimeTitle = response.data.payload[1].title;
          this.lifetimeSubTitle = response.data.payload[1].subTitle;
          this.lifetimePrice = response.data.payload[1].price;
          this.lifetimeMembershipBtnState =
            response.data.payload[1].ticketBtnStatus;
          this.isFetching = false;

          if (this.lifetimeMembershipBtnState === "remove-from-cart") {
            this.activeMembershipBtn = "lifetime";
          } else if (this.annualMembershipBtnState === "remove-from-cart") {
            this.activeMembershipBtn = "annual";
          } else {
            this.activeMembershipBtn = "annual-lifetime";
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  handleExpandBtnClick(event) {
    event.preventDefault();
    this.isEnlarged = !this.isEnlarged;
    if (this.isEnlarged === true) {
      this.expandBtnLabel = "Minimise";
    } else {
      this.expandBtnLabel = "Know More";
    }
  }

  render() {
    return (
      <div class="container">
        <div class="membership-header-container">
          <h2 class="membership-heading">
            Become a member. Get exclusive content, networking opportunities & event discounts
          </h2>
          <button
            class="expand-btn"
            onClick={(event) => this.handleExpandBtnClick(event)}
          >
            {this.expandBtnLabel}
          </button>
        </div>
        <div class={this.isEnlarged ? "membership-band" : "hide"}>
          <div
            class={
              this.activeMembershipBtn === "annual"
                ? "membership-card membership-card-left disable-membership-tab-left"
                : "membership-card membership-card-left"
            }
          >
            {this.isFetching ? (
              <div>
                <br />
                <c-skel-line color="accent" width={75}></c-skel-line>
                <br />
                <c-skel-line color="accent" width={75}></c-skel-line>
              </div>
            ) : (
              <p class="membership-name">
                {this.lifetimeTitle} <br />
                <span class="membership-subheading">
                  {this.lifetimeSubTitle.toUpperCase()}
                </span>
              </p>
            )}

            {this.isFetching ? (
              <c-skel-line color="accent" width={75}></c-skel-line>
            ) : (
              <div class="membership-price-band">
                <span class="membership-price">
                  {Store.getCurrencySymbol()}
                  {this.lifetimePrice}
                  <br />
                  <span
                    class={this.isEnlarged ? "membership-subheading" : "hide"}
                  >
                    Incl. Taxes (GST)
                  </span>
                </span>
                <c-button
                  type="ghost_Small_onDark"
                  name="lifetimeMembershipCheckout"
                  value={this.lifetimeTicketID}
                >
                  Buy
                </c-button>
              </div>
            )}
          </div>
          <div
            class={
              this.activeMembershipBtn === "lifetime"
                ? "membership-card membership-card-right disable-membership-tab-right"
                : "membership-card membership-card-right"
            }
          >
            {this.isFetching ? (
              <div>
                <br />
                <c-skel-line color="accent" width={75}></c-skel-line>
                <br />
                <c-skel-line color="accent" width={75}></c-skel-line>
              </div>
            ) : (
              <p class="membership-name">
                {this.annualTitle} <br />
                <span class="membership-subheading">
                  {this.annualSubTitle.toUpperCase()}
                </span>
              </p>
            )}

            {this.isFetching ? (
              <c-skel-line color="accent" width={75}></c-skel-line>
            ) : (
              <div class="membership-price-band">
                <span class="membership-price">
                  {Store.getCurrencySymbol()}
                  {this.annualPrice}
                  <span class="membership-price-subscription"> / yr</span>
                  <br />
                  <span
                    class={this.isEnlarged ? "membership-subheading" : "hide"}
                  >
                    Incl. Taxes (GST)
                  </span>
                </span>
                <c-button
                  type="ghost_Small_onDark"
                  name="annualMembershipCheckout"
                  value={this.annualTicketID}
                >
                  Buy
                </c-button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
}
