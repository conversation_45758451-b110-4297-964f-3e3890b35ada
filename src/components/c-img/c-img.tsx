import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-img",
  styleUrl: "c-img.css",
})
export class CImg {
  @Prop() type: string;
  @Prop() src: string;
  @Prop() alt: string;

  render() {
    if (this.type === "oauthIcon") {
      return <img class="oauth-icon" src={this.src} alt={this.alt} />;
    } else if (this.type === "oauthDp") {
      return (
        <img
          class="oauth-dp"
          src="https://media-exp1.licdn.com/dms/image/C5103AQFkYeQbg8KWZQ/profile-displayphoto-shrink_400_400/0/1587238650233?e=1648684800&v=beta&t=HoLV0IndPXB-RrVR7-FZW2qMOiw1PBEkFnEhxkHQkJ8"
          alt={this.alt}
        />
      );
    } else if (this.type === "menuDp") {
      return (
        <img
          class="menu-dp"
          src="https://media-exp1.licdn.com/dms/image/C5103AQFkYeQbg8KWZQ/profile-displayphoto-shrink_400_400/0/1587238650233?e=1648684800&v=beta&t=HoLV0IndPXB-RrVR7-FZW2qMOiw1PBEkFnEhxkHQkJ8"
          alt={this.alt}
        />
      );
    } else if (this.type === "sidebarLogo") {
      return <img class="sidebar-logo" src={this.src} alt={this.alt} />;
    }
  }
}
