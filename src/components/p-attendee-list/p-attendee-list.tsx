import { Component, Prop, h, State } from "@stencil/core";
import { RouterHistory, MatchResults, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-attendee-list",
  styleUrl: "p-attendee-list.css",
})
export class PAttendeeList {
  @Prop() history: RouterHistory;
  @Prop() match: MatchResults;
  @State() attendeeList: any = [];

  @State() isDataFetched: boolean = false;

  private totalAttendees: number = 0;
  private eventCode: string = "";
  private eventName: string = "";

  componentWillLoad() {
    this.eventCode = this.match.params.eventCode.trim();
  }

  componentDidLoad() {
    this.fetchData();
  }

  fetchData() {
    let payload = {
      eventCode: this.eventCode,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/attendee-list-v3`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.attendeeList = [];
          this.attendeeList = response.data.payload.attendees;
          this.attendeeList = [...this.attendeeList];
          this.eventName = response.data.payload.eventName;
          this.totalAttendees = response.data.payload.attendeeCount;
          this.isDataFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <main>
        {this.isDataFetched ? (
          <div>
            <header>
              <c-text>{this.eventName}</c-text>
              <c-text type="subtext">
                Total attendees: {this.totalAttendees}
              </c-text>
            </header>
            <br />
            <c-banner>
              <p>
                <strong>Instructions for Registration SVs:</strong>
              </p>
              <ul>
                <li>
                  People marked as "Unverified Students" should be asked to
                  produce their <u>proof of student status</u>. If they fail to
                  do so, kindly escalate the matter to the General Chairs.
                </li>

                {this.eventCode ===
                  "india-hci-2024-2b28ee8b-054d-4bbf-9364-aba716edc553" && (
                  <div>
                    <br />
                    <li>
                      Kindly ask <u>Naman Soni (<EMAIL>)</u> to
                      show proof of payment of ₹5900 paid on 30th June 2024. If
                      they fail to do so, kindly escalate the matter to the
                      General Chairs.
                    </li>
                  </div>
                )}
              </ul>
            </c-banner>
            <br />
            <c-list
              type="attendeeList"
              listItemsAsString={JSON.stringify(this.attendeeList)}
            ></c-list>
          </div>
        ) : (
          <p>Fetching attendee list.. </p>
        )}
      </main>
    );
  }
}

injectHistory(PAttendeeList);
