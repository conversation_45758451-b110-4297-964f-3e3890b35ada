c-rm-purchases .card {
  background: white;
  padding: 1em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5em;
}

c-rm-purchases .row {
  display: flex;
  justify-content: space-between;
}

c-rm-purchases .primary-info {
  font-size: 0.9em;
}

c-rm-purchases .row-item {
  padding: 0;
}

c-rm-purchases .row-item label {
  color: rgba(0, 0, 0, 0.5);
}

c-rm-purchases .row-item-1 {
  width: 30%;
  margin: 0;
}

c-rm-purchases .row-item-2 {
  width: 45%;
  margin: 0;
}

c-rm-purchases .row-item-3 {
  width: 20%;
  margin: 0;
}

c-rm-purchases .secondary-info {
  font-size: 0.8em;
  margin-top: 1em;
}

c-rm-purchases .secondary-info .row-item {
  margin-top: 0.5em;
}

c-rm-purchases .subtext {
  font-size: 0.8em;
}

c-rm-purchases .secondary-info .row-item label {
  font-size: 0.85em;
  font-weight: 700;
}

c-rm-purchases .purchased {
  background: var(--accent-green-darker);
  color: white;
  font-weight: 700;
  padding: 0.25em 0.5em;
  border-radius: 0.25em;
  font-size: 0.7em;
  margin-top: 0.5em;
}

c-rm-purchases .verifying {
  background: orange;
  color: white;
  font-weight: 700;
  padding: 0.25em 0.5em;
  border-radius: 0.25em;
  font-size: 0.7em;
  margin-top: 0.5em;
}

c-rm-purchases .show-on-mobile {
  display: none;
}

c-rm-purchases .show-on-desktop {
  display: block;
}

@media only screen and (max-width: 768px) {
  c-rm-purchases .show-on-mobile {
    display: flex;
  }

  c-rm-purchases .show-on-desktop {
    display: none;
  }
}
