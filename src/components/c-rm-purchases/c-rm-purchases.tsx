import { Component, State, Listen, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-purchases",
  styleUrl: "c-rm-purchases.css",
})
export class CRmPurchases {
  @State() isFetching: boolean = true;
  @State() purchasesArr: any;
  private monthName: string = "";
  private totalPurchases: number = 0;

  @Listen("dropdown-input-event")
  filterHandler(event) {
    this.monthName = event.detail.value;
    this.isFetching = true;
    this.fetchData();
  }

  @Listen("download-purchase-data")
  downloadPurchaseDataHandler() {
    window.open(`${state.baseUrl}/downloadpurchases/${this.monthName}`);
  }

  componentWillLoad() {
    this.fetchData();
    state.isMobileDashboardOptionsVisible = false;
  }

  fetchData() {
    let payload = {
      monthName: this.monthName,
      eventCode: state.eventCodeForMonitoring,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/purchases-summary-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.purchasesArr = "";
          // this.purchasesArr = response.data.payload.purchases;
          // this.totalPurchases = response.data.payload.total;
          this.purchasesArr = response.data.payload;
          this.purchasesArr = [...this.purchasesArr];
          this.totalPurchases = response.data.payload.length;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div>
        {state.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile">
            <c-control-bar
              count={!this.isFetching && this.totalPurchases}
              name="purchases"
            ></c-control-bar>
          </div>
        )}
        <div class="show-on-desktop">
          <c-control-bar
            count={!this.isFetching && this.totalPurchases}
            name="purchases"
          ></c-control-bar>
        </div>

        <div class="purchase-list-container">
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div>
              <c-text type="sectionDivider">
                All sales{" "}
                {state.eventNameForMonitoring &&
                  `of ${state.eventNameForMonitoring}`}
              </c-text>{" "}
              {this.purchasesArr.map((item: any) => (
                <c-payment-summary-items
                  name={item.name}
                  email={item.email}
                  currency={item.currency}
                  purchase-date={item.purchaseDate}
                  purchase-status={item.paymentStatus}
                  purchased-items={JSON.stringify(item.purchasedItems)}
                  cart-total={item.cartTotal}
                  gateway-fee={item.gatewayFee}
                  grand-total={item.grandTotal}
                  transaction-id={item.transactionId}
                  payment-method={item.paymentMethod}
                  purchase-id={item.purchaseId}
                  is-coupon-applied={item.isCouponApplied}
                  coupon-name={item.couponName}
                  coupon-deduction-type={item.couponDeductionType}
                  coupon-deduction-value={item.couponDeductionValue}
                  cart-amount-before-deduction={item.cartAmountBeforeDeduction}
                  deducted-amount={item.deductedAmount}
                  cart-amount-after-deduction={item.cartAmountAfterDeduction}
                ></c-payment-summary-items>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }
}
