import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-text",
  styleUrl: "c-text.css",
})
export class CText {
  @Prop() type: string;
  @Prop() theme: string;
  @Prop() isMandatory: boolean = false;
  @Prop() iconName: string;

  render() {
    if (this.type === "sectionDivider") {
      return (
        <h1 class="section-divider">
          <slot />
        </h1>
      );
    } else if (this.type === "cardHeader") {
      return (
        <h1 class="card-header">
          <slot />
        </h1>
      );
    } else if (this.type === "heading") {
      return (
        <h1 class="heading">
          <slot />
        </h1>
      );
    } else if (this.type === "subtext") {
      return (
        <p class="subtext">
          <slot />
        </p>
      );
    } else if (this.type === "subtextWithIcon") {
      return (
        <div class="subtext-with-icon">
          <ion-icon name={this.iconName}></ion-icon>
          <p class="subtext ">
            <slot />
          </p>
        </div>
      );
    } else if (this.type === "small") {
      return (
        <p class="small">
          <slot />
        </p>
      );
    } else if (this.type === "buttonLabel") {
      return (
        <span>
          <slot />
        </span>
      );
    } else if (this.type === "errorHeading") {
      return (
        <p class="error-heading">
          <slot />
        </p>
      );
    } else if (this.type === "cardHeading") {
      return (
        <p class="card-heading">
          <slot />
        </p>
      );
    } else if (this.type === "cardSubSectionHeading") {
      return (
        <p class="card-subsection-heading">
          <slot />
        </p>
      );
    } else if (this.type === "wizardHeading") {
      return (
        <h3 class="wizard-heading">
          <slot />
        </h3>
      );
    } else if (this.type === "wizardSubHeading") {
      return (
        <p class="wizard-sub-heading">
          <slot />
        </p>
      );
    } else if (this.type === "modalLabel") {
      return (
        <p class="modal-label">
          <slot /> {this.isMandatory && <span class="mandatory">*</span>}
        </p>
      );
    } else if (this.type === "warning") {
      return (
        <p class="warning">
          <slot />
        </p>
      );
    } else if (this.type === "danger") {
      return (
        <p class="danger">
          <slot />
        </p>
      );
    } else if (this.type === "navLabel") {
      return (
        <p class="nav-label">
          <slot />
        </p>
      );
    } else {
      return (
        <p>
          <slot />
        </p>
      );
    }
  }
}
