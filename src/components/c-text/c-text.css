c-text * {
  margin: 0;
  padding: 0;
}

c-text .heading {
  color: var(--accent-color);
  font-weight: 600;
}

c-text .card-header {
  color: var(--accent-color);
  font-size: 1.25em;
}

c-text .section-divider {
  color: rgba(0, 0, 0, 0.3);
  font-weight: 400;
  /* padding-bottom: 0.25em; */
  margin-bottom: 1em;
  font-size: 1.25em;
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
  /* margin-top: 3em; */
}

c-text .subtext {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.67);
  margin-top: 0.5em;
}

c-text .small {
  font-size: 0.9em;
}

c-text .error-heading {
  font-weight: 700;
  color: var(--red-500);
}

c-text .card-heading {
  font-weight: 700;
  color: var(--violet-400);
}

c-text .card-subsection-heading {
  font-weight: 700;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.8em;
}

c-text .wizard-heading {
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin: 0;
}

c-text .wizard-sub-heading {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.4);
}

c-text .modal-label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
  margin: 0;
}

c-text .modal-label .modal-label-light {
  font-weight: 400;
}

c-text .mandatory {
  color: red;
}

c-text .warning {
  padding: 0.5em 1em;
  border-radius: 0.25em;
  background: var(--orange-50);
  color: var(--orange-600);
  font-size: 0.9em;
}

c-text .danger {
  padding: 0.5em 1em;
  border-radius: 0.25em;
  background: var(--red-50);
  color: var(--red-600);
  font-size: 0.9em;
}

c-text .subtext-with-icon {
  display: flex;
  align-items: center;
  margin-top: 0.25em;
}

c-text .subtext-with-icon p {
  margin: 0;
  padding: 0;
  margin-left: 0.5em;
}

c-text .nav-label {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 0.25em;
}
