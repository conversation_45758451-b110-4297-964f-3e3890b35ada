c-payment-summary-items .container {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  margin-bottom: 2.5em;
  font-size: 1em;
}

c-payment-summary-items header {
  margin: 0 auto;
  /* background: rgba(238, 234, 245, 0.5); */
  border-radius: 0.25em 0.25em 0em 0em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: black;
}

c-payment-summary-items header .row {
  margin: 0 auto;
  padding: 1em;
}

c-payment-summary-items header .row-item-1 {
  width: 40%;
  /* background: pink; */
}
c-payment-summary-items header .row-item-2 {
  width: 20%;
  /* background: yellow; */
}

c-payment-summary-items header .row-item-3 {
  width: 15%;
  /* background: cyan; */
}

c-payment-summary-items header .row-item-4 {
  width: 17.5%;
  /* background: cyan; */
}

c-payment-summary-items .row {
  display: flex;
}

c-payment-summary-items .row-item-1 {
  width: 60%;
}
c-payment-summary-items .row-item-2 {
  width: 38%;
}

c-payment-summary-items .label-value {
  opacity: 0.8;
  /* color: var(--accent-color); */
}

c-payment-summary-items .label-value-bubble {
  padding: 0.15em 0.5em;
  font-size: 0.7em;
  border-radius: 0.25em;
}

c-payment-summary-items .paid-item-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  width: 50%;
  margin-left: 2.5%;
}

c-payment-summary-items main .paid-item-list {
  width: 55%;
}

c-payment-summary-items .item-price {
  font-size: 0.9em;
  text-align: right;
  width: 50px;
}

c-payment-summary-items .main-title {
  font-size: 0.9em;
}

c-payment-summary-items .paid-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1em;
}

c-payment-summary-items .paid-item p {
  margin: 0;
  padding: 0;
}

c-payment-summary-items .paid-item .title {
  width: 75%;
}

c-payment-summary-items .paid-item .sub-title {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.8em;
}

c-payment-summary-items main {
  width: 90.5%;
  display: flex;
  justify-content: space-between;
  border-radius: 0.25em;
  padding-top: 1em;
  margin-left: 0;
  padding-right: 1em;
}

c-payment-summary-items .button-container {
  width: 15%;
}

c-payment-summary-items .btn-show-details {
  color: var(--accent-color);
  transition: all 0.15s ease-in;
}

c-payment-summary-items .btn-show-details:hover {
  background: var(--accent-color-bg-lightest);
  transition: all 0.15s ease-in;
}

c-payment-summary-items .btn-hide-details {
  color: rgba(231, 76, 60, 1);
  transition: all 0.15s ease-in;
  padding: 0.5em 1.5em;
}

c-payment-summary-items .btn-hide-details:hover {
  background: rgba(231, 76, 60, 0.1);
  transition: all 0.15s ease-in;
}

c-payment-summary-items .seperator {
  width: 100%;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.15);
  margin-bottom: 1em;
}

c-payment-summary-items .payment-summary {
  margin: 0;
}

c-payment-summary-items .payment-summary-value {
  padding-bottom: 1em;
  color: #333;
}

c-payment-summary-items .payment-summary-row {
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  font-size: 0.9em;
  padding: 1em;
}

c-payment-summary-items .show {
  display: block;
}

c-payment-summary-items .hide {
  display: none;
}

c-payment-summary-items .header-skel {
  padding: 1.5em 0;
}

c-payment-summary-items .main-skel {
  padding-top: 1em;
  padding-bottom: 3em;
}

c-payment-summary-items .purchased {
  background: var(--accent-green-darker);
  color: white;
}

c-payment-summary-items .under-verification {
  background: #ff9800;
  color: white;
}

c-payment-summary-items .paid-item-list-row {
  width: 100%;
  justify-content: space-between;
}

c-payment-summary-items .white-label {
  color: rgba(0, 0, 0, 0.4);
  font-size: 0.7em;
  font-weight: 700;
}

c-payment-summary-items .grey-label {
  color: rgba(0, 0, 0, 0.4);
  font-size: 0.75em;
  font-weight: 700;
}

c-payment-summary-items .header-row-items {
  margin: 0;
}

c-payment-summary-items .hseperator {
  margin-top: 1em;
  margin-bottom: 1em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

c-payment-summary-items .hseperator-no-bottom-margin {
  margin-bottom: 0;
}

c-payment-summary-items .hseperator-no-top-margin {
  margin-top: 0;
}

c-payment-summary-items .red {
  color: red;
}

c-payment-summary-items .discount-statement {
  color: rgba(0, 0, 0, 0.6);
}

c-payment-summary-items .discount-applied-bubble {
  margin: 0;
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.15em 0.5em;
  border-radius: 0.25em;
  /* background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker); */
  color: white;
  background: var(--accent-blue-darker);
}

c-payment-summary-items c-btn button {
  width: 80px;
  font-size: 0.9em;
  text-align: center;
}
