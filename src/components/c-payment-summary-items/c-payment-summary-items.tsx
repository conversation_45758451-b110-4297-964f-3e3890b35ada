import { Component, State, Listen, h, Prop } from "@stencil/core";

@Component({
  tag: "c-payment-summary-items",
  styleUrl: "c-payment-summary-items.css",
})
export class CPaymentSummaryItems {
  @Listen("button-click")
  buttonClickHandler(event) {
    if (event.detail.name === "expandPurchaseSummary") {
      if (this.isShowingDetails === true) {
        this.expandButtonLabel = "Expand";
        this.isShowingDetails = false;
      } else {
        this.expandButtonLabel = "Hide";
        this.isShowingDetails = true;
      }
    }
  }

  @State() isShowingDetails: boolean = false;
  @State() expandButtonLabel: string = "Expand";
  @Prop() name: string;
  @Prop() email: string;
  @Prop() currency: string;
  @Prop() purchaseDate: string;
  private purchasedDateAdjusted: string;
  @Prop() purchaseStatus: string;
  @Prop() purchasedItems: string;
  private purchasedItemsArr = [];

  @Prop() isCouponApplied: boolean = false;
  @Prop() couponName: string = "";
  @Prop() couponDeductionType: string = "";
  @Prop() couponDeductionValue: number = 0;
  @Prop() cartAmountBeforeDeduction: number = 0;
  @Prop() deductedAmount: number = 0;
  @Prop() cartAmountAfterDeduction: number = 0;

  @Prop() cartTotal: string;
  @Prop() gatewayFee: string;
  @Prop() grandTotal: string;

  @Prop() transactionId: string;
  @Prop() paymentMethod: string;
  @Prop() purchaseId: string;

  private discountStatement: string = "";

  componentWillLoad() {
    this.purchasedItemsArr = JSON.parse(this.purchasedItems);
    let buffDate = new Date(this.purchaseDate);
    this.purchasedDateAdjusted = buffDate.toString().substring(4, 15);
    if (this.couponDeductionType === "fixed") {
      this.discountStatement = `${this.currency}${this.couponDeductionValue} off on above cart total`;
    } else if (this.couponDeductionType === "percentage") {
      this.discountStatement = `${this.couponDeductionValue}% of above cart total`;
    }
  }

  render() {
    return (
      <div class="container">
        <header>
          <div class="row">
            <p class="header-row-items row-item-1">
              <strong>
                <span class="label-value">{this.name}</span>
              </strong>
              <br />
              <c-text-link
                url={`mailto:${this.email}`}
                label={`${this.email}`}
              ></c-text-link>
            </p>
            <p class="header-row-items row-item-2">
              <label class="white-label">PURCHASED ON</label> <br />{" "}
              <span class="label-value">{this.purchasedDateAdjusted}</span>
            </p>
            <p class="header-row-items row-item-3">
              <label class="white-label">PAID</label> <br />{" "}
              <span class="label-value">
                {this.currency}
                {this.grandTotal}
              </span>
            </p>
            <p class="header-row-items row-item-4">
              <label class="white-label">STATUS</label> <br />
              <span
                class={
                  this.purchaseStatus === "purchased"
                    ? "label-value-bubble purchased"
                    : "label-value-bubble under-verification"
                }
              >
                <strong>
                  {this.purchaseStatus === "under-verification"
                    ? "VERIFYING"
                    : ""}
                  {this.purchaseStatus === "purchased" ? "PAID" : ""}
                </strong>
              </span>
              &nbsp;&nbsp;
              <br />
              {this.isCouponApplied && (
                <span class="discount-applied-bubble">DISCOUNTED</span>
              )}
            </p>
          </div>
        </header>
        <main>
          <div class="paid-item-list">
            {this.purchasedItemsArr.map((item: any) => (
              <div class="paid-item">
                <p class="title">
                  <span class="main-title">{item.title}</span>
                  <br />
                  <span class="sub-title">{item.tier && `${item.tier}`}</span>
                </p>
                <p class="item-price">
                  {this.currency}
                  {item.price}
                </p>
              </div>
            ))}
          </div>
          <c-btn
            name="expandPurchaseSummary"
            label={this.expandButtonLabel}
            action-label=""
            value=""
            type="ghost"
            is-in-action={false}
            is-disabled={false}
          ></c-btn>
          {/* <div class="button-container">
            <button
              class={
                this.isShowingDetails ? "btn-hide-details" : "btn-show-details"
              }
              onClick={(event) => this.handleBtnClick(event)}
            >
              {this.isShowingDetails ? "-" : "+"}
            </button>
          </div> */}
        </main>
        <footer class={this.isShowingDetails ? "show" : "hide"}>
          <div class="hseperator hseperator-no-top-margin"></div>
          {this.isCouponApplied ? (
            <ul class="paid-item-list summary-list">
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Cart Total Before Discount</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.cartAmountBeforeDeduction}
                  </span>
                </p>
              </li>
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">
                    Discount{" "}
                    {this.discountStatement && `(${this.discountStatement})`}
                  </span>
                  <br />
                  <span class="main-title discount-statement">
                    {this.couponName}
                  </span>
                </p>
                <p class="red">
                  -
                  <span class="item-price">
                    {this.currency}
                    {this.deductedAmount}
                  </span>
                </p>
              </li>
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Cart Total After Discount</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.cartAmountAfterDeduction}
                  </span>
                </p>
              </li>
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Gateway Fee</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.gatewayFee}
                  </span>
                </p>
              </li>
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Grand Total</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.grandTotal}
                  </span>
                </p>
              </li>
            </ul>
          ) : (
            <ul class="paid-item-list summary-list">
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Cart Total</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.cartTotal}
                  </span>
                </p>
              </li>
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Gateway Fee</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.gatewayFee}
                  </span>
                </p>
              </li>
              <li class="paid-item">
                <p class="title">
                  <span class="main-title">Grand Total</span>
                </p>
                <p>
                  <span class="item-price">
                    {this.currency}
                    {this.grandTotal}
                  </span>
                </p>
              </li>
            </ul>
          )}
          <div class="hseperator hseperator-no-bottom-margin"></div>
          <div class="row payment-summary-row">
            <p class="payment-summary">
              <label class="grey-label">
                PAYMENT METHOD & TRANSACTION CODE
              </label>{" "}
              <br />{" "}
              <span class="label-value payment-summary-value">
                {this.paymentMethod}, Transaction code: {this.transactionId}
              </span>
            </p>
            {/* <p class="payment-summary">
              <label class="grey-label">TRANSACTION ID</label> <br />{" "}
              <span class="label-value payment-summary-value">
                {this.transactionId}
              </span>
            </p> */}
            <p class="payment-summary">
              <label class="grey-label">ORDER ID</label> <br />
              <span class="label-value payment-summary-value">
                {this.purchaseId}
              </span>
            </p>
          </div>
        </footer>
      </div>
    );
  }
}
