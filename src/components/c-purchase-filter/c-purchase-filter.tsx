import { Component, State, Prop, h } from "@stencil/core";

@Component({
  tag: "c-purchase-filter",
  styleUrl: "c-purchase-filter.css",
})
export class CPurchaseFilter {
  @State() isFetching: boolean = true;
  @State() isDownloadBtnDisabled: boolean = false;
  @Prop() purchaseCount: string;
  @Prop() btnLabel: string;

  private isDownloading: boolean = false;
  private monthOptions = [
    {
      label: "Month",
      value: "",
    },
    {
      label: "March",
      value: "March",
    },
    {
      label: "April",
      value: "April",
    },
    {
      label: "May",
      value: "May",
    },
    {
      label: "June",
      value: "June",
    },
    {
      label: "July",
      value: "July",
    },
    {
      label: "August",
      value: "August",
    },
    {
      label: "September",
      value: "September",
    },
    {
      label: "October",
      value: "October",
    },
    {
      label: "November",
      value: "November",
    },
    {
      label: "December",
      value: "December",
    },
    {
      label: "January",
      value: "January",
    },
    {
      label: "February",
      value: "February",
    },
  ];

  render() {
    return (
      <div class="container">
        <div class="row">
          <c-dropdown
            name="purchaseMonthFilter"
            option-str={JSON.stringify(this.monthOptions)}
          ></c-dropdown>
          <c-text-link
            url={`/accesslist`}
            label="Generate access list →"
          ></c-text-link>
          <c-btn
            name="downloadPurchaseData"
            label={this.btnLabel}
            action-label=""
            is-in-action={this.isDownloading}
            is-disabled={
              parseInt(this.purchaseCount) > 0
                ? this.isDownloadBtnDisabled
                : true
            }
          ></c-btn>
        </div>
        <p class="filter-desc">
          Showing <strong>{this.purchaseCount}</strong> purchases
        </p>
      </div>
    );
  }
}
