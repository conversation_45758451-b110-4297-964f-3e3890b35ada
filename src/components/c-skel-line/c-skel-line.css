c-skel-line .skel-line {
  height: 10px;
  border-radius: 0.25em;
}

.color-accent {
  background: var(--accent-color-lighter);
  background-image: linear-gradient(
    90deg,
    var(--accent-color-light) 0px,
    var(--accent-color-lightest) 50%,
    var(--accent-color-light) 100%
  );
  background-size: 200%;
  animation: shine-line 1s infinite ease-out;
}

.color-accent-light {
  background: var(--accent-color-lighter);
  background-image: linear-gradient(
    90deg,
    var(--accent-color-bg-lighter) 0px,
    var(--accent-color-bg-lightest) 50%,
    var(--accent-color-bg-lighter) 100%
  );
  background-size: 200%;
  animation: shine-line 1s infinite ease-out;
}

.color-gray {
  background: #ccc;
  background-image: linear-gradient(
    90deg,
    #f4f4f4 0px,
    rgba(229, 229, 229, 0.8) 40%,
    #f4f4f4 100%
  );
  background-size: 200%;
  animation: shine-line 1s infinite ease-out;
}

@keyframes shine-line {
  0% {
    background-position: -100px;
  }
  40%,
  100% {
    background-position: 140px;
  }
}

.width-100 {
  width: 100%;
}

.width-75 {
  width: 75%;
}

.width-50 {
  width: 50%;
}

.width-25 {
  width: 25%;
}
