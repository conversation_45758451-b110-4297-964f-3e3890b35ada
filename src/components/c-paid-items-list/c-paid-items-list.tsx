import { Component, State, Prop, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-paid-items-list",
  styleUrl: "c-paid-items-list.css",
})
export class CPaidItemsList {
  @State() isFetching: boolean = true;
  @Prop() ownPurchases: boolean;
  private purchases: Array<object>;
  private hasPurchases: boolean = false;

  componentDidLoad() {
    if (this.ownPurchases) {
      axios({
        method: "GET",
        baseURL: `${state.baseUrl}/purchases-summary-v2`,
        withCredentials: true,
        responseType: "json",
      })
        .then((response) => {
          if (response.data.status === "Failed") {
            alert(response.data.msg);
          } else if (response.data.status === "Success") {
            this.isFetching = false;
            this.purchases = response.data.payload;
            if (this.purchases) this.hasPurchases = true;
            else this.hasPurchases = false;
          }
        })
        .catch((error) => {
          alert(error);
        });
    } else {
      let payload = {
        email: state.expandedUserEmail.toLowerCase(),
      };
      axios({
        method: "POST",
        baseURL: `${state.baseUrl}/getpurchases`,
        data: payload,
        withCredentials: true,
        responseType: "json",
      })
        .then((response) => {
          if (response.data.status === "Failed") {
            alert(response.data.msg);
          } else if (response.data.status === "Success") {
            this.isFetching = false;
            this.purchases = response.data.payload;
            if (this.purchases) this.hasPurchases = true;
            else this.hasPurchases = false;
          }
        })
        .catch((error) => {
          alert(error);
        });
    }
  }

  render() {
    return (
      <div>
        {this.isFetching === true ? (
          <div class="skel-container">
            <div class="header-skel">
              <c-skel-line color="gray" width={25}></c-skel-line>
            </div>
            <div class="main-skel">
              <c-skel-line color="gray" width={100}></c-skel-line>
              <br />
              <c-skel-line color="gray" width={75}></c-skel-line>
              <br />
              <c-skel-line color="gray" width={50}></c-skel-line>
            </div>
          </div>
        ) : this.hasPurchases ? (
          this.purchases.map((item: any) => (
            <c-paid-items
              currency={item.currency}
              purchase-date={item.purchaseDate}
              purchase-status={item.paymentStatus}
              purchased-items={JSON.stringify(item.purchasedItems)}
              cart-total={item.cartTotal}
              gateway-fee={item.gatewayFee}
              grand-total={item.grandTotal}
              transaction-id={item.transactionId}
              payment-method={item.paymentMethod}
              purchase-id={item.purchaseId}
              is-coupon-applied={item.isCouponApplied}
              coupon-name={item.couponName}
              coupon-deduction-type={item.couponDeductionType}
              coupon-deduction-value={item.couponDeductionValue}
              cart-amount-before-deduction={item.cartAmountBeforeDeduction}
              deducted-amount={item.deductedAmount}
              cart-amount-after-deduction={item.cartAmountAfterDeduction}
            ></c-paid-items>
          ))
        ) : (
          <p>No purchases found</p>
        )}
      </div>
    );
  }
}
