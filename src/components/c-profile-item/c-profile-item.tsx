import { Component, Prop, Listen, State, h } from "@stencil/core";
import Store from "../../global/store";

@Component({
  tag: "c-profile-item",
  styleUrl: "c-profile-item.css",
})
export class CProfileItem {
  @State() isEditing: boolean = false;
  @State() isInputboxDisabled: boolean = false;
  @State() newValue: string;
  @Prop() label: string;
  @Prop() value: string;
  @Prop() name: string;
  @Prop() isEditable: boolean;

  @Listen("profile-edit-btn-click")
  profileEditHandler() {
    this.isEditing = true;
  }

  @Listen("profile-edit-close")
  profileEditClose() {
    this.isEditing = false;
  }

  @Listen("profile-item-saved")
  profileItemSavedHandler() {
    this.isEditing = false;
    if (this.name === "name") {
      this.newValue = `${Store.getFirstName()} ${Store.getLastName()}`;
    } else if (this.name === "email") {
      this.newValue = `${Store.getEmail()}`;
    } else if (this.name === "password") {
      this.newValue = "********";
    } else if (this.name === "phone") {
      this.newValue = `+${Store.getIsdCode()} ${Store.getMobileNumber()}`;
    } else if (this.name === "country") {
      this.newValue = `${Store.getCountry()}`;
    } else if (this.name === "orginsti") {
      this.newValue = `${Store.getOrgInsti()}`;
    } else if (this.name === "jobdegree") {
      this.newValue = `${Store.getJobDegree()}`;
    }
  }

  componentWillLoad() {
    this.label = this.label.toUpperCase();
  }

  render() {
    return (
      <div>
        {this.isEditing && this.isEditable ? (
          <c-profile-item-edit name={this.name}></c-profile-item-edit>
        ) : (
          <div class="row">
            <p>
              {this.name === "name" ? (
                ""
              ) : (
                <div>
                  <span class="label">{this.label}</span>
                </div>
              )}
              <span class="value">
                {this.newValue ? this.newValue : this.value}
              </span>
            </p>
            {this.isEditable ? <c-edit-button></c-edit-button> : ""}
          </div>
        )}
      </div>
    );
  }
}
