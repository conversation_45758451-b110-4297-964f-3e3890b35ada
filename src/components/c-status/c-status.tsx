import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-status",
  styleUrl: "c-status.css",
})
export class CStatus {
  @Prop() type: string;
  render() {
    if (this.type === "success") {
      return (
        <div class="success">
          <slot />
        </div>
      );
    } else if (this.type === "danger") {
      return (
        <div class="danger">
          <slot />
        </div>
      );
    } else if (this.type === "disabled") {
      return (
        <div class="disabled">
          <slot />
        </div>
      );
    }
  }
}
