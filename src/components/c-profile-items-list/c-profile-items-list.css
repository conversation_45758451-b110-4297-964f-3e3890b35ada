c-profile-items-list .container {
  margin-bottom: 4em;
}

c-profile-items-list .first-container {
  display: flex;
  align-items: center;
  background: white;
  padding: 0em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  margin-bottom: 4em;
  width: 100%;
  box-sizing: border-box;
}

c-profile-items-list .profile-items-container {
  width: 100%;
  margin: 0 auto;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  padding: 1em 0em;
}

c-profile-items-list .dp {
  width: 85px;
  height: 85px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 100%;
  border: 2px solid white;
  margin-right: 2em;
}

c-profile-items-list h2 {
  color: rgba(0, 0, 0, 0.3);
  margin-bottom: 0.5em;
  margin-top: 0.5em;
}

c-profile-items-list .name {
  margin-bottom: 0;
  margin-right: 1em;
}

c-profile-items-list .email {
  margin-top: 0;
}

c-profile-items-list .name-container c-profile-item .row {
  width: 100%;
  font-size: 1em;
  border-bottom: 0;
  margin: 0;
  padding: 0;
  font-size: 2em;
  font-weight: 700;
}

c-profile-items-list .dp-initial {
  background: var(--accent-color-bg-lightest);
  padding: 0.5em 0.6em;
  border-radius: 100%;
  font-weight: 400;
  color: var(--accent-color-lighter);
  margin-right: 1em;
}

c-profile-items-list .dp-container {
  padding: 1em 0.6em;
}

c-profile-items-list .profile-item-seperator {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 1em;
  margin-bottom: 1em;
}

c-profile-items-list .membership-details-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 1em;
}

c-profile-items-list .membership-detail {
  width: 40%;
  font-size: 0.9em;
  margin: 0;
}

c-profile-items-list .membership-detail .label {
  font-weight: 400;
  font-size: 0.8em;
  margin-bottom: 1em;
  color: rgba(0, 0, 0, 0.4);
}

c-profile-items-list .label {
  font-weight: bold;
  font-size: 0.8em;
}

c-profile-items-list .checkout-btn-container c-btn button {
  /* background: rgba(238, 234, 245, 0.5);
  margin: 0 auto; */
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 1em;
  padding-bottom: 1em;
}

c-profile-items-list c-section-buy-membership .container {
  margin-bottom: 0;
}

c-profile-items-list c-section-buy-membership .seperator {
  margin: 0;
}

c-profile-items-list .header-membership-purchase-container {
  margin-bottom: 4em;
}
c-profile-items-list .header-membership-purchase-container .container {
  margin-top: 0em;
}
c-profile-items-list .section-header {
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin-bottom: 0.5em;
  font-size: 1.5em;
}

c-profile-items-list c-profile-item .row {
  width: 92.5%;
  margin: 0em auto;
}

c-profile-items-list .name-container .value {
  font-size: 0.9em;
  font-weight: 400;
}

c-profile-items-list .name-container {
  width: 100%;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-profile-items-list main {
    width: 100%;
  }

  c-profile-items-list .section-header {
    font-size: 1em;
    margin: 0;
    margin-bottom: 1em;
  }

  c-profile-items-list .container {
    margin-bottom: 1.5em;
  }

  c-profile-items-list .first-container {
    margin-bottom: 1.5em;
  }

  c-profile-items-list .dp-initial {
    font-size: 0.9em;
  }

  c-profile-items-list .name-container .value {
    font-size: 0.5em;
    font-weight: 400;
  }

  c-profile-items-list .name-container c-profile-item .row {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-top: -0.4em;
  }
}
