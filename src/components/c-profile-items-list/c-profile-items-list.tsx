import { Component, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";
import Store from "../../global/store";

@Component({
  tag: "c-profile-items-list",
  styleUrl: "c-profile-items-list.css",
})
export class CProfileItemsList {
  @State() isFetchingData: boolean = true;

  private isMember: boolean = false;
  private membershipID: string;
  private membershipType: string;
  private membershipStartDate: string;
  private membershipEndDate: string;
  private name: string;

  componentDidLoad() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/profile`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Failed to fetch data");
        } else if (response.data.status === "Success") {
          let payload = response.data.payload;
          Store.setFirstName(payload.name.first);
          Store.setLastName(payload.name.last);
          state.firstName = payload.name.first;
          state.lastName = payload.name.last;
          state.isRegistrationManager = payload.isRegistrationManager;
          state.isAdmin = payload.isAdmin;
          Store.setEmail(payload.email.toLowerCase());
          Store.setCountry(payload.country);
          Store.setIsdCode(payload.mobile.isdCode);
          Store.setMobileCountry(payload.mobile.country);
          Store.setMobileNumber(payload.mobile.number);
          Store.setOccupation(payload.professional.occupation);
          Store.setOrgInsti(payload.professional.orgInsti);
          Store.setJobDegree(payload.professional.jobDegree);
          Store.setGSTInvoicePreference(payload.billing.isGSTInvoicePreferred);
          Store.setBusinessName(payload.billing.businessName);
          Store.setTaxID(payload.billing.taxID);
          Store.setTaxJurisdiction(payload.billing.taxJurisdiction);
          Store.setBillingAddressLine1(payload.billing.billingAddressLine1);
          Store.setBillingAddressLine2(payload.billing.billingAddressLine2);
          Store.setBillingAddressLine3(payload.billing.billingAddressLine3);
          this.isMember = payload.membership.isMember;
          this.membershipID = payload.membership.id;
          if (payload.membership.type === "lifetime") {
            this.membershipType = "Lifetime Membership";
          } else if (payload.membership.type === "annual") {
            this.membershipType = "Annual Membership";
          }
          this.name = `${Store.getFirstName()} ${Store.getLastName()}`;
          this.membershipStartDate = new Date(payload.membership.startDate)
            .toString()
            .substring(4, 15);
          this.membershipEndDate = payload.membership.endDate;
          if (this.membershipEndDate) {
            this.membershipEndDate = new Date(payload.membership.endDate)
              .toString()
              .substring(4, 15);
          }
          this.isFetchingData = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <main>
        {this.isFetchingData ? (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : (
          <div>
            {/* {this.isMember ? (
              ""
            ) : (
              <div class="header-membership-purchase-container">
                <c-section-buy-membership></c-section-buy-membership>
                <div class="checkout-btn-container">
                  {state.isCheckoutBtnDisabled ? (
                    ""
                  ) : (
                    <c-btn
                      name="checkout"
                      label="Proceed to Checkout"
                      action-label="Checking.."
                      is-in-action={this.isChecking}
                      is-disabled={false}
                    ></c-btn>
                  )}
                </div>
              </div>
            )} */}
            <div class="first-container">
              {/* {state.dpUrl.length > 0 ? (
                <div class="dp-container">
                  <c-img type="oauthDp" src={state.dpUrl}></c-img>
                </div>
              ) : (
                <h1 class="dp-initial">
                  {Store.getFirstName()[0]}
                  {Store.getLastName()[0]}
                </h1>
              )} */}

              <h1 class="dp-initial">
                {Store.getFirstName()[0]}
                {Store.getLastName()[0]}
              </h1>

              <div class="name-container">
                <c-profile-item
                  label=""
                  name="name"
                  is-editable={true}
                  value={this.name}
                ></c-profile-item>
              </div>
            </div>
            <div class="container">
              <h1 class="section-header">Account Info</h1>
              <div class="profile-items-container">
                <c-profile-item
                  label="Email"
                  name="email"
                  value={Store.getEmail()}
                  is-editable={true}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Password"
                  name="password"
                  value="********"
                  is-editable={true}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Mobile No"
                  name="phone"
                  value={
                    Store.getIsdCode().length > 0 &&
                    Store.getMobileNumber().length > 0
                      ? `+${Store.getIsdCode()} - ${Store.getMobileNumber()}`
                      : "-"
                  }
                  is-editable={true}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Country"
                  name="country"
                  value={
                    Store.getCountry().length > 0 ? Store.getCountry() : "-"
                  }
                  is-editable={true}
                ></c-profile-item>
              </div>
            </div>
            <div class="container">
              <h1 class="section-header">Professional Info</h1>
              <div class="profile-items-container">
                <c-profile-item
                  label="Occupation"
                  name="occupation"
                  is-editable={false}
                  value={Store.getOccupation()}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Organisation"
                  name="orginsti"
                  value={
                    Store.getOrgInsti().length > 0 ? Store.getOrgInsti() : "-"
                  }
                  is-editable={true}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Designation"
                  name="jobdegree"
                  value={
                    Store.getJobDegree().length > 0 ? Store.getJobDegree() : "-"
                  }
                  is-editable={true}
                ></c-profile-item>
              </div>
            </div>
            <div class="container">
              <h1 class="section-header">Billing Info</h1>
              <div class="profile-items-container">
                <c-profile-item
                  label="GST Status"
                  name="taxInvoicePref"
                  is-editable={false}
                  value={
                    Store.getGSTInvoicePreference()
                      ? "Registered"
                      : "Not Registered"
                  }
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Business Name"
                  name="businessname"
                  value={
                    Store.getBusinessName().length > 0
                      ? Store.getBusinessName()
                      : "-"
                  }
                  is-editable={false}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="GSTIN"
                  name="taxID"
                  value={Store.getTaxID().length > 0 ? Store.getTaxID() : "-"}
                  is-editable={false}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Tax Jurisdiction"
                  name="taxJurisdiction"
                  value={Store.getTaxJurisdiction()}
                  is-editable={false}
                ></c-profile-item>
                <div class="profile-item-seperator"></div>
                <c-profile-item
                  label="Billing address"
                  name="billingAddress"
                  value={Store.getBillingAddress()}
                  is-editable={false}
                ></c-profile-item>
              </div>
            </div>
            <div class="container">
              <h1 class="section-header">Membership Info</h1>

              {this.isMember ? (
                <div class="profile-items-container">
                  <c-profile-item
                    label="MEMBER ID"
                    name=""
                    value={this.membershipID}
                    is-editable={false}
                  ></c-profile-item>
                  <div class="profile-item-seperator"></div>

                  <c-profile-item
                    label="MEMBER TYPE"
                    name=""
                    value={this.membershipType}
                    is-editable={false}
                  ></c-profile-item>
                  <div class="profile-item-seperator"></div>

                  <c-profile-item
                    label="START DATE"
                    name=""
                    value={this.membershipStartDate}
                    is-editable={false}
                  ></c-profile-item>
                  <div class="profile-item-seperator"></div>

                  <c-profile-item
                    label="END DATE"
                    name=""
                    value={
                      this.membershipEndDate ? this.membershipEndDate : "-"
                    }
                    is-editable={false}
                  ></c-profile-item>
                </div>
              ) : (
                <div>
                  <c-section-buy-membership></c-section-buy-membership>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    );
  }
}
