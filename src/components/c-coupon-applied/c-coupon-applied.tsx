import {
  Component,
  State,
  Event,
  EventEmitter,
  Listen,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-coupon-applied",
  styleUrl: "c-coupon-applied.css",
})
export class CCouponApplied {
  @Event({
    eventName: "get-cart",
    bubbles: true,
  })
  getCartEventEmitter: EventEmitter;

  @Listen("button-click")
  buttonClickHandler(event) {
    if (event.detail.name === "clearCoupon") {
      this.clearCoupon();
    }
  }

  @State() componentState: string = "init";

  clearCoupon() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/clearcoupon`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.componentState = "failed";
        } else if (response.data.status === "Success") {
          this.getCartEventEmitter.emit();
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="coupon-applied-container">
        {this.componentState === "init" && (
          <div>
            <p class="copy coupon-title">
              <strong>{state.appliedCouponName}</strong> coupon applied
            </p>
            <c-btn
              name="clearCoupon"
              value=""
              type="ghost"
              label="Clear Coupon"
              action-label=""
              is-in-action={false}
              is-disabled={false}
            ></c-btn>
          </div>
        )}

        {this.componentState === "failed" && (
          <p class="copy failed-message">Invalid coupon code</p>
        )}
      </div>
    );
  }
}
