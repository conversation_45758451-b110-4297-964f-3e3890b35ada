import {
  Component,
  Event,
  EventEmitter,
  Prop,
  State,
  Listen,
  h,
} from "@stencil/core";
import {
  isProfileInfoStringValid,
  isEmailValid,
  isNameValid,
  isPasswordConfirmationInputsValid,
  isMobileValid,
  isCountryValid,
} from "../../utils/";
import { countries } from "../../global/app";
import Store from "../../global/store";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-profile-item-edit",
  styleUrl: "c-profile-item-edit.css",
})
export class CProfileItemEdit {
  @Prop() name: string;
  @State() isInputboxDisabled: boolean = false;
  @State() isPhoneInputDisabled: boolean = false;
  @State() isSaveBtnDisabled: boolean = true;
  @State() isCancelBtnDisabled: boolean = false;

  private isDropDownDisabled: boolean = false;
  private label: string;
  private isSingleRow: boolean = false;
  private isSaving: boolean = false;
  private scrollY: any = window.scrollY;

  @Event({
    eventName: "profile-item-saved",
    bubbles: true,
  })
  profileItemSaved: EventEmitter;

  @Listen("input-event")
  inputEventHandler() {
    this.changeComponentState("input-event");
  }

  @Listen("save-profile-edits")
  saveProfileEditsHandler() {
    this.isSaveBtnDisabled = true;
    this.isSaving = true;

    let profileItem: string = this.name;
    let newValueObj: object;

    if (this.name === "name") {
      newValueObj = {
        firstName: Store.getFirstName(),
        lastName: Store.getLastName(),
      };
    } else if (this.name === "email") {
      newValueObj = {
        email: Store.getEmail(),
      };
    } else if (this.name === "password") {
      newValueObj = {
        password: Store.getPassword(),
      };
    } else if (this.name === "phone") {
      newValueObj = {
        isdCode: Store.getIsdCode(),
        mobileCountry: Store.getMobileCountry(),
        mobileNumber: Store.getMobileNumber(),
      };
    } else if (this.name === "country") {
      newValueObj = {
        country: Store.getCountry(),
      };
    } else if (this.name === "orginsti") {
      newValueObj = {
        profileInfoString: Store.getOrgInsti(),
      };
    } else if (this.name === "jobdegree") {
      newValueObj = {
        profileInfoString: Store.getJobDegree(),
      };
    }

    let payload = {
      item: profileItem,
      value: newValueObj,
    };
    this.sendProfileUpdates(payload);
  }

  componentWillLoad() {
    if (this.name === "name") {
      this.label = "Edit Name";
    } else if (this.name === "email") {
      this.label = "Edit Email";
      this.isSingleRow = true;
    } else if (this.name === "password") {
      this.label = "Edit Password";
    } else if (this.name === "phone") {
      this.label = "Edit Mobile";
    } else if (this.name === "country") {
      this.label = "Edit Country";
      this.isSingleRow = true;
    } else if (this.name === "orginsti") {
      this.label = "Edit Organisation";
      this.isSingleRow = true;
    } else if (this.name === "jobdegree") {
      this.label = "Edit Designation";
      this.isSingleRow = true;
    }
  }

  handleDropDownInput(event, name) {
    if (name === "country") {
      if (event.target.value.trim() != "Choose Option") {
        Store.setCountry(event.target.value.trim());
        Store.setCurrency("inr");
      } else {
        Store.setCountry("");
      }
    }
    this.changeComponentState("dropdown-event");
  }

  handleValidationError(error) {
    if (error) {
      this.isSaveBtnDisabled = true;
    } else {
      this.isSaveBtnDisabled = false;
    }
  }

  sendProfileUpdates(payload) {
    axios({
      method: "PATCH",
      baseURL: `${state.baseUrl}/profile`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.changeComponentState("profile-update-failed");
        } else if (response.data.status === "Success") {
          this.changeComponentState("profile-update-success");
        }
      })
      .catch((error) => {
        alert(error);
        this.changeComponentState("profile-update-error");
      });
  }

  changeComponentState(lastEventName: string) {
    if (lastEventName === "input-event" || lastEventName === "dropdown-event") {
      if (this.name === "name") {
        let obj = {
          firstName: Store.getFirstName(),
          lastName: Store.getLastName(),
        };
        let { error } = isNameValid(obj);
        this.handleValidationError(error);
      } else if (this.name === "email") {
        let obj = {
          email: Store.getEmail(),
        };
        let { error } = isEmailValid(obj);
        this.handleValidationError(error);
      } else if (this.name === "password") {
        let obj = Store.getPasswordConfirmationDetails();
        let { error } = isPasswordConfirmationInputsValid(obj);
        this.handleValidationError(error);
      } else if (this.name === "phone") {
        let obj = {
          isdCode: Store.getIsdCode(),
          mobileCountry: Store.getMobileCountry(),
          mobileNumber: Store.getMobileNumber(),
        };
        let { error } = isMobileValid(obj);
        this.handleValidationError(error);
      } else if (this.name === "country") {
        let obj = {
          country: Store.getCountry(),
        };
        let { error } = isCountryValid(obj);
        this.handleValidationError(error);
      } else if (this.name === "orginsti") {
        let obj = {
          profileInfoString: Store.getOrgInsti(),
        };
        let { error } = isProfileInfoStringValid(obj);
        this.handleValidationError(error);
      } else if (this.name === "jobdegree") {
        let obj = {
          profileInfoString: Store.getJobDegree(),
        };
        let { error } = isProfileInfoStringValid(obj);
        this.handleValidationError(error);
      }
    } else if (
      lastEventName === "profile-update-failed" ||
      "profile-update-success" ||
      "profile-update-error"
    ) {
      this.isSaving = false;
      this.isInputboxDisabled = false;
      this.isSaveBtnDisabled = false;

      if (lastEventName === "profile-update-success") {
        this.profileItemSaved.emit({ profileItem: this.name });
      }
    }
  }

  render() {
    return (
      <div class="container" style={{ marginTop: `${this.scrollY + 200}px` }}>
        <div class="heading-row">
          <label>{this.label}</label>
        </div>
        <div
          class={this.isSingleRow ? "single-row content-row" : "content-row"}
        >
          {this.name === "name" ? (
            <div class="name-row">
              <c-inputbox
                type="text"
                name="firstName"
                placeholder="First Name"
                is-disabled={this.isInputboxDisabled}
                value={Store.getFirstName()}
              ></c-inputbox>
              <c-inputbox
                type="text"
                name="lastName"
                placeholder="Last Name"
                is-disabled={this.isInputboxDisabled}
                value={Store.getLastName()}
              ></c-inputbox>
            </div>
          ) : (
            ""
          )}
          {this.name === "email" ? (
            <div class="edit-inputs">
              <c-inputbox
                type="email"
                name="email"
                placeholder="Email"
                is-disabled={this.isInputboxDisabled}
                value={Store.getEmail()}
              ></c-inputbox>
            </div>
          ) : (
            ""
          )}

          {this.name === "password" ? (
            <div class="name-row">
              <c-inputbox
                class="name-box"
                type="password"
                name="password"
                placeholder="Password (min. 8 chars)"
                is-disabled={this.isInputboxDisabled}
              ></c-inputbox>
              <c-inputbox
                class="name-box"
                type="password"
                name="confirmpassword"
                placeholder="Re-type password"
                is-disabled={this.isInputboxDisabled}
              ></c-inputbox>
            </div>
          ) : (
            ""
          )}

          {this.name === "phone" ? (
            <div class="edit-inputs">
              <c-inputphone
                is-disabled={this.isPhoneInputDisabled}
                numpad-placeholder="Mobile no."
                dropdown-value={Store.getMobileCountry()}
                inputboxValue={Store.getMobileNumber()}
              ></c-inputphone>
            </div>
          ) : (
            ""
          )}

          {this.name === "country" ? (
            <div class="edit-inputs">
              <select
                onInput={(event) => this.handleDropDownInput(event, "country")}
                disabled={this.isDropDownDisabled}
              >
                <option value="">Your country</option>
                {countries.map((country) => (
                  <option
                    value={country.name}
                    disabled={country.isdCode.length === 0 ? true : false}
                  >
                    {country.name}
                  </option>
                ))}
              </select>
            </div>
          ) : (
            ""
          )}

          {this.name === "orginsti" ? (
            <div class="edit-inputs">
              <c-inputbox
                type="text"
                name="orgInsti"
                placeholder={
                  Store.getOccupation() === "student"
                    ? "Institute"
                    : "Company/Organisation"
                }
                is-disabled={this.isInputboxDisabled}
                value={Store.getOrgInsti()}
              ></c-inputbox>
            </div>
          ) : (
            ""
          )}

          {this.name === "jobdegree" ? (
            <div class="edit-inputs">
              <c-inputbox
                type="text"
                name="jobDegree"
                placeholder={
                  Store.getOccupation() === "student" ? "Degree" : "Job Title"
                }
                is-disabled={this.isInputboxDisabled}
                value={Store.getJobDegree()}
              ></c-inputbox>
            </div>
          ) : (
            ""
          )}
        </div>
        <div class="btn-row">
          <c-btn-light
            label="Cancel"
            name="closeProfileEdits"
            action-label=""
            is-in-action={false}
            is-disabled={this.isCancelBtnDisabled}
          ></c-btn-light>
          <c-btn
            label="Save"
            name="saveProfileEdits"
            action-label=""
            is-in-action={this.isSaving}
            is-disabled={this.isSaveBtnDisabled}
          ></c-btn>
        </div>
      </div>
    );
  }
}
