c-profile-item-edit .container {
  position: absolute;
  top: 0;
  left: 0;
  margin-left: 35%;
  border-radius: 0.25em;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05);
  z-index: 999999;
  padding-top: 0;
  margin-bottom: 0;
}

c-profile-item-edit select {
  border: 0;
  border: 1px solid rgba(0, 0, 0, 0.15);
  padding: 0.75em 0em 0.75em 0.75em;
  width: 100%;
  outline: none 0;
  border-radius: 0.25em;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 1em;
  margin-bottom: 1em;
  background: transparent;
  background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: 98%;
  background-position-y: 12px;
  transition: all 0.15s ease-in;
}

c-profile-item-edit option:checked {
  color: red;
}

c-profile-item-edit select::-ms-expand {
  display: none;
}

c-profile-item-edit select:hover {
  cursor: pointer;
  border: 1px solid var(--accent-color);
}

c-profile-item-edit .heading-row {
  width: 250px;
  padding: 1em 1em 0 1em;
  background: white;
  /* background: var(--accent-color-bg-lightest); */
  font-weight: 400;
  /* color: var(--accent-color); */
  color: rgba(0, 0, 0, 0.6);
  border-top-left-radius: 0.25em;
  border-top-right-radius: 0.25em;
}

c-profile-item-edit .content-row {
  width: 250px;
  padding: 1em;
  background: white;
  padding-bottom: 1em;
}

c-profile-item-edit .content-row c-inputphone .inputphone-container {
  padding-bottom: 1em;
}

c-profile-item-edit .btn-row {
  width: 250px;
  display: flex;
  justify-content: space-between;
  padding: 0em 1em 1em 1em;
  background: white;
  border-bottom-left-radius: 0.25em;
  border-bottom-right-radius: 0.25em;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-profile-item-edit .container {
    position: fixed;
    width: 90vw;
    z-index: 99999;
    margin: 0 auto;
    left: 0;
    right: 0;
  }

  c-profile-item-edit .heading-row {
    width: 100%;
    box-sizing: border-box;
  }

  c-profile-item-edit .content-row {
    width: 100%;
    box-sizing: border-box;
  }

  c-profile-item-edit .btn-row {
    width: 100%;
    box-sizing: border-box;
  }
}
