import { Component, Host, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-account-control",
  styleUrl: "c-account-control.css",
})
export class CAccountControl {
  render() {
    return (
      <Host>
        <div class="account-control-container hide-on-mobile">
          <c-button
            type="accountControl"
            name="goToProfile"
            icon-name="log-out-outline"
            label={state.firstName}
          ></c-button>
          <div class="vseperator"></div>
          <c-button
            type="accountControl"
            name="logoutUser"
            icon-name=""
            label="Logout"
          ></c-button>
        </div>
        <div class="show-on-mobile">
          <c-button
            type="accountControl"
            name="logoutUser"
            icon-name=""
            label="Logout"
          ></c-button>
        </div>
      </Host>
    );
  }
}
