c-account-control .account-control-container {
  display: flex;
  justify-content: space-between;
}

c-account-control .vseperator {
  border-left: 1px solid rgba(0, 0, 0, 0.08);
}

c-account-control .hide-on-mobile {
  display: flex;
}

c-account-control .show-on-mobile {
  display: none;
}

@media only screen and (max-width: 768px) {
  c-account-control .account-control-container {
    padding-top: 1em;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  c-account-control .hide-on-mobile {
    display: none;
  }

  c-account-control .show-on-mobile {
    display: block;
  }
}
