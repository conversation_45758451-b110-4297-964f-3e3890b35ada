import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-checkbox",
  styleUrl: "c-checkbox.css",
})
export class CCheckbox {
  @Prop() label: string;
  @Prop() name: string;
  @Prop() item: string;
  @Prop() isChecked: boolean = false;

  @Event({
    eventName: "checkbox-input-event",
    bubbles: true,
  })
  inputEvent: EventEmitter;

  handleChange(event) {
    this.inputEvent.emit({
      name: this.name,
      value: event.target.checked,
      item: this.item,
    });
  }
  render() {
    return (
      <div>
        <input
          type="checkbox"
          name={this.name}
          onChange={(event) => this.handleChange(event)}
          checked={this.isChecked}
        ></input>
        <label>{this.label}</label>
      </div>
    );
  }
}
