import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-ticket-eligibility-item",
  styleUrl: "c-ticket-eligibility-item.css",
})
export class CTicketEligibilityItem {
  @Event({
    eventName: "ticketEligibilityClicked",
    bubbles: true,
  })
  ticketEligibilityClicked: EventEmitter;

  @Prop() itemId: string;
  @Prop() itemDate: string;
  @Prop() itemDateRange: string;
  @Prop() isMultiDate: boolean;

  handleClick() {
    this.ticketEligibilityClicked.emit({
      itemId: this.itemId,
      itemDate: this.itemDate,
      itemDateRange: this.itemDateRange,
      isMultiDate: this.isMultiDate,
    });
  }

  render() {
    return (
      <div
        class="ticket-price-item__container"
        onClick={() => this.handleClick()}
      >
        <div class="ticket-price__subitem ticket-price__subitem--1">
          <c-text>{this.isMultiDate ? "Muti-day" : "One day"}</c-text>
        </div>
        <div class="ticket-price__subitem ticket-price__subitem--2">
          <c-text>
            {this.isMultiDate ? this.itemDateRange : this.itemDate}
          </c-text>
        </div>
      </div>
    );
  }
}
