c-rm-overview .container {
  width: 110%;
  margin-bottom: 5em;
}

c-rm-overview .overview-section-header {
  color: rgba(0, 0, 0, 0.4);
  font-size: 1.5em;
  margin-bottom: 0.25em;
  font-weight: 400;
}

c-rm-overview .vseperator {
  margin-left: 0.75em;
  margin-right: 0.75em;
}

c-rm-overview .row {
  display: flex;
}

c-rm-overview .card-header {
  background: rgba(238, 234, 245, 0.5);
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.75em 0.75em 0 0;
  font-size: 0.9em;
  font-weight: 700;
  color: var(--accent-color);
  display: flex;
  justify-content: space-between;
}

c-rm-overview .card-header p {
  margin-top: 0;
  margin-bottom: 0;
}

c-rm-overview .card-content {
  display: flex;
  justify-content: space-between;
  padding: 0em 1em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em;
  font-size: 0.9em;
}

c-rm-overview .overview-section {
  margin-bottom: 2.5em;
}

c-rm-overview .label-total-row {
  width: 100%;
  justify-content: space-between;
}

c-rm-overview .elem-2 {
  width: 60%;
  text-align: right;
}

c-rm-overview .elem-3 {
  width: 20%;
  text-align: left;
}

c-rm-overview .total-count-row {
  display: flex;
  justify-content: space-between;
}

c-rm-overview .wide-card {
  width: 350px;
}

c-rm-overview .card-footer {
  background: white;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em 0em 0.75em 0.75em;
  font-size: 0.9em;
  display: flex;
  justify-content: space-between;
}

c-rm-overview .card-footer p {
  margin: 0;
  padding: 0;
}

c-rm-overview .round-bottom {
  border-radius: 0 0 0.75em 0.75em;
}

c-rm-overview .card-subheading p {
  font-size: 0.7em;
  margin: 0;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
}

c-rm-overview .sale-count {
  color: rgba(0, 0, 0, 0.4);
}

c-rm-overview .no-footer-card {
  border-radius: 0 0 0.75em 0.75em;
}

c-rm-overview .overview-section:first-child c-text .section-divider {
  margin-top: 0;
}

c-rm-overview .row__primary-container {
  /* background: rgba(238, 234, 245, 0.5); */
  color: var(--accent-color);
  /* font-weight: 700; */
  /* padding: 1em;
  border-radius: 0.75em 0 0 0.75em;
  border: 1px solid rgba(0, 0, 0, 0.1); */
}

c-rm-overview .row__green-container {
  color: var(--accent-green-darker);
}

c-rm-overview .row__red-container {
  color: var(--red-600);
}

c-rm-overview .vertical-divider {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

c-rm-overview .overview-header {
  text-align: center;
}

c-rm-overview .overview-number {
  font-size: 1.5em;
}

c-rm-overview c-text .section-divider {
  border-bottom: 0;
  margin-bottom: 0.5em;
}

c-rm-overview c-card .basic-card-container {
  padding: 1em;
  border-radius: 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-rm-overview .sales-overview-header {
  width: 65%;
  padding: 1em;
  border-radius: 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
