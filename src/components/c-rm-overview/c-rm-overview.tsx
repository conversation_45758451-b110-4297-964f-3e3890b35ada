import { Component, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-overview",
  styleUrl: "c-rm-overview.css",
})
export class CRmOverview {
  @State() isFetching: boolean = true;
  private overviewDetails: any;

  componentDidLoad() {
    this.getOverviewData();
  }

  getOverviewData() {
    let payload = {
      eventCode: state.eventCodeForMonitoring,
      filterType: "eventCode",
      startDate: "",
      endDate: "",
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/overview-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.overviewDetails = response.data.payload;
          state.notificationCount = this.overviewDetails.notificationCount;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        {this.isFetching ? (
          ""
        ) : (
          <div class="account-overview">
            <c-text type="sectionDivider">Account overview </c-text>
            <c-card>
              <div class="overview-header">
                <c-row>
                  <div class="row__primary-container">
                    <e-text>
                      <ion-icon name="people-outline"></ion-icon>
                      &nbsp;&nbsp;Accounts
                      <br />
                      <span class="overview-number">
                        {this.overviewDetails.registrants.total}
                      </span>
                    </e-text>
                  </div>
                  <div class="vertical-divider"></div>
                  <e-text>
                    <ion-icon name="briefcase-outline"></ion-icon>
                    &nbsp;&nbsp;Professionals <br />
                    <span class="overview-number">
                      {" "}
                      {this.overviewDetails.registrants.professional}
                    </span>
                  </e-text>
                  <e-text>
                    <ion-icon name="school-outline"></ion-icon>
                    &nbsp;&nbsp;Students <br />
                    <span class="overview-number">
                      {" "}
                      {this.overviewDetails.registrants.student}
                    </span>
                  </e-text>
                  <e-text>
                    <ion-icon name="ribbon-outline"></ion-icon>
                    &nbsp;&nbsp;HCIPAI Members <br />
                    <span class="overview-number">
                      {" "}
                      {this.overviewDetails.registrants.member}
                    </span>
                  </e-text>
                </c-row>
              </div>
            </c-card>
          </div>
        )}
        {/* <section class="overview-section">
          {!this.isFetching && (
            <c-text type="sectionDivider">
              Total accounts & sales summary{" "}
              {state.eventNameForMonitoring &&
                `for ${state.eventNameForMonitoring}`}
            </c-text>
          )}
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div class="row">
              {this.isFetching ? (
                <div>
                  <c-skel-card></c-skel-card>
                </div>
              ) : (
                <div class="card">
                  <div class="card-header">
                    <div class="row label-total-row">
                      <p class="elem-1">Total Accounts</p>
                      <div>
                        <p class="elem-2">
                          {this.overviewDetails.registrants.total}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="card-subheading">
                    <p>BY PERSONA</p>
                  </div>
                  <div class="card-content">
                    <div>
                      <c-overview-info
                        highlight=""
                        label="Professionals"
                        count={this.overviewDetails.registrants.professional}
                        layout="label-count"
                      ></c-overview-info>
                      <c-overview-info
                        highlight=""
                        label="HCIPAI Members"
                        count={this.overviewDetails.registrants.member}
                        layout="label-count"
                      ></c-overview-info>
                      <c-overview-info
                        highlight=""
                        label="Students"
                        count={this.overviewDetails.registrants.student}
                        layout="label-count"
                      ></c-overview-info>
                    </div>
                  </div>
                  <div class="card-subheading">
                    <p>
                      BY{" "}
                      {state.eventNameForMonitoring.length > 0 &&
                        `${state.eventNameForMonitoring.toUpperCase()}`}{" "}
                      CONF. TICKET PURCHASE{" "}
                    </p>
                  </div>
                  <div class="card-content round-bottom">
                    <div>
                      <c-overview-info
                        highlight=""
                        label="With ticket"
                        count={this.overviewDetails.registrants.withPurchases}
                        layout="label-count"
                      ></c-overview-info>
                      <c-overview-info
                        highlight=""
                        label="Without ticket"
                        count={
                          this.overviewDetails.registrants.withoutPurchases
                        }
                        layout="label-count"
                      ></c-overview-info>
                    </div>
                  </div>
                </div>
              )}
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Total Sales</p>
                    <div>
                      <p class="elem-2">
                        ₹
                        {
                          this.overviewDetails.purchases.allPurchases.sales
                            .total
                        }
                      </p>
                    </div>
                  </div>
                </div>
                <div class="card-subheading">
                  <p>BY VERIFICATION</p>
                </div>
                <div class="card-content">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="Verified Sales"
                      count={
                        this.overviewDetails.purchases.allPurchases.sales
                          .confirmedCount
                      }
                      total={`₹${this.overviewDetails.purchases.allPurchases.sales.confirmed}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Unverified Sales"
                      count={
                        this.overviewDetails.purchases.allPurchases.sales
                          .unconfirmedCount
                      }
                      total={`₹${this.overviewDetails.purchases.allPurchases.sales.unconfirmed}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                  </div>
                </div>
                <div class="card-subheading">
                  <p>BY PAYMENT METHOD</p>
                </div>
                <div class="card-content round-bottom">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="Razorpay"
                      count={
                        this.overviewDetails.purchases.allPurchases.sales
                          .razorpayCount
                      }
                      total={`₹${this.overviewDetails.purchases.allPurchases.sales.razorpaySales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Bank transfer"
                      count={
                        this.overviewDetails.purchases.allPurchases.sales
                          .bankTransferCount
                      }
                      total={`₹${this.overviewDetails.purchases.allPurchases.sales.bankTransferSales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                  </div>
                </div>
              </div>
            </div>
          )}
        </section> */}
        <section class="overview-section">
          {!this.isFetching && (
            <c-text type="sectionDivider">
              Sales overview{" "}
              {state.eventNameForMonitoring &&
                `of ${state.eventNameForMonitoring}`}
            </c-text>
          )}

          {!this.isFetching && (
            <div class="overview-header sales-overview-header">
              <c-row>
                <div class="row__green-container">
                  <e-text>
                    <ion-icon name="ticket-outline"></ion-icon>
                    &nbsp;&nbsp;People with tickets
                    <br />
                    <span class="overview-number">
                      {this.overviewDetails.registrants.withPurchases}
                    </span>
                  </e-text>
                </div>
                <div class="vertical-divider"></div>
                <div class="row__red-container">
                  <e-text>
                    <ion-icon name="people-outline"></ion-icon>
                    &nbsp;&nbsp;People without tickets
                    <br />
                    <span class="overview-number">
                      {this.overviewDetails.registrants.withoutPurchases}
                    </span>
                  </e-text>
                </div>
                {/* <div class="vertical-divider"></div>
                  <div class="row__green-container">
                    <e-text>
                      <ion-icon name="ribbon-outline"></ion-icon>
                      &nbsp;&nbsp;Memberships sold
                      <br />
                      <span class="overview-number">
                        {this.overviewDetails.purchases.membership.count
                          .annual +
                          this.overviewDetails.purchases.membership.count
                            .lifetime}
                      </span>
                    </e-text>
                  </div> */}
              </c-row>
            </div>
          )}

          {/* {!this.isFetching && (
            <c-banner theme="danger">
              <strong>
                {this.overviewDetails.registrants.withoutPurchases} accounts
              </strong>{" "}
              are yet to purchase tickets for{" "}
              <u>
                {state.eventNameForMonitoring &&
                  `${state.eventNameForMonitoring}`}
              </u>
              <strong>
                {this.overviewDetails.registrants.withPurchases} out of{" "}
                {this.overviewDetails.registrants.total}
              </strong>{" "}
            </c-banner>
          )} */}
        </section>
        <section class="overview-section">
          {!this.isFetching && (
            <c-text type="sectionDivider">
              Detailed sales overview{" "}
              {state.eventNameForMonitoring &&
                `of ${state.eventNameForMonitoring}`}
            </c-text>
          )}

          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div class="row triple-column-card">
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Full conference tickets</p>
                  </div>
                </div>
                <div class="card-content">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="HCIPAI Members"
                      count={
                        this.overviewDetails.purchases.fullConference.count
                          .member
                      }
                      total={`₹${this.overviewDetails.purchases.fullConference.sales.member}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Professionals"
                      count={
                        this.overviewDetails.purchases.fullConference.count
                          .nonMember
                      }
                      total={`₹${this.overviewDetails.purchases.fullConference.sales.nonMember}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Students"
                      count={
                        this.overviewDetails.purchases.fullConference.count
                          .student
                      }
                      total={`₹${this.overviewDetails.purchases.fullConference.sales.student}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="row label-total-row">
                    <p class="elem-1">
                      Total{" "}
                      <span class="sale-count">
                        x{" "}
                        {
                          this.overviewDetails.purchases.fullConference.count
                            .total
                        }
                      </span>
                    </p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹
                        {
                          this.overviewDetails.purchases.fullConference.sales
                            .total
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Partial Conference</p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹
                        {
                          this.overviewDetails.purchases.partialConference.sales
                            .total
                        }
                      </p>
                      <p class="elem-3">
                        (
                        {
                          this.overviewDetails.purchases.partialConference.count
                            .total
                        }
                        )
                      </p>
                    </div>
                  </div>
                </div>
                <div class="card-content">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="For 7th only"
                      count={`${this.overviewDetails.purchases.partialConference.count.singleDay}`}
                      total={`₹${this.overviewDetails.purchases.partialConference.sales.singleDay}`}
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="For 7th & 8th"
                      count={`${this.overviewDetails.purchases.partialConference.count.doubleDay}`}
                      total={`₹${this.overviewDetails.purchases.partialConference.sales.doubleDay}`}
                    ></c-overview-info>
                  </div>
                </div>
              </div>
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Digital pass + Day pass</p>
                  </div>
                </div>
                <div class="card-content">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="Flying unicorn track"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .unicornStartup_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.unicornStartup_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Day pass (09th Nov)"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .dayPass09th_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.dayPass09th_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Day pass (10th Nov)"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .dayPass10th_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.dayPass10th_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Day pass (11th Nov)"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .dayPass11th_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.dayPass11th_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Digital pass"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .digitalPass_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.digitalPass_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Digital pass (09th Nov)"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .digitalPass09th_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.digitalPass09th_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Digital pass (10th Nov)"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .digitalPass10th_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.digitalPass10th_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Digital pass (11th Nov)"
                      count={
                        this.overviewDetails.purchases.indiaHci2022.count
                          .digitalPass11th_Count
                      }
                      total={`₹${this.overviewDetails.purchases.indiaHci2022.sales.digitalPass11th_Sales}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="row label-total-row">
                    <p class="elem-1">
                      Total{" "}
                      <span class="sale-count">
                        x{" "}
                        {
                          this.overviewDetails.purchases.indiaHci2022.count
                            .nonFullTicketTotal_Count
                        }
                      </span>
                    </p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹
                        {
                          this.overviewDetails.purchases.indiaHci2022.sales
                            .nonFullTicketTotal_Sales
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">HCIPAI membership</p>
                  </div>
                </div>
                <div class="card-content">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="Annual membership"
                      count={
                        this.overviewDetails.purchases.membership.count.annual
                      }
                      total={`₹${this.overviewDetails.purchases.membership.sales.annual}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Lifetime membership"
                      count={
                        this.overviewDetails.purchases.membership.count.lifetime
                      }
                      total={`₹${this.overviewDetails.purchases.membership.sales.lifetime}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="row label-total-row">
                    <p class="elem-1">
                      Total{" "}
                      <span class="sale-count">
                        x{" "}
                        {this.overviewDetails.purchases.membership.count
                          .annual +
                          this.overviewDetails.purchases.membership.count
                            .lifetime}
                      </span>
                    </p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹
                        {parseFloat(
                          this.overviewDetails.purchases.membership.sales.annual
                        ) +
                          parseFloat(
                            this.overviewDetails.purchases.membership.sales
                              .lifetime
                          )}
                      </p>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="row label-total-row">
                    <p class="elem-1">
                      Total{" "}
                      <span class="sale-count">
                        x{" "}
                        {this.overviewDetails.purchases.membership.count.total}
                      </span>
                    </p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹{this.overviewDetails.purchases.membership.sales.total}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Tracks</p>
                  </div>
                </div>
                <div class="card-content">
                  <div>
                    <c-overview-info
                      highlight=""
                      label="Workshops"
                      count={
                        this.overviewDetails.purchases.workshops.totalCount
                      }
                      total={`₹${this.overviewDetails.purchases.workshops.totalSale}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                    <c-overview-info
                      highlight=""
                      label="Courses"
                      count={this.overviewDetails.purchases.courses.totalCount}
                      total={`₹${this.overviewDetails.purchases.courses.totalSale}`}
                      layout="label-count-sales"
                    ></c-overview-info>
                  </div>
                </div>
                <div class="card-footer">
                  <div class="row label-total-row">
                    <p class="elem-1">
                      Total{" "}
                      <span class="sale-count">
                        x{" "}
                        {this.overviewDetails.purchases.workshops.totalCount +
                          this.overviewDetails.purchases.courses.totalCount}
                      </span>
                    </p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹
                        {this.overviewDetails.purchases.workshops.totalSale +
                          this.overviewDetails.purchases.courses.totalSale}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </section>
        {/* <div class="hspacer"></div>
        <section class="overview-section">
          <c-text type="sectionDivider">Track Participants</c-text>
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div class="row">
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Workshops</p>
                  </div>
                </div>
                <c-list
                  type="dashboardOverviewWC"
                  listItemsAsString={JSON.stringify(
                    this.overviewDetails.purchases.workshops.unitSale
                  )}
                ></c-list>
                <div>
                  {this.overviewDetails.purchases.workshops.unitSale.map(
                    (workshop) => (
                      <c-overview-info
                        highlight=""
                        is-btn={true}
                        label={workshop.name}
                        count={workshop.sold}
                        total={`₹${workshop.total}`}
                        layout="label-count-expand"
                        width={400}
                      ></c-overview-info>
                    )
                  )}
                </div>
              </div>
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1">Courses</p>
                  </div>
                </div>
                <c-list
                  type="dashboardOverviewWC"
                  listItemsAsString={JSON.stringify(
                    this.overviewDetails.purchases.courses.unitSale
                  )}
                ></c-list>
                <div>
                  {this.overviewDetails.purchases.courses.unitSale.map(
                    (course) => (
                      <c-overview-info
                        highlight=""
                        is-btn={true}
                        label={course.name}
                        count={course.sold}
                        total={`₹${course.total}`}
                        layout="label-count-expand"
                        width={400}
                      ></c-overview-info>
                    )
                  )}
                </div>
              </div>
            </div>
          )}
        </section> */}
      </div>
    );
  }
}
