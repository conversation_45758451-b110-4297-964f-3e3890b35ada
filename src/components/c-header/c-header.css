c-header header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
  margin-top: 1em;
  margin-bottom: 0;
  padding: 0;
}

c-header .header-content {
  width: 95%;
  margin: 0em auto;
  display: flex;
  color: white;
  justify-content: space-between;
  align-items: center;
}

.link-btn {
  padding: 1;
  border-radius: 0.25em;
  background: none;
  color: var(--accent-color);
  transition: all 0.15s ease-in;
}
.link-btn:hover {
  background: var(--accent-color-bg-lightest);
}
/* 
@media only screen and (min-width: 768px) {
  c-header header {
    margin-bottom: 10em;
  }
} */
