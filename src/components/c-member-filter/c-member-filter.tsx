import { Component, State, Prop, h } from "@stencil/core";

@Component({
  tag: "c-member-filter",
  styleUrl: "c-member-filter.css",
})
export class CMemberFilter {
  @State() isFetching: boolean = true;
  @State() isDownloadBtnDisabled: boolean = false;
  @State() btnLabel: string = "Download (all members)";
  @Prop() memberCount: number = 0;

  private isDownloading: boolean = false;
  private memberOptions = [
    {
      label: "Member Type",
      value: "",
    },
    {
      label: "Annual",
      value: "annual",
    },
    {
      label: "Lifetime",
      value: "lifetime",
    },
  ];
  render() {
    return (
      <div class="container">
        <div class="control-row">
          <c-dropdown
            name="memberFilter"
            option-str={JSON.stringify(this.memberOptions)}
          ></c-dropdown>
          <c-btn
            name="downloadLegacyMemberData"
            label={this.btnLabel}
            action-label="Downloading.."
            is-in-action={this.isDownloading}
            is-disabled={this.isDownloadBtnDisabled}
          ></c-btn>
        </div>
        <p class="filter-desc">
          Showing <strong>{this.memberCount}</strong> members
        </p>
      </div>
    );
  }
}
