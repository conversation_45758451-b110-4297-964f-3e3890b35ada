import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  Prop,
  State,
  Host,
  h,
} from "@stencil/core";

interface PriceTiersInterface {
  accessId: string;
  tierData: Array<object>;
}

interface CreateTierInterface {
  accessId: string;
}

@Component({
  tag: "c-ticket-price-input-partial",
  styleUrl: "c-ticket-price-input-partial.css",
})
export class CTicketPriceInputPartial {
  @Event({
    eventName: "tierChange",
    bubbles: true,
  })
  tierChangeEvent: EventEmitter;

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "newPricingTier") {
      this.initAccessDetails_ForNewTier(e.detail.value);
      this.mode = "newTierItem";
    } else if (e.detail.name === "cancelPriceTier") {
      this.resetAccessDetails_ForNewTier();
      this.mode = "viewTierItem";
    } else if (e.detail.name === "createPriceTier") {
      this.createPriceTier();
    } else if (e.detail.name === "deletePriceTier") {
      this.deletePriceTier();
    } else if (e.detail.name === "editPriceTier") {
      this.editPriceTier();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "tierName") {
      this.newTierName = e.detail.value;
    } else if (e.detail.name === "studentPrice") {
      this.newStudentPrice = e.detail.value;
    } else if (e.detail.name === "proPrice") {
      this.newProfessionalPrice = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "tierStartDate") {
      this.newTierStartDate = e.detail.value;
    } else if (e.detail.name === "tierEndDate") {
      this.newTierEndDate = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("accessDateClicked") handleTicketTierClick(e) {
    this.initAccessDetails_ForNewTier(e.detail.accessId);
    this.tierId = e.detail.tierId;
    this.tierName = e.detail.tierName;
    this.tierStartDate = e.detail.tierStart;
    this.tierEndDate = e.detail.tierEnd;
    this.tierRange = e.detail.tierRange;
    this.studentPrice = e.detail.price_Student;
    this.professionalPrice = e.detail.price_Professional;
    console.log(this.tierId);
    console.log(this.tierRange);
    this.mode = "editTierItem";
  }

  @Prop() accessDatesString: string = "";
  @Prop() accessDatesPriceListString: string = "";

  @State()
  mode: string = "viewTierItem";
  @State() isSaveTierDisabled: boolean = true;
  @State() tierData: any = [];
  @State() priceList: any = [];
  @State() newTier_AccessId: string = "";
  @State() newTier_AccessName: string = "";
  @State() newTier_IsMultiDayAccess: boolean = false;

  private tierId: string = "";
  private tierName: string = "";
  private tierStartDate: string = "";
  private tierEndDate: string = "";
  private tierRange: string = "";
  private studentPrice: number = 0;
  private professionalPrice: number = 0;

  private newTierName: string = "";
  private newTierStartDate: string = "";
  private newTierEndDate: string = "";
  private newStudentPrice: number = 0;
  private newProfessionalPrice: number = 0;

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    this.generatePriceList();
  }

  /*-------
  Functions
  -------*/

  createPriceTier() {
    console.log("create price tier");
    let obj = {
      id: Math.random().toString(),
      name: this.newTierName,
      start: this.newTierStartDate,
      end: this.newTierEndDate,
      range: `${this.newTierStartDate}-${this.newTierEndDate}`,
      price: {
        student: this.newStudentPrice,
        professional: this.newProfessionalPrice,
      },
    };

    this.priceList.map((priceListItem: any, index: number) => {
      if (priceListItem.accessId === this.newTier_AccessId) {
        this.priceList[index].pricingTiers.push(obj);
      }
    });
    this.priceList = [...this.priceList];
    console.log(this.priceList);
    // this.tierData.push(obj);
    // this.tierData = [...this.tierData];
    // this.tierChangeEvent.emit();
    this.isSaveTierDisabled = true;
    this.mode = "viewTierItem";
  }

  deletePriceTier() {
    this.tierChangeEvent.emit();
    console.log("delete price tier");
  }

  editPriceTier() {
    this.tierChangeEvent.emit();
    console.log("edit price tier");
  }

  generatePriceList() {
    if (this.accessDatesPriceListString) {
      this.priceList = JSON.parse(this.accessDatesPriceListString);
    } else {
      let accessDates: any = JSON.parse(this.accessDatesString);
      accessDates.map((date: any) => {
        let pricingTiers: any = [];
        let obj: any = {
          accessId: date.id,
          accessName: date.accessName,
          isMultiDayAccess: date.isMultiDayAccess,
          accessStartDate: date.accessStartDate,
          accessEndDate: date.accessEndDate,
          pricingTiers: pricingTiers,
        };
        this.priceList.push(obj);
      });
    }
    this.priceList = [...this.priceList];
  }

  initAccessDetails_ForNewTier(accessId: string) {
    this.priceList.map((item) => {
      if (item.accessId === accessId) {
        this.newTier_AccessId = item.accessId;
        this.newTier_AccessName = item.accessName;
        this.newTier_IsMultiDayAccess = item.isMultiDayAccess;
      }
    });
  }

  resetAccessDetails_ForNewTier() {
    this.newTier_AccessId = "";
    this.newTier_AccessName = "";
    this.newTier_IsMultiDayAccess = false;
  }

  validateInputs() {
    if (this.mode === "newTierItem") {
      this.validateInputs_NewTier();
    } else if (this.mode === "editTierItem") {
      this.validateInputs_EditTier();
    }
  }

  validateInputs_NewTier() {
    if (
      this.newTierName.length > 0 &&
      this.newTierStartDate.length > 0 &&
      this.newTierEndDate.length > 0 &&
      this.newStudentPrice > 0 &&
      this.newProfessionalPrice > 0
    ) {
      this.isSaveTierDisabled = false;
    } else {
      this.isSaveTierDisabled = true;
    }
  }

  validateInputs_EditTier() {
    let hasTierNameChanged: boolean = false;
    let hasTierStartDateChanged: boolean = false;
    let hasTierEndDateChanged: boolean = false;
    let hasStudentPriceChanged: boolean = false;
    let hasProfessionalPriceChanged: boolean = false;

    if (this.newTierName != this.tierName) {
      hasTierNameChanged = true;
    } else {
      hasTierNameChanged = false;
    }

    if (this.newTierStartDate != this.tierStartDate) {
      hasTierStartDateChanged = true;
    } else {
      hasTierStartDateChanged = false;
    }

    if (this.newTierEndDate != this.tierEndDate) {
      hasTierEndDateChanged = true;
    } else {
      hasTierEndDateChanged = false;
    }

    if (this.newStudentPrice != this.studentPrice) {
      hasStudentPriceChanged = true;
    } else {
      hasStudentPriceChanged = false;
    }

    if (this.newProfessionalPrice != this.professionalPrice) {
      hasProfessionalPriceChanged = true;
    } else {
      hasProfessionalPriceChanged = false;
    }

    if (
      hasTierNameChanged ||
      hasTierStartDateChanged ||
      hasTierEndDateChanged ||
      hasStudentPriceChanged ||
      hasProfessionalPriceChanged
    ) {
      this.isSaveTierDisabled = false;
    } else {
      this.isSaveTierDisabled = true;
    }
  }

  /*-------------------
  Functional Components
  -------------------*/

  ViewTierItem: FunctionalComponent<PriceTiersInterface> = ({
    accessId,
    tierData,
  }) => (
    <div>
      {tierData.length > 0 ? (
        tierData.map((tier: any) => (
          <c-ticket-price-item-partial
            accessId={accessId}
            tierId={tier.id}
            tierName={tier.name}
            tierStart={tier.start}
            tierEnd={tier.end}
            tierRange={tier.range}
            price_Student={tier.price.student}
            price_Professional={tier.price.professional}
          ></c-ticket-price-item-partial>
        ))
      ) : (
        <div class="no-tier-container">
          <c-text>Found 0 pricing tiers</c-text>
        </div>
      )}
      {this.mode === "viewTierItem" && (
        <this.CreateTier accessId={accessId}></this.CreateTier>
      )}
    </div>
  );

  ViewDateTier: FunctionalComponent = () =>
    this.priceList.map((item) => (
      <div class="date-tier-container">
        <div class="date-tier-item date-tier-item__1">
          <c-text>{item.accessName}</c-text>
          <c-text>
            {item.isMultiDayAccess ? (
              <span class="bubble bubble--green">Multi day</span>
            ) : (
              <span class="bubble bubble--blue">One day</span>
            )}
          </c-text>
        </div>
        <div class="date-tier-item date-tier-item__2">
          <this.ViewTierItem
            accessId={item.accessId}
            tierData={item.pricingTiers}
          ></this.ViewTierItem>
        </div>
      </div>
    ));

  InputTierItem: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TIER NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="tierName"
          placeholder="Tier name e.g. Early, Late, Walk-in"
          isDisabled={false}
          value={this.mode === "editTierItem" ? this.tierName : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TIER START & END DATES
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="tierStartDate"
            pickTime={false}
            date={this.tierStartDate}
          ></c-date-picker>
          <c-date-picker
            name="tierEndDate"
            pickTime={false}
            date={this.tierEndDate}
          ></c-date-picker>
        </div>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TIER PRICE
        </c-text>
        <div class="input-price-container">
          <div class="input-price-container__price">
            <c-text>₹</c-text>
            <c-textbox
              input-type="text"
              name="studentPrice"
              placeholder="Student price"
              isDisabled={false}
              value={
                this.mode === "editTierItem" ? this.studentPrice.toString() : ""
              }
            ></c-textbox>
          </div>
          &nbsp;&nbsp;
          <div class="input-price-container__price">
            <c-text>₹</c-text>
            <c-textbox
              input-type="text"
              name="proPrice"
              placeholder="Pro price"
              isDisabled={false}
              value={
                this.mode === "editTierItem"
                  ? this.professionalPrice.toString()
                  : ""
              }
            ></c-textbox>
          </div>
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelPriceTier">
            Cancel
          </c-button>
          <div class="button-group">
            {this.mode === "editTierItem" && (
              <c-button type="ghost_Danger_Small" name="deletePriceTier">
                Delete
              </c-button>
            )}
            &nbsp;&nbsp;&nbsp;&nbsp;
            <c-button
              type="ghost_Small"
              name={
                this.mode === "newTierItem"
                  ? "createPriceTier"
                  : "editPriceTier"
              }
              isDisabled={this.isSaveTierDisabled}
            >
              {this.mode === "newTierItem" && "Create"}
              {this.mode === "editTierItem" && "Edit"}
            </c-button>
          </div>
        </c-row>
      </div>
    </div>
  );

  CreateTier: FunctionalComponent<CreateTierInterface> = ({ accessId }) => (
    <div class="ticket-price-table__footer">
      <c-button
        name="newPricingTier"
        type="newPricingTier"
        isDisabled={false}
        isInAction={false}
        value={accessId}
      ></c-button>
    </div>
  );

  render() {
    return (
      <Host>
        <div class="ticket-price__top-row">
          <c-text type="modalLabel" isMandatory={true}>
            PRICING TIERS
          </c-text>
          {/* <c-button
            name="newPricingTier"
            type="newPricingTier"
            isDisabled={false}
            isInAction={false}
          ></c-button> */}
        </div>
        <div class="ticket-price-table__header">
          <div class="ticket-price-table__header--item ticket-price-table__header--item--1">
            <c-text>Access type</c-text>
          </div>
          <div class="ticket-price-table__header--item ticket-price-table__header--item--2">
            <c-text>Tier name</c-text>
          </div>
          <div class="ticket-price-table__header--item ticket-price-table__header--item--3">
            <c-text>Tier start & end</c-text>
          </div>
          <div class="ticket-price-table__header--item ticket-price-table__header--item--4">
            <c-text>Tier prices</c-text>
          </div>
        </div>
        <div class="ticket-price-table__body">
          {this.mode === "viewTierItem" ? (
            <this.ViewDateTier></this.ViewDateTier>
          ) : (
            <this.InputTierItem></this.InputTierItem>
          )}
        </div>
      </Host>
    );
  }
}
