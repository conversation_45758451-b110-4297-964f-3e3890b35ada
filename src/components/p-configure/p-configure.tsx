import {
  Component,
  Listen,
  Prop,
  State,
  FunctionalComponent,
  h,
} from "@stencil/core";
import { RouterHistory, MatchResults, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";
import { gsap } from "gsap";

@Component({
  tag: "p-configure",
  styleUrl: "p-configure.css",
})
export class PConfigure {
  /*-------------
  Event Listeners
  -------------*/
  @Listen("button-click")
  buttonClickLegacyHandler(event) {
    if (event.detail.name === "previewRegForm") {
      this.history.push(`/registration/${state.eventCodeForConfiguration}`, {});
    }
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "openAddPageForm") {
      this.openAddPageFormHandler();
    } else if (e.detail.name === "addPage") {
      this.addPageHandler();
    } else if (e.detail.name === "editPage") {
      this.editPageById(e.detail.value);
    } else if (e.detail.name === "deletePage") {
      this.deletePageById(e.detail.value);
    } else if (e.detail.name === "openAddTicketModal") {
      this.openModal(e.detail.name);
    }
  }
  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    state.configureActivePageId = event.detail.cNavItemName;
    state.configureActivePageLabel = event.detail.label;
    state.configureActivePageIcon = event.detail.icon;
  }
  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "newPageLabel") {
      this.newPageLabel = e.detail.value;
      this.isAddPageTextboxFilled = true;
      this.addPageFormChecker();
    } else if (e.detail.name === "newPageIcon") {
      this.newPageIcon = e.detail.value;
      this.isAddPageTextboxFilled = true;
      this.addPageFormChecker();
    }
  }
  @Listen("pageEdit")
  pageEditHandler(e) {
    this.editedPageLabel = e.detail.editedPageLabel;
    this.editedPageIcon = e.detail.editedPageIcon;
  }
  @Listen("closeModal")
  closeModalEventHandler() {
    this.isModalActive = false;
    this.modalName = "";
  }

  /*----
  Props
  ----*/
  @Prop() history: RouterHistory;
  @Prop() match: MatchResults;

  /*----
  States
  ----*/
  @State() isThereSections: boolean = false;
  @State() pages: any = [];
  @State() ticketsInPage: any = [];
  @State() isRegSysFetched: boolean = false;
  @State() isTicketsFetched: boolean = false;
  @State() isAddPageFormOpen: boolean = false;
  @State() isAddPageTextboxFilled: boolean = false;
  @State() isPageNameTextboxFocused: boolean = false;
  @State() newPageLabel: string;
  @State() newPageIcon: string;
  @State() isAddPageDisabled: boolean = true;
  @State() isInAction: boolean = false;
  @State() modalName: string = "";
  @State()
  isModalActive: boolean = false;

  /*--------
  References
  --------*/
  newPageFormEl!: HTMLDivElement;
  pageNameInput!: HTMLInputElement;
  pageIconInput!: HTMLInputElement;

  /*-------
  Variables
  -------*/
  private editedPageLabel: string = "";
  private editedPageIcon: string = "";

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    if (this.match.params.eventCodeForConfiguration) {
      state.eventCodeForConfiguration =
        this.match.params.eventCodeForConfiguration.trim();
    }
  }

  componentDidLoad() {
    this.getRegSys();
  }

  /*-----
  Methods
  -----*/
  addPageHandler() {
    this.isInAction = true;

    let payload = {
      eventCode: state.eventCodeForConfiguration,
      pageLabel: this.newPageLabel,
      pageIcon: this.newPageIcon,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/addpagetoeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.clearAddPageForm();
        }
        this.getPagesByEventCode();
        this.isInAction = false;
      })
      .catch((error) => {
        alert(error);
        this.isInAction = false;
      });
  }
  addPageFormChecker() {
    if (this.newPageLabel && this.newPageIcon) {
      this.isAddPageDisabled = false;
    } else {
      this.isAddPageDisabled = true;
    }
  }
  changeAddPageState() {
    this.isAddPageFormOpen = !this.isAddPageFormOpen;
  }
  clearAddPageForm() {
    this.newPageLabel = "";
    this.newPageIcon = "";
    this.isAddPageTextboxFilled = false;
    this.isPageNameTextboxFocused = false;
    this.isInAction = false;
    this.isAddPageDisabled = true;
  }
  closeAddPageForm() {
    this.clearAddPageForm();
    let tl = gsap.timeline();
    tl.to(this.newPageFormEl, {
      height: "0px",
      marginBottom: "0em",
      duration: 0.15,
    });
  }
  deletePageById(pageId: string) {
    this.isInAction = true;

    let payload = {
      eventCode: state.eventCodeForConfiguration,
      pageForDeletionId: pageId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/deletepagebypageid`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          if (response.data.isPageEmpty) {
            this.getPagesByEventCode();
          } else {
            alert(response.data.msg);
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }
  editPageById(pageId: string) {
    this.isInAction = true;
    let payload = {
      eventCode: state.eventCodeForConfiguration,
      pageId: pageId,
      pageLabel: this.editedPageLabel,
      pageIcon: this.editedPageIcon,
    };

    console.log(payload);

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/updatepagebyid`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getPagesByEventCode();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }
  getPagesByEventCode() {
    let payload = {
      eventCode: state.eventCodeForConfiguration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getpagesbyeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        let newPageList = response.data.payload.pages;
        this.pages = [...newPageList];
        this.isInAction = false;
      })
      .catch((error) => {
        alert(error);
        this.isInAction = false;
      });
  }
  getRegSys() {
    if (this.isRegSysFetched) this.isRegSysFetched = false;
    let payload = {
      eventCode: state.eventCodeForConfiguration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getregsysbyeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.pages = response.data.payload.pages;
          state.configureActivePageId = this.pages[0].name;
          state.configureActivePageLabel = this.pages[0].label;
          state.configureActivePageIcon = this.pages[0].icon;
          this.getTicketsByEventCodeAndPageId();
          this.isRegSysFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }
  getTicketsByEventCodeAndPageId() {
    this.isTicketsFetched = false;
    let payload = {
      eventCode: state.eventCodeForConfiguration,
      pageId: state.configureActivePageId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getticketsbypageidandeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.ticketsInPage = response.data.payload;
          this.ticketsInPage = [...this.ticketsInPage];
          this.isTicketsFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }
  openAddPageFormHandler() {
    this.changeAddPageState();
    if (this.isAddPageFormOpen) {
      this.openAddPageForm();
    } else {
      this.closeAddPageForm();
    }
  }
  openAddPageForm() {
    let tl = gsap.timeline();
    tl.to(this.newPageFormEl, {
      display: "block",
      overflow: "auto",
      marginBottom: "1em",
      duration: 0,
    });
    tl.to(this.newPageFormEl, {
      height: "auto",
      duration: 0.15,
    });
    this.isPageNameTextboxFocused = true;
  }
  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  /*------------------
  Functional Component
  ------------------*/
  NoPages: FunctionalComponent = () => (
    <c-sidebar type="left">
      <div class="empty-container">
        <c-text>No pages</c-text>
      </div>
    </c-sidebar>
  );
  NoTickets: FunctionalComponent = () => (
    <c-section>
      <div class="empty-container">
        <div>
          <c-text>
            <strong>
              No tickets found in <u>{state.configureActivePageLabel}</u>
            </strong>
          </c-text>
          <br />
          <c-button name="openAddTicketModal" isInAction={this.isInAction}>
            Add Ticket
          </c-button>
        </div>
      </div>
    </c-section>
  );
  PageList: FunctionalComponent = () => (
    <c-sidebar type="left">
      <div class="new-page__container">
        <div class="page-sidebar-header">
          <c-text type="navLabel">PAGES</c-text>
          <c-button
            type="openAddPageForm"
            name="openAddPageForm"
            isInActiveState={this.isAddPageFormOpen}
          ></c-button>
        </div>
        <div
          class="new-page-form__container"
          ref={(el) => (this.newPageFormEl = el as HTMLDivElement)}
        >
          <c-text>Add new page</c-text>
          <c-textbox
            name="newPageLabel"
            placeholder="Page name e.g. Workshops"
            isTextboxFilled={this.isAddPageTextboxFilled}
            isInFocus={this.isPageNameTextboxFocused}
          ></c-textbox>
          <c-textbox
            name="newPageIcon"
            placeholder="Icon code"
            isTextboxFilled={this.isAddPageTextboxFilled}
          ></c-textbox>
          <c-link
            type="textWithIcon"
            iconName="help-circle-outline"
            url="https://ionic.io/ionicons"
          >
            See icon reference
          </c-link>
          <c-row>
            <div></div>
            <c-button
              name="addPage"
              isDisabled={this.isAddPageDisabled}
              isInAction={this.isInAction}
            >
              Add
            </c-button>
          </c-row>
        </div>
      </div>
      {!this.isInAction && (
        <c-vnav
          nav-opts-str={JSON.stringify(this.pages)}
          isConfigMode={true}
        ></c-vnav>
      )}
    </c-sidebar>
  );
  PageLoader: FunctionalComponent = () => (
    <c-sidebar type="left">
      <c-skel variant="leftNavigation"></c-skel>
    </c-sidebar>
  );
  TicketList: FunctionalComponent = () => (
    <c-section>
      {this.ticketsInPage.map(
        (ticket: any) =>
          ticket.ticketType === "fullTicket" &&
          ticket.isTicketVisible && (
            <c-ticket-full-ticket
              ticketId={ticket.ticketId}
              type={ticket.ticketType}
              ticketTitle={ticket.ticketTitle}
              persona={ticket.persona}
              tierString={JSON.stringify(ticket.ticketTier)}
              isVisible={ticket.isTicketVisible}
              isDisabled={ticket.isTicketDisabled}
              isPrimary={ticket.isTicketPrimary}
              isPrimaryDependent={ticket.isTicketPrimaryDependent}
              isConfigMode={true}
            ></c-ticket-full-ticket>
          )
      )}
    </c-section>
  );
  TicketLoader: FunctionalComponent = () => (
    <c-section>
      <c-skel variant="ticketList"></c-skel>
    </c-section>
  );

  render() {
    return (
      <c-page>
        <c-modal name={this.modalName} is-active={this.isModalActive}></c-modal>
        <c-topbar></c-topbar>
        {this.isRegSysFetched ? (
          this.pages.length > 0 ? (
            <this.PageList></this.PageList>
          ) : (
            <this.NoPages></this.NoPages>
          )
        ) : (
          <this.PageLoader></this.PageLoader>
        )}

        {this.isTicketsFetched ? (
          this.ticketsInPage.length > 0 ? (
            <this.TicketList></this.TicketList>
          ) : (
            <this.NoTickets></this.NoTickets>
          )
        ) : (
          <this.TicketLoader></this.TicketLoader>
        )}

        {state.isAdmin && <c-control-bar name="configure"></c-control-bar>}
      </c-page>
    );
  }
}

injectHistory(PConfigure);
