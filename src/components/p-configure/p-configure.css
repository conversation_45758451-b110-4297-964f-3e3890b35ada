p-configure c-sidebar .left-sidebar {
  width: 250px;
}

p-configure .empty-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.25em;
  color: rgba(0, 0, 0, 0.3);
  height: 80vh;
}

p-configure .new-page__container {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.25em;
  /* margin-bottom: 1em; */
}

p-configure .page-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

p-configure .page-sidebar-header .modal-close {
  padding: 0.25em;
}

p-configure .new-page-form__container {
  display: none;
  height: 0px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.05);
  /* border: 1px solid rgba(0, 0, 0, 0.08); */
  padding: 0em 1em 0em 1em;
  margin-bottom: 1em;
  border-radius: 0.5em 0em 0.5em 0.5em;
}

p-configure .new-page-form__container c-text p {
  margin-top: 0.5em;
  color: rgba(0, 0, 0, 0.5);
  font-size: 0.9em;
}

p-configure .new-page-form__container c-textbox input {
  margin-top: 0.5em;
}

p-configure .new-page-form__container .text-with-icon {
  margin-top: 0.25em;
  /* margin-bottom: 1em; */
  font-size: 0.7em;
}

p-configure .new-page-form__container c-textbox input::placeholder {
  margin-bottom: 1em;
  font-size: 0.9em;
}

p-configure .new-page-form__container button {
  font-size: 0.8em;
}

p-configure .new-page-form__container c-row .row-container {
  margin-bottom: 1em;
}

p-configure c-section section {
  margin-left: 23.25%;
}

p-configure .left-sidebar c-vnav nav {
  padding-top: 1em;
  height: 99vh;
  overflow: auto;
  padding-bottom: 12em;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

p-configure .left-sidebar c-vnav nav::-webkit-scrollbar {
  display: none;
}
