import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-priceband",
  styleUrl: "c-priceband.css",
})
export class CPriceband {
  @Prop() heading: string;
  @Prop() subheading: string;
  @Prop() currency: string;
  @Prop() price: number;
  @Prop() isDiscount: boolean = false;

  render() {
    return (
      <div class="priceband-container">
        <div class="priceband-part priceband-part-left">
          <p class="heading">{this.heading}</p>
          <p class="subheading">{this.subheading}</p>
        </div>
        <div
          class={`priceband-part priceband-part-right ${
            this.isDiscount && "red-font"
          }`}
        >
          <p class="heading label">
            {this.currency}
            {this.price}
          </p>
        </div>
      </div>
    );
  }
}
