c-card .past-event {
  width: 300px;
}

c-card .past-event h1 {
  font-size: 1.5em;
  margin: 0;
}

/* Upcoming Event */
c-card .event-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3.5em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
}

c-card .event-image-container {
  /* width: 55%;
  height: 100px; */
  background: grey;
  border-radius: 0.25em 0 0 0.25em;
}

c-card .event-image {
  width: auto;
  border-radius: 0.25em 0 0 0.25em;
}

c-card .event-description-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 45%;
  position: relative;
  padding: 1em 1em 1em 1.2em;
  border-left: 1px solid rgba(0, 0, 0, 0.075);
}

c-card .event-description-header c-text .subtext {
  margin-top: 0;
  margin-bottom: 0.5em;
}

c-card .event-link-container {
  margin-top: 0.5em;
  margin-bottom: 2em;
}

c-card .archived-footer-buttons c-btn button {
  width: 220px;
}

c-card .registration-open-two-footer-buttons c-btn:first-child button {
  margin-bottom: 1em;
}

c-card .event-description-middle {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
  padding: 0.75em 0;
}

c-card .event-description-middle .text-with-icon {
  margin-bottom: 0.75em;
}

c-card .registration-closed-footer {
  margin-top: 1em;
}

/* Poster container */
c-card .event-poster-container {
  width: 59%;
}

/* ----------------
EVENT CONTROLS
---------------- */

c-card .admin-control-container {
  position: relative;
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
  margin-bottom: 0.5em;
}

c-card .admin-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75em;
}

c-card .admin-control-list-container {
  position: absolute;
  box-sizing: border-box;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  height: 0px;
  width: 105%;
  background: white;
  overflow: hidden;
  z-index: 99;
}

c-card c-button .admin-control-list-item {
  margin-bottom: 0.5em;
}

c-card .link-copy-confirmation {
  /* margin-left: 160px; */
  margin-left: 140px;
  margin-top: -37px;
  opacity: 0;
}

c-card .event-publish-label {
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.25em 0.5em;
  margin: 0;
  margin-bottom: 0.5em;
  border-radius: 0.25em;
  margin-right: 1em;
  width: 75px;
  text-align: center;
}

c-card .event-published-label {
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
}

c-card .event-unpublished-label {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.6);
}

c-card .banner-container {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  /* background: rgba(0, 0, 0, 0.1); */
  width: 50%;
  border-radius: 0.2em 0 0 0.2em;
  border-right: 1px solid rgba(0, 0, 0, 0.01);
}

c-card .banner {
  width: 100%;
  height: auto;
  /* border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
}

c-card .poster-container {
  position: relative;
  width: 50%;
  height: auto;
  background: white;
  overflow: hidden;
  border-radius: 0.25em 0 0 0.25em;
  border-right: 1px oslid black;
}

c-card .poster {
  position: absolute;
  width: 90%;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  top: 0;
  bottom: 0;
  margin-top: auto;
  margin-bottom: auto;
  z-index: 99;
  border-radius: 0.25em;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
}

c-card .poster-bg {
  width: 100%;
  height: 100%;
  background-size: 200%;
  filter: blur(20px);
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-card .poster-container {
    position: static;
    width: 100%;
  }

  c-card .poster {
    position: static;
    width: 100%;
    box-shadow: none;
    -webkit-box-shadow: none;
    border-radius: 0.25em 0.25em 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  c-card .event-container {
    display: block;
  }

  c-card .banner-container {
    padding: 0;
    width: 100%;
  }

  c-card .event-description-container {
    width: 100%;
    border-radius: 0 0 0.25em 0.25em;
    box-sizing: border-box;
    padding: 1em;
    border-left: 0;
  }
}
