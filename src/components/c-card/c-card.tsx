import {
  Component,
  FunctionalComponent,
  Listen,
  State,
  Prop,
  h,
} from "@stencil/core";
import state from "../../global/state";
import { gsap } from "gsap";
import axios from "axios";

@Component({
  tag: "c-card",
  styleUrl: "c-card.css",
})
export class CCard {
  /* --
  PROPS
  -- */
  @Prop() type: string;
  @Prop() eventId: string;
  @Prop() eventCode: string;
  @Prop() eventName: string;
  @Prop() eventTagline: string = "Humanising Technology";
  @Prop() eventVenueLabel: string = "IIT Bombay, Powai, Mumbai";
  @Prop() eventVenueUrl: string = "https://www.iitb.ac.in";
  @Prop() eventWebsiteUrl: string = "https://indiahci.org";
  @Prop() eventLogoUrl: string;
  @Prop() eventBannerUrl: string;
  @Prop() eventPosterUrl: string;
  @Prop() eventStartsOn: string;
  @Prop() eventEndsOn: string;
  @Prop() eventRegStartsOn: string;
  @Prop() eventRegEndsOn: string;
  @Prop() eventIsArchived: boolean;
  @Prop() eventIsPublished: boolean;
  @Prop() eventIsRegistrationOpen: boolean;
  @Prop() eventIsActive: boolean;
  @Prop() eventIsManager: boolean;

  /* ---
  STATES
  --- */
  @State() isAdminControlsOpen: boolean = false;
  @State() isEventPublished: boolean = false;
  @State() isPublishingInAction: boolean = false;

  /* ------
  VARIABLES
  ------ */
  private eventDateString: string = "";
  private registrationStartString: string = "";
  private registrationEndString: string = "";
  private accessRights: string = "";
  private eventLink: string = "";

  /* -------
  REFERENCES
  ------- */
  adminControlContainer!: HTMLDivElement;
  eventContainerEl!: HTMLDivElement;
  eventDescriptionBufferEl!: HTMLDivElement;
  copyLinkConfirmationContainer!: HTMLDivElement;

  /* -----------
  EVENT LISTENER
  ----------- */
  @Listen("buttonClick") handleButtonClick(e) {
    if (e.detail.name === "toggleAdminControlDD") {
      this.toggleAdminControls();
    } else if (e.detail.name === "openEditEventModal") {
      state.editEventName = this.eventName;
      state.editEventCode = this.eventCode;
    } else if (e.detail.name === "configureEvent") {
      console.log("configure event");
    } else if (e.detail.name === "unpublishEvent") {
      this.updateEventPublishingStatus("unpublish");
    } else if (e.detail.name === "publishEvent") {
      this.updateEventPublishingStatus("publish");
    } else if (e.detail.name === "copyEventLink") {
      this.confirmLinkCopy();
    }
  }

  componentWillLoad() {
    this.isEventPublished = this.eventIsPublished;
    this.prepareEventDateString();
    this.prepareRegistrationStrings();
    this.prepareAccessRights();
    this.prepareEventLink();
  }

  confirmLinkCopy() {
    navigator.clipboard.writeText(this.eventLink);
    let tl = gsap.timeline();
    tl.to(this.copyLinkConfirmationContainer, {
      opacity: 1,
      marginLeft: "160px",
      duration: 0.15,
    });
    tl.to(this.copyLinkConfirmationContainer, {
      opacity: 1,
      marginLeft: "160px",
      duration: 0.15,
    });
    tl.to(this.copyLinkConfirmationContainer, {}, 1);
    tl.to(this.copyLinkConfirmationContainer, {
      opacity: 0,
      marginLeft: "140px",
      duration: 0.15,
    });
  }

  updateEventPublishingStatus(newEventPublishStatus: string) {
    let payload = {
      eventCode: this.eventCode,
      newEventPublishStatus: newEventPublishStatus,
    };

    this.isPublishingInAction = true;

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/updateeventpublishing`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          if (newEventPublishStatus === "publish") {
            this.isEventPublished = true;
          } else if (newEventPublishStatus === "unpublish") {
            this.isEventPublished = false;
          }
          this.isPublishingInAction = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  prepareEventLink() {
    if (document.domain === "localhost") {
      this.eventLink = `http://localhost:3333/auth/${this.eventCode}`;
    } else {
      this.eventLink = `https://${document.domain}/auth/${this.eventCode}`;
    }
  }

  prepareEventDateString() {
    let startsOn: string = new Date(this.eventStartsOn).toDateString();
    let endsOn: string = new Date(this.eventEndsOn).toDateString();

    let startDate: string = startsOn.substring(8, 10);
    let startMonth: string = startsOn.substring(4, 7);
    let startYear: string = startsOn.substring(11, 15);

    let endDate: string = endsOn.substring(8, 10);
    let endMonth: string = endsOn.substring(4, 7);
    let endYear: string = endsOn.substring(11, 15);

    let isDateSame: boolean = false;
    let isMonthSame: boolean = false;
    let isYearSame: boolean = false;

    if (startDate === endDate) {
      isDateSame = true;
    }

    if (startMonth === endMonth) {
      isMonthSame = true;
    }

    if (startYear === endYear) {
      isYearSame = true;
    }

    if (isDateSame && isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear}`;
    } else if (!isDateSame && isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} - ${endDate} ${startMonth}, ${startYear}`;
    } else if (isDateSame && !isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} - ${endDate} ${endMonth}, ${startYear}`;
    } else if (isDateSame && isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    } else if (!isDateSame && !isMonthSame && isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} - ${endDate} ${endMonth}, ${startYear}`;
    } else if (isDateSame && !isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    } else if (!isDateSame && isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    } else if (!isDateSame && !isMonthSame && !isYearSame) {
      this.eventDateString = `${startDate} ${startMonth} ${startYear} - ${endDate} ${endMonth} ${endYear}`;
    }
  }

  prepareRegistrationStrings() {
    let startsOn: string = new Date(this.eventRegStartsOn).toDateString();
    let endsOn: string = new Date(this.eventRegEndsOn).toDateString();

    let startDate: string = startsOn.substring(8, 10);
    let startMonth: string = startsOn.substring(4, 7);
    let startYear: string = startsOn.substring(11, 15);
    let startTime: string = new Date(this.eventRegStartsOn).toLocaleString(
      "en-US",
      { hour: "numeric", minute: "numeric", hour12: true }
    );
    this.registrationStartString = `${startDate} ${startMonth} ${startYear}, ${startTime.toLowerCase()} (IST)`;

    let endDate: string = endsOn.substring(8, 10);
    let endMonth: string = endsOn.substring(4, 7);
    let endYear: string = endsOn.substring(11, 15);
    let endTime: string = new Date(this.eventRegEndsOn).toLocaleString(
      "en-US",
      { hour: "numeric", minute: "numeric", hour12: true }
    );
    this.registrationEndString = `${endDate} ${endMonth} ${endYear}, ${endTime.toLowerCase()} (IST)`;
  }

  prepareAccessRights() {
    if (state.isAdmin && this.eventIsManager) {
      this.accessRights = "admin";
    } else if (!state.isAdmin && this.eventIsManager) {
      this.accessRights = "manager";
    } else if (state.isAdmin && !this.eventIsManager) {
      this.accessRights = "admin";
    }
  }

  toggleAdminControls() {
    if (this.isAdminControlsOpen) {
      this.closeAdminControls();
    } else {
      this.openAdminControls();
    }
  }

  openAdminControls() {
    let eventContainerHeight =
      this.eventDescriptionBufferEl.getBoundingClientRect().height + 10;
    let tl = gsap.timeline();
    tl.to(this.adminControlContainer, {
      paddingTop: "1em",
      height: `${eventContainerHeight}px`,
      duration: 0.15,
    });
    this.isAdminControlsOpen = true;
  }

  closeAdminControls() {
    let tl = gsap.timeline();
    tl.to(this.adminControlContainer, {
      paddingTop: "0em",
      height: "0px",
      duration: 0.15,
      borderBottom: 0,
    });
    this.isAdminControlsOpen = false;
  }

  AdminControls: FunctionalComponent = () => (
    <div class="admin-control-container">
      <div class="admin-control-header">
        {this.isEventPublished ? (
          <span class="event-publish-label event-published-label">
            Published
          </span>
        ) : (
          <span class="event-publish-label event-unpublished-label">
            Unpublished
          </span>
        )}
        <c-button
          type="adminControlDD"
          name="toggleAdminControlDD"
          iconName={
            this.isAdminControlsOpen
              ? "chevron-up-outline"
              : "chevron-down-outline"
          }
          label="Admin"
        ></c-button>
      </div>
      <div
        class="admin-control-list-container"
        ref={(el) => (this.adminControlContainer = el as HTMLDivElement)}
      >
        <c-button
          type="adminControlListItem"
          name="openEditEventModal"
          iconName="pencil-outline"
          label="Edit"
        ></c-button>
        <c-button
          type="adminControlListItem"
          name="goToEventConfiguration"
          iconName="settings-outline"
          label="Configure"
          value={this.eventCode}
        ></c-button>
        <c-button
          type="adminControlListItem"
          name={this.isEventPublished ? "unpublishEvent" : "publishEvent"}
          iconName={this.isEventPublished ? "remove-outline" : "pulse-outline"}
          label={this.isEventPublished ? "Unpublish" : "Publish"}
          isInAction={this.isPublishingInAction}
          isInActionLabel={
            this.isPublishingInAction ? "Unpublishing" : "Publishing"
          }
        ></c-button>
        <c-button
          type="adminControlListItem"
          name="copyEventLink"
          iconName="copy-outline"
          label="Copy link"
        ></c-button>
        <div
          class="link-copy-confirmation"
          ref={(el) =>
            (this.copyLinkConfirmationContainer = el as HTMLDivElement)
          }
        >
          <c-badge color="blue" label="Link copied"></c-badge>
        </div>
      </div>
    </div>
  );

  render() {
    if (this.type === "event") {
      return (
        <div class="event-container">
          <div class="poster-container">
            <img class="poster" src={this.eventBannerUrl} width={200}></img>
            <div
              class="poster-bg"
              style={{ backgroundImage: `url(${this.eventBannerUrl})` }}
            ></div>
          </div>

          <div class="event-description-container">
            {state.isAdmin && <this.AdminControls></this.AdminControls>}
            <div
              class="event-description-buffer-container"
              ref={(el) =>
                (this.eventDescriptionBufferEl = el as HTMLDivElement)
              }
            >
              <div class="event-description-header">
                <c-text type="cardHeader">{this.eventName}</c-text>
                <c-text type="subtext">{this.eventTagline}</c-text>
              </div>
              <div class="event-description-middle">
                <c-link
                  type="textWithIcon"
                  url={""}
                  iconName="calendar-outline"
                >
                  {this.eventDateString}
                </c-link>
                <c-link
                  type="textWithIcon"
                  url={this.eventVenueUrl}
                  iconName="location-outline"
                >
                  {this.eventVenueLabel}
                </c-link>
                <c-link
                  type="textWithIcon"
                  url={this.eventWebsiteUrl}
                  iconName="link-outline"
                >
                  {this.eventWebsiteUrl}
                </c-link>
              </div>
              <div class="event-description-footer">
                {this.eventIsArchived ? (
                  <div class="archived-footer">
                    <div class="archived-footer-buttons">
                      {this.accessRights === "manager" && (
                        <c-btn
                          name="goToDashboard"
                          value={this.eventCode}
                          type="ghost"
                          label="Dashboard"
                          action-label=""
                          is-in-action={false}
                          is-disabled={false}
                        ></c-btn>
                      )}

                      {this.accessRights === "admin" && (
                        <c-btn
                          name="goToDashboard"
                          value={this.eventCode}
                          type="ghost"
                          label="Dashboard"
                          action-label=""
                          is-in-action={false}
                          is-disabled={false}
                        ></c-btn>
                      )}
                    </div>
                    <c-text type="subtext">Registration closed</c-text>
                  </div>
                ) : (
                  <div class="registration-open-footer">
                    <div
                      class={`registration-open-footer-buttons ${
                        this.eventIsManager || state.isAdmin
                          ? "registration-open-two-footer-buttons"
                          : ""
                      }`}
                    >
                      {this.accessRights === "manager" && (
                        <c-btn
                          name="goToDashboard"
                          value={this.eventCode}
                          type="ghost"
                          label="Dashboard"
                          action-label=""
                          is-in-action={false}
                          is-disabled={false}
                        ></c-btn>
                      )}

                      {this.accessRights === "admin" && (
                        <c-btn
                          name="goToDashboard"
                          value={this.eventCode}
                          type="ghost"
                          label="Dashboard"
                          action-label=""
                          is-in-action={false}
                          is-disabled={false}
                        ></c-btn>
                      )}
                      <c-btn
                        name="goToRegistration"
                        value={this.eventCode}
                        label="Register"
                        action-label="Checking.."
                        is-in-action={false}
                        is-disabled={!this.eventIsRegistrationOpen}
                      ></c-btn>
                    </div>

                    {this.eventIsRegistrationOpen ? (
                      <c-text type="subtext">
                        Closes on {this.registrationEndString}
                      </c-text>
                    ) : (
                      <div class="registration-closed-footer">
                        <c-text type="warning">
                          Registration will open on <br />
                          {this.registrationStartString}
                        </c-text>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div class="basic-card-container">
          <slot />
        </div>
      );
    }
  }
}
