import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-sidebar",
  styleUrl: "c-sidebar.css",
})
export class CSidebar {
  @Prop() type: string;
  render() {
    if (this.type === "left") {
      return (
        <div class="left-sidebar hide-on-mobile">
          <slot />
        </div>
      );
    } else if (this.type === "right") {
      return (
        <div class="right-sidebar hide-on-mobile">
          <slot />
        </div>
      );
    }
  }
}
