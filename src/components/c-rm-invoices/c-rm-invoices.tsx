import { Component, Listen, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-invoices",
  styleUrl: "c-rm-invoices.css",
})
export class CRmInvoices {
  @State() isFetching: boolean = true;
  @State() expandedInvoiceGroup: any;
  @State()
  isModalActive: boolean = false;
  @State() modalName: string = "";
  @State() isToastActive: boolean = false;

  @Listen("uploadInvoiceGroup")
  create_InvoiceGroup_Handler() {
    this.openModal("createInvoice");
  }

  @Listen("invoiceGroupUploadSuccess")
  invoiceGroupUploadSuccess_Handler(event) {
    this.closeModal();
    this.fetchData();
    this.showToast({
      type: event.detail.type,
      label: event.detail.label,
    });
  }

  @Listen("invoiceGroupUploadFailed")
  invoiceGroupUploadFailed_Handler(event) {
    this.closeModal();
    this.fetchData();
    this.showToast({
      type: event.detail.type,
      label: event.detail.label,
    });
  }

  @Listen("expandInvoiceGroupDetails")
  expandInvoiceGroupDetails(event) {
    this.prepareExpandedInvoiceGroup(event.detail.invoiceGroupId);
    this.openModal("showInvoiceDetails");
  }

  @Listen("dropdown-input-event")
  dropDownInputEvent(event) {
    if (event.detail.filter === "invoiceYearsFilter") {
      console.log(event.detail.value);
    }
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.closeModal();
  }

  private invoiceGroupCount: number = 0;
  private invoiceGroupArr: any;
  private invoiceYears: any;
  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;

  componentWillLoad() {
    state.isMobileDashboardOptionsVisible = false;
  }

  componentDidLoad() {
    this.fetchData();
  }

  fetchData() {
    this.isFetching = true;
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/get-invoice-groups`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.invoiceGroupArr = response.data.payload.invoiceGroups;
          this.invoiceYears = response.data.payload.invoiceYears;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  prepareExpandedInvoiceGroup(invoiceGroupId: string) {
    this.invoiceGroupArr.map((invoiceGroup: any) => {
      if (invoiceGroup.id === invoiceGroupId) {
        this.expandedInvoiceGroup = invoiceGroup;
      }
    });
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  closeModal() {
    console.log("close modal");
    this.isModalActive = false;
    this.modalName = "";
  }

  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  render() {
    return (
      <div class="invoice-container">
        <c-modal
          name={this.modalName}
          is-active={this.isModalActive}
          data={JSON.stringify(this.expandedInvoiceGroup)}
        ></c-modal>
        {this.isToastActive && (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        )}

        {state.isMobileDashboardOptionsVisible
          ? !this.isFetching && (
              <div class="show-on-mobile">
                <c-control-bar
                  count={!this.isFetching && this.invoiceGroupCount}
                  name="invoices"
                  invoiceYearsString={JSON.stringify(this.invoiceYears)}
                ></c-control-bar>
              </div>
            )
          : ""}
        <div class="show-on-desktop">
          {!this.isFetching && (
            <c-control-bar
              count={!this.isFetching && this.invoiceGroupCount}
              name="invoices"
              invoiceYearsString={JSON.stringify(this.invoiceYears)}
            ></c-control-bar>
          )}
        </div>

        {this.isFetching ? (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : this.invoiceGroupArr.length > 0 ? (
          <c-list
            type="dashboardInvoiceGroupList"
            listItemsAsString={JSON.stringify(this.invoiceGroupArr)}
          ></c-list>
        ) : (
          <c-text>No invoices found</c-text>
        )}
      </div>
    );
  }
}
