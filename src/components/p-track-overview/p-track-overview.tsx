import { Component, Prop, Listen, State, h } from "@stencil/core";
import { RouterHistory, MatchResults } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-track-overview",
  styleUrl: "p-track-overview.css",
  shadow: true,
})
export class PTrackOverview {
  @Prop() match: MatchResults;
  @Prop() history: RouterHistory;

  @State() isFetching: boolean = true;
  @State() accountDetails: any;
  @State() salesDetails: any;
  @State() ticketSales: any = [];
  @State()
  isModalActive: boolean = false;
  @State() modalName: string = "";

  @State() modalData: any;

  @Listen("buttonClick") handleButtonClick(e) {
    if (e.detail.name === "showSessionInfo") {
      this.openModal(e.detail.name);
      let value = e.detail.value.split("---");
      this.modalData = {
        ticketId: value[0],
        sessionId: value[1],
      };
    }
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.isModalActive = false;
    this.modalName = "";
  }

  private eventCode: string;

  componentWillLoad() {
    this.eventCode = this.match.params.eventCode;
  }

  componentDidLoad() {
    this.getOverviewData();
  }

  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  getOverviewData() {
    let payload = {
      eventCode: this.eventCode,
      filterType: "eventCode",
      startDate: "",
      endDate: "",
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/overview-v3`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.accountDetails = response.data.payload.accounts;
          this.salesDetails = response.data.payload.sales;
          this.ticketSales = this.salesDetails.tickets;

          state.notificationCount = response.data.payload.notifications.count;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        <c-modal
          data={this.modalData}
          name={this.modalName}
          is-active={this.isModalActive}
        ></c-modal>

        {this.isFetching ? (
          ""
        ) : (
          <div class="account-overview">
            <c-card>
              <c-text type="subtext">WORKSHOPS & COURSES</c-text>
              <div class="spacing"></div>
              {this.ticketSales.map(
                (ticket: any) =>
                  ticket.ticketType === "trackTicket" &&
                  ticket.sessions.map((session: any) => (
                    <div class="track__container">
                      <c-row>
                        <div class="row__item__1">
                          {" "}
                          <c-button
                            name="showSessionInfo"
                            type="link_Small"
                            value={`${ticket.ticketId}---${session.sessionId}`}
                          >
                            {session.ticketTitle}
                          </c-button>
                        </div>
                        <div class="row__item__2">
                          <c-text>{session.saleCount}</c-text>
                        </div>
                      </c-row>
                    </div>
                  ))
              )}
            </c-card>
          </div>
        )}
      </div>
    );
  }
}
