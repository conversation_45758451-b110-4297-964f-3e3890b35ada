import { newSpecPage } from '@stencil/core/testing';
import { PTrackOverview } from '../p-track-overview';

describe('p-track-overview', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PTrackOverview],
      html: `<p-track-overview></p-track-overview>`,
    });
    expect(page.root).toEqualHtml(`
      <p-track-overview>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </p-track-overview>
    `);
  });
});
