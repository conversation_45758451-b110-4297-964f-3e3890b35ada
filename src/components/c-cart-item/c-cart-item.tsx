import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-cart-item",
  styleUrl: "c-cart-item.css",
})
export class CCartItem {
  @Prop() heading: string;
  @Prop() subTitle: string;
  @Prop() currency: string;
  @Prop() price: string;

  componentWillLoad() {
    this.subTitle = this.subTitle.toUpperCase();
  }
  render() {
    return (
      <div class="cart-item-container">
        <p class="cart-item-details">
          <p class="sub-title">{this.subTitle.toUpperCase()}</p>
          <p class="cart-item-details-heading">
            <span class="heading">{this.heading}</span>
            <span class="price">
              {this.currency}
              {this.price}
            </span>
          </p>
        </p>
      </div>
    );
  }
}
