import { newSpecPage } from '@stencil/core/testing';
import { CAccountDetailsModal } from '../c-account-details-modal';

describe('c-account-details-modal', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CAccountDetailsModal],
      html: `<c-account-details-modal></c-account-details-modal>`,
    });
    expect(page.root).toEqualHtml(`
      <c-account-details-modal>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-account-details-modal>
    `);
  });
});
