c-account-details-modal .modal-container {
  background: var(--bg-color);
  margin: 0em auto 10em 20%;
  padding: 1.5em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
  position: fixed;
  top: 2em;
  box-sizing: border-box;
  width: 60vw;
  max-height: 80vh;
  overflow: auto;
  margin: 0 auto;
  padding: 1em;
}

c-account-details-modal .row {
  display: flex;
  justify-content: space-between;
}

c-account-details-modal .badge-row {
  margin-bottom: 0.25em;
  justify-content: flex-start;
}

c-account-details-modal .name,
.email,
.country,
.subtext,
.headline {
  margin: 0;
  padding: 0;
}

c-account-details-modal .name {
  color: var(--accent-color);
}

c-account-details-modal .email,
.country {
  margin-top: 0.25em;
}

c-account-details-modal .subtext {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

c-account-details-modal .section-heading {
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 0;
}

c-account-details-modal .membership-info label {
  font-size: 0.75em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
}

c-account-details-modal .membership-info {
  width: 20%;
  margin-bottom: 0;
  margin-top: 0;
}

c-account-details-modal .membership-info span {
  font-size: 0.9em;
}

c-account-details-modal h3 {
  color: rgba(0, 0, 0, 0.3);
}

c-account-details-modal c-paid-items-list .container {
  margin-bottom: 1em;
}

c-account-details-modal c-paid-items-list main {
  padding-top: 1em;
}

c-account-details-modal c-paid-items-list main .row {
  width: 100%;
}

c-account-details-modal c-paid-items-list main .paid-item-list-row {
  width: 95%;
}

c-account-details-modal
  c-paid-items-list
  main
  .paid-item-list-row
  .paid-item-list {
  width: 66%;
  margin-left: 0;
}

c-account-details-modal
  c-paid-items-list
  main
  .paid-item-list-row
  .button-container {
  width: 8%;
}

c-account-details-modal c-paid-items-list .seperator {
  border: 0;
  height: 1px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: none;
  margin-bottom: 0;
}

c-account-details-modal c-paid-items-list .summary-list {
  margin-top: 1em;
}

c-account-details-modal c-paid-items-list .payment-summary-row {
  margin-bottom: 0;
}

c-account-details-modal c-skel-line .skel-line {
  margin-bottom: 1em;
}

c-account-details-modal .info-section-header {
  margin-top: 2em;
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin-bottom: 0.5em;
}

c-account-details-modal .taxid-title {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.1em 0.5em;
  border-radius: 0.25em;
}

c-account-details-modal .card {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
}

c-account-details-modal .details-list {
  background: white;
  padding: 1em;
  border-radius: 0.5em;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-account-details-modal .detail-list-item-seperator {
  margin: 1em 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

c-account-details-modal c-badge .badge {
  margin-bottom: 1em;
}

c-account-details-modal c-button .modal-close {
  padding: 0;
}

@media only screen and (max-width: 768px) {
  c-account-details-modal .modal-container {
    width: 90vw;
  }
}
