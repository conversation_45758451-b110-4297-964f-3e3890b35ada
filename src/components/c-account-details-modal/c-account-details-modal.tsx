import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-account-details-modal",
  styleUrl: "c-account-details-modal.css",
})
export class CAccountDetailsModal {
  /*------------------
  Event Emitters
  ------------------*/
  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    }
  }

  @State() isFetching: boolean = true;

  private usersObj: any;
  private mailToString: string;

  componentWillLoad() {
    let payload = {
      email: state.expandedUserEmail.toLowerCase(),
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/singleuser`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.usersObj = "";
          this.usersObj = response.data.payload;
          this.mailToString = `mailto:${this.usersObj.email}`;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="modal-container">
        {this.isFetching ? (
          <div class="skel-row">
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={25}></c-skel-line>
            <br />
          </div>
        ) : (
          <div class="card">
            <c-row>
              <div>
                <div class="row badge-row">
                  {this.usersObj.occupation === "professional" && (
                    <c-badge label="Professional" color="blue"></c-badge>
                  )}
                  {this.usersObj.occupation === "student" && (
                    <c-badge label="Student" color="pink"></c-badge>
                  )}
                  {this.usersObj.memberType === "lifetime" && (
                    <c-badge label="HCIPAI Lifetime" color="golden"></c-badge>
                  )}
                  {this.usersObj.memberType === "annual" && (
                    <c-badge label="HCIPAI Annual" color="golden"></c-badge>
                  )}
                </div>
                <h2 class="name">
                  {this.usersObj.firstName} {this.usersObj.lastName}
                </h2>
                <p class="subtext">
                  Account created on{" "}
                  {new Date(this.usersObj.createdAt)
                    .toString()
                    .substring(4, 15)}
                </p>
              </div>
              <c-button
                type="modalClose"
                name="closeModal"
                icon-name=""
                label=""
              ></c-button>
            </c-row>
          </div>
        )}

        {this.isFetching ? (
          <div>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          <div>
            <h2 class="info-section-header">Professional & Contact</h2>
            <div class="details-list">
              <div class="detail-list-item">
                <c-text type="subtext">OCCUPATION</c-text>
                <c-text>
                  {this.usersObj.jobDegree} @ {this.usersObj.orgInsti}
                </c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">EMAIL</c-text>
                <c-text-link
                  url={this.mailToString}
                  label={this.usersObj.email}
                ></c-text-link>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">MOBILE</c-text>
                <c-text>
                  {" "}
                  {this.usersObj.isdCode ? `+${this.usersObj.isdCode} ` : "-"}
                  {this.usersObj.mobileNo ? ` ${this.usersObj.mobileNo}` : ""}
                </c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">COUNTRY</c-text>
                <c-text>{this.usersObj.country}</c-text>
              </div>
            </div>
          </div>
        )}

        <div>
          <h2 class="info-section-header">Purchases</h2>
          <c-paid-items-list own-purchases={false}></c-paid-items-list>
        </div>

        {this.isFetching ? (
          <div>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          <div>
            <h2 class="info-section-header">Billing</h2>
            <div class="details-list">
              <div class="detail-list-item">
                <c-text type="subtext">TAX JURISDICTION</c-text>
                <c-text>{this.usersObj.taxJurisdiction}</c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">BILLING ADDRESS</c-text>
                <c-text>{this.usersObj.billingAddress}</c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">GST STATUS</c-text>
                <c-text>{this.usersObj.gstStatus}</c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">BUSINESS NAME</c-text>
                <c-text>{this.usersObj.businessName}</c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">GSTIN</c-text>
                <c-text> {this.usersObj.taxID}</c-text>
              </div>
            </div>
          </div>
        )}

        {this.isFetching ? (
          <div>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          <div>
            <h2 class="info-section-header">Membership</h2>
            <div class="details-list">
              <div class="detail-list-item">
                <c-text type="subtext">ID</c-text>
                <c-text>
                  {this.usersObj.isMember ? (
                    <span>{this.usersObj.memberID}</span>
                  ) : (
                    "-"
                  )}
                </c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">TYPE</c-text>
                <c-text>
                  {this.usersObj.isMember ? (
                    <span>{this.usersObj.memberType}</span>
                  ) : (
                    "-"
                  )}
                </c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">START DATE</c-text>
                <c-text>
                  {this.usersObj.isMember ? (
                    <span>
                      {new Date(this.usersObj.startDate)
                        .toString()
                        .substring(4, 15)}
                    </span>
                  ) : (
                    "-"
                  )}
                </c-text>
              </div>
              <div class="detail-list-item-seperator"></div>
              <div class="detail-list-item">
                <c-text type="subtext">END DATE</c-text>
                <c-text>
                  {this.usersObj.isMember ? (
                    <span>
                      {this.usersObj.endDate
                        ? new Date(this.usersObj.endDate)
                            .toString()
                            .substring(4, 15)
                        : "-"}
                    </span>
                  ) : (
                    "-"
                  )}
                </c-text>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
}
