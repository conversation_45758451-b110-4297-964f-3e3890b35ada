import { Component, Listen, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-coupons",
  styleUrl: "c-rm-coupons.css",
})
export class CRmCoupons {
  @State() isFetching: boolean = true;

  @State() modalName: string = "";
  @State()
  isModalActive: boolean = false;

  @Listen("expand-coupon-details")
  expandCouponDetails(event) {
    state.expandedCouponId = event.detail.couponId;
    this.openModal("showCouponDetails");
  }

  @Listen("dropdown-input-event")
  dropDownInputEvent(event) {
    if (event.detail.filter === "couponDeductionFilter") {
      this.couponDeductionFilter = event.detail.value;
    } else if (event.detail.filter === "couponAccessFilter") {
      this.couponAccessFilter = event.detail.value;
    }
    this.fetchData();
  }

  @Listen("coupon-creation-response")
  couponCtionResponseHandler() {
    this.closeModal();
    this.fetchData();
  }

  @Listen("create-coupon")
  createCouponClickHandler() {
    this.openModal("createCoupon");
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.closeModal();
  }

  private couponCount: number = 0;
  private couponArr: any = "";
  private couponDeductionFilter: string = "";
  private couponAccessFilter: string = "";

  componentWillLoad() {
    state.isMobileDashboardOptionsVisible = false;
  }

  componentDidLoad() {
    this.fetchData();
  }

  fetchData() {
    this.isFetching = true;
    let payload = {
      eventCode: state.eventCodeForMonitoring,
      couponDeductionFilter: this.couponDeductionFilter,
      couponAccessFilter: this.couponAccessFilter,
    };

    axios({
      data: payload,
      method: "POST",
      baseURL: `${state.baseUrl}/getcoupons`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.couponCount = response.data.payload.totalCoupons;
          this.couponArr = response.data.payload.couponArr;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  closeModal() {
    this.isModalActive = false;
    this.modalName = "";
  }

  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  render() {
    return (
      <div class="coupon-container">
        <c-modal name={this.modalName} is-active={this.isModalActive}></c-modal>
        {state.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile">
            <c-control-bar
              count={!this.isFetching && this.couponCount}
              name="coupons"
            ></c-control-bar>
          </div>
        )}
        <div class="show-on-desktop">
          <c-control-bar
            count={!this.isFetching && this.couponCount}
            name="coupons"
          ></c-control-bar>
        </div>
        <c-text type="sectionDivider">
          Coupons{" "}
          {state.eventNameForMonitoring && `of ${state.eventNameForMonitoring}`}
        </c-text>{" "}
        {this.isFetching ? (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : this.couponArr.length > 0 ? (
          <c-list
            type="dashboardCouponInfoList"
            listItemsAsString={JSON.stringify(this.couponArr)}
          ></c-list>
        ) : (
          <c-text>No coupons found</c-text>
        )}
      </div>
    );
  }
}
