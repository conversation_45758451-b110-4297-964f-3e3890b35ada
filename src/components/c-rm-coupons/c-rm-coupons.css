c-rm-coupons .control-row {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

c-rm-coupons .button-row {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

c-rm-coupons .filter-desc {
  font-size: 0.8em;
  margin: 0;
  margin-top: 0.5em;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 2.5em;
}

c-rm-coupons .control-row c-btn button {
  font-size: 0.8em;
}

c-rm-coupons .dark-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  -webkit-animation: fadein 0.25s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: fadein 0.25s; /* Firefox < 16 */
  -ms-animation: fadein 0.25s; /* Internet Explorer */
  -o-animation: fadein 0.25s; /* Opera < 12.1 */
  animation: fadein 0.25s;
}

c-rm-coupons .show-on-mobile {
  display: none;
}

c-rm-coupons .show-on-desktop {
  display: block;
}

@media only screen and (max-width: 768px) {
  c-rm-coupons .show-on-mobile {
    display: flex;
  }

  c-rm-coupons .show-on-desktop {
    display: none;
  }

  c-rm-coupons c-control-bar .control-bar-container {
    width: 90vw;
  }
}
