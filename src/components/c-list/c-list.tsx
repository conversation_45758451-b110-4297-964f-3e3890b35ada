import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Prop,
  h,
  Watch,
} from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-list",
  styleUrl: "c-list.css",
})
export class CList {
  @Prop() name: string;
  @Prop() type: string;
  @Prop() subType: string;
  @Prop() listItemsAsString: string;

  @Event({
    eventName: "expand-user-details",
    bubbles: true,
  })
  expandUserDetails: EventEmitter;

  @Event({
    eventName: "expand-coupon-details",
    bubbles: true,
  })
  expandCouponDetails: EventEmitter;

  @Event({
    eventName: "expandInvoiceGroupDetails",
    bubbles: true,
  })
  expandInvoiceGroupDetails: EventEmitter;

  @Event({
    eventName: "expand-wc-details",
    bubbles: true,
  })
  expandWCDetails: EventEmitter;

  @Event({
    eventName: "managerSelected",
    bubbles: true,
  })
  managerSelectedEvent: EventEmitter;

  @Watch("listItemsAsString")
  isActiveWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      this.listItems = JSON.parse(this.listItemsAsString);
    }
  }

  private listItems: any;

  componentWillLoad() {
    this.listItems = JSON.parse(this.listItemsAsString);
  }

  handleListItemClick(a, b) {
    if (this.type === "dashboardAccountInfoList") {
      state.expandedUserEmail = a;
      this.expandUserDetails.emit();
    } else if (this.type === "dashboardCouponInfoList") {
      this.expandCouponDetails.emit({
        couponId: a,
      });
    } else if (this.type === "dashboardOverviewWC") {
      this.expandWCDetails.emit({
        name: a,
        soldCount: b,
      });
    } else if (this.type === "searchDropList") {
      if (this.name === "managerSearchDropList") {
        this.managerSelectedEvent.emit({
          managerName: a,
          managerEmail: b,
        });
      }
    } else if (this.type === "dashboardInvoiceGroupList") {
      this.expandInvoiceGroupDetails.emit({
        invoiceGroupId: a,
      });
    }
  }

  AttendeeList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((sale) => (
        <li class="dashboard-primary-list-item dashboard-sales-list-item">
          <div class="list-item list-item-1">
            <div class="name-email-container">
              <p class="name">{sale.name}</p>
              <c-text-link
                url={`mailto:${sale.email}`}
                label={sale.email}
              ></c-text-link>
              <br />
              {sale.occupation === "student" && (
                <span class="badge green">Student</span>
              )}
              {sale.occupation === "professional" && (
                <span class="badge blue">Professional</span>
              )}
              {sale.occupation === "student" && !sale.isStudentVerified && (
                <span class="badge outline--pink">Unverified Student</span>
              )}
            </div>
          </div>
          <div class="list-item list-item-2">
            <ul class="attendee-list">
              {sale.purchasedItems.map((item) => (
                <li class="attendee-list-item">
                  {item.isFreeAccess && (
                    <div>
                      <span class="badge orange">Free Access</span>
                      <br />
                    </div>
                  )}
                  <span class="invoice-total">
                    {item.item.tier.toUpperCase()}
                    <br />
                  </span>
                  {item.item.title}{" "}
                </li>
              ))}
            </ul>
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardSalesInfoList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((sale) => (
        <li class="dashboard-primary-list-item dashboard-sales-list-item">
          <div class="list-item list-item-1">
            <div class="name-email-container">
              <p class="name">{sale.name}</p>
              <c-text-link
                url={`mailto:${sale.email}`}
                label={sale.email}
              ></c-text-link>
            </div>
          </div>
          <div class="list-item list-item-2">
            {sale.paymentStatus === "purchased" && (
              <span class="badge green">Paid</span>
            )}
            {sale.paymentStatus === "under-verification" && (
              <span class="badge orange">Verifying</span>
            )}
            {sale.isCouponApplied && <span class="badge pink">Discounted</span>}
            {sale.grandTotal === 0 && <span class="badge blue">Free</span>}
            {sale.persona === "student" && !sale.isStudentVerified && (
              <span class="badge outline--pink">Unverified Student</span>
            )}

            <ul class="purchased-ticket-list">
              {sale.purchasedItems.map((item) => (
                <li class="purchased-ticket-list-item">
                  <span class="invoice-total">
                    {item.tier.toUpperCase()}
                    <br />
                  </span>
                  {item.title}{" "}
                </li>
              ))}
            </ul>

            {sale.isCouponApplied && (
              <div class="row">
                <ion-icon name="ticket-outline"></ion-icon>
                &nbsp;&nbsp;
                <p class="invoice-total">{sale.couponName}</p>
              </div>
            )}

            {sale.isCouponApplied ? (
              <div class="row">
                <ion-icon name="cash-outline"></ion-icon>
                &nbsp;&nbsp;
                <p class="invoice-total">
                  {sale.currency}
                  {sale.cartAmountBeforeDeduction} - {sale.currency}
                  {sale.deductedAmount}{" "}
                  {sale.paymentMethod != "BankTransfer" && (
                    <span>
                      + {sale.currency}
                      {sale.gatewayFee}
                    </span>
                  )}{" "}
                  = {sale.currency}
                  {sale.grandTotal}
                </p>{" "}
              </div>
            ) : (
              <div class="row">
                <ion-icon name="cash-outline"></ion-icon>
                &nbsp;&nbsp;
                <p class="invoice-total">
                  {sale.currency}
                  {sale.grandTotal}
                </p>{" "}
              </div>
            )}

            {sale.grandTotal > 0 && (
              <div class="row">
                <ion-icon name="receipt-outline"></ion-icon>
                &nbsp;&nbsp;
                <p class="invoice-total">
                  {" "}
                  {sale.paymentMethod}: {sale.transactionId}
                </p>
              </div>
            )}
            <div class="row">
              <ion-icon name="calendar-outline"></ion-icon>
              &nbsp;&nbsp;
              <p class="invoice-total">
                {" "}
                {new Date(sale.purchaseDate).toString().substring(4, 15)}
              </p>
            </div>
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardCouponInfoList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((coupon: any) => (
        <li
          class="dashboard-primary-list-item dashboard-coupon-list-item dashboard-primary-list-item-clickable"
          onClick={() => this.handleListItemClick(coupon.id, "")}
        >
          <div class="list-item list-item-1">
            <p class="name">{coupon.name}</p>
          </div>
          <div class="list-item list-item-2">
            {coupon.deduction.type === "fixed" && (
              <p class="badge blue">{`₹${coupon.deduction.value} discount`}</p>
            )}
            {coupon.deduction.type === "percentage" && (
              <p class="badge blue">{`${coupon.deduction.value}% discount`}</p>
            )}
            {coupon.deduction.type === "ticketType" && (
              <p class="badge blue">{`${coupon.name}`}</p>
            )}
            {coupon.meta.access === "open" && (
              <p class="badge golden">{coupon.meta.code}</p>
            )}
            <div class="row icon-label list-item-3-content">
              <ion-icon name="key-outline"></ion-icon> &nbsp;&nbsp;
              <p>
                {coupon.meta.access === "open" && (
                  <span class="member-info">Open to all</span>
                )}
                {coupon.meta.access === "emaillist" && (
                  <span class="member-info">Restricted (email list)</span>
                )}
              </p>
            </div>
            <div class="row icon-label list-item-3-content">
              <ion-icon name="ticket-outline"></ion-icon> &nbsp;&nbsp;
              <p>
                {coupon.meta.access === "open" && (
                  <span class="member-info">
                    {coupon.usedCount} coupons used
                  </span>
                )}
                {coupon.meta.access === "emaillist" && (
                  <span class="member-info">
                    {coupon.usedCount} of {coupon.issuedCount} coupons used
                  </span>
                )}
              </p>
            </div>
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardAccountInfoList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((user) => (
        <li
          class="dashboard-primary-list-item dashboard-registrant-list-item dashboard-primary-list-item-clickable"
          onClick={() => this.handleListItemClick(user.email, "")}
        >
          <div class="list-item list-item-1">
            <div class="dp-container">
              <p class="dp-initials">
                {user.firstName[0].toUpperCase()}
                {user.lastName[0].toUpperCase()}
              </p>
            </div>
            <div class="name-email-container">
              <p class="name">
                {user.firstName} {user.lastName}
              </p>
              <c-text-link
                url={`mailto:${user.email}`}
                label={user.email}
              ></c-text-link>
            </div>
          </div>
          <div class="list-item list-item-2">
            {user.memberType === "lifetime" && (
              <span class="badge golden">HCIPAI Lifetime</span>
            )}
            {user.memberType === "annual" && (
              <span class="badge golden">HCIPAI Annual</span>
            )}
            {user.occupation === "professional" && (
              <span class="badge blue">Professional</span>
            )}
            {user.occupation === "student" && (
              <span class="badge pink">Student</span>
            )}

            <div class="row">
              <ion-icon
                name={
                  user.occupation === "student"
                    ? "school-outline"
                    : "briefcase-outline"
                }
              ></ion-icon>
              &nbsp;&nbsp;
              <p class="invoice-total">
                {user.jobDegree ? user.jobDegree : "Not Mentioned"}
              </p>
            </div>

            <div class="row">
              <ion-icon
                name={
                  user.occupation === "student"
                    ? "book-outline"
                    : "business-outline"
                }
              ></ion-icon>
              &nbsp;&nbsp;
              <p class="invoice-total">
                {user.orgInsti ? user.orgInsti : "Not Mentioned"}
              </p>
            </div>
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardMemberInfoList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((member: any) => (
        <li class="dashboard-primary-list-item dashboard-member-list-item">
          <div class="list-item-1">
            <p class="name">{member.name}</p>
            <c-text-link
              url={`mailto:${member.email}`}
              label={member.email}
            ></c-text-link>
          </div>
          <div class="list-item-2">
            {member.membershipType === "annual" && (
              <p class="badge green">Annual</p>
            )}
            {member.membershipType === "lifetime" && (
              <p class="badge blue">Lifetime</p>
            )}
            <div class="icon-label list-item-row list-item-2-content">
              <ion-icon name="id-card-outline"></ion-icon> &nbsp;&nbsp;
              <p>{member.memberID}</p>
            </div>
            <div class="icon-label list-item-row list-item-3-content">
              <ion-icon name="calendar-outline"></ion-icon> &nbsp;&nbsp;
              <p>
                {" "}
                {new Date(member.membershipStartDate)
                  .toString()
                  .substring(4, 15)}
              </p>
              {member.membershipType === "annual" && <p>&nbsp;-&nbsp;</p>}
              {member.membershipType === "annual" && (
                <p>
                  {" "}
                  {new Date(member.membershipEndDate)
                    .toString()
                    .substring(4, 15)}
                </p>
              )}
            </div>

            {member.membershipType === "annual" && (
              <div class="icon-label list-item-row list-item-3-content">
                <ion-icon name="hourglass-outline"></ion-icon>
                &nbsp;&nbsp;
                <p>Ends in {member.expiresIn} days</p>
              </div>
            )}
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardAlertInfoList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((alert: any) => (
        <li class="dashboard-primary-list-item dashboard-alert-list-item">
          {!alert.status.isActive && (
            <div class="card-lower">
              <p>
                {alert.type === "Bank Transfer" ? "Verified by" : "Closed by"}{" "}
                <strong>
                  {alert.resolver.name.first} {alert.resolver.name.last}
                </strong>{" "}
                ({alert.resolver.email}) on{" "}
                {new Date(alert.timestamp.resolvedOn)
                  .toString()
                  .substring(4, 15)}
              </p>
            </div>
          )}
          <div class="card-upper">
            <div class="list-item-1">
              <p class="name">
                {alert.issuer.name.first} {alert.issuer.name.last}
              </p>
              <c-text-link
                url={`mailto:${alert.issuer.email}`}
                label={alert.issuer.email}
              ></c-text-link>

              {alert.type === "Student ID Verification" && (
                <c-text type="subtext">
                  {alert.details.prop4 && alert.details.prop5
                    ? `${alert.details.prop5} @ ${alert.details.prop4}`
                    : ""}
                </c-text>
              )}
            </div>
            <div class="list-item-2">
              {alert.type === "Bank Transfer" && (
                <div>
                  <p class="badge pink">Bank Transfer</p>
                  <div class="icon-label list-item-2-content">
                    <ion-icon name="cash-outline"></ion-icon> &nbsp;&nbsp;
                    <p class="issue-date">₹{alert.details.prop2}</p>
                  </div>
                  <div class="icon-label list-item-2-content">
                    <ion-icon name="receipt-outline"></ion-icon> &nbsp;&nbsp;
                    <p class="issue-date">{alert.details.prop1}</p>
                  </div>
                </div>
              )}

              {alert.type === "Razorpay Payment Failure" && (
                <p class="badge pink">Razorpay Payment Failure</p>
              )}

              {alert.type === "Partial Registration" && (
                <p class="badge pink">Partial Registration</p>
              )}

              {alert.type === "Student ID Verification" && (
                <p class="badge pink">ID Verification</p>
              )}

              {alert.type === "Bank Transfer" && (
                <div class="icon-label list-item-2-content">
                  <ion-icon name="calendar-outline"></ion-icon> &nbsp;&nbsp;
                  <p class="issue-date">
                    {new Date(alert.timestamp.issuedOn)
                      .toString()
                      .substring(4, 15)}
                  </p>
                </div>
              )}

              {alert.type === "Student ID Verification" && (
                <div class="icon-label list-item-2-content">
                  <ion-icon name="calendar-outline"></ion-icon> &nbsp;&nbsp;
                  <p class="issue-date">
                    Expiry:&nbsp;
                    {new Date(alert.details.prop3).toString().substring(4, 15)}
                  </p>
                </div>
              )}

              {!alert.status.isActive && alert.status.remarks.length > 0 ? (
                <p class="badge orange">{alert.status.remarks}</p>
              ) : (
                ""
              )}

              {alert.type != "Partial Registration" ||
                (alert.type != "Student ID Verification" && (
                  <div class="icon-label list-item-2-content">
                    <ion-icon name="receipt-outline"></ion-icon> &nbsp;&nbsp;
                    <p class="issue-date">{alert.details.prop1}</p>
                  </div>
                ))}
              {alert.type != "Partial Registration" ||
                (alert.type != "Student ID Verification" && (
                  <div class="icon-label list-item-2-content">
                    <ion-icon name="cash-outline"></ion-icon> &nbsp;&nbsp;
                    <p class="issue-date">₹{alert.details.prop2}</p>
                  </div>
                ))}
              {alert.type === "Student ID Verification" && (
                <div>
                  <div class="icon-label list-item-2-content">
                    <c-link url={alert.details.prop1} target="_blank">
                      <ion-icon name="link-outline"></ion-icon>&nbsp;&nbsp;ID
                      Front
                    </c-link>
                  </div>

                  {alert.details.prop2 && (
                    <div class="icon-label list-item-2-content">
                      {" "}
                      <c-link url={alert.details.prop2} target="_blank">
                        <ion-icon name="link-outline"></ion-icon>&nbsp;&nbsp;ID
                        Back
                      </c-link>
                    </div>
                  )}
                </div>
              )}
            </div>

            {alert.status.isActive && (
              <div class="row-item-5">
                <c-btn
                  name="resolveAlert"
                  label={
                    alert.type === "Bank Transfer" ||
                    alert.type === "Student ID Verification"
                      ? "Verify"
                      : "Close"
                  }
                  action-label=""
                  value={alert.id}
                  type="ghost"
                  is-in-action={false}
                  is-disabled={false}
                ></c-btn>
                {alert.type === "Student ID Verification" && (
                  <c-button
                    type="ghost_Danger_Small"
                    name="cancelIDVerification"
                    value={alert.id}
                  >
                    Cancel
                  </c-button>
                )}
              </div>
            )}
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardInvoiceGroupList: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list">
      {this.listItems.map((invoiceGroup: any) => (
        <li
          class="dashboard-primary-list-item dashboard-invoice-list-item dashboard-primary-list-item-clickable"
          onClick={() => this.handleListItemClick(invoiceGroup.id, "")}
        >
          <div class="list-item list-item-1">
            <p class="invoice-group-name">{invoiceGroup.name}</p>
          </div>
          <div class="list-item list-item-2">
            <div class="row">
              <ion-icon name="document-text-outline"></ion-icon> &nbsp;&nbsp;
              <p class="invoice-total">
                {" "}
                Invoices: {invoiceGroup.invoiceCount}
              </p>
            </div>
            <div class="row">
              <ion-icon name="mail-outline"></ion-icon> &nbsp;&nbsp;
              <p class="invoice-bulk-send">
                Mail attempts: {invoiceGroup.bulkSendCount}
              </p>
            </div>
            <div class="row">
              <ion-icon name="calendar-outline"></ion-icon> &nbsp;&nbsp;
              <p class="invoice-bulk-send">
                {new Date(invoiceGroup.createdOn).toString().substring(4, 15)}
              </p>
            </div>
          </div>
        </li>
      ))}
    </ul>
  );

  DashboardOverviewWC: FunctionalComponent = () => (
    <ul class="dashboard-primary-info-list dashboard-overview-wc-list">
      {this.listItems.map((wc: any) => (
        <li
          class="dashboard-primary-list-item dashboard-primary-list-item-clickable dashboard-overview-wc-item"
          onClick={() => this.handleListItemClick(wc.name, wc.sold)}
        >
          <div class="list-item-1">
            {" "}
            <p class="name">{wc.name}</p>
            <div class="icon-label list-item-1-content">
              <ion-icon name="people-outline"></ion-icon> &nbsp;&nbsp;
              <p>{wc.sold} participants</p>
            </div>
          </div>
        </li>
      ))}
    </ul>
  );

  RegistrantSearchDropList: FunctionalComponent = () => (
    <ul class="search-drop-list">
      {this.listItems.map((registrant: any) => (
        <li
          class="search-drop-list-item"
          onClick={() =>
            this.handleListItemClick(
              `${registrant.firstName} ${registrant.lastName}`,
              registrant.email
            )
          }
        >
          <div class="dp-container">
            <p class="dp-initials">
              {registrant.firstName[0].toUpperCase()}
              {registrant.lastName[0].toUpperCase()}
            </p>
          </div>
          <div class="name-email-container">
            <c-text>
              {registrant.firstName} {registrant.lastName}
            </c-text>
            <c-text-link
              url={`mailto:${registrant.email}`}
              label={registrant.email}
            ></c-text-link>
          </div>
        </li>
      ))}
    </ul>
  );

  render() {
    if (this.type === "dashboardSalesInfoList") {
      return <this.DashboardSalesInfoList></this.DashboardSalesInfoList>;
    } else if (this.type === "dashboardAccountInfoList") {
      return <this.DashboardAccountInfoList></this.DashboardAccountInfoList>;
    } else if (this.type === "dashboardCouponInfoList") {
      return <this.DashboardCouponInfoList></this.DashboardCouponInfoList>;
    } else if (this.type === "dashboardMemberInfoList") {
      return <this.DashboardMemberInfoList></this.DashboardMemberInfoList>;
    } else if (this.type === "dashboardAlertInfoList") {
      return <this.DashboardAlertInfoList></this.DashboardAlertInfoList>;
    } else if (this.type === "dashboardOverviewWC") {
      return <this.DashboardOverviewWC></this.DashboardOverviewWC>;
    } else if (this.type === "searchDropList") {
      if (this.name === "managerSearchDropList" && this.listItems.length > 0) {
        return <this.RegistrantSearchDropList></this.RegistrantSearchDropList>;
      }
    } else if (this.type === "dashboardInvoiceGroupList") {
      return <this.DashboardInvoiceGroupList></this.DashboardInvoiceGroupList>;
    } else if (this.type === "attendeeList") {
      return <this.AttendeeList></this.AttendeeList>;
    }
  }
}
