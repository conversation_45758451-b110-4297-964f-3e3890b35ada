c-list ol {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

c-list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

/* -----------------------------
Dashboard primary info list item
----------------------------- */

c-list .dashboard-primary-list-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  background: white;
  padding: 1em 1em 1em 1.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0);
  transition: all 0.15s ease-in;
}

c-list .dashboard-primary-list-item-clickable:hover {
  cursor: pointer;
  border: 1px solid var(--accent-color-bg);
}

c-list .dashboard-primary-list-item:last-child:hover {
  border-bottom: 1px solid var(--accent-color-bg);
}

c-list .dashboard-primary-list-item .badge {
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.25em 1em;
  margin: 0;
  /* margin-bottom: 0.5em; */
  border-radius: 0.5em;
  margin-right: 0.75em;
}

c-list .dashboard-primary-list-item .golden {
  display: inline-block;
  background: var(--accent-golden-bg-lighter);
  color: var(--accent-golden-darker);
}

c-list .dashboard-primary-list-item .blue {
  display: inline-block;
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}

c-list .dashboard-primary-list-item .pink {
  display: inline-block;
  background: var(--accent-pink-bg-lighter);
  color: var(--accent-pink-darker);
}

c-list .outline--pink {
  color: var(--accent-pink-darker);
  border: 1px solid var(--accent-pink-darker);
}

c-list .dashboard-primary-list-item .orange {
  display: inline-block;
  background: var(--orange-100);
  color: var(--orange-600);
}

c-list .dashboard-primary-list-item .green {
  display: inline-block;
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
}

c-list .dashboard-primary-list-item:first-child {
  border-radius: 0.5em 0.5em 0 0;
}

c-list .dashboard-primary-list-item:last-child {
  border-radius: 0 0 0.5em 0.5em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

c-list .dashboard-primary-list-item .name {
  font-size: 0.9em;
  margin: 0em;
}

c-list .dashboard-primary-list-item a {
  font-size: 0.75em;
  text-decoration: none;
  color: var(--accent-color);
}

/* --------------------------------
Dashboard registrant info list item
-------------------------------- */

c-list .dashboard-registrant-list-item .list-item p {
  margin: 0;
  padding: 0;
}

c-list .dashboard-registrant-list-item .list-item-1 {
  display: flex;
  align-items: center;
  width: 50%;
}

c-list .dashboard-registrant-list-item .list-item-2 {
  width: 45%;
}

c-list .dashboard-sales-list-item .list-item-1 {
  display: flex;
  align-items: center;
  width: 45%;
}

c-list .dashboard-sales-list-item .list-item-2 {
  width: 50%;
}

/* c-list .dashboard-registrant-list-item .list-item-3 {
  width: 50%;
} */

c-list .dashboard-registrant-list-item .list-item-3-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

c-list .dashboard-registrant-list-item .list-item-3-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
}

c-list .dashboard-registrant-list-item .disabled {
  opacity: 0.3;
}

c-list .dp-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 40px;
  width: 40px;
  border-radius: 100%;
  background: var(--accent-color-bg-lightest);
  transition: var(--site-transition);
}

c-list .dp-initials {
  font-size: 1.1em;
  color: var(--accent-color-lighter);
}

c-list .name-email-container {
  margin-left: 1em;
}

c-list .dashboard-sales-list-item .name-email-container {
  margin-left: 0em;
}

c-list .name-email-container a {
  font-size: 0.8em;
}

c-list .dashboard-registrant-list-item .email {
  font-size: 0.8em;
  margin-top: 2em;
  padding-top: 2em;
}

c-list .dashboard-registrant-list-item .jobdegree {
  font-size: 0.9em;
}

c-list .dashboard-registrant-list-item .orginsti {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.75em;
}

c-list .dashboard-registrant-list-item .registered-on {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
  padding: 0;
}

/* ----------------------------
Dashboard coupon info list item
---------------------------- */

c-list .dashboard-coupon-list-item {
  justify-content: space-between;
}

c-list .dashboard-coupon-list-item .list-item-1 {
  width: 50%;
}

c-list .dashboard-coupon-list-item .list-item-2 {
  width: 45%;
}

/* c-list .dashboard-coupon-list-item .list-item-3 {
  width: 130px;
} */

c-list .dashboard-coupon-list-item .list-item-3-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

c-list .dashboard-coupon-list-item .list-item-3-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
}

/* ----------------------------
Dashboard member info list item
---------------------------- */
c-list .dashboard-member-list-item {
  justify-content: space-between;
}

c-list .dashboard-member-list-item .list-item-1 {
  width: 50%;
}

c-list .dashboard-member-list-item .list-item-2 {
  width: 45%;
}

c-list .dashboard-member-list-item .list-item-2-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

c-list .dashboard-member-list-item .list-item-2-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
}

c-list .dashboard-member-list-item .list-item-3-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

c-list .dashboard-member-list-item .list-item-3-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
}

/* ---------------------------
Dashboard alert info list item
--------------------------- */

c-list .dashboard-alert-list-item {
  display: block;
}

c-list .dashboard-alert-list-item:hover {
  /* border: 0;
  outline: none; */
}

c-list .dashboard-alert-list-item .card-upper {
  display: flex;
  justify-content: space-between;
}

c-list .dashboard-alert-list-item .card-lower {
  background: rgba(238, 234, 245, 0.5);
  font-size: 0.8em;
  color: var(--accent-color);
  border-radius: 0.5em;
  margin-bottom: 1em;
}

c-list .dashboard-alert-list-item .card-lower p {
  margin: 0;
  padding: 0.5em 1em;
  border-radius: 0 0 0.25em 0.25em;
}

c-list .dashboard-alert-list-item .list-item-1 {
  width: 35%;
}

c-list .dashboard-alert-list-item .list-item-2 {
  width: 35%;
}

c-list .dashboard-alert-list-item .list-item-3 {
  width: 15%;
}

c-list .dashboard-alert-list-item .list-item-2-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
  padding: 0.3em 0;
}

c-list .dashboard-alert-list-item .list-item-2-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
  overflow-wrap: anywhere;
}

c-list .dashboard-alert-list-item .row-item-5 .ghost-danger-small {
  margin-top: 1em;
}

/* ----------------------------
Dashboard overview WC list item
---------------------------- */
c-list .dashboard-overview-wc-list {
  width: 375px;
}

c-list .dashboard-overview-wc-list li:first-child {
  border-radius: 0 0 0 0;
  border-top: 0;
}

c-list .dashboard-overview-wc-list li:last-child {
  border-radius: 0 0 0.5em 0.5em;
}

c-list .dashboard-overview-wc-item .list-item-1-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 0.5em;
}

c-list .dashboard-overview-wc-item .list-item-1-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
}

c-list c-text .subtext {
  margin-top: 0;
}
/* ------------------
Search drop down list
------------------ */

c-list .search-drop-list {
  position: absolute;
  width: 76.5%;
  border: var(--site-border);
  max-height: 400px;
  overflow: auto;
  border-top: 0;
  z-index: 9;
  border-radius: 0 0 0.25em 0.25em;
}

c-list .search-drop-list-item {
  display: flex;
  background: white;
  padding: 0.75em;
  font-size: 0.9em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--site-transition);
}

c-list .search-drop-list-item a {
  pointer-events: none;
}

c-list .search-drop-list-item:hover {
  background: var(--accent-color-bg-lightest);
  cursor: pointer;
}

c-list .search-drop-list-item:hover .dp-container {
  background: var(--accent-color-bg-lighter);
}

c-list .search-drop-list-item:last-child {
  border-bottom: 0;
  border-radius: 0 0 0.25em 0.25em;
}

/* ----------------------------
Dashboard invoices  list item
---------------------------- */
/* ----------------------------
Dashboard coupon info list item
---------------------------- */

c-list .dashboard-invoice-list-item {
  justify-content: space-between;
}

c-list .dashboard-invoice-list-item .list-item-1 {
  width: 50%;
}

c-list .dashboard-invoice-list-item .list-item-2 {
  width: 45%;
}

c-list .dashboard-invoice-list-item .list-item-3-content {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

c-list .dashboard-invoice-list-item .list-item-3-content p {
  margin: 0;
  padding: 0;
  font-size: 0.75em;
}

c-list .invoice-group-name {
  margin: 0;
  margin-bottom: 0em;
}
c-list .invoice-created-on {
  font-size: 0.8em;
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
}
c-list .invoice-total {
  margin: 0;
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}
c-list .invoice-bulk-send {
  font-size: 0.8em;
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
}

c-list .row {
  display: flex;
  padding: 0.25em 0;
}

c-list .list-item-row {
  margin: 0.5em 0;
}

c-list .purchased-ticket-list {
  margin: 1em 0;
  padding: 1em 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  list-style-type: none;
}

c-list .purchased-ticket-list-item {
  margin-bottom: 1em;
}

c-list .purchased-ticket-list-item:last-child {
  margin-bottom: 0;
}

c-list .attendee-list {
  list-style-type: none;
}

c-list .attendee-list-item {
  margin-bottom: 1em;
}

@media only screen and (max-width: 768px) {
  c-list .dashboard-sales-list-item .list-item-2 {
    width: 100%;
    margin-top: 1em;
  }

  c-list .dashboard-primary-list-item {
    display: block;
    padding: 1em;
  }

  c-list .dashboard-coupon-list-item .list-item-2 {
    width: 100%;
    display: block;
    margin-top: 1em;
  }

  c-list .dashboard-invoice-list-item .list-item-2 {
    display: block;
    width: 100%;
    justify-content: space-between;
    margin-top: 1em;
  }

  c-list .dashboard-coupon-list-item .list-item-3 {
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-top: 1em;
  }

  c-list .dashboard-alert-list-item .card-upper {
    display: block;
  }

  c-list .dashboard-alert-list-item .list-item-1 {
    width: 100%;
  }
  c-list .dashboard-primary-list-item .pink {
    display: inline-block;
  }
  c-list .dashboard-alert-list-item .list-item-2 {
    margin: 1em 0;
    width: 100%;
  }
  c-list .dashboard-alert-list-item .list-item-3 {
    width: 100%;
    margin-bottom: 1em;
  }

  c-list .dashboard-alert-list-item .row-item-5 {
    /* display: flex;
    flex-direction: row-reverse;
    align-items: center; */
    width: 100%;
  }

  c-list .dashboard-alert-list-item .row-item-5 .ghost-danger-small {
    display: block;
    margin-top: 1em;
    padding: 0.7em 1em;
  }

  c-list .dashboard-member-list-item .list-item-1 {
    width: 100%;
  }

  c-list .dashboard-member-list-item .list-item-2 {
    width: 100%;
    margin-top: 1em;
  }

  c-list .dashboard-member-list-item .list-item-2 .badge {
    display: inline-block;
  }

  c-list .dashboard-member-list-item .list-item-3 {
    width: 90%;
  }

  c-list .dashboard-registrant-list-item .list-item-1 {
    width: 90%;
    /* justify-content: space-between;
    flex-direction: row-reverse; */
  }

  c-list .dashboard-registrant-list-item .list-item-2 {
    width: 80%;
    margin-top: 1em;
    margin-left: 3.1em;
  }

  c-list .purchased-ticket-list {
    margin: 1em 0;
    padding: 1em;
    /* border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5em;
    list-style-type: none;
  }

  c-list .attendee-list {
    margin: 1em 0;
    padding: 1em;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5em;
    list-style-type: none;
  }
}
