import { Component, Prop, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-ticket-track",
  styleUrl: "c-ticket-track.css",
})
export class CTicketTrack {
  @Prop() ticketID: string;
  @Prop() isDisabled: boolean;
  @Prop() type: string;
  @Prop() subType: string;
  @Prop() ticketTitle: string;
  @Prop() subTitle: string;
  @Prop() people: string;
  @Prop() desc: string;
  @Prop() start: string;
  @Prop() end: string;
  @Prop() price: string;
  @Prop() quantity: string;
  @Prop() ticketBtnStatus: string;
  @Prop() url: string;
  @Prop() date: string;

  componentDidLoad() {
    state.isFreePurchase = false;
    state.isCouponApplied = false;
  }
  render() {
    return (
      <div
        class={
          this.isDisabled
            ? `${this.subType} container  disabled `
            : `${this.subType} container`
        }
      >
        <p class="type">
          <span class="bubble">{this.subType.toUpperCase()}</span>
        </p>
        <div class="row">
          <p class="ticketTitle">
            {this.ticketTitle}
            <br />
          </p>
          <p class="price">
            {parseInt(this.price) > 0 ? `₹${this.price}` : "FREE"}
          </p>
        </div>
        <div class="row half-row">
          <p class="time">
            <strong>
              {this.date}, {this.start} - {this.end}
            </strong>{" "}
            (IST)
          </p>
          {parseInt(this.quantity) > 0 ? (
            <p class="time">
              {/* Only <strong>{this.quantity} seats</strong> left */}
            </p>
          ) : (
            <p class="time">No seats left</p>
          )}
          <c-text-link
            url={this.url}
            label={`Read ${this.subType} description >`}
          ></c-text-link>
        </div>
      </div>
    );
  }
}
