import { Component, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-user-info-modal",
  styleUrl: "c-user-info-modal.css",
})
export class CUserInfoModal {
  @State() isFetching: boolean = true;
  private usersObj: any;
  private mailToString: string;
  private scrollY: any = window.scrollY;

  componentWillLoad() {
    let payload = {
      email: state.expandedUserEmail,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/singleuser`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.usersObj = "";
          this.usersObj = response.data.payload;
          this.mailToString = `mailto:${this.usersObj.email}`;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div
        class="modal-container"
        style={{ marginTop: `${this.scrollY + 60}px` }}
      >
        {this.isFetching ? (
          <div class="skel-row">
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={25}></c-skel-line>
            <br />
          </div>
        ) : (
          <div>
            <div class="row card">
              <div class="left">
                <h2 class="name">
                  {this.usersObj.firstName} {this.usersObj.lastName}
                </h2>
                <p class="headline">
                  {this.usersObj.jobDegree} @ {this.usersObj.orgInsti}
                </p>
                <div class="row badge-row">
                  {this.usersObj.occupation === "professional" ? (
                    <c-badge label="Professional" color="blue"></c-badge>
                  ) : (
                    ""
                  )}
                  {this.usersObj.occupation === "student" ? (
                    <c-badge label="Student" color="pink"></c-badge>
                  ) : (
                    ""
                  )}
                  {this.usersObj.memberType === "lifetime" ? (
                    <c-badge label="HCIPAI Lifetime" color="golden"></c-badge>
                  ) : (
                    ""
                  )}
                  {this.usersObj.memberType === "annual" ? (
                    <c-badge label="HCIPAI Annual" color="golden"></c-badge>
                  ) : (
                    ""
                  )}
                </div>
                <p class="subtext">
                  Account created on{" "}
                  {new Date(this.usersObj.createdAt)
                    .toString()
                    .substring(4, 15)}
                </p>
              </div>
              <div class="right">
                <c-text-link
                  url={this.mailToString}
                  label={this.usersObj.email}
                ></c-text-link>
                <p class="email text-item">
                  {this.usersObj.isdCode ? `+${this.usersObj.isdCode} ` : "-"}
                  {this.usersObj.mobileNo ? ` ${this.usersObj.mobileNo}` : ""}
                </p>
                <p class="country text-item">{this.usersObj.country}</p>
              </div>
            </div>
          </div>
        )}
        <div>
          <h2 class="info-section-header">Purchases</h2>
          <c-paid-items-list own-purchases={false}></c-paid-items-list>
        </div>

        {this.isFetching ? (
          <div>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          <div>
            <h2 class="info-section-header">Billing</h2>
            <div class="row membership-row card">
              <p class="membership-info">
                <label>GST STATUS</label>
                <br />
                <span>{this.usersObj.gstStatus}</span>
              </p>
              <p class="membership-info">
                <label>BUSINESS NAME</label>
                <br />
                <span>{this.usersObj.businessName}</span>
                <br />
                <br />
                <span class="taxid">
                  <label>GSTIN</label>
                  <br />
                  {this.usersObj.taxID}
                </span>
              </p>
              <p class="membership-info">
                <label>TAX JURISDICTION</label>
                <br />
                <span>{this.usersObj.taxJurisdiction}</span>
              </p>
              <p class="membership-info">
                <label>BILLING ADDRESS</label>
                <br />
                <span>{this.usersObj.billingAddress}</span>
              </p>
            </div>
          </div>
        )}
        {this.isFetching ? (
          <div>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          <div>
            <h2 class="info-section-header">Membership</h2>
            <div class="row membership-row card">
              <p class="membership-info">
                <label>ID</label>
                <br />
                {this.usersObj.isMember ? (
                  <span>{this.usersObj.memberID}</span>
                ) : (
                  "-"
                )}
              </p>
              <p class="membership-info">
                <label>TYPE</label>
                <br />
                {this.usersObj.isMember ? (
                  <span>{this.usersObj.memberType}</span>
                ) : (
                  "-"
                )}
              </p>
              <p class="membership-info">
                <label>START DATE</label>
                <br />
                {this.usersObj.isMember ? (
                  <span>
                    {new Date(this.usersObj.startDate)
                      .toString()
                      .substring(4, 15)}
                  </span>
                ) : (
                  "-"
                )}
              </p>
              <p class="membership-info">
                <label>END DATE</label>
                <br />
                {this.usersObj.isMember ? (
                  <span>
                    {this.usersObj.endDate
                      ? new Date(this.usersObj.endDate)
                          .toString()
                          .substring(4, 15)
                      : "-"}
                  </span>
                ) : (
                  "-"
                )}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  }
}
