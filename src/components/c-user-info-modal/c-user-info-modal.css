c-user-info-modal .modal-container {
  position: absolute;
  width: 55%;
  background: var(--bg-color);
  margin: 0em auto 10em 20%;
  padding: 1.5em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
}

c-user-info-modal .row {
  display: flex;
  justify-content: space-between;
}

c-user-info-modal .badge-row {
  margin-top: 0.5em;
  justify-content: flex-start;
}

c-user-info-modal .name,
.email,
.country,
.subtext,
.headline {
  margin: 0;
  padding: 0;
}

c-user-info-modal .name {
  color: var(--accent-color);
}

c-user-info-modal .email,
.country {
  margin-top: 0.25em;
}

c-user-info-modal .subtext {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

c-user-info-modal .section-heading {
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 0;
}

c-user-info-modal .membership-info label {
  font-size: 0.75em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
}

c-user-info-modal .membership-info {
  width: 20%;
  margin-bottom: 0;
  margin-top: 0;
}

c-user-info-modal .membership-info span {
  font-size: 0.9em;
}

c-user-info-modal h3 {
  color: rgba(0, 0, 0, 0.3);
}

c-user-info-modal c-paid-items-list .container {
  margin-bottom: 1em;
}

c-user-info-modal c-paid-items-list main {
  padding-top: 1em;
}

c-user-info-modal c-paid-items-list main .row {
  width: 100%;
}

c-user-info-modal c-paid-items-list main .paid-item-list-row {
  width: 95%;
}

c-user-info-modal c-paid-items-list main .paid-item-list-row .paid-item-list {
  width: 66%;
  margin-left: 0;
}

c-user-info-modal c-paid-items-list main .paid-item-list-row .button-container {
  width: 8%;
}

c-user-info-modal c-paid-items-list .seperator {
  border: 0;
  height: 1px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: none;
  margin-bottom: 0;
}

c-user-info-modal c-paid-items-list .summary-list {
  margin-top: 1em;
}

c-user-info-modal c-paid-items-list .payment-summary-row {
  margin-bottom: 0;
}

c-user-info-modal c-skel-line .skel-line {
  margin-bottom: 1em;
}

c-user-info-modal .info-section-header {
  margin-top: 3em;
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin-bottom: 0.5em;
}

c-user-info-modal .taxid-title {
  background: rgba(0, 0, 0, 0.1);
  padding: 0.1em 0.5em;
  border-radius: 0.25em;
}

c-user-info-modal .card {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
}
/* 
c-user-info-modal c-paid-items-list main .hseperator {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border: 0;
} */
