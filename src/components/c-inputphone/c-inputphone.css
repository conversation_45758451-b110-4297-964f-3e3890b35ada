c-inputphone select {
  border: 0;
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: var(--site-padding);
  width: 100%;
  outline: none;
  border-radius: 0;
  border-radius: 0.25em 0.25em 0 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 0.9em;
  margin-bottom: 0;
  background: transparent;
  background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 20 30' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: 98%;
  background-position-y: 12px;
  transition: all 0.15s ease-in;
  /* color: rgba(0, 0, 0, 0.4); */
}

c-inputphone select:focus {
  /* outline: var(--accent-color) solid 1px; */
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-inputphone select:hover {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-inputphone select::-ms-expand {
  display: none;
}

c-inputphone select:hover {
  cursor: pointer;
}

.phonenumber-input-box {
  outline: none;
  width: 100%;
  box-sizing: border-box;
  padding: var(--site-padding);
  font-size: 0.9em;
  border: var(--site-border);
  border-radius: 0 0 0.25em 0.25em;
  border-top: 0;
  margin-top: 0;
}
.phonenumber-input-box::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

.disabled {
  opacity: 0.3;
}

.disable-option {
  pointer-events: none;
  opacity: 0.6;
}
