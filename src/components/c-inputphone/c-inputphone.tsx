import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";
import Store from "../../global/store";
import { countries } from "../../global/app";
import { getCountryFromIsdCode } from "../../utils";

@Component({
  tag: "c-inputphone",
  styleUrl: "c-inputphone.css",
})
export class CInputphone {
  selectEl!: HTMLSelectElement;

  @Prop() isDisabled: boolean;
  @Prop() numpadPlaceholder: string;
  @Prop() dropdownValue: string;
  @Prop() inputboxValue: string;
  @Prop() selectValue: string;
  @Event({
    eventName: "phoneInputEvent",
    bubbles: true,
  })
  phoneInputEventEmitter: EventEmitter;

  private isdCode: string = "";
  private mobileCountry: string = "";
  private mobileNumber: string = "";

  handleSelectInput(event) {
    if (event.target.value === "") {
      this.isdCode = "";
      this.mobileCountry = "";
    } else {
      this.isdCode = event.target.value.trim();
      this.mobileCountry = getCountryFromIsdCode(event.target.value.trim());
    }
    this.emitPhoneDetails();
  }

  handleInput(event) {
    Store.setMobileNumber(event.target.value.trim());
    if (event.target.value)
      if (event.target.value.trim())
        this.mobileNumber = event.target.value.trim();
    this.emitPhoneDetails();
  }

  emitPhoneDetails() {
    this.phoneInputEventEmitter.emit({
      isdCode: this.isdCode,
      mobileCountry: this.mobileCountry,
      mobileNumber: this.mobileNumber,
    });
  }

  componentWillLoad() {
    this.isdCode = `${countries[0].isdCode}`;
    this.mobileCountry = countries[0].name;
    this.mobileNumber = this.inputboxValue;
  }

  componentDidLoad() {
    this.selectEl.value = this.selectValue;
  }

  render() {
    return (
      <div class="inputphone-container">
        <select
          onInput={(event) => this.handleSelectInput(event)}
          disabled={this.isDisabled}
          ref={(el) => (this.selectEl = el as HTMLSelectElement)}
        >
          {countries.map((country) => (
            <option
              value={country.isdCode}
              disabled={country.isdCode.length === 0 ? true : false}
            >
              <span>{country.name}</span>
              &nbsp;
              {country.isdCode ? `(+${country.isdCode})` : ``}
            </option>
          ))}
        </select>
        <input
          class={
            this.isDisabled
              ? "phonenumber-input-box disabled"
              : "phonenumber-input-box"
          }
          inputmode="numeric"
          pattern="[0-9]*"
          type="text"
          onInput={(event) => this.handleInput(event)}
          disabled={this.isDisabled}
          placeholder={this.numpadPlaceholder}
          value={this.inputboxValue}
        />
      </div>
    );
  }
}
