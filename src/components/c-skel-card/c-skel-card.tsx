import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-skel-card",
  styleUrl: "c-skel-card.css",
})
export class CSkelCard {
  @Prop() container: boolean = true;
  render() {
    return (
      <div class={this.container ? "container" : ""}>
        <c-skel-line color="gray" width={100}></c-skel-line>
        <br />
        <c-skel-line color="gray" width={75}></c-skel-line>
        <br />
        <c-skel-line color="gray" width={50}></c-skel-line>
      </div>
    );
  }
}
