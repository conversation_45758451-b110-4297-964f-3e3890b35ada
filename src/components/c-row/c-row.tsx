import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-row",
  styleUrl: "c-row.css",
})
export class CRow {
  @Prop() type: string;

  render() {
    if (this.type === "wrap") {
      return (
        <div class="row-wrap-container">
          <slot />
        </div>
      );
    } else if (this.type === "oauthProfileDetails") {
      return (
        <div class="oauth-profile-details">
          <slot />
        </div>
      );
    } else {
      return (
        <div class="row-container">
          <slot />
        </div>
      );
    }
  }
}
