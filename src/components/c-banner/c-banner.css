c-banner .banner-container {
  box-sizing: border-box;
  padding: 0.75em 1.5em;
  width: 100%;
}

c-banner .banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 0;
  margin-right: 0;
  font-size: 0.95em;
}

c-banner c-text .subtext {
  margin-bottom: 0.3em;
}

c-banner c-button .danger-ghost {
  padding: 0.5em;
  font-size: 0.95em;
}

c-banner c-textbox input {
  border: 1px solid rgba(0, 0, 0, 0.3);
  padding: 0.5em;
  font-size: 0.95em;
}

c-banner .disable {
  opacity: 0.3;
  pointer-events: none;
}

/*-------------
Position
---------------*/
c-banner .position--bottom {
  position: fixed;
  background: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  bottom: 0;
  left: 0;
  z-index: 9999999;
}

/*-------------
Themes
---------------*/
c-banner .theme--danger {
  color: var(--accent-pink-darker);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: #ffebf4;
}

c-banner .theme--default {
  color: var(--accent-color);
  border: 1px solid var(--accent-color-bg-lighter);
  background: var(--accent-color-bg-lightest);
  border-radius: 0.25em;
}

c-banner .show-on-desktop {
  display: flex;
  justify-content: space-between;
}

c-banner .hide-on-desktop {
  display: none;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-banner c-textbox input {
    margin-bottom: 1em;
  }

  c-banner .hide-on-mobile {
    display: none;
  }

  c-banner .show-on-mobile {
    display: block;
  }

  c-banner .banner-content {
    display: flex;
    margin-top: 1em;
    align-items: baseline;
  }

  c-banner .banner-content c-textbox input {
    width: 150px;
  }

  c-banner .banner-content c-button button {
    width: 110px;
  }

  c-banner .banner-container {
    padding: 0.75em 1.5em 1em 1.5em;
  }
}
