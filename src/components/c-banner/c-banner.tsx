import {
  Component,
  Prop,
  State,
  Listen,
  FunctionalComponent,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-banner",
  styleUrl: "c-banner.css",
})
export class CBanner {
  @Prop() isVisible: boolean;
  @Prop() variant: string = "default";
  @Prop() theme: string = "default";
  @Prop() position: string = "default";

  @State() isBtn1Disabled: boolean = false;
  @State() isBtn1InAction: boolean = false;
  @State() isTextBox1Disabled: boolean = false;

  private cssClasses: string = "";
  private verificationCode_Email: string = "";

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "sendEmailVerificationCode") {
      this.isBtn1InAction = true;
      this.sendEmailVerificationCode();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "emailVerificationCode") {
      this.verificationCode_Email = e.detail.value;
      this.checkEmailVerificationCode();
    }
  }

  checkEmailVerificationCode() {
    if (this.verificationCode_Email.length === 4) {
      this.isTextBox1Disabled = true;
      this.verifyEmailVerificationCode();
    }
  }

  componentWillLoad() {
    this.generateCssClasses();
  }

  generateCssClasses() {
    this.cssClasses = "banner-container";
    if (this.isVisible) {
      this.cssClasses = `${this.cssClasses} isVisible`;
    } else {
      this.cssClasses = `${this.cssClasses} isNotVisible`;
    }

    if (this.theme === "danger") {
      this.cssClasses = `${this.cssClasses} theme--danger`;
    } else if (this.theme === "default") {
      this.cssClasses = `${this.cssClasses} theme--default`;
    }

    if (this.position === "bottom") {
      this.cssClasses = `${this.cssClasses} position--bottom`;
    }
  }

  sendEmailVerificationCode() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/sendemailverificationcode`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          state.notificationType = "error";
          state.notificationMessage = "Could not send verification code";
          state.notificationPosition = "topCentre";
          state.isNotificationActive = true;
        } else if (response.data.status === "Success") {
          state.notificationType = "success";
          state.notificationMessage = `Verification code sent to ${state.email}`;
          state.notificationPosition = "topCentre";
          state.isNotificationActive = true;
        }
        this.isBtn1InAction = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  verifyEmailVerificationCode() {
    let verificationCode: number = parseInt(this.verificationCode_Email);
    let payload = {
      emailVerificationCode: verificationCode,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/verifyemail`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          state.notificationType = "error";
          state.notificationMessage = response.data.msg;
          state.notificationPosition = "topCentre";
          state.isNotificationActive = true;
        } else if (response.data.status === "Success") {
          state.notificationType = "success";
          state.notificationMessage = `${state.email} is now verified`;
          state.notificationPosition = "topCentre";
          state.isNotificationActive = true;
          state.isEmailVerified = true;
        }
        this.isTextBox1Disabled = false;
      })
      .catch((error) => {
        alert(error);
      });
  }

  VerifyEmail: FunctionalComponent = () => (
    <div class={`${this.cssClasses}`}>
      <div class="hide-on-mobile show-on-desktop">
        <div class="banner-content">
          <c-text>
            {/* To verify <strong>{state.email}</strong> kindly enter the code sent
            to your email */}
            Verify your email
          </c-text>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <c-textbox
            input-type="text"
            name="emailVerificationCode"
            placeholder="Enter 4-digit code"
            isDisabled={this.isTextBox1Disabled}
            isTextboxFilled={this.isTextBox1Disabled}
          ></c-textbox>
        </div>
        <c-button
          name="sendEmailVerificationCode"
          type="ghost"
          theme="danger"
          label="Re-send code"
          isInActionLabel="Sending.."
          isInAction={this.isBtn1InAction}
        ></c-button>
      </div>
      <div class="hide-on-desktop show-on-mobile">
        <c-text>
          {/* To verify <strong>{state.email}</strong> kindly enter the code sent
            to your email */}
          Verify your email
        </c-text>
        <div class="banner-content">
          <c-textbox
            input-type="text"
            name="emailVerificationCode"
            placeholder="Enter 4-digit code"
            isDisabled={this.isTextBox1Disabled}
            isTextboxFilled={this.isTextBox1Disabled}
          ></c-textbox>
          <c-button
            name="sendEmailVerificationCode"
            type="ghost"
            theme="danger"
            label="Re-send code"
            isInActionLabel="Sending.."
            isInAction={this.isBtn1InAction}
          ></c-button>
        </div>
      </div>
    </div>
  );

  DefaultBanner: FunctionalComponent = () => (
    <div class={this.cssClasses}>
      <slot />
    </div>
  );

  render() {
    if (this.variant === "verifyEmail") {
      return <this.VerifyEmail></this.VerifyEmail>;
    } else {
      return <this.DefaultBanner></this.DefaultBanner>;
    }
  }
}
