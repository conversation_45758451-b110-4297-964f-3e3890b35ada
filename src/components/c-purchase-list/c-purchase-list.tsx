import { Component, Prop, State, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-purchase-list",
  styleUrl: "c-purchase-list.css",
})
export class CPurchaseList {
  @Prop() history: RouterHistory;

  @State() isFetching: boolean = true;

  componentDidLoad() {
    this.getCart();
  }

  getCart() {
    let payload = {
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.cartItems = response.data.payload.cartItems;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div>
        <h1>Checkout</h1>
        <p class="one-line">
          By paying, you accept our{" "}
          <c-text-link
            url="https://indiahci.org/cancellation-refund.html"
            label="cancellation & refund policy"
          ></c-text-link>
        </p>

        {this.isFetching ? (
          <div>
            <c-skel-line color="gray" width={50}></c-skel-line>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
            <br />
            <c-skel-line color="gray" width={50}></c-skel-line>
          </div>
        ) : (
          state.cartItems.map((item) => (
            <c-purchased-item
              ticket-id={item.ticketID}
              heading={item.title}
              subtitle={item.subTitle}
              currency={item.currency}
              price={item.price}
              tier={item.tier}
            ></c-purchased-item>
          ))
        )}
      </div>
    );
  }
}

injectHistory(CPurchaseList);
