c-page .default-page {
  width: 98%;
  box-sizing: border-box;
  max-width: 1400px;
  margin: 0 auto;
  margin-top: 6em;
}

c-page .auth-page {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: space-between;
}

c-page .auth-page-disabled {
  display: flex;
  justify-content: space-around;
}

c-page .post-oauth {
  width: 300px;
  margin: 0 auto;
  padding: 2em 1.5em;
  border-radius: 0.5em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: white;
  margin-top: 4em;
}

c-page .push-down {
  margin-top: 2em;
}

@media only screen and (max-width: 768px) {
  c-page .default-page {
    width: 100%;
    max-width: 350px;
    margin-top: 1em;
  }

  c-page .post-oauth {
    margin-top: 2em;
    padding: 1em;
  }
}
