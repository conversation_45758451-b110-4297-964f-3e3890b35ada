import { Component, Prop, Host, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-page",
  styleUrl: "c-page.css",
})
export class CPage {
  @Prop() type: string;

  private cssClasses: string;

  componentWillLoad() {
    this.generateCssClasses();
  }

  generateCssClasses() {
    if (this.type === "auth") {
      this.cssClasses = "auth-page";
    } else if (this.type === "authDisabled") {
      this.cssClasses = "auth-page-disabled";
    } else if (this.type === "postOauth") {
      this.cssClasses = "post-oauth";
    } else {
      this.cssClasses = "default-page";
    }
  }

  render() {
    return (
      <Host>
        <c-notification isActive={state.isNotificationActive}></c-notification>
        <div class={this.cssClasses}>
          <slot />
        </div>
      </Host>
    );
  }
}
