import {
  Component,
  State,
  Event,
  EventEmitter,
  Listen,
  h,
} from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-section-get-member-discount",
  styleUrl: "c-section-get-member-discount.css",
})
export class CSectionGetMemberDiscount {
  private isApplyingDiscount: boolean = false;
  @State() isInputboxDisabled: boolean = false;
  @State() isApplyDiscountBtnDisabled: boolean = true;

  @Event({
    eventName: "member-discount-applied",
    bubbles: true,
  })
  memberDiscountApplied: EventEmitter;

  @Listen("apply-member-discount")
  applyMemberDiscountHandler() {
    let payload = {
      membershipID: state.submittedID,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/is-member`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(`${response.data.status}: ${response.data.msg}`);
        } else if (response.data.status === "Success") {
          this.getCart();
          this.memberDiscountApplied.emit();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }
  updateTickets() {
    let payload = {
      type: "full-conference",
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getticketdetails`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
        } else if (response.data.status === "Success") {
          state.fullConferenceTicketID = response.data.payload.ticketID;
          state.fullConferenceTicketSubTitle = response.data.payload.subTitle;
          state.fullConferenceTicketPrice = response.data.payload.price;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  getCart() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/cart`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.cartItems = response.data.payload.cartItems;
          state.cartTotal = response.data.payload.cartTotal;
          this.updateTickets();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }
  // changeComponentState(lastEventName: string) {
  //   let membershipDetails = Store.getMembershipId();
  //   if (lastEventName === "input-event") {
  //     let { error } = isMembershipIdValid(membershipDetails);
  //     if (error) {
  //       // STATE: Login inputs invalid
  //       this.isApplyDiscountBtnDisabled = true;
  //     } else {
  //       // STATE: Login inputs valid
  //       this.isApplyDiscountBtnDisabled = false;
  //     }
  //   } else if (lastEventName === "apply-membership-click-event") {
  //     // STATE: Submitting
  //     this.isApplyingDiscount = true;
  //     this.isInputboxDisabled = true;
  //     this.isApplyDiscountBtnDisabled = true;
  //   } else if (
  //     lastEventName === "apply-discount-failed" ||
  //     "apply-discount-success" ||
  //     "apply-discount-error"
  //   ) {
  //     this.isApplyingDiscount = false;
  //     this.isInputboxDisabled = false;
  //     this.isApplyDiscountBtnDisabled = true;
  //   }
  // }
  render() {
    return [
      <div class="container">
        <h2 class="membership-heading">Get your HCIPAI membership discount</h2>
        <div class="email-btn-band">
          {" "}
          <c-inputbox
            class="hide-inputbox"
            type="text"
            name="membershipid"
            placeholder="Enter your HCIPAI memberID"
            is-disabled={this.isInputboxDisabled}
          ></c-inputbox>
          <c-btn
            name="applymemberdiscount"
            label="Get Discount"
            action-label="Applying.."
            is-in-action={this.isApplyingDiscount}
            is-disabled={state.submittedID.length > 5 ? false : true}
          ></c-btn>
        </div>
        <span class="support-text">
          Forgot your membership Id? Kindly mail{" "}
          <a class="email-link" href="mailto:<EMAIL>">
            <EMAIL>
          </a>
        </span>
      </div>,
    ];
  }
}
