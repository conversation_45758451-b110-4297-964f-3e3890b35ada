c-section-get-member-discount {
  width: 100%;
}

c-section-get-member-discount .container {
  background: rgb(89, 49, 150);
  background: linear-gradient(
    135deg,
    rgba(89, 49, 150, 1) 0%,
    rgba(106, 70, 161, 1) 35%,
    rgba(122, 90, 171, 1) 100%
  );
  padding: 1em;
  border-radius: 0.4em;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05);
}

c-section-get-member-discount .seperator {
  margin: 2em 0 2em 0;
}

.support-text {
  color: var(--accent-color-bg-light);
  margin-bottom: 0;
  font-size: 0.8em;
}

.email-link {
  text-decoration: none;
  font-weight: 700;
  color: var(--accent-color-bg-lighter);
}

.membership-heading {
  color: var(--accent-color-bg-lighter);
  margin-top: 0;
  font-size: 0.9em;
  font-weight: 400;
}

.email-btn-band {
  display: flex;
  width: 90%;
  justify-content: space-between;
  margin-bottom: 0.5em;
  align-items: center;
}

.email-btn-band c-inputbox {
  width: 60%;
  border-radius: 0.25em 0 0 0.25em;
}

.email-btn-band c-inputbox input {
  margin-bottom: 0;
}

c-section-get-member-discount .email-btn-band .inputbox {
  border-radius: 0.25em 0 0 0.25em;
  padding: 0.7em 1em;
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--accent-color-bg-lightest);
}

.email-btn-band c-btn {
  width: 40%;
}

.email-btn-band c-btn button {
  border-radius: 0 0.25em 0.25em 0;
  outline: none;
  color: var(--accent-color-bg-lightest);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: none;
  border-left: 0;
}

.email-btn-band c-btn button:hover {
  background: rgba(255, 255, 255, 0.1);
}

c-section-get-member-discount ::-webkit-input-placeholder {
  color: var(--accent-color-bg-lighter);
}

c-section-get-member-discount :-ms-input-placeholder {
  color: var(--accent-color-bg-lighter);
}

c-section-get-member-discount ::placeholder {
  color: var(--accent-color-bg-lighter);
}
