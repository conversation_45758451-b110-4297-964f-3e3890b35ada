import { Component, Prop, Listen, State, h } from "@stencil/core";

@Component({
  tag: "c-alert",
  styleUrl: "c-alert.css",
})
export class CAlert {
  @Prop() alertId: string;
  @Prop() alertType: string;
  @Prop() firstNameIssuer: string;
  @Prop() lastNameIssuer: string;
  @Prop() emailIssuer: string;
  @Prop() firstNameResolver: string;
  @Prop() lastNameResolver: string;
  @Prop() emailResolver: string;
  @Prop() issuedOn: string;
  @Prop() resolvedOn: string;
  @Prop() propOne: string;
  @Prop() propTwo: string;
  @Prop() propThree: string;
  @Prop() isActive: boolean;
  @State() isVerifyBtnDisabled: boolean = false;
  @State() isVerifyBtnActive: boolean = false;

  @Listen("resolve-alert")
  resolveAlerts() {
    this.isVerifyBtnActive = true;
    this.isVerifyBtnDisabled = true;
  }

  render() {
    return (
      <div class="alert-container">
        {this.isActive ? (
          ""
        ) : (
          <div class="card-lower">
            <p>
              {this.alertType === "Bank Transfer" ? "Verified by" : "Closed by"}{" "}
              <strong>
                {this.firstNameResolver} {this.lastNameResolver}
              </strong>{" "}
              ({this.emailResolver}) on{" "}
              {new Date(this.resolvedOn).toString().substring(4, 15)}
            </p>
          </div>
        )}
        <div class={this.isActive ? "card" : "card card-closed"}>
          <div class="card-upper">
            <div class="row-item-1">
              <p class="name">
                {this.firstNameIssuer} {this.lastNameIssuer}
              </p>
              <c-text-link
                url={`mailto:${this.emailIssuer}`}
                label={this.emailIssuer}
              ></c-text-link>
            </div>
            <div class="row-item-2">
              <p class="issue-date">
                Created on {new Date(this.issuedOn).toString().substring(4, 15)}
              </p>
              {this.alertType === "Bank Transfer" ? (
                this.isActive ? (
                  <c-badge label="Bank Transfer" color="pink"></c-badge>
                ) : (
                  <c-badge label="Bank Transfer" color="pink"></c-badge>
                )
              ) : (
                ""
              )}
              {this.alertType === "Razorpay Payment Failure" ? (
                this.isActive ? (
                  <c-badge
                    label="Razorpay Payment Failure"
                    color="pink"
                  ></c-badge>
                ) : (
                  <c-badge
                    label="Razorpay Payment Failure"
                    color="pink"
                  ></c-badge>
                )
              ) : (
                ""
              )}
              {this.alertType === "Partial Registration" ? (
                this.isActive ? (
                  <c-badge
                    label="Incomplete Registration"
                    color="pink"
                  ></c-badge>
                ) : (
                  <c-badge
                    label="Incomplete Registration"
                    color="pink"
                  ></c-badge>
                )
              ) : (
                ""
              )}
            </div>
            <div class="row-item-3">
              {this.alertType === "Partial Registration" ? (
                ""
              ) : (
                <p>
                  <span class="alert-label">TX CODE</span>
                  <br />
                  <span class="alert-info">{this.propOne}</span>
                </p>
              )}
            </div>
            <div class="row-item-4">
              {this.alertType === "Partial Registration" ? (
                ""
              ) : (
                <p>
                  <span class="alert-label">AMOUNT</span>
                  <br />
                  <span class="alert-info">₹{this.propTwo}</span>
                </p>
              )}
            </div>
            {this.isActive ? (
              <div class="row-item-5">
                <c-btn
                  name="resolveAlert"
                  label={
                    this.alertType === "Bank Transfer" ? "Verify" : "Close"
                  }
                  action-label=""
                  value={this.alertId}
                  type="ghost"
                  is-in-action={this.isVerifyBtnActive}
                  is-disabled={this.isVerifyBtnDisabled}
                ></c-btn>
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
      </div>
    );
  }
}
