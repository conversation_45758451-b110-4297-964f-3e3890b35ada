c-alert .alert-container {
  margin-bottom: 1.5em;
}

c-alert .card {
  background: white;
  padding: 1em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  align-items: baseline;
}

c-alert .card-upper {
  display: flex;
  justify-content: space-between;
}

c-alert .row-item-1 {
  width: 25%;
  /* background: yellow; */
}

c-alert .row-item-2 {
  width: 25%;
  /* background: pink; */
}

c-alert .row-item-3 {
  width: 20%;
  /* background: cyan; */
}

c-alert .row-item-4 {
  width: 10%;
  /* background: yellow; */
}

c-alert .row-item-5 {
  width: 10%;
  /* background: pink; */
}

c-alert .row-item-5 c-btn button {
  font-size: 0.8em;
}

c-alert .row-item-2 c-badge p {
  width: 50%;
}

c-alert .card-lower {
  /* background: rgba(238, 234, 245, 0.5); */
  background: rgba(238, 234, 245, 0.5);
  font-size: 0.8em;
  color: var(--accent-color);
  border-radius: 0.25em 0.25em 0 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 0;
}

c-alert .card-lower p {
  margin: 0;
  padding: 0.5em 1em;
  border-radius: 0 0 0.25em 0.25em;
}

c-alert .card p {
  margin: 0;
}

c-alert .label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
}

c-alert.row-item-1 {
  /* background: pink; */
  width: 20%;
}

c-alert .name {
  font-weight: 700;
  font-size: 0.9em;
}

c-alert .hspacer {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

c-alert .member-info {
  font-size: 0.8em;
}

c-alert c-text-link a {
  font-size: 0.8em;
}

c-alert .issue-date {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
  padding-bottom: 0.4em;
}

c-alert .alert-info {
  font-size: 0.8em;
}

c-alert .alert-label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
}

c-alert .card-closed {
  border-radius: 0 0 0.25em 0.25em;
  border-top: 0;
}
