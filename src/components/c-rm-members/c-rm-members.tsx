import { Component, State, Listen, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-members",
  styleUrl: "c-rm-members.css",
})
export class CRmMembers {
  @State() isFetching: boolean = true;
  private memberList: any;
  private memberCount: any;
  private memberType: string = "";
  private annualMembers: number = 0;
  private lifetimeMembers: number = 0;

  @Listen("dropdown-input-event")
  filterHandler(event) {
    this.memberType = event.detail.value;
    this.isFetching = true;
    this.fetchData();
  }

  @Listen("download-legacy-member-data")
  downloadLegacyMemberData() {
    if (this.memberType.length > 0) {
      window.open(`${state.baseUrl}/downloadmembers/${this.memberType}`);
    } else {
      window.open(`${state.baseUrl}/downloadmembers`);
    }
  }

  componentWillLoad() {
    state.isMobileDashboardOptionsVisible = false;
  }

  componentDidLoad() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getmembers`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.memberList = response.data.payload.memberList;
          this.memberCount = response.data.payload.memberCount;
          this.annualMembers = response.data.payload.annualMemberCount;
          this.lifetimeMembers = response.data.payload.lifetimeMemberCount;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  fetchData() {
    let payload = {
      memberType: this.memberType,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getmembers`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.memberList = "";
          this.memberList = response.data.payload.memberList;
          this.memberCount = response.data.payload.memberCount;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="member-container">
        {state.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile-control-bar">
            <c-control-bar
              name="members"
              count={!this.isFetching && this.memberCount}
            ></c-control-bar>
          </div>
        )}
        <div class="show-on-desktop-control-bar">
          <c-control-bar
            name="members"
            count={!this.isFetching && this.memberCount}
          ></c-control-bar>
        </div>

        {this.isFetching ? (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : (
          <div>
            <c-card>
              <div class="member-header show-on-desktop">
                <c-row>
                  <div class="row__primary-container">
                    <e-text>
                      <ion-icon name="ribbon-outline"></ion-icon>
                      &nbsp;&nbsp;Total members
                      <br />
                      <span class="overview-number">
                        {this.annualMembers + this.lifetimeMembers}
                      </span>
                    </e-text>
                  </div>
                  <div class="vertical-divider"></div>
                  <e-text>
                    <ion-icon name="hourglass-outline"></ion-icon>
                    &nbsp;&nbsp;Annual members
                    <br />
                    <span class="overview-number">{this.annualMembers}</span>
                  </e-text>
                  <e-text>
                    <ion-icon name="pulse-outline"></ion-icon>
                    &nbsp;&nbsp;Lifetime members <br />
                    <span class="overview-number">{this.lifetimeMembers}</span>
                  </e-text>
                </c-row>
              </div>
              <div class="member-header show-on-mobile">
                <div class="member-header_row row__primary-container">
                  <c-row>
                    <c-text>
                      <ion-icon name="ribbon-outline"></ion-icon>
                      &nbsp;&nbsp;Total members
                    </c-text>
                    <c-text>{this.annualMembers + this.lifetimeMembers}</c-text>
                  </c-row>
                </div>
                <div class="member-header_row">
                  <c-row>
                    <c-text>
                      <ion-icon name="hourglass-outline"></ion-icon>
                      &nbsp;&nbsp;Annual members
                    </c-text>
                    <c-text>{this.annualMembers}</c-text>
                  </c-row>
                </div>
                <div class="member-header_row">
                  <c-row>
                    <c-text>
                      <ion-icon name="pulse-outline"></ion-icon>
                      &nbsp;&nbsp;Lifetime members
                    </c-text>
                    <c-text>{this.lifetimeMembers}</c-text>
                  </c-row>
                </div>
              </div>
            </c-card>
            {this.memberList.length > 0 ? (
              <c-list
                type="dashboardMemberInfoList"
                listItemsAsString={JSON.stringify(this.memberList)}
              ></c-list>
            ) : (
              <c-text>No members found</c-text>
            )}
          </div>
        )}
      </div>
    );
  }
}
