c-rm-members .member-header {
  text-align: center;
}

c-rm-members .row__primary-container {
  color: var(--accent-color);
}

c-rm-members .overview-number {
  font-size: 1.5em;
}

c-rm-members .vertical-divider {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

c-rm-members c-card .basic-card-container {
  padding: 1em;
  border-radius: 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5em;
}

c-rm-members .overview-subtext {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 1em;
}

c-rm-members .card-header {
  margin: 0;
  padding: 0;
  margin-bottom: 1em;
  padding-bottom: 0.75em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

/* c-rm-members .hide-on-mobile {
  display: block;
} */

c-rm-members .show-on-mobile {
  display: none;
}

c-rm-members .show-on-desktop {
  display: block;
}

c-rm-members .show-on-mobile-control-bar {
  display: none;
}

c-rm-members .show-on-desktop-control-bar {
  display: block;
}

@media only screen and (max-width: 768px) {
  c-rm-members .member-header c-row .row-container .vertical-divider {
    display: none;
  }

  c-rm-members .row__primary-container {
    padding-bottom: 0.75em;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  c-rm-members .show-on-mobile .row__primary-container {
    color: var(--accent-color);
  }

  c-rm-members .member-header_row {
    padding: 0.75em 0;
  }

  c-rm-members c-control-bar .control-bar-container {
    width: 90vw;
  }

  c-rm-members .show-on-mobile-control-bar {
    display: flex;
  }

  c-rm-members .show-on-desktop-control-bar {
    display: none;
  }

  c-rm-members .show-on-mobile {
    display: block;
  }

  c-rm-members .show-on-desktop {
    display: none;
  }
}
