import { Component, Listen, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-alerts",
  styleUrl: "c-rm-alerts.css",
})
export class CRmAlerts {
  @State() isFetching: boolean = true;
  @State() isToastActive: boolean = false;
  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;

  private alertArr: any;
  private alertCount: any;
  private filterObj = {
    alertType: "",
    alertStatus: "",
    eventCode: state.eventCodeForMonitoring,
  };

  componentWillLoad() {
    state.isMobileDashboardOptionsVisible = false;
  }

  componentDidLoad() {
    this.fetchData();
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "cancelIDVerification") {
      this.cancelIDVerification(e.detail.value);
    }
  }

  cancelIDVerification(alertId: string) {
    let reason = prompt();
    if (!reason) {
      return;
    }
    let payload = {
      alertId: alertId,
      reason: reason,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/cancel-id-verification-alert`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.isFetching = true;
          this.showToast({
            type: response.data.status,
            label: response.data.msg,
          });
          this.fetchData();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  @Listen("dropdown-input-event")
  filterHandler(event) {
    this.filterObj[event.detail.filter] = event.detail.value;
    this.isFetching = true;
    this.fetchData();
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  @Listen("resolve-alert")
  resolveAlerts(event) {
    event.preventDefault();
    let payload = event.detail;
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/resolvealerts`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.isFetching = true;
          this.showToast({
            type: response.data.status,
            label: response.data.msg,
          });
          this.fetchData();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  fetchData() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getalerts`,
      data: this.filterObj,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.alertArr = "";
          this.alertCount = response.data.payload.totalAlerts;
          this.alertArr = response.data.payload.alertArr;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div>
        {this.isToastActive && (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        )}

        {state.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile">
            <c-control-bar
              name="alerts"
              count={!this.isFetching && this.alertCount}
            ></c-control-bar>
          </div>
        )}
        <div class="show-on-desktop">
          {" "}
          <c-control-bar
            name="alerts"
            count={!this.isFetching && this.alertCount}
          ></c-control-bar>
        </div>

        {this.isFetching ? (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : this.alertArr.length > 0 ? (
          <c-list
            type="dashboardAlertInfoList"
            listItemsAsString={JSON.stringify(this.alertArr)}
          ></c-list>
        ) : (
          <c-text>No alerts found</c-text>
        )}
      </div>
    );
  }
}
