c-coupon-details-list .list-header-container {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-coupon-details-list .list-container {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em 0em 0.25em 0.25em;
  padding: 0.25em 0 0.5em 0;
}

c-coupon-details-list .list-header-container .row label {
  color: rgba(0, 0, 0, 0.5);
}

c-coupon-details-list .row {
  display: flex;
  justify-content: space-between;
  padding: 0.5em 0.2em 0.5em 1em;
  border-radius: 0.25em 0.25em 0em 0em;
}

c-coupon-details-list .row-item {
  margin: 0;
  font-size: 0.8em;
}

c-coupon-details-list .row-item-targeted-1 {
  width: 22.5%;
}

c-coupon-details-list .row-item-targeted-2 {
  width: 25%;
}

c-coupon-details-list .row-item-targeted-3 {
  width: 12.5%;
}

c-coupon-details-list .row-item-targeted-4 {
  width: 15%;
}

c-coupon-details-list .row-item-generic-1 {
  width: 28%;
}

c-coupon-details-list .row-item-generic-2 {
  width: 28%;
}

c-coupon-details-list .row-item-generic-3 {
  width: 20%;
}

c-coupon-details-list .green-text {
  font-weight: 700;
  color: green;
}

c-coupon-details-list .red-text {
  color: red;
  font-weight: 700;
}

c-coupon-details-list .bubble {
  margin: 0;
  font-size: 0.8em;
  font-weight: 700;
  padding: 0.25em 0.25em 0.25em 0.5em;
  border-radius: 0.25em;
}

c-coupon-details-list .gold-bubble {
  font-size: 0.75em;
  background: var(--accent-golden-bg-lighter);
  color: var(--accent-golden-darker);
}

c-coupon-details-list .control-row {
  /* display: flex;
  align-items: center; */
  display: flex;
  justify-content: space-around;
  background: white;
  padding: 1em;
  margin-bottom: 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em 0.25em 0 0;
  border-bottom: 0;
  margin-top: 0;
}

c-coupon-details-list .inputbox {
  width: 150px;
  border-radius: 0.25em 0 0 0.25em;
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.8);
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-right: none;
}

c-coupon-details-list .inputbox:hover {
  border: 1px solid rgba(0, 0, 0, 0.5);
}

c-coupon-details-list ::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.9em;
}

c-coupon-details-list :-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.9em;
}

c-coupon-details-list ::placeholder {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.9em;
}

c-coupon-details-list .container {
  color: rgba(0, 0, 0, 0.5);
  margin-top: 1em;
}

c-coupon-details-list .control-row c-btn button {
  font-size: 0.8em;
  padding: 0.62em 1em;
  align-items: center;
}

c-coupon-details-list .hide {
  display: none;
}

c-coupon-details-list .email-stats {
  margin-bottom: 0.5em;
}

c-coupon-details-list .center-text {
  text-align: center;
  margin: 1em 0 1em 0;
}

c-coupon-details-list .generate-coupon-group c-btn button {
  font-size: 0.8em;
  padding: 0.62em 1em;
  border-radius: 0 0.25em 0.25em 0;
  width: 100px;
  border: 1px solid var(--accent-color);
}

c-coupon-details-list .rounded-top {
  border-radius: 0.25em 0.25em 0 0;
}

c-coupon-details-list .table-skel-container {
  padding: 1em;
  margin: 0.5em 0em;
}

c-coupon-details-list c-btn button {
  padding: 0;
}

c-coupon-details-list c-dropdown select {
  width: 120px;
  margin-right: 0;
  padding-right: 0;
}

c-coupon-details-list .mobile-only-ui {
  display: none;
}

c-coupon-details-list .desktop-only-ui {
  display: block;
  /* display: flex; */
  justify-content: space-between;
}

@media only screen and (max-width: 768px) {
  c-coupon-details-list .list-header-container {
    display: none;
  }

  c-coupon-details-list c-dropdown select {
    width: 100%;
  }

  c-coupon-details-list .inputbox {
    width: 60%;
  }

  c-coupon-details-list .generate-coupon-group c-btn button {
    width: 40%;
  }

  c-coupon-details-list .control-row {
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  c-coupon-details-list .row {
    display: block;
  }

  c-coupon-details-list .mobile-only-ui {
    display: block;
    padding: 1em;
  }

  c-coupon-details-list .mobile-only-ui .row {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1em 0;
  }

  c-coupon-details-list .mobile-only-ui .row:first-child {
    padding-top: 0;
  }

  c-coupon-details-list .mobile-only-ui .row:last-child {
    padding-bottom: 0;
    border-bottom: 0;
  }

  c-coupon-details-list .desktop-only-ui {
    display: none;
  }

  c-coupon-details-list .row-item {
    display: flex;
    width: 100%;
  }

  c-coupon-details-list .row-item-icon {
    margin-right: 0.75em;
    margin-top: 2px;
  }

  c-coupon-details-list .list-container {
    margin-top: 0em;
    padding: 0em;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.25em;
  }

  c-coupon-details-list .mobile-only-ui--restricted {
    border-top: 0;
  }

  c-coupon-details-list .list-container--emaillist {
    border-top: 0;
    border-radius: 0 0 0.25em 0.25em;
  }
}
