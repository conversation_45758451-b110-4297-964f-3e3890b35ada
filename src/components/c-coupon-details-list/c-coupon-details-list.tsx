import { Component, Listen, Prop, h, State } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

interface RedeemerSchema {
  redeemerId: string;
  name: {
    first: string;
    last: string;
  };
  email: string;
  code: string;
  isUsed: string;
  usedOn: string;
}

@Component({
  tag: "c-coupon-details-list",
  styleUrl: "c-coupon-details-list.css",
})
export class CCouponDetailsList {
  @Prop() couponId: string;
  @Prop() access: string;
  @Prop() redeemerListString: string;
  @Prop() issuedCount: number = 0;
  @Prop() usedCount: number = 0;

  @State() isFetching: boolean = false;
  @State() isEmailSubmitButtonActive: boolean = false;
  @State() isEmailSubmissionInProgress: boolean = false;
  @State() displayCount: number = 0;
  @State() openList: Array<Object> = [];
  @State() restrictedEmailList: Array<Object> = [];

  private redeemerListArray: Array<Object> = [];
  // private couponUsageOptions = [
  //   { label: "Usage", value: "" },
  //   { label: "Used", value: "used" },
  //   { label: "Not yet used", value: "notYetUsed" },
  // ];
  private couponUsageFilter: string = "";
  private emailForNewCouponCode: string = "";

  componentWillLoad() {
    if (this.access === "open") {
      this.redeemerListArray = JSON.parse(this.redeemerListString);
      this.openList = this.redeemerListArray;
      this.displayCount = this.usedCount;
    } else if (this.access === "emaillist") {
      this.redeemerListArray = JSON.parse(this.redeemerListString);
      this.restrictedEmailList = this.redeemerListArray;
      this.displayCount = this.issuedCount;
    }
  }

  @Listen("button-click") buttonClickHandler(event) {
    if (event.detail.name === "generateCouponByEmailBtn") {
      this.generateNewCouponForEmail(this.emailForNewCouponCode);
    } else if (event.detail.name === "deleteCouponCode") {
      this.deleteCouponCode(event.detail.value);
    } else if (event.detail.name === "downloadCouponEmails") {
      window.open(
        `${state.baseUrl}/downloadcouponemails/${this.couponId}/${this.couponUsageFilter}`
      );
    }
  }

  @Listen("input-event") inputHandler(event) {
    if (event.detail.name === "newCouponEmailInput") {
      let isEmailValid = this.validateEmail(event.detail.value);
      if (isEmailValid) {
        this.isEmailSubmitButtonActive = true;
        this.emailForNewCouponCode = event.detail.value.trim();
      } else {
        this.isEmailSubmitButtonActive = false;
      }
    }
  }

  @Listen("dropdown-input-event") dropDownHandler(event) {
    if (event.detail.filter === "couponUsageFilter") {
      this.couponUsageFilter = event.detail.value;
    }
    this.fetchData();
  }

  validateEmail(emailForNewCouponCode: string) {
    let re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(emailForNewCouponCode).toLowerCase());
  }

  fetchData() {
    this.isFetching = true;
    let payload = {
      couponId: this.couponId,
      redeemerUsage: this.couponUsageFilter,
    };

    axios({
      data: payload,
      method: "POST",
      baseURL: `${state.baseUrl}/getredeemerlist`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.restrictedEmailList = response.data.payload.updatedRedeemerList;
          this.displayCount = this.restrictedEmailList.length;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  generateNewCouponForEmail(emailForNewCouponCode: string) {
    this.isEmailSubmissionInProgress = true;
    this.isEmailSubmitButtonActive = false;

    let payload = {
      couponId: this.couponId,
      emailForNewCouponCode: emailForNewCouponCode,
    };

    axios({
      data: payload,
      method: "POST",
      baseURL: `${state.baseUrl}/createcouponcodeforemail`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchData();
        }
        this.isEmailSubmissionInProgress = false;
        this.isEmailSubmitButtonActive = true;
      })
      .catch((error) => {
        alert(error);
      });
  }

  deleteCouponCode(email: string) {
    let payload = {
      couponId: this.couponId,
      emailForNewCouponCode: email,
    };

    axios({
      data: payload,
      method: "POST",
      baseURL: `${state.baseUrl}/deletecouponcode`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchData();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        <p class="email-stats">
          Showing{" "}
          <strong>
            {this.displayCount}{" "}
            {this.access === "open" ? "redeemers" : "emails"}
          </strong>
        </p>{" "}
        <div class={`${this.access === "open" ? "hide" : "control-row"}`}>
          {/* {this.access === "emaillist" && (
            <c-dropdown
              name="couponUsageFilter"
              option-str={JSON.stringify(this.couponUsageOptions)}
            ></c-dropdown>
          )} */}

          {this.access === "emaillist" && (
            <div class="generate-coupon-group">
              <c-inputbox
                type="text"
                name="newCouponEmailInput"
                placeholder="Email"
                is-disabled={this.isEmailSubmissionInProgress ? true : false}
                value=""
              ></c-inputbox>
              <c-btn
                name="generateCouponByEmailBtn"
                label="Add to list"
                action-label=""
                value=""
                is-in-action={this.isEmailSubmissionInProgress ? true : false}
                is-disabled={this.isEmailSubmitButtonActive ? false : true}
              ></c-btn>
            </div>
          )}

          {/* {this.access === "emaillist" && (
            <c-btn
              name="downloadCouponEmails"
              label="Download .csv"
              action-label=""
              value=""
              type="ghost"
              is-in-action={false}
              is-disabled={false}
            ></c-btn>
          )} */}
        </div>
        <div
          class={`list-header-container ${
            this.access === "open" && "rounded-top"
          }`}
        >
          {this.access === "emaillist" && (
            <div class="row ">
              <label class="row-item-targeted-1">NAME</label>
              <label class="row-item-targeted-2">EMAIL</label>
              <label class="row-item-targeted-3">COUPON</label>
              <label class="row-item-targeted-4">USED ON</label>
              <label class="row-item-targeted-5"></label>
            </div>
          )}

          {this.access === "open" && (
            <div class="row">
              <label class="row-item-generic-1">NAME</label>
              <label class="row-item-generic-2">EMAIL</label>
              <label class="row-item-generic-3">USED ON</label>
            </div>
          )}
        </div>
        <div
          class={`list-container ${
            this.access === "emaillist" && "list-container--emaillist"
          }`}
        >
          {this.isFetching ? (
            <div class="table-skel-container">
              <c-skel-line color="gray" width={100}></c-skel-line>
              <br />
              <c-skel-line color="gray" width={100}></c-skel-line>
              <br />
              <c-skel-line color="gray" width={100}></c-skel-line>
            </div>
          ) : this.access === "open" ? (
            this.openList.length > 0 ? (
              <div>
                <div class="mobile-only-ui">
                  {this.openList.map((redeemer: RedeemerSchema) => (
                    <div class="row">
                      <p class="row-item row-item-generic-1">
                        <div class="row-item-icon">
                          <ion-icon name="person-outline"></ion-icon>
                        </div>
                        {redeemer.name.first} {redeemer.name.last}
                      </p>
                      <p class="row-item row-item-generic-2">
                        <div class="row-item-icon">
                          <ion-icon name="mail-outline"></ion-icon>
                        </div>
                        <c-text-link
                          url={`mailto:${redeemer.email}`}
                          label={`${redeemer.email}`}
                        ></c-text-link>
                      </p>
                      {redeemer.usedOn.length > 0 ? (
                        <p class="row-item row-item-generic-3">
                          <div class="row-item-icon">
                            <ion-icon name="calendar-clear-outline"></ion-icon>
                          </div>
                          {new Date(redeemer.usedOn)
                            .toString()
                            .substring(4, 15)}
                        </p>
                      ) : (
                        <p class="row-item row-item-generic-3">-</p>
                      )}
                    </div>
                  ))}
                </div>
                <div class="desktop-only-ui">
                  {this.openList.map((redeemer: RedeemerSchema) => (
                    <div class="row">
                      <p class="row-item row-item-generic-1">
                        {redeemer.name.first} {redeemer.name.last}
                      </p>
                      <p class="row-item row-item-generic-2">
                        <c-text-link
                          url={`mailto:${redeemer.email}`}
                          label={`${redeemer.email}`}
                        ></c-text-link>
                      </p>
                      {redeemer.usedOn.length > 0 ? (
                        <p class="row-item row-item-generic-3">
                          {new Date(redeemer.usedOn)
                            .toString()
                            .substring(4, 15)}
                        </p>
                      ) : (
                        <p class="row-item row-item-generic-3">-</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p class="center-text">No one has redeemed this coupon yet</p>
            )
          ) : this.access === "emaillist" &&
            this.restrictedEmailList.length > 0 ? (
            <div>
              <div class="mobile-only-ui">
                {this.restrictedEmailList.map((redeemer: RedeemerSchema) => (
                  <div class="row">
                    {redeemer.name.first.length > 0 ||
                    redeemer.name.last.length > 0 ? (
                      <p class="row-item row-item-targeted-1">
                        <div class="row-item-icon">
                          <ion-icon name="person-outline"></ion-icon>
                        </div>
                        {redeemer.name.first} {redeemer.name.last}
                      </p>
                    ) : (
                      <p class="row-item row-item-targeted-1">
                        <div class="row-item-icon">
                          <ion-icon name="person-outline"></ion-icon>
                        </div>
                        -
                      </p>
                    )}
                    <p class="row-item row-item-targeted-2">
                      <div class="row-item-icon">
                        <ion-icon name="mail-outline"></ion-icon>
                      </div>
                      <c-text-link
                        url={`mailto:${redeemer.email}`}
                        label={`${redeemer.email}`}
                      ></c-text-link>
                    </p>
                    <p class="row-item row-item-targeted-3 ">
                      <div class="row-item-icon">
                        <ion-icon name="ticket-outline"></ion-icon>
                      </div>
                      <span class="bubble gold-bubble">{redeemer.code}</span>
                    </p>

                    {redeemer.usedOn.length > 0 ? (
                      <p class="row-item row-item-targeted-4">
                        <div class="row-item-icon">
                          <ion-icon name="calendar-outline"></ion-icon>
                        </div>
                        {new Date(redeemer.usedOn).toString().substring(4, 15)}
                      </p>
                    ) : (
                      <p class="row-item row-item-targeted-4">
                        <div class="row-item-icon">
                          <ion-icon name="calendar-outline"></ion-icon>
                        </div>
                        -
                      </p>
                    )}
                    {/* <div class="delete-btn">
                      <c-btn
                        name="deleteCouponCode"
                        label=""
                        action-label=""
                        value={redeemer.email}
                        type="icon-only-ghost"
                        is-in-action={false}
                        is-disabled={false}
                        icon={true}
                        icon-url="https://res.cloudinary.com/layerpark/image/upload/v1634385289/ui-icons/delete/delete-normal_pgwcqb.svg"
                      ></c-btn>
                    </div> */}
                  </div>
                ))}
              </div>
              <div class="desktop-only-ui">
                {this.restrictedEmailList.map((redeemer: RedeemerSchema) => (
                  <div class="row">
                    {redeemer.name.first.length > 0 ||
                    redeemer.name.last.length > 0 ? (
                      <p class="row-item row-item-targeted-1">
                        {redeemer.name.first} {redeemer.name.last}
                      </p>
                    ) : (
                      <p class="row-item row-item-targeted-1">-</p>
                    )}
                    <p class="row-item row-item-targeted-2">
                      <c-text-link
                        url={`mailto:${redeemer.email}`}
                        label={`${redeemer.email}`}
                      ></c-text-link>
                    </p>
                    <span class="row-item row-item-targeted-3 bubble gold-bubble">
                      {redeemer.code}
                    </span>
                    {redeemer.usedOn.length > 0 ? (
                      <p class="row-item row-item-targeted-4">
                        {new Date(redeemer.usedOn).toString().substring(4, 15)}
                      </p>
                    ) : (
                      <p class="row-item row-item-targeted-4 ">-</p>
                    )}
                    {/* <div class="delete-btn">
                      <c-btn
                        name="deleteCouponCode"
                        label=""
                        action-label=""
                        value={redeemer.email}
                        type="icon-only-ghost"
                        is-in-action={false}
                        is-disabled={false}
                        icon={true}
                        icon-url="https://res.cloudinary.com/layerpark/image/upload/v1634385289/ui-icons/delete/delete-normal_pgwcqb.svg"
                      ></c-btn>
                    </div> */}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p class="center-text">No one has redeemed this coupon yet</p>
          )}
        </div>
      </div>
    );
  }
}
