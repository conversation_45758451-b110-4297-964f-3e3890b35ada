import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-ticket-price-item-track",
  styleUrl: "c-ticket-price-item-track.css",
})
export class CTicketPriceItemTrack {
  @Event({
    eventName: "ticketTierClicked",
    bubbles: true,
  })
  ticketTierClicked: EventEmitter;

  @Prop() subTicketId: string;
  @Prop() name: string;
  @Prop() price_Student: number;
  @Prop() price_Professional: number;

  handleClick() {
    this.ticketTierClicked.emit({
      subTicketId: this.subTicketId,
      subTicketName: this.name,
      price_Student: this.price_Student,
      price_Professional: this.price_Professional,
    });
  }

  render() {
    return (
      <div
        class="ticket-price-item__container"
        onClick={() => this.handleClick()}
      >
        <div class="ticket-price__subitem ticket-price__subitem--1">
          <c-text>{this.name}</c-text>
        </div>
        <div class="ticket-price__subitem ticket-price__subitem--2">
          <div class="ticket-price__subitem--3__item">
            <c-text>
              ₹{this.price_Student}
              <span class="bubble bubble--green">Student</span>{" "}
            </c-text>
          </div>
          <div class="ticket-price__subitem--3__item">
            <c-text>
              ₹{this.price_Professional}
              <span class="bubble bubble--blue">Professional</span>
            </c-text>
          </div>
        </div>
      </div>
    );
  }
}
