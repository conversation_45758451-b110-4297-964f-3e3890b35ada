import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-link",
  styleUrl: "c-link.css",
})
export class CLink {
  @Event({
    eventName: "linkClick",
    bubbles: true,
  })
  linkClick: EventEmitter;

  @Prop() name: string;
  @Prop() type: string;
  @Prop() url: string;
  @Prop() target: string = "";
  @Prop() iconName: string = "";

  handleClick(event) {
    event.preventDefault();
    this.linkClick.emit({
      name: this.name,
    });
  }

  render() {
    if (this.type === "bubble") {
      return (
        <a class="bubble" href={this.url} target="_blank">
          <slot />
        </a>
      );
    } else if (this.type === "textWithEvent") {
      return (
        <a
          class="text"
          href="#"
          target="_blank"
          onClick={(event) => this.handleClick(event)}
        >
          <slot />
        </a>
      );
    } else if (this.type === "textWithIcon") {
      return (
        <a class="text text-with-icon" href={this.url} target="_blank">
          <ion-icon name={this.iconName}></ion-icon>
          <p>
            <slot />
          </p>
        </a>
      );
    } else {
      return (
        <a class="text" href={this.url} target={this.target}>
          <slot />
        </a>
      );
    }
  }
}
