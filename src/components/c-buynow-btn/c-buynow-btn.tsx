import { Component, Event, State, EventEmitter, Prop, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-buynow-btn",
  styleUrl: "c-buynow-btn.css",
})
export class CBuynowBtn {
  @Event({
    eventName: "buy-now",
    bubbles: true,
  })
  buyNowEvent: EventEmitter;

  @Prop() ticketId: string;
  @State() compState: string = "buy-now";

  handleBuyNowBtnClick(event) {
    event.preventDefault();
    this.changeComponentState("buying");
    let payload = {
      ticketID: this.ticketId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
          this.changeComponentState("buy-now");
        } else if (response.data.status === "Success") {
          this.buyNowEvent.emit();
        }
      })
      .catch((error) => {
        alert(error);
        this.changeComponentState("buy-now");
      });
  }

  changeComponentState(newState) {
    this.compState = newState;
  }

  render() {
    return (
      <button
        class={this.compState}
        onClick={(event) => this.handleBuyNowBtnClick(event)}
      >
        {this.compState === "buy-now" ? "BUY NOW" : <c-spinner></c-spinner>}
      </button>
    );
  }
}
