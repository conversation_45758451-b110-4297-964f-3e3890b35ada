import { Component, h } from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-cart-preview",
  styleUrl: "c-cart-preview.css",
})
export class CCartPreview {
  private isChecking: boolean = false;

  componentDidLoad() {
    this.getCart();
  }

  getCart() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/cart`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.cartItems = response.data.payload.cartItems;
          state.cartTotal = response.data.payload.cartTotal;
          if (state.cartItems.length > 0) {
            state.isCheckoutBtnDisabled = false;
          } else {
            state.isCheckoutBtnDisabled = true;
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        <header>
          <c-cart-total></c-cart-total>
        </header>
        <c-btn
          name="checkout"
          label="Checkout"
          action-label="Checking.."
          is-in-action={this.isChecking}
          is-disabled={state.isCheckoutBtnDisabled}
        ></c-btn>
        <div class="cart-list-container">
          <c-cart-list></c-cart-list>
        </div>
      </div>
    );
  }
}
