c-cart-preview {
  width: 320px;
}

c-cart-preview .cart-label {
  font-weight: 700;
  font-size: 0.7em;
  color: rgba(0, 0, 0, 0.6);
  margin: 1.5em 0 1.5em 0;
}

c-cart-preview .container {
  top: 6em;
  right: 1em;
  position: fixed;
  width: 200px;
  background: white;
  padding: 1em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

c-cart-preview .cart-list-container {
  overflow-y: scroll;
  max-height: 60vh;
}

c-cart-preview .container c-btn button {
  font-size: 0.9em;
}

@media only screen and (max-width: 768px) {
  c-cart-preview .container {
    display: none;
  }
}
