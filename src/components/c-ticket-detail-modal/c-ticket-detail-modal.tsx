import { Component, Listen, State, h } from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-ticket-detail-modal",
  styleUrl: "c-ticket-detail-modal.css",
})
export class CTicketDetailModal {
  @State() isFetching: boolean = true;
  private scrollY: any = window.scrollY;
  private trackRegistrants: any;

  @Listen("download-track-registrants")
  downloadPurchaseDataHandler() {
    let trackName = state.expandedTrackTicketTitle;
    let encodedTrackName = encodeURIComponent(trackName);
    let downloadTrackRegistrantsURI = `${state.baseUrl}/downloadtrackregistrants/${encodedTrackName}`;
    window.open(downloadTrackRegistrantsURI);
  }

  componentWillLoad() {
    let payload = {
      trackTicketTitle: state.expandedTrackTicketTitle,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/gettrackregistrants`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.trackRegistrants = response.data.payload;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div
        class="main-container"
        style={{ marginTop: `${this.scrollY + 60}px` }}
      >
        <h1 class="title">{state.expandedTrackTicketTitle}</h1>
        {state.expandedTrackTicketSoldUnits > 0 ? (
          this.isFetching ? (
            <c-skel-card></c-skel-card>
          ) : (
            <div>
              <div class="row track-info-container">
                <p>
                  Total{" "}
                  <strong>{this.trackRegistrants.registrants.length}</strong>{" "}
                  registrants
                </p>
                <c-text-link
                  url={`/instructor/${this.trackRegistrants.trackId}`}
                  label="Share this list →"
                ></c-text-link>
                <c-btn
                  name="downloadTrackRegistrants"
                  label="Download"
                  action-label=""
                  is-in-action={false}
                  is-disabled={false}
                ></c-btn>
              </div>
              <ul class="registrant-list">
                {this.trackRegistrants.registrants.map((registrant) => (
                  <li class="registrant-list-item">
                    <span>{registrant.name}</span>{" "}
                    <span>{registrant.email}</span>
                  </li>
                ))}
              </ul>
            </div>
          )
        ) : (
          <p class="no-sale">No registrants yet</p>
        )}
      </div>
    );
  }
}
