import {
  Component,
  Prop,
  FunctionalComponent,
  Watch,
  h,
  State,
} from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-ticket-basic-ticket",
  styleUrl: "c-ticket-basic-ticket.css",
})
export class CTicketBasicTicket {
  /*---
  Props
  ----*/
  @Prop() ticketId: string;
  @Prop() mode: string;
  @Prop() type: string;
  @Prop() ticketTitle: string;
  @Prop() persona: string;
  @Prop() tierString: string;
  @Prop() isVisible: boolean;
  @Prop() isDisabled: boolean;
  @Prop() isPrimary: boolean;
  @Prop() isPrimaryDependent: boolean;
  @Prop() isConfigMode: boolean = false;
  @Prop() isTicketInCart: boolean = false;
  @Prop() isTicketPurchased: boolean = false;
  @Prop() purchaseStatus: string = "";

  /*---
  State
  ----*/
  @State() isInCart: boolean = false;

  /*------
  Watchers
  ------*/
  @Watch("isTicketInCart") isTicketInCartWatcher(
    newVal: boolean,
    oldVal: boolean
  ) {
    if (newVal != oldVal) {
      this.isInCart = this.isTicketInCart;

      if (this.isPrimary) {
        state.isPrimaryTicketInCart = this.isTicketInCart;
      }
    }
  }

  /*-------
  Variables
  -------*/
  private tierName: string = "";
  // private tierEndDate: string = "";
  private tierStudentPrice: number = 0;
  private tierProfessionalPrice: number = 0;

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    if (this.isPrimary) {
      if (this.isTicketInCart) {
        state.isPrimaryTicketInCart = this.isTicketInCart;
      }
      if (this.isTicketPurchased) {
        state.isPrimaryTicketPurchased = this.isTicketPurchased;
      }
    }
    this.generatePriceFromTierDetails();
    this.isInCart = this.isTicketInCart;
  }

  generatePriceFromTierDetails() {
    let tierDetails = JSON.parse(this.tierString);
    this.tierName = tierDetails.name;
    // this.tierEndDate = tierDetails.endDate;
    this.tierStudentPrice = tierDetails.studentPrice;
    this.tierProfessionalPrice = tierDetails.professionalPrice;
  }

  /*-------------------
  Functional Components
  -------------------*/
  AdminBasicTicketView: FunctionalComponent = () => (
    <div class="ticket-container">
      <div class="ticket-container__details">
        <h1>{this.ticketTitle}</h1>
        <div class="ticket-container__details__perks">
          <c-text>✅ Get full access on event day</c-text>
          <c-text>⚠️ Does not include workshops, courses & accomodation</c-text>
        </div>
      </div>
      <div class="ticket-container__price">
        <div class="ticket-container__price__tier-name">
          <c-text type="subtext">{this.tierName.toUpperCase()}</c-text>
        </div>
        <div class="ticket-container__price__item">
          <c-text type="heading">₹{this.tierStudentPrice}</c-text>
          <p class="bubble green-bubble">Student</p>
        </div>
        <div class="ticket-container__price__item">
          <c-text type="heading">₹{this.tierProfessionalPrice}</c-text>
          <p class="bubble blue-bubble">Professional</p>
        </div>
        {/* <div class="ticket-container__price__deadline">
          <c-text type="subtext">
            Prices will increase on{" "}
            {new Date(this.tierEndDate).toString().substring(4, 15)} (IST)
          </c-text>
        </div> */}
        <div class="ticket-container__price__footer">
          <c-button type="ghost_Danger_Small_onDark" name="deleteFullTicket">
            Delete
          </c-button>
          <c-button type="ghost_Small_onDark" name="editFullTicket">
            Edit
          </c-button>
        </div>
      </div>
    </div>
  );

  DefaultBasicTicketView: FunctionalComponent = () => (
    <div class="ticket-container">
      <div class="ticket-container__details">
        <h1>{this.ticketTitle}</h1>
        <div class="ticket-container__details__perks">
          {this.ticketTitle === "Hybrid Mode Ticket" ? (
            <c-text>✅ Access the event virtually</c-text>
          ) : (
            <c-text>✅ Access events on given date/s</c-text>
          )}
          <c-text>✅ Includes lunch</c-text>
          {this.ticketTitle ===
          "Visual Discourse 2024 One Day Pass (6th Nov)" ? (
            <c-text>✅ Includes 1 free workshop</c-text>
          ) : (
            <c-text>⚠️ Does not include workshops/courses</c-text>
          )}

          {this.ticketTitle != "Hybrid Mode Ticket" && (
            <c-text>⚠️ Does not include accomodation</c-text>
          )}
        </div>
      </div>
      <div class="ticket-container__price">
        <div class="ticket-container__price__tier-name">
          <c-text type="subtext">{this.tierName.toUpperCase()}</c-text>
        </div>

        {this.persona === "student" && (
          <div class="ticket-container__price__item">
            <c-text type="heading">₹{this.tierStudentPrice}</c-text>
            <br />
            <p class="bubble green-bubble">Student</p>
          </div>
        )}
        {this.persona === "professional" && (
          <div class="ticket-container__price__item">
            <c-text type="heading">₹{this.tierProfessionalPrice}</c-text>
            <p class="bubble blue-bubble">Professional</p>
          </div>
        )}
        <div class="ticket-container__price__deadline">
          {!this.isTicketPurchased && (
            <c-text type="subtext">Price includes GST</c-text>
          )}
          {/* {!this.isTicketPurchased && (
            <c-text type="subtext">
              Price will increase on{" "}
              {new Date(this.tierEndDate).toString().substring(4, 15)} (IST)
            </c-text>
          )} */}
        </div>
        <div class="ticket-container__price__footer ticket-container__price__footer--default">
          {this.isTicketPurchased ? (
            <div>
              {this.purchaseStatus === "purchased" && (
                <p class="ticket-purchased-confirmation">Purchased</p>
              )}
              {this.purchaseStatus === "under-verification" && (
                <p class="ticket-under-verification">Under verification</p>
              )}
            </div>
          ) : // <p class="ticket-purchased-confirmation">Purchased</p>
          this.isInCart ? (
            <c-button
              type="ghost_Danger_Small_onDark"
              name="removeBasicTicketFromCart"
              value={`${this.ticketId}---${
                this.isPrimary ? "primary" : "notPrimary"
              }`}
            >
              Remove from cart
            </c-button>
          ) : (
            <c-button
              type="ghost_Small_onDark"
              name="addBasicTicketToCart"
              value={`${this.ticketId}---${
                this.isPrimary ? "primary" : "notPrimary"
              }`}
            >
              Add to cart
            </c-button>
          )}
        </div>
      </div>
    </div>
  );

  render() {
    return (
      <c-card>
        {this.isConfigMode ? (
          <this.AdminBasicTicketView></this.AdminBasicTicketView>
        ) : (
          <this.DefaultBasicTicketView></this.DefaultBasicTicketView>
        )}
      </c-card>
    );
  }
}
