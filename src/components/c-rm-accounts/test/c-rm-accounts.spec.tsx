import { newSpecPage } from '@stencil/core/testing';
import { CRmAccounts } from '../c-rm-accounts';

describe('c-rm-accounts', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CRmAccounts],
      html: `<c-rm-accounts></c-rm-accounts>`,
    });
    expect(page.root).toEqualHtml(`
      <c-rm-accounts>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-rm-accounts>
    `);
  });
});
