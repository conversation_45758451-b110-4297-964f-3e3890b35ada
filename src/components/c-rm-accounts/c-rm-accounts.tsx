import { Component, Host, Listen, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-rm-accounts",
  styleUrl: "c-rm-accounts.css",
})
export class CRmAccounts {
  private searchTimeout: any;
  private searchString: string = "";
  @Listen("textInput") handleTextInput(e) {
    if (e.detail.name === "registrantSearch") {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.searchString = e.detail.value.trim();
        this.fetchData();
      }, 500);
    }
  }

  @Listen("downloadAccounts")
  downloadAccounts() {
    let persona: string = "-";
    let membership: string = "-";
    let purchaseStatus: string = "-";

    if (this.filterObj.categoryFilter) {
      persona = this.filterObj.categoryFilter;
    }
    if (this.filterObj.membershipFilter) {
      membership = this.filterObj.membershipFilter;
    }
    if (this.filterObj.purchaseStatusFilter) {
      purchaseStatus = this.filterObj.purchaseStatusFilter;
    }

    window.open(
      `${state.baseUrl}/download-accounts-v2/${state.eventCodeForMonitoring}/${persona}/${membership}/${purchaseStatus}`
    );
  }

  @Listen("dropdown-input-event")
  filterHandler(event) {
    this.filterObj[event.detail.filter] = event.detail.value;
    state.registrantSearchString = "";
    this.isFetching = true;
    this.currentPageNo = 1;
    this.fetchData();
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "nextPage") {
      if (this.currentPageNo === this.maxPageNo) {
        return;
      }
      this.currentPageNo = this.currentPageNo + 1;
      this.fetchData();
    } else if (e.detail.name === "previousPage") {
      if (this.currentPageNo === 1) {
        return;
      }
      this.currentPageNo = this.currentPageNo - 1;
      this.fetchData();
    }
  }

  @Listen("expand-user-details")
  expandUserDtails() {
    this.openModal("expandUserDetails");
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.closeModal();
  }

  @State() isFetching: boolean = true;
  @State() registrantArr: any;
  @State() currentPageNo: number = 1;
  @State() lowestIndex: number = 0;
  @State() highestIndex: number = 0;
  @State() modalName: string = "";
  @State()
  isModalActive: boolean = false;

  private fetchedRegistrantArr: any;
  private totalAccounts: number = 0;
  private memberAccounts: number = 0;
  private professionalAccounts: number = 0;
  private studentAccounts: number = 0;
  private maxPageNo: number = 0;
  private filterObj: any = {
    categoryFilter: "",
    membershipFilter: "",
    purchaseStatusFilter: "",
  };

  componentWillLoad() {
    state.isMobileDashboardOptionsVisible = false;
  }

  componentDidLoad() {
    this.fetchData();
  }

  fetchData() {
    this.isFetching = true;
    let data = {
      categoryFilter: this.filterObj.categoryFilter,
      membershipFilter: this.filterObj.membershipFilter,
      purchaseStatusFilter: this.filterObj.purchaseStatusFilter,
      pageNo: this.currentPageNo,
      searchString: this.searchString,
      eventCode: state.eventCodeForMonitoring,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/user`,
      data: data,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchedRegistrantArr = "";
          this.fetchedRegistrantArr = response.data.payload;
          this.registrantArr = this.fetchedRegistrantArr;
          this.totalAccounts = response.data.accounts.total;
          this.memberAccounts = response.data.accounts.members;
          this.professionalAccounts = response.data.accounts.professions;
          this.studentAccounts = response.data.accounts.students;
          this.lowestIndex = response.data.page.lowestIndex;
          this.highestIndex = response.data.page.highestIndex;
          this.maxPageNo = response.data.page.total;
          this.isFetching = false;
          // setInterval(() => {
          //   this.isFetching = false;
          // }, 500);
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  closeModal() {
    this.isModalActive = false;
    this.modalName = "";
  }

  openModal(name: string) {
    this.isModalActive = true;
    this.modalName = name;
  }

  render() {
    return (
      <Host>
        <c-modal name={this.modalName} is-active={this.isModalActive}></c-modal>

        {state.isMobileDashboardOptionsVisible && (
          <div class="show-on-mobile-control-bar">
            {" "}
            <c-control-bar
              count={!this.isFetching && this.registrantArr.length}
              name="registrants"
            ></c-control-bar>
          </div>
        )}
        <div class="show-on-desktop-control-bar">
          <c-control-bar
            count={!this.isFetching && this.registrantArr.length}
            name="registrants"
          ></c-control-bar>
        </div>

        <div class="user-list-container">
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div>
              <c-card>
                <div class="accounts-header hide-on-mobile">
                  <c-row>
                    <div class="row__primary-container">
                      <e-text>
                        <ion-icon name="people-outline"></ion-icon>
                        &nbsp;&nbsp;Total
                        <br />
                        <span class="overview-number">
                          {this.totalAccounts}
                        </span>
                      </e-text>
                    </div>
                    <div class="vertical-divider"></div>
                    <e-text>
                      <ion-icon name="briefcase-outline"></ion-icon>
                      &nbsp;&nbsp;Professionals
                      <br />
                      <span class="overview-number">
                        {this.professionalAccounts}
                      </span>
                    </e-text>
                    <e-text>
                      <ion-icon name="school-outline"></ion-icon>
                      &nbsp;&nbsp;Students
                      <br />
                      <span class="overview-number">
                        {this.studentAccounts}
                      </span>
                    </e-text>
                    <e-text>
                      <ion-icon name="ribbon-outline"></ion-icon>
                      &nbsp;&nbsp;Members <br />
                      <span class="overview-number">{this.memberAccounts}</span>
                    </e-text>
                  </c-row>
                </div>
                <div class="accounts-header show-on-mobile">
                  <div class="accounts-header_row row__primary-container">
                    <c-row>
                      <c-text>
                        <ion-icon name="people-outline"></ion-icon>
                        &nbsp;&nbsp;Total
                      </c-text>
                      <c-text>{this.totalAccounts}</c-text>
                    </c-row>
                  </div>
                  <div class="accounts-header_row">
                    <c-row>
                      <c-text>
                        <ion-icon name="briefcase-outline"></ion-icon>
                        &nbsp;&nbsp;Professionals
                      </c-text>
                      <c-text>{this.professionalAccounts}</c-text>
                    </c-row>
                  </div>
                  <div class="accounts-header_row">
                    <c-row>
                      <c-text>
                        <ion-icon name="school-outline"></ion-icon>
                        &nbsp;&nbsp;Students
                      </c-text>
                      <c-text>{this.studentAccounts}</c-text>
                    </c-row>
                  </div>
                  <div class="accounts-header_row">
                    <c-row>
                      <c-text>
                        <ion-icon name="ribbon-outline"></ion-icon>
                        &nbsp;&nbsp;HCIPAI Members
                      </c-text>
                      <c-text>{this.memberAccounts}</c-text>
                    </c-row>
                  </div>
                </div>
              </c-card>
              <div class="page-controls">
                <c-row>
                  <c-text type="subtext">
                    Showing {this.lowestIndex}-{this.highestIndex} results
                  </c-text>
                  <div class="page-no-controls">
                    <c-button
                      name="previousPage"
                      isDisabled={this.currentPageNo === 1 && true}
                    >
                      &lt;
                    </c-button>
                    &nbsp; &nbsp;
                    <c-text type="subtext">
                      Page {this.currentPageNo} of {this.maxPageNo}
                    </c-text>
                    &nbsp; &nbsp;
                    <c-button
                      name="nextPage"
                      isDisabled={this.currentPageNo === this.maxPageNo && true}
                    >
                      &gt;
                    </c-button>
                  </div>
                </c-row>
              </div>
              {this.registrantArr.length > 0 ? (
                <c-list
                  type="dashboardAccountInfoList"
                  listItemsAsString={JSON.stringify(this.registrantArr)}
                ></c-list>
              ) : (
                <c-text>No accounts found</c-text>
              )}
            </div>
          )}
        </div>
      </Host>
    );
  }
}
