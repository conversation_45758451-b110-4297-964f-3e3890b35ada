c-rm-accounts .accounts-header {
  text-align: center;
}

c-rm-accounts .row__primary-container {
  color: var(--accent-color);
}

c-rm-accounts .overview-number {
  font-size: 1.5em;
}

c-rm-accounts .vertical-divider {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

c-rm-accounts c-card .basic-card-container {
  padding: 1em;
  border-radius: 0.4em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 0em;
}

c-rm-accounts .page-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5em;
  color: rgba(0, 0, 0, 0.6);
}

c-rm-accounts .page-control-row button {
  padding: 0.3em;
  background: none;
}

c-rm-accounts .page-control-row button:hover {
  padding: 0.3em;
  background: rgba(0, 0, 0, 0.05);
}

c-rm-accounts .page-control-row button ion-icon {
  font-size: 0.8em;
}

c-rm-accounts .page-no {
  margin-left: 1em;
  margin-right: 1em;
}

c-rm-accounts .hide-on-mobile {
  display: block;
}

c-rm-accounts .show-on-mobile {
  display: none;
}

c-rm-accounts .page-controls {
  margin: 1em 0;
}

c-rm-accounts .page-controls .subtext {
  margin-top: 0;
}

c-rm-accounts .page-controls c-button button {
  padding: 0em 0.3em;
  color: rgba(0, 0, 0, 0.5);
  background: none;
}
c-rm-accounts .page-controls c-button button:hover {
  background: rgba(0, 0, 0, 0.1);
}
c-rm-accounts .page-no-controls {
  display: flex;
  align-items: center;
}

c-rm-accounts .show-on-mobile {
  display: none;
}

c-rm-accounts .show-on-desktop {
  display: block;
}

c-rm-accounts .show-on-mobile-control-bar {
  display: none;
}

c-rm-accounts .show-on-desktop-control-bar {
  display: block;
}

@media only screen and (max-width: 768px) {
  c-rm-accounts .hide-on-mobile {
    display: none;
  }

  c-rm-accounts .show-on-mobile {
    display: block;
  }

  c-rm-accounts .row__primary-container {
    padding-bottom: 0.75em;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  c-rm-accounts .show-on-mobile .row__primary-container {
    color: var(--accent-color);
  }

  c-rm-accounts .accounts-header_row {
    padding: 0.75em 0;
  }

  c-rm-accounts .show-on-mobile-control-bar {
    display: flex;
  }

  c-rm-accounts .show-on-desktop-control-bar {
    display: none;
  }
}
