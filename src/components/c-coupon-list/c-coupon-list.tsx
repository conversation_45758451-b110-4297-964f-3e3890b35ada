import {
  Component,
  State,
  Event,
  EventEmitter,
  Listen,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-coupon-list",
  styleUrl: "c-coupon-list.css",
})
export class CCouponList {
  private availableCouponArr: any;
  @State() isFetching: boolean = true;

  @Event({
    eventName: "get-cart",
    bubbles: true,
  })
  getCartEventEmitter: EventEmitter;

  componentDidLoad() {
    this.getAvailableCoupons();
  }

  @Listen("button-click")
  buttonClickHandler(event) {
    if (event.detail.name === "applyCoupon") {
      this.applyCoupon(event.detail.value);
    }
  }

  getAvailableCoupons() {
    let payload = {
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getavailablecoupons`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          if (response.data.payload.length > 0) {
            this.availableCouponArr = response.data.payload;
            state.isCouponsAvailable = true;
            this.isFetching = false;
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  applyCoupon(couponCode) {
    let payload = {
      eventCode: state.eventCodeForRegistration,
      couponCode: couponCode,
      cartTotal: state.cartTotal,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/check-and-apply-coupon-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
        } else if (response.data.status === "Success") {
          this.getCartEventEmitter.emit();
          state.isCouponApplied = true;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="coupon-list-container">
        <div
          class={` hide ${
            state.isCouponsAvailable ? "show bottom-border" : "hide"
          }`}
        >
          {this.isFetching ? (
            ""
          ) : (
            <div>
              <p class="coupon-section-heading">COUPONS AVAILABLE</p>
              {this.availableCouponArr.map((coupon: any) => (
                <c-coupon-item
                  coupon-name={coupon.name}
                  coupon-code={coupon.code}
                ></c-coupon-item>
              ))}
            </div>
          )}
        </div>
        <c-coupon-input></c-coupon-input>
      </div>
    );
  }
}
