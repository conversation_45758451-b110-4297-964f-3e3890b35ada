import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-ticket-price-item-partial",
  styleUrl: "c-ticket-price-item-partial.css",
})
export class CTicketPriceItemPartial {
  @Event({
    eventName: "accessDateClicked",
    bubbles: true,
  })
  accessDateClicked: EventEmitter;

  @Prop() accessId: string;
  @Prop() tierId: string;
  @Prop() tierName: string;
  @Prop() tierStart: string;
  @Prop() tierEnd: string;
  @Prop() tierRange: string;
  @Prop() price_Student: number;
  @Prop() price_Professional: number;

  handleClick() {
    this.accessDateClicked.emit({
      accessId: this.accessId,
      tierId: this.tierId,
      tierName: this.tierName,
      tierStart: this.tierStart,
      tierEnd: this.tierEnd,
      tierRange: this.tierRange,
      price_Student: this.price_Student,
      price_Professional: this.price_Professional,
    });
  }

  render() {
    return (
      <div
        class="ticket-price-item__container"
        onClick={() => this.handleClick()}
      >
        <div class="ticket-price__subitem ticket-price__subitem--1">
          <c-text>{this.tierName}</c-text>
        </div>
        <div class="ticket-price__subitem ticket-price__subitem--2">
          <c-text>{this.tierRange}</c-text>
        </div>
        <div class="ticket-price__subitem ticket-price__subitem--3">
          <div class="ticket-price__subitem--3__item">
            <c-text>
              ₹{this.price_Student}
              <span class="bubble bubble--green">Student</span>{" "}
            </c-text>
          </div>
          <div class="ticket-price__subitem--3__item">
            <c-text>
              ₹{this.price_Professional}
              <span class="bubble bubble--blue">Professional</span>
            </c-text>
          </div>
        </div>
      </div>
    );
  }
}
