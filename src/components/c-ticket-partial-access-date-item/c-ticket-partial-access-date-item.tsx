import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-ticket-partial-access-date-item",
  styleUrl: "c-ticket-partial-access-date-item.css",
})
export class CTicketPartialAccessDateItem {
  @Event({
    eventName: "accessDateClicked",
    bubbles: true,
  })
  accessDateClicked: EventEmitter;

  @Prop() dateId: string;
  @Prop() name: string;
  @Prop() isMultiDay: boolean;
  @Prop() startDate: string;
  @Prop() endDate: string;

  handleClick() {
    this.accessDateClicked.emit({
      id: this.dateId,
      name: this.name,
      isMultiDay: this.isMultiDay,
      startDate: this.startDate,
      endDate: this.endDate,
    });
  }

  render() {
    return (
      <div
        class="ticket-price-item__container"
        onClick={() => this.handleClick()}
      >
        <div class="ticket-price__subitem ticket-price__subitem--1">
          <c-text>{this.name}</c-text>
          <c-text>
            {this.isMultiDay ? (
              <span class="bubble bubble--green">Multi day</span>
            ) : (
              <span class="bubble bubble--blue">One day</span>
            )}
          </c-text>
        </div>
        <div class="ticket-price__subitem ticket-price__subitem--2">
          <c-text>{this.startDate}</c-text>
        </div>
        <div class="ticket-price__subitem ticket-price__subitem--3">
          <c-text>{this.isMultiDay ? this.endDate : "-"}</c-text>
        </div>
      </div>
    );
  }
}
