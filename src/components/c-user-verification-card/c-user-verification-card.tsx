import {
  Component,
  Event,
  EventEmitter,
  Prop,
  State,
  Listen,
  h,
} from "@stencil/core";

@Component({
  tag: "c-user-verification-card",
  styleUrl: "c-user-verification-card.css",
})
export class CUserVerificationCard {
  @Event({
    eventName: "verify-bank-transaction",
    bubbles: true,
  })
  verifyBankTransaction: EventEmitter;

  @Listen("bank-transactions-btn-click")
  verifyBankTransactionBtnClick(event) {
    event.preventDefault();
    this.isVerifyBtnActive = true;
    this.isVerifyBtnDisabled = true;
    this.verifyBankTransaction.emit({
      email: this.email,
      bankTxCode: this.bankTxCode,
      orderID: this.orderID,
    });
  }

  @Prop() firstName: string;
  @Prop() lastName: string;
  @Prop() email: string;
  @Prop() verificationType: string;
  @Prop() orderID: string;
  @Prop() bankTxCode: string;
  @Prop() transferredAmount: string;

  @State() isVerifyBtnDisabled: boolean = false;
  private isVerifyBtnActive: boolean = false;

  render() {
    return (
      <div class="card">
        <div class="info-container primary-info-container">
          <p class="primary-info">
            <span class="info-value name">
              {this.firstName} {this.lastName}
            </span>
            <br />
            <span class="info-value email">
              <c-text-link
                url={`mailto:${this.email}`}
                label={this.email}
              ></c-text-link>
            </span>
            <br />
            {this.verificationType === "BankTransfer" ? (
              <p class="badge green">Bank Transfer</p>
            ) : (
              ""
            )}
          </p>
        </div>
        <div class="info-container secondary-info-container">
          <div class="row">
            <p class="first-row-item">
              <label>ORDER ID</label>
              <br />
              <span class="user-info-value">{this.orderID}</span>
            </p>
            <p class="second-row-item">
              <label>AMOUNT</label>
              <br />
              <span class="user-info-value">₹{this.transferredAmount}</span>
            </p>
            <p class="third-row-item">
              <label>BANK TX CODE</label>
              <br />
              <span class="user-info-value">{this.bankTxCode}</span>
            </p>
          </div>
        </div>
        <div class="info-container tertiary-info-container">
          <c-btn
            name="verifyBankTransaction"
            label="Verify"
            action-label=""
            value={this.orderID}
            is-in-action={this.isVerifyBtnActive}
            is-disabled={this.isVerifyBtnDisabled}
          ></c-btn>
          <br />
        </div>
      </div>
    );
  }
}
