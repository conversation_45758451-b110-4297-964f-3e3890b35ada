c-user-verification-card .card {
  background: white;
  padding: 1em 1.5em 0.5em 1.5em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 1em;
  display: flex;
  justify-content: space-between;
}

c-user-verification-card .row {
  display: flex;
  justify-content: space-between;
}

c-user-verification-card .row p {
  margin-top: 0;
  margin-bottom: 0.25em;
}

c-user-verification-card .row p label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.5);
}

c-user-verification-card .badge-row {
  justify-content: left;
}

c-user-verification-card .hseperator {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

c-user-verification-card .vseperator {
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

c-user-verification-card .primary-info-container {
  width: 25%;
}

c-user-verification-card .secondary-info-container {
  width: 60%;
}

c-user-verification-card .secondary-info-container .row .first-row-item {
  width: 30%;
}
c-user-verification-card .secondary-info-container .row .second-row-item {
  width: 20%;
}
c-user-verification-card .secondary-info-container .row .third-row-item {
  width: 25%;
}

c-user-verification-card c-btn button {
  font-size: 0.8em;
  outline: none;
}

c-user-verification-card .primary-info {
  margin-top: 0;
}

c-user-verification-card .name {
  font-weight: 700;
  font-size: 0.9em;
}

c-user-verification-card .user-info-value {
  font-size: 0.75em;
}

c-user-verification-card .user-info-purchases {
  font-size: 0.9em;
  margin-left: 0.5em;
  color: rgba(0, 0, 0, 0.6);
}

c-user-verification-card .email {
  font-size: 0.8em;
  margin-top: 2em;
  padding-top: 2em;
}

c-user-verification-card a {
  font-size: 0.9em;
  text-decoration: none;
  color: var(--accent-color);
}

c-user-verification-card .purchases-row {
  display: flex;
  margin-top: 0.4em;
}

c-user-verification-card .badge {
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.25em 0.5em;
  margin: 0;
  margin-bottom: 1em;
  border-radius: 0.25em;
  margin-right: 1em;
  margin-top: 0.5em;
}

c-user-verification-card .golden {
  background: var(--accent-golden-bg-lighter);
  color: var(--accent-golden-darker);
}

c-user-verification-card .blue {
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}

c-user-verification-card .pink {
  background: var(--accent-pink-bg-lighter);
  color: var(--accent-pink-darker);
}

c-user-verification-card .green {
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
}
