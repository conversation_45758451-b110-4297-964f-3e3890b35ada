import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-coupon-item",
  styleUrl: "c-coupon-item.css",
})
export class CCouponItem {
  @Prop() couponName: string;
  @Prop() couponCode: string;

  render() {
    return (
      <div class="coupon-item-container">
        <p class="coupon-title">{this.couponName}</p>
        <c-btn
          name="applyCoupon"
          label="Apply Coupon"
          value={this.couponCode}
          action-label=""
          type="ghost"
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
    );
  }
}
