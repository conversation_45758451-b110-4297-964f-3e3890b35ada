import {
  Component,
  Event,
  EventEmitter,
  Host,
  Listen,
  Prop,
  State,
  Watch,
  FunctionalComponent,
  h,
} from "@stencil/core";
import state from "../../global/state";
import { gsap } from "gsap";

@Component({
  tag: "c-vnav-item",
  styleUrl: "c-vnav-item.css",
})
export class CVnavItem {
  editPageFormEl!: HTMLDivElement;

  @Prop() name: string;
  @Prop() label: string;
  @Prop() state: string;
  @Prop() isDisabled: boolean = false;
  @Prop() subText: string;
  @Prop() route: string;
  @Prop() icon: string;
  @Prop() navName: string;
  @Prop() configure: boolean = false;
  @Prop() navLen: number = -1;
  @Prop() isConfigMode: boolean = false;

  @State() isConfigureButtonActive: boolean = false;
  @State() variant: string = "defaultNavItem";
  @State() isPageSettingsOpen: boolean = false;
  @State() isEditPageButtonDisabled: boolean = true;
  @State() isEditInAction: boolean = false;
  @State() isDeletePageButtonDisabled: boolean = false;
  @State() isDeleteInAction: boolean = false;
  @State() isPageNameTextboxFocused: boolean = false;

  @Watch("navLen") navLenWatcher(newVal: number, oldVal: number) {
    if (newVal != oldVal) {
      this.disableDelete();
    }
  }

  private btnClass: string;
  private editedPageLabel: string;
  private editedPageIcon: string;
  private isPageLabelChanged: boolean = false;
  private isPageIconChanged: boolean = false;

  @Event({
    eventName: "vnav-route-change",
    bubbles: true,
  })
  vNavItemClick: EventEmitter;

  @Event({
    eventName: "pageEdit",
    bubbles: true,
  })
  pageEdit_EventEmitter: EventEmitter;

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "openPageSettingsForm") {
      this.openPageSettingsForm_EventHandler();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "editPageLabel") {
      this.editedPageLabel = e.detail.value;
      this.editPageFormChecker();
    } else if (e.detail.name === "editPageIcon") {
      this.editedPageIcon = e.detail.value;
      this.editPageFormChecker();
    }
  }

  editPageFormChecker() {
    if (this.label != this.editedPageLabel) {
      this.isPageLabelChanged = true;
    } else {
      this.isPageLabelChanged = false;
    }

    if (this.icon != this.editedPageIcon) {
      this.isPageIconChanged = true;
    } else {
      this.isPageIconChanged = false;
    }

    if (this.isPageLabelChanged || this.isPageIconChanged) {
      this.isEditPageButtonDisabled = false;
    } else {
      this.isEditPageButtonDisabled = true;
    }

    if (this.editedPageLabel.length === 0 || this.editedPageIcon.length === 0) {
      this.isEditPageButtonDisabled = true;
    } else {
      this.isEditPageButtonDisabled = false;
    }

    this.pageEdit_EventEmitter.emit({
      editedPageLabel: this.editedPageLabel,
      editedPageIcon: this.editedPageIcon,
    });
  }

  componentWillLoad() {
    if (this.state === "active") {
      this.btnClass = "active";
    } else if (this.state === "enabled") {
      this.btnClass = "";
    } else if (this.state === "disabled") {
      this.btnClass = "disabled";
    }

    if (this.isDisabled) {
      this.btnClass = `${this.btnClass} disabled`;
    }

    if (this.name === "back") {
      this.btnClass = `${this.btnClass} back-button`;
    }

    if (this.configure) {
      state.configureActivePageId = this.name;
    }

    this.editedPageLabel = this.label;
    this.editedPageIcon = this.icon;

    this.pageEdit_EventEmitter.emit({
      editedPageLabel: this.editedPageLabel,
      editedPageIcon: this.editedPageIcon,
    });

    this.setState();
    this.disableDelete();
  }

  componentWillUpdate() {
    if (this.state === "active") {
      this.btnClass = "active";
    } else if (this.state === "enabled") {
      this.btnClass = "";
    } else if (this.state === "disabled") {
      this.btnClass = "disabled";
    }
  }

  disableDelete() {
    if (this.navLen > 1) {
      this.isDeletePageButtonDisabled = false;
    } else {
      this.isDeletePageButtonDisabled = true;
    }
  }

  handleNavChange(event) {
    event.preventDefault();
    this.vNavItemClick.emit({
      cNavItemName: this.name,
      navName: this.navName,
      label: this.label,
      icon: this.icon,
    });
  }

  setState() {
    if (this.isConfigMode) this.variant = "configureNavItem";
  }

  openPageSettingsForm_EventHandler() {
    this.changePageSettingsFormState();
    if (this.isPageSettingsOpen) {
      this.openPageSettingsForm();
    } else {
      this.closePageSettingsForm();
    }
  }

  changePageSettingsFormState() {
    this.isPageSettingsOpen = !this.isPageSettingsOpen;
  }

  openPageSettingsForm() {
    let tl = gsap.timeline();
    tl.to(this.editPageFormEl, {
      display: "block",
      overflow: "auto",
      marginBottom: "1em",
      padding: "0.25em 1em 0.2em 1em",
      border: "1px solid rgba(0, 0, 0, 0.1)",
      duration: 0,
    });
    tl.to(this.editPageFormEl, {
      height: "auto",
      duration: 0.15,
    });
    this.isPageNameTextboxFocused = true;
  }

  closePageSettingsForm() {
    this.clearAddPageForm();
    let tl = gsap.timeline();
    tl.to(this.editPageFormEl, {
      height: "0px",
      marginBottom: "0em",
      padding: "0em",
      border: 0,
      duration: 0.15,
    });
  }

  clearAddPageForm() {
    this.editedPageLabel = "";
    this.editedPageIcon = "";
    this.isPageNameTextboxFocused = false;
    this.isEditInAction = false;
    this.isDeleteInAction = false;
  }

  private navButton: FunctionalComponent = () => (
    <button
      class={this.btnClass}
      onClick={(event) => this.handleNavChange(event)}
    >
      {this.icon.length > 0 && <ion-icon name={this.icon}></ion-icon>}
      <label>{this.label}</label>
      {this.btnClass != "active" &&
        state.notificationCount > 0 &&
        this.name === "verification" && (
          <span class="notification-bubble">{state.notificationCount}</span>
        )}
    </button>
  );

  private defaultNavItem: FunctionalComponent = () => (
    <div class={`container ${this.configure && "configure-row"}`}>
      <this.navButton></this.navButton>
    </div>
  );

  private configureNavItem: FunctionalComponent = () => (
    <div>
      <div class={`container ${this.configure && "configure-row"}`}>
        <this.navButton></this.navButton>
        <c-button
          type="openPageSettingsForm"
          name="openPageSettingsForm"
          isInActiveState={this.isPageSettingsOpen}
        ></c-button>
      </div>
      <div
        class="edit-page-form__container"
        ref={(el) => (this.editPageFormEl = el as HTMLDivElement)}
      >
        <c-text>Edit page details</c-text>
        <c-textbox
          name="editPageLabel"
          placeholder="Page name e.g. Workshops"
          value={this.label}
          isInFocus={this.isPageNameTextboxFocused}
        ></c-textbox>
        <c-textbox
          name="editPageIcon"
          placeholder="Icon code"
          value={this.icon}
        ></c-textbox>
        <c-link
          type="textWithIcon"
          iconName="help-circle-outline"
          url="https://ionic.io/ionicons"
        >
          See icon reference
        </c-link>
        <c-row>
          <c-button
            name="deletePage"
            type="deletePage"
            isDisabled={this.isDeletePageButtonDisabled}
            isInAction={this.isDeleteInAction}
            value={this.name}
          ></c-button>
          <c-button
            name="editPage"
            type="editPage"
            isDisabled={this.isEditPageButtonDisabled}
            isInAction={this.isEditInAction}
            value={this.name}
          ></c-button>
        </c-row>
      </div>
    </div>
  );

  render() {
    return (
      <Host>
        {this.variant === "defaultNavItem" && (
          <this.defaultNavItem></this.defaultNavItem>
        )}
        {this.variant === "configureNavItem" && (
          <this.configureNavItem></this.configureNavItem>
        )}
      </Host>
    );
  }
}
