c-vnav-item button {
  background: none;
  outline: none;
  border: 0;
  font-size: 1em;
  text-align: left;
  padding: 0em 0em;
  border-radius: 0.25em;
  color: var(--accent-color);
  transition: all 0.15s ease-in;
  outline: none;
  padding: 0.5em 0.75em;
  margin-top: 0;
}

c-vnav-item button:hover,
label:hover {
  cursor: pointer;
  color: var(--accent-color-darkest);
}

c-vnav-item .sub-text {
  font-size: 0.7em;
  margin-top: 0;
  padding-top: 0;
  margin-left: 1em;
  color: rgba(0, 0, 0, 0.3);
  color: red;
}

c-vnav-item .active {
  background: var(--accent-color-bg-lighter);
  color: var(--accent-color-darkest);
  border-radius: 0.3em;
}

/* c-vnav-item .active:hover {
  background: var(--accent-color-bg-lighter);
} */

c-vnav-item .notification-bubble {
  background: red;
  color: white;
  padding: 0.25em 0.5em;
  font-size: 0.7em;
  font-weight: 700;
  margin-left: 0.25em;
  border-radius: 0.25em;
}

c-vnav-item .container button {
  display: flex;
  align-items: center;
}

c-vnav-item label {
  margin-left: 0.5em;
}

c-vnav-item .disabled {
  pointer-events: none;
  color: rgba(0, 0, 0, 0.4);
  background: none;
  opacity: 0.3;
}

c-vnav-item .configure-row {
  width: 250px;
  display: flex;
}

c-vnav-item .configure-row c-button button {
  /* padding: 0.5em; */
}

c-vnav-item .configure-controls {
  display: flex;
  align-items: center;
}

c-vnav-item .configure-controls button {
  padding: 0.25em;
}

c-vnav-item .configure-controls ion-icon {
  height: 0.8em;
  width: 0.8em;
}

/*-------------
Edit page form
-------------*/

c-vnav-item .edit-page-form__container {
  display: none;
  height: 0px;
  overflow: hidden;
  /* background: rgba(0, 0, 0, 0.05); */
  /* padding: 0.25em 1em 0.2em 1em; */
  margin-bottom: 1em;
  border-radius: 0.5em 0em 0.5em 0.5em;
  /* border: 1px solid rgba(0, 0, 0, 0.1); */
  /* margin-top: 0.25em; */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

c-vnav-item .edit-page-form__container::-webkit-scrollbar {
  display: none;
}

c-vnav-item .edit-page-form__container c-text p {
  margin-top: 0.5em;
  color: rgba(0, 0, 0, 0.5);
  font-size: 0.9em;
}

c-vnav-item .edit-page-form__container c-textbox input {
  margin-top: 0.5em;
}

c-vnav-item .edit-page-form__container .text-with-icon {
  margin-top: 0.25em;
  margin-bottom: 1em;
  font-size: 0.7em;
}

c-vnav-item .edit-page-form__container c-textbox input::placeholder {
  margin-bottom: 1em;
  font-size: 0.9em;
}

c-vnav-item .edit-page-form__container button {
  font-size: 0.8em;
}

c-vnav-item .edit-page-form__container c-row .row-container {
  margin-bottom: 1em;
}

c-vnav-item .back-button {
  padding-left: 0;
}

c-vnav-item .back-button:active {
  background: none;
  padding-left: 0;
}
