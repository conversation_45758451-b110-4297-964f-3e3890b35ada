c-coupon-details-modal p {
  font-size: 0.9em;
  margin: 0;
  margin-bottom: 1.5em;
}

c-coupon-details-modal h2 {
  font-size: 1.25em;
  margin: 0;
  color: var(--accent-color);
}

c-coupon-details-modal label {
  font-size: 0.75em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.5);
}

c-coupon-details-modal .modal-container {
  background: var(--bg-color);
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
  position: fixed;
  top: 2em;
  box-sizing: border-box;
  width: 60vw;
  max-height: 80vh;
  overflow: auto;
  margin: 0 auto;
  padding: 1em;
}

c-coupon-details-modal .row {
  display: flex;
  justify-content: space-between;
}

c-coupon-details-modal .row-item-1 {
  width: 40%;
}

c-coupon-details-modal .row-item-2 {
  width: 30%;
}

c-coupon-details-modal .card {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
}

c-coupon-details-modal .subtext {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
  margin: 0;
}

c-coupon-details-modal .email-stats {
  margin-bottom: 0.25em;
  font-size: 0.8em;
}

c-coupon-details-modal .control-row {
  display: flex;
  justify-content: space-between;
  margin-top: 4em;
  margin-bottom: 0.25em;
}

c-coupon-details-modal .bubble-label {
  margin: 0;
  font-size: 0.65em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 0.25em;
}

c-coupon-details-modal .bubble {
  margin: 0;
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.4em 1em;
  border-radius: 0.25em;
  margin-right: 0.75em;
}

c-coupon-details-modal .blue-bubble {
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}

c-coupon-details-modal .gold-bubble {
  background: var(--accent-golden-bg-lighter);
  color: var(--accent-golden-darker);
}

c-coupon-details-modal .card-slim {
  padding: 1.5em 1.25em 1.5em 1em;
}

c-coupon-details-modal .email-list-container {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
}

c-coupon-details-modal .inputbox {
  width: 180px;
  background-color: rgba(238, 234, 245, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  padding: 0.45em;
  font-size: 0.9em;
  padding-left: 0.5em;
  color: var(--accent-color-lighter);
}

c-coupon-details-modal ::placeholder {
  color: var(--accent-color);
  opacity: 0.5;
  font-weight: 400;
  padding: 0.5em;
}

c-coupon-details-modal :-ms-input-placeholder {
  color: var(--accent-color-lighter);
  padding: 0.5em;
}

c-coupon-details-modal ::-ms-input-placeholder {
  color: var(--accent-color-lighter);
  padding: 0.5em;
}

c-coupon-details-modal .generate-coupon-group {
  display: flex;
  align-items: center;
}

c-coupon-details-modal .no-margin-bottom {
  margin-bottom: 0;
}

c-coupon-details-modal .bubble-container {
  margin-top: 0.75em;
}

c-coupon-details-modal .label {
  font-size: 0.8em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
}

c-coupon-details-modal .member-info {
  font-size: 0.9em;
}

c-coupon-details-modal .row-container c-button .modal-close {
  padding: 0;
}

c-coupon-details-modal .mobile-only-ui {
  display: none;
}

c-coupon-details-modal .desktop-only-ui {
  display: block;
}

c-coupon-details-modal .desktop-only-ui c-button .modal-close {
  padding: 0;
}

@media only screen and (max-width: 768px) {
  c-coupon-details-modal .mobile-only-ui {
    display: block;
  }

  c-coupon-details-modal .mobile-only-ui c-button .modal-close {
    padding: 0;
  }

  c-coupon-details-modal .desktop-only-ui {
    display: none;
  }

  c-coupon-details-modal .control-row c-dropdown select {
    margin-top: 0;
  }

  c-coupon-details-modal .modal-container {
    position: fixed;
    top: 2em;
    box-sizing: border-box;
    width: 92.5vw;
    max-height: 90vh;
    overflow: auto;
    margin: 0 auto;
    padding: 0.75em;
  }

  c-coupon-details-modal .row {
    display: block;
  }

  c-coupon-details-modal .row-item-1 {
    width: 100%;
  }

  c-coupon-details-modal .row-item-2 {
    width: 100%;
    margin-top: 1em;
  }

  c-coupon-details-modal .card {
    padding-bottom: 0;
  }

  c-coupon-details-modal .generate-coupon-group {
    display: block;
  }
}
