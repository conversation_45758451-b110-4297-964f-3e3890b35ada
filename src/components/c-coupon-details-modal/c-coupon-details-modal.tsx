import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-coupon-details-modal",
  styleUrl: "c-coupon-details-modal.css",
})
export class CCouponDetailsModal {
  /*------------------
  Event Emitters
  ------------------*/
  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    }
  }

  @State() isFetching: boolean = true;

  private fetchedData: any;

  componentDidLoad() {
    this.fetchData();
  }

  fetchData() {
    let payload = {
      couponId: state.expandedCouponId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getcouponbyid`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchedData = response.data.payload;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="modal-container">
        {this.isFetching ? (
          <div>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
            <c-skel-card></c-skel-card>
          </div>
        ) : (
          <div>
            <div class="row card">
              <div class="row-item row-item-1">
                <c-row>
                  <h2>{this.fetchedData.name}</h2>
                  <div class="mobile-only-ui">
                    <c-button
                      type="modalClose"
                      name="closeModal"
                      icon-name=""
                      label=""
                    ></c-button>
                  </div>
                </c-row>
                <p class="no-margin-bottom subtext">
                  Created on{" "}
                  <span>
                    {new Date(this.fetchedData.meta.issuedOn)
                      .toString()
                      .substring(4, 15)}
                  </span>{" "}
                  by{" "}
                  <c-text-link
                    url={`mailto:${this.fetchedData.meta.issuer.email}`}
                    label={`${this.fetchedData.meta.issuer.email}`}
                  ></c-text-link>
                </p>
                <div class="bubble-container">
                  {this.fetchedData.deduction.type === "fixed" && (
                    <span class="bubble blue-bubble">
                      ₹{this.fetchedData.deduction.value} discount
                    </span>
                  )}
                  {this.fetchedData.deduction.type === "percentage" && (
                    <span class="bubble blue-bubble">
                      {this.fetchedData.deduction.value}% discount
                    </span>
                  )}
                  {this.fetchedData.deduction.type === "ticketType" && (
                    <span class="bubble blue-bubble">
                      {this.fetchedData.name}
                    </span>
                  )}
                  {this.fetchedData.meta.access === "open" && (
                    <span class="bubble gold-bubble">
                      {this.fetchedData.meta.code}
                    </span>
                  )}
                </div>
              </div>
              <div class="row-item row-item-2">
                {this.fetchedData.meta.access === "open" && (
                  <p>
                    <span class="label">ACCESS</span>
                    <br />
                    <span class="member-info">
                      Anyone with the coupon code can use this coupon
                    </span>
                  </p>
                )}

                {this.fetchedData.meta.access === "emaillist" && (
                  <p>
                    <span class="label">ACCESS</span>
                    <br />
                    <span class="member-info">
                      Only a restricted email list can use this coupon
                    </span>
                  </p>
                )}
              </div>
              <div class="desktop-only-ui">
                <c-button
                  type="modalClose"
                  name="closeModal"
                  icon-name=""
                  label=""
                ></c-button>
              </div>
            </div>
            <c-coupon-details-list
              coupon-id={this.fetchedData.id}
              access={this.fetchedData.meta.access}
              redeemer-list-string={
                this.fetchedData.redeemerList.length > 0
                  ? JSON.stringify(this.fetchedData.redeemerList)
                  : ""
              }
              issued-count={this.fetchedData.issuedCount}
              used-count={this.fetchedData.usedCount}
            ></c-coupon-details-list>
          </div>
        )}
      </div>
    );
  }
}
