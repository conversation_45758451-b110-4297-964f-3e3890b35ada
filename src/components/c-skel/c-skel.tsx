import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "c-skel",
  styleUrl: "c-skel.css",
})
export class CSkel {
  @Prop() variant: string;

  render() {
    if (this.variant === "leftNavigation") {
      return (
        <div class="left-navigation-skel__container">
          <div class="left-navigation-skel__item">
            <div class="shine shine--grey left-navigation-skel__item__icon"></div>
            <div class="shine shine--grey left-navigation-skel__item__label"></div>
          </div>
          <div class="left-navigation-skel__item">
            <div class="shine shine--grey left-navigation-skel__item__icon"></div>
            <div class="shine shine--grey left-navigation-skel__item__label"></div>
          </div>
          <div class="left-navigation-skel__item">
            <div class="shine shine--grey left-navigation-skel__item__icon"></div>
            <div class="shine shine--grey left-navigation-skel__item__label"></div>
          </div>
        </div>
      );
    } else if (this.variant === "ticketList") {
      return (
        <div class="ticket-list__container">
          <div class="shine shine--grey ticket-list__item"></div>
          <div class="shine shine--grey ticket-list__item"></div>
        </div>
      );
    } else if (this.variant === "overview") {
      <div class="ticket-list__container">
        <div class="shine shine--grey ticket-list__item"></div>
        <div class="shine shine--grey ticket-list__item"></div>
      </div>;
    }
  }
}
