import { Component, State, Listen, h } from "@stencil/core";
import { isBankTxCodeValid } from "../../utils/";
import axios from "axios";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "c-checkout-options",
  styleUrl: "c-checkout-options.css",
})
export class CCheckoutOptions {
  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "paymentMode") {
      state.paymentGateway = event.detail.value;
      if (state.paymentGateway === "razorpay") {
        state.isPaymentBtnDisabled = false;
      } else if (state.paymentGateway === "bank") {
        state.isPaymentBtnDisabled = true;
      }
      this.getData();
    }
  }

  @Listen("input-event")
  inputEventHandler(event) {
    if (event.detail.name === "bankTxCode") {
      Store.setBankTxCode(event.detail.value.trim());
      let payload = {
        bankTxCode: event.detail.value.trim(),
      };
      let { error } = isBankTxCodeValid(payload);
      if (error) {
        state.isPaymentBtnDisabled = true;
      } else {
        state.isPaymentBtnDisabled = false;
      }
    }
  }

  @Listen("pay-btn-click-event")
  payBtnClickHandler(event) {
    event.preventDefault();
    state.isPaymentBtnDisabled = true;
    state.isPaying = true;
  }

  @Listen("get-cart")
  getCartHandler(event) {
    event.preventDefault();
    this.getData();
  }

  @State() isFetching: boolean = true;

  componentDidLoad() {
    this.getData();
  }

  getData() {
    this.isFetching = true;
    let payload = {
      eventCode: state.eventCodeForRegistration,
      paymentGateway: state.paymentGateway,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-checkout-total`,
      withCredentials: true,
      data: payload,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.currency = response.data.payload.currency;
          state.cartTotal = response.data.payload.cartTotal;
          state.gatewayFee = response.data.payload.gatewayFee;
          state.grandTotal = response.data.payload.grandTotal;
          if (response.data.payload.coupon.isApplied) {
            state.isCouponApplied = response.data.payload.coupon.isApplied;
            state.appliedCouponName = response.data.payload.coupon.name;
            state.appliedCouponDeductionType =
              response.data.payload.coupon.deductionType;
            state.appliedCouponDeductionValue =
              response.data.payload.coupon.deductionValue;
            state.cartTotalAfterDiscount =
              response.data.payload.coupon.cartTotalAfterDiscount;
            state.deductedAmount = response.data.payload.coupon.deductedAmount;
          }
          if (state.grandTotal === 0) {
            state.isFreePurchase = true;
          }
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="card" style={{ width: `280px` }}>
        <div
          class={`pricebands-container pricebands-container-4 ${
            state.isFreePurchase ? "disabled" : ""
          }`}
        >
          <p class="payment-method-heading">CHOOSE PAYMENT METHOD</p>
          <c-radio
            name="paymentMode"
            label1="Card/Netbanking/UPI/Wallet"
            label2=""
            label3="Gateway fee: 4% of cart total"
            val="razorpay"
            isChecked={state.paymentGateway === "razorpay" ? true : false}
          ></c-radio>
          <div class="hspace"></div>
          <c-radio
            name="paymentMode"
            label1="Bank Transfer"
            label2=": How to transfer?"
            isLabel2Link={true}
            url="https://www.indiahci.org/hcipai-bank-details.html"
            label3="No gateway fee"
            val="bank"
            isChecked={state.paymentGateway === "bank" ? true : false}
          ></c-radio>
          <div
            class={
              state.paymentGateway === "bank"
                ? "bank-tx-inputbox"
                : "hide-inputbox-container"
            }
          >
            <c-inputbox
              class="hide-inputbox"
              type="text"
              name="bankTxCode"
              placeholder="Enter Transaction Code"
            ></c-inputbox>
          </div>
        </div>

        {state.isCouponApplied ? (
          <c-coupon-applied></c-coupon-applied>
        ) : (
          <c-coupon-list></c-coupon-list>
        )}

        {this.isFetching ? (
          <div class="pricebands-container">
            <c-skel-line color="accent-light" width={100}></c-skel-line>
            <br />
            <c-skel-line color="accent-light" width={100}></c-skel-line>
            <br />
            <c-skel-line color="accent-light" width={100}></c-skel-line>
          </div>
        ) : (
          <div class="pricebands-container pricebands-container-1">
            <c-priceband
              heading="Cart total"
              subheading={state.isCouponApplied ? "Before discount" : ""}
              currency={state.currency}
              price={state.cartTotal}
            ></c-priceband>

            {state.isCouponApplied && (
              <c-priceband
                heading="Discount"
                subheading={`${
                  state.appliedCouponDeductionType === "fixed"
                    ? "₹" + state.appliedCouponDeductionValue + " from"
                    : ""
                }${
                  state.appliedCouponDeductionType === "percentage"
                    ? state.appliedCouponDeductionValue + "% of"
                    : ""
                } cart total`}
                is-discount={true}
                currency={`- ${state.currency}`}
                price={state.deductedAmount}
              ></c-priceband>
            )}

            {state.isCouponApplied && <div class="hseperator"></div>}

            {state.isCouponApplied && (
              <c-priceband
                heading="New cart total"
                subheading="After discount"
                currency={state.currency}
                price={state.cartTotalAfterDiscount}
              ></c-priceband>
            )}

            {state.paymentGateway === "razorpay" &&
            state.isFreePurchase === false ? (
              <c-priceband
                heading="Gateway fee"
                subheading={`4% of ${
                  state.isCouponApplied ? "new" : ""
                } cart total`}
                currency={state.currency}
                price={state.gatewayFee}
              ></c-priceband>
            ) : (
              ""
            )}

            <strong>
              <c-priceband
                heading="Grand total"
                subheading=""
                currency={state.currency}
                price={state.grandTotal}
              ></c-priceband>
            </strong>
          </div>
        )}

        <div class="pricebands-container pricebands-container-3">
          <c-btn
            name="pay"
            label={
              state.isFreePurchase || state.paymentGateway === "bank"
                ? "Continue"
                : "Pay"
            }
            action-label="Paying.."
            is-in-action={state.isPaying}
            is-disabled={
              state.isFreePurchase ? false : state.isPaymentBtnDisabled
            }
          ></c-btn>
        </div>
      </div>
    );
  }
}
