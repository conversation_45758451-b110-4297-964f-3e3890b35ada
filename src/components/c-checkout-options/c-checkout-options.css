c-checkout-options .card {
  background: white;
  margin: 0 auto;
  /* padding: 0.5em 1.5em 0.5em 1.5em; */
  border-radius: 0.25em;
  /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05); */
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 5em;
  padding: 1em;
}

c-checkout-options .hide-inputbox-container {
  display: none;
}

c-checkout-options .bank-tx-inputbox c-inputbox .inputbox {
  width: 92%;
  margin-left: 8%;
  font-size: 0.9em;
  margin-top: 0.5em;
  margin-bottom: 0;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.5em 0 0.5em 0.75em;
}

c-checkout-options .bank-tx-inputbox c-inputbox .inputbox:focus {
  border: 1px solid rgba(0, 0, 0, 0.3);
}

c-checkout-options c-radio .radio-btn-container {
  margin-top: 0;
  margin-bottom: 0em;
  align-items: center;
}

c-checkout-options .pricebands-container {
  background: var(--accent-color-bg-lightest);
  padding: 0.75em 0.75em 0.75em 1.25em;
  border-radius: 0.25em 0.25em 0 0;
  margin-top: 1em;
  border: 1px solid var(--accent-color-bg-lighter);
}

c-checkout-options .card c-btn button {
  margin-top: 0;
  border-radius: var(--border-radius);
  font-size: 0.9em;
}

c-checkout-options footer {
  display: block;
}

c-checkout-options .radio-btn-label-1 {
  font-size: 0.9em;
}

c-checkout-options .heading {
  margin-bottom: 0em;
  margin-top: 0em;
  font-size: 1.1em;
}

c-checkout-options .hseperator {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.3);
  margin-top: 0.5em;
  margin-bottom: 1em;
}

c-checkout-options .pricebands-container-1 {
  padding: 1em;
  border-bottom: 0;
}

c-checkout-options .pricebands-container-2 {
  padding: 0.5em 0.75em 0.75em 0.75em;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(0, 0, 0, 0.03);
  border-radius: var(--border-radius);
}

c-checkout-options .pricebands-container-3 {
  margin-top: 0;
  border-top: 0;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  padding: 0em 1em 1em 1em;
}

c-checkout-options .payment-section-header {
  margin: 0;
  font-size: 0.8em;
  font-weight: 700;
  margin-bottom: 0.5em;
  color: rgba(0, 0, 0, 0.6);
}

c-checkout-options .coupon-applied-success {
  background: #eafaf1;
  color: var(--accent-green-darker);
}

c-checkout-options .hseperator {
  border-bottom: 1px solid var(--accent-color-bg-lighter);
}

c-checkout-options c-checkbox input[type="checkbox"] {
  outline: 1px solid rgba(0, 0, 0, 0.1);
}

c-checkout-options c-checkbox input[type="checkbox"]:hover {
  cursor: pointer;
  border: 1px solid red;
  outline: 1px solid rgba(0, 0, 0, 0.3);
}

c-checkout-options c-checkbox label {
  font-size: 0.9em;
  margin-left: 0.5em;
}

c-checkout-options .pricebands-container-4 {
  margin-top: 0;
  border-radius: var(--border-radius);
  padding: 1em;
  background: none;
  border: 1px solid var(--accent-color-bg-lightest);
}

c-checkout-options .payment-method-heading {
  margin: 0;
  font-size: 0.7em;
  /* font-weight: 700; */
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 0.75em;
}

c-checkout-options .disabled {
  opacity: 0.3;
  pointer-events: none;
}

c-checkout-options .hspace {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

c-checkout-options
  .pricebands-container
  strong
  c-priceband
  .priceband-container:last-child {
  margin-bottom: 0;
}

c-checkout-options .pricebands-container-4 {
  padding: 0;
  border: 0;
}

c-checkout-options c-radio .radio-btn-label-3 {
  margin-left: 2em;
}

@media only screen and (max-width: 768px) {
  c-checkout-options .pricebands-container-4 {
    padding: 0;
    border: 0;
  }

  c-checkout-options c-radio .radio-btn-label-3 {
    margin-left: 2em;
  }

  c-checkout-options .bank-tx-inputbox c-inputbox .inputbox {
    margin-left: 1.75em;
    width: 90%;
  }
}
