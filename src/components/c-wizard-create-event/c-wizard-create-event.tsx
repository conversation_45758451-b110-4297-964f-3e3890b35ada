import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  State,
  h,
} from "@stencil/core";
import { gsap } from "gsap";
import axios from "axios";
import state from "../../global/state";

interface ManagerBubbleInterface {
  name: string;
  email: string;
}

@Component({
  tag: "c-wizard-create-event",
  styleUrl: "c-wizard-create-event.css",
})
export class CWizardCreateEvent {
  @State()
  isBasicNextDisabled: boolean = true;
  @State() isScheduleNextDisabled: boolean = true;
  @State() isInvoicingNextDisabled: boolean = true;
  @State() createEventDisabled: boolean = true;
  @State() wizardStepCount: number = 0;
  @State() managers: any = [];
  @State() isRegistrantsFetched: boolean = false;
  @State() registrantArr: any = [];
  @State() isRegistrantNameFilled: boolean = false;
  @State() isCreateButtonActive: boolean = false;
  @State() invoicePrefix: string = "";
  @State() progressBarText: string = "Saving details..";

  private eventName: string = "";
  private eventTagline: string = "";
  private eventVenueLabel: string = "";
  private eventVenueUrl: string = "";
  private eventWebsite: string = "";
  private eventStartDate: string = "";
  private eventStartTime: string = "";
  private eventEndDate: string = "";
  private eventEndTime: string = "";
  private eventLogo: any;
  private eventBanner: any;
  private eventPoster: any;
  private registrationStartDate: string = "";
  private registrationStartTime: string = "";
  private registrationEndDate: string = "";
  private registrationEndTime: string = "";
  private fetchedRegistrantArr: any = [];
  private filteredRegistrantArr: any = [];
  private delaySearch: any;
  private wizardSteps: any = [
    { name: "Basic" },
    { name: "Schedule" },
    { name: "Assets" },
    { name: "Invoicing" },
    { name: "Managers" },
    { name: "Almost done!" },
  ];
  private isEventPublished: boolean = false;

  progressBarEl!: HTMLDivElement;

  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Event({
    eventName: "fetchNewData",
    bubbles: true,
  })
  fetchNewDataEvent: EventEmitter;

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "nextStep") {
      this.incrementStep();
    } else if (e.detail.name === "previousStep") {
      this.decrementStep();
    } else if (e.detail.name === "createEvent") {
      this.createEvent();
    } else if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    }
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "eventStart") {
      this.eventStartDate = e.detail.value;
    } else if (e.detail.name === "eventEnd") {
      this.eventEndDate = e.detail.value;
    } else if (e.detail.name === "registrationStart") {
      this.registrationStartDate = e.detail.value;
    } else if (e.detail.name === "registrationEnd") {
      this.registrationEndDate = e.detail.value;
    }
    this.wizardStepCheck();
  }

  @Listen("timeInput") handleTimeInput(e) {
    if (e.detail.name === "eventStart") {
      this.eventStartTime = e.detail.value;
    } else if (e.detail.name === "eventEnd") {
      this.eventEndTime = e.detail.value;
    } else if (e.detail.name === "registrationStart") {
      this.registrationStartTime = e.detail.value;
    } else if (e.detail.name === "registrationEnd") {
      this.registrationEndTime = e.detail.value;
    }
    this.wizardStepCheck();
  }

  @Listen("textInput") handleTextInput(e) {
    if (e.detail.name === "eventName") {
      this.eventName = e.detail.value;
    } else if (e.detail.name === "eventTagline") {
      this.eventTagline = e.detail.value;
    } else if (e.detail.name === "eventVenueLabel") {
      this.eventVenueLabel = e.detail.value;
    } else if (e.detail.name === "eventVenueUrl") {
      this.eventVenueUrl = e.detail.value;
    } else if (e.detail.name === "eventWebsite") {
      this.eventWebsite = e.detail.value;
    } else if (e.detail.name === "invoicePrefix") {
      this.invoicePrefix = e.detail.value;
    } else if (e.detail.name === "registrantSearch") {
      this.isRegistrantNameFilled = true;
      if (e.detail.value.length > 0) {
        this.generateRegistrantListFromSearchString(e.detail.value);
      } else {
        this.registrantArr = [];
      }
    }
    this.wizardStepCheck();
  }

  @Listen("managerSelected") managerSelectedHandler(e) {
    let obj = {
      fullName: e.detail.managerName,
      email: e.detail.managerEmail,
    };
    let isManagerInArray: boolean = false;
    this.managers.map((manager: any) => {
      if (obj.email === manager.email) {
        isManagerInArray = true;
      }
    });
    if (!isManagerInArray) {
      this.managers.push(obj);
    }
    this.clearManagerSearch();
  }

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "publishEvent") {
      if (event.detail.value === "yes") {
        this.isEventPublished = true;
      } else if (event.detail.value === "no") {
        this.isEventPublished = false;
      }
    }
  }

  @Listen("fileChangeEvent")
  fileChangeEventHandler(event) {
    if (event.detail.name === "eventLogo") {
      this.eventLogo = event.detail.file;
    } else if (event.detail.name === "eventBanner") {
      this.eventBanner = event.detail.file;
    } else if (event.detail.name === "eventPoster") {
      this.eventPoster = event.detail.file;
    }
  }

  componentDidLoad() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-all-accounts`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchedRegistrantArr = response.data.payload;
          this.isRegistrantsFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  clearManagerSearch() {
    this.registrantArr = [];
    this.isRegistrantNameFilled = false;
  }

  createEvent() {
    this.isCreateButtonActive = true;
    this.startProgressBarAnimation();

    let formData: any = new FormData();
    formData.append("eventName", this.eventName);
    formData.append("eventTagline", this.eventTagline);
    formData.append("eventVenueLabel", this.eventVenueLabel);
    formData.append("eventVenueUrl", this.eventVenueUrl);
    formData.append("eventWebsite", this.eventWebsite);
    formData.append("invoicePrefix", this.invoicePrefix);
    formData.append("eventStartDate", this.eventStartDate);
    formData.append("eventStartTime", this.eventStartTime);
    formData.append("eventEndDate", this.eventEndDate);
    formData.append("eventEndTime", this.eventEndTime);
    formData.append("registrationStartDate", this.registrationStartDate);
    formData.append("registrationStartTime", this.registrationStartTime);
    formData.append("registrationEndDate", this.registrationEndDate);
    formData.append("registrationEndTime", this.registrationEndTime);
    formData.append("eventLogo", this.eventLogo);
    formData.append("eventBanner", this.eventBanner);
    formData.append("eventPoster", this.eventPoster);
    formData.append("managers", JSON.stringify(this.managers));
    formData.append("isPublished", JSON.stringify(this.isEventPublished));

    axios({
      method: "POST",
      data: formData,
      baseURL: `${state.baseUrl}/createevent`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          this.handleDataFetchFailure();
        } else if (response.data.status === "Success") {
          this.handleDataFetchSuccess();
          console.log("inside success");
        }
      })
      .catch((error: any) => {
        alert(error);
      });
  }

  animationCompletionCheckTimeout: any;

  handleDataFetchSuccess() {
    this.fetchNewDataEvent.emit();
    this.closeModalEvent.emit();
  }

  handleDataFetchFailure() {
    alert("Failed to create event. Please try again");
    this.initComponent();
  }

  initComponent() {
    this.isBasicNextDisabled = true;
    this.isScheduleNextDisabled = true;
    this.isInvoicingNextDisabled = true;
    this.createEventDisabled = true;
    this.wizardStepCount = 0;
    this.managers = [];
    this.isRegistrantsFetched = false;
    this.registrantArr = [];
    this.isRegistrantNameFilled = false;
    this.isCreateButtonActive = false;
    this.invoicePrefix = "";
    this.progressBarText = "Saving details..";
    this.eventName = "";
    this.eventTagline = "";
    this.eventVenueLabel = "";
    this.eventVenueUrl = "";
    this.eventWebsite = "";
    this.eventStartDate = "";
    this.eventStartTime = "";
    this.eventEndDate = "";
    this.eventEndTime = "";
    this.eventLogo = "";
    this.eventBanner = "";
    this.eventPoster = "";
    this.registrationStartDate = "";
    this.registrationStartTime = "";
    this.registrationEndDate = "";
    this.registrationEndTime = "";
    this.fetchedRegistrantArr = [];
    this.filteredRegistrantArr = [];
    this.isEventPublished = false;
  }

  startProgressBarAnimation() {
    setTimeout(() => {
      let tl = gsap.timeline();
      tl.to(this.progressBarEl, {
        width: "30%",
        duration: 1,
      });
      tl.to(this.progressBarEl, {
        width: "50%",
        duration: 2,
      });
      tl.to(this.progressBarEl, {
        width: "70%",
        duration: 2,
      });
      tl.to(this.progressBarEl, {
        width: "90%",
        duration: 2,
      });
      tl.to(this.progressBarEl, {
        width: "100%",
        duration: 1,
      });
    }, 500);

    setTimeout(() => {
      this.progressBarText = "Uploading logo..";
    }, 1500);
    setTimeout(() => {
      this.progressBarText = "Uploading banner..";
    }, 2500);
    setTimeout(() => {
      this.progressBarText = "Uploading poster..";
    }, 4500);
    setTimeout(() => {
      this.progressBarText = "Finalising..";
    }, 6500);
  }

  incrementStep() {
    this.wizardStepCount = this.wizardStepCount + 1;
  }

  decrementStep() {
    this.wizardStepCount = this.wizardStepCount - 1;
  }

  wizardStepCheck() {
    if (
      this.eventName &&
      this.eventTagline &&
      this.eventVenueLabel &&
      this.eventVenueUrl &&
      this.eventWebsite
    ) {
      this.isBasicNextDisabled = false;
    } else {
      this.isBasicNextDisabled = true;
    }

    if (
      this.eventStartDate &&
      this.eventStartTime &&
      this.eventEndDate &&
      this.eventEndTime &&
      this.registrationStartDate &&
      this.registrationStartTime &&
      this.registrationEndDate &&
      this.registrationEndTime
    ) {
      this.isScheduleNextDisabled = false;
    } else {
      this.isScheduleNextDisabled = true;
    }

    if (this.invoicePrefix) {
      this.isInvoicingNextDisabled = false;
    } else {
      this.isInvoicingNextDisabled = true;
    }
  }

  generateRegistrantListFromSearchString(searchString: string) {
    clearTimeout(this.delaySearch);
    this.delaySearch = setTimeout(() => {
      this.filteredRegistrantArr = this.fetchedRegistrantArr.filter(
        (registrant) => {
          let fullName = registrant.firstName + " " + registrant.lastName;
          return (
            registrant.firstName
              .toLowerCase()
              .includes(searchString.toLowerCase()) ||
            registrant.lastName
              .toLowerCase()
              .includes(searchString.toLowerCase()) ||
            registrant.email
              .toLowerCase()
              .includes(searchString.toLowerCase()) ||
            fullName.toLowerCase().includes(searchString.toLowerCase())
          );
        }
      );
      this.registrantArr = "";
      this.registrantArr = this.filteredRegistrantArr;
    }, 500);
  }

  removeManager(email: string) {
    let buffManagers: any = [];
    this.managers.map((manager) => {
      if (manager.email != email) {
        buffManagers.push(manager);
      }
    });
    this.managers = buffManagers;
  }

  BasicStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="eventName"
          placeholder={`e.g. India HCI ${new Date().getFullYear() + 1}`}
          isDisabled={false}
          value=""
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TAGLINE
        </c-text>
        <c-textbox
          input-type="text"
          name="eventTagline"
          placeholder={`e.g. Design for disruption`}
          isDisabled={false}
          value=""
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          VENUE
        </c-text>
        <div class="double-textbox">
          <c-textbox
            input-type="text"
            name="eventVenueLabel"
            placeholder={`Name e.g. IIT Bombay`}
            isDisabled={false}
            value=""
          ></c-textbox>
          <c-textbox
            input-type="text"
            name="eventVenueUrl"
            placeholder={`Google map url`}
            isDisabled={false}
            value=""
          ></c-textbox>
        </div>
      </div>

      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          WEBSITE
        </c-text>
        <c-textbox
          input-type="text"
          name="eventWebsite"
          placeholder={`e.g. indiahci.org/2020`}
          isDisabled={false}
          value=""
        ></c-textbox>
      </div>

      <div class="field-container">
        <div class="field-container-row">
          <div></div>
          <c-button
            name="nextStep"
            icon-name=""
            isDisabled={this.isBasicNextDisabled}
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  ScheduleStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          EVENT DATES
        </c-text>
        <div class="field-container-row">
          <c-date-picker name="eventStart"></c-date-picker>
          <span class="mdash">&mdash;</span>
          <c-date-picker name="eventEnd"></c-date-picker>
        </div>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          REGISTRATION DATES
        </c-text>
        <div class="field-container-row">
          <c-date-picker name="registrationStart"></c-date-picker>
          <span class="mdash">&mdash;</span>
          <c-date-picker name="registrationEnd"></c-date-picker>
        </div>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="nextStep"
            icon-name=""
            isDisabled={this.isScheduleNextDisabled}
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  InvoicingStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          INVOICE PREFIX
        </c-text>
        <c-textbox
          input-type="text"
          name="invoicePrefix"
          placeholder={`e.g. IHCI${new Date().getFullYear() + 1}`}
          isDisabled={false}
          value=""
        ></c-textbox>
        <div class="subtext-container invoice-subtext-container">
          {this.invoicePrefix && (
            <c-text type="subtext">
              Example of invoice number: {this.invoicePrefix}-0001
            </c-text>
          )}
        </div>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="nextStep"
            icon-name=""
            isDisabled={this.isInvoicingNextDisabled}
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  AssetsStep: FunctionalComponent = () => (
    <div class="wizard-content step-2-content">
      <div class="field-container">
        <c-text type="modalLabel">
          LOGO{" "}
          <span class="modal-label-light">
            (Max size = 1MB, Aspect Ratio = Any)
          </span>{" "}
        </c-text>
        <c-uploader file-type="image" name="eventLogo"></c-uploader>
      </div>
      <div class="field-container">
        <c-text type="modalLabel">
          BANNER{" "}
          <span class="modal-label-light">
            (Max size = 1MB, Aspect Ratio = 4:3)
          </span>{" "}
        </c-text>
        <c-uploader file-type="image" name="eventBanner"></c-uploader>
      </div>
      <div class="field-container">
        <c-text type="modalLabel">
          POSTER{" "}
          <span class="modal-label-light">
            (Max size = 1MB, Aspect Ratio = 3:2)
          </span>{" "}
        </c-text>
        <c-uploader file-type="image" name="eventPoster"></c-uploader>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button name="nextStep" icon-name="" isDisabled={false}>
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  ManagersStep: FunctionalComponent = () => (
    <div onClick={() => this.clearManagerSearch()}>
      {this.isRegistrantsFetched ? (
        <div class="wizard-content" onClick={() => this.clearManagerSearch()}>
          <div class="field-container">
            <c-text type="modalLabel">SEARCH REGISTERED USERS</c-text>
            <c-textbox
              input-type="text"
              name="registrantSearch"
              placeholder="Enter name or email"
              isDisabled={false}
              isTextboxFilled={this.isRegistrantNameFilled}
              value=""
            ></c-textbox>
            <c-list
              type="searchDropList"
              name="managerSearchDropList"
              listItemsAsString={JSON.stringify(this.registrantArr)}
            ></c-list>
          </div>
          <div class="field-container">
            <c-text type="modalLabel">MANAGERS</c-text>
            {this.managers.length > 0 ? (
              <div>
                <div class="manager-list-container">
                  {this.managers.map((manager) => (
                    <this.ManagerBubble
                      name={manager.fullName}
                      email={manager.email}
                    ></this.ManagerBubble>
                  ))}
                </div>
                <div class="subtext-container">
                  <ion-icon name="alert-circle-outline"></ion-icon>
                  <c-text type="subtext">
                    The above individuals will be notified via email
                  </c-text>
                </div>
              </div>
            ) : (
              <div class="subtext-container">
                <c-text type="subtext">0 managers added</c-text>
              </div>
            )}
          </div>
          <div class="field-container">
            <div class="field-container-row">
              <c-button
                name="previousStep"
                type="back"
                icon-name=""
                isDisabled={false}
              >
                Back
              </c-button>
              <c-button
                name="nextStep"
                icon-name=""
                isDisabled={this.isInvoicingNextDisabled}
              >
                Next
              </c-button>
            </div>
          </div>
        </div>
      ) : (
        <this.LoadingScreen></this.LoadingScreen>
      )}
    </div>
  );

  PublishStep: FunctionalComponent = () => (
    <div class="wizard-content">
      {this.isCreateButtonActive ? (
        <this.ProgressBar></this.ProgressBar>
      ) : (
        <div class="field-container publish-options-container">
          <c-text type="modalLabel">IMPORTANT</c-text>
          <c-text>
            A newly created event remains in <strong>unpublished</strong> state
            i.e. they are visible only to the admins and not to other users.
          </c-text>
          <br />
          <c-text>
            An admin must manually publish the event only after adding the
            appropriate tickets
          </c-text>
        </div>
      )}

      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="createEvent"
            icon-name=""
            isDisabled={false}
            isInAction={this.isCreateButtonActive}
          >
            {this.isCreateButtonActive ? "" : " Create event"}
          </c-button>
        </div>
      </div>
    </div>
  );

  ProgressBar: FunctionalComponent = () => (
    <div class="progress-container">
      <c-text>{this.progressBarText}</c-text>
      <div class="progress-bar-container">
        <div
          class="progress-bar"
          ref={(el) => (this.progressBarEl = el as HTMLInputElement)}
        ></div>
        <div class="progress-bar-background"></div>
      </div>
    </div>
  );

  LoadingScreen: FunctionalComponent = () => (
    <div class="loading-screen-container">
      <div class="spinner-container">
        <c-spinner-dark></c-spinner-dark>
        &nbsp;&nbsp;
        <c-text>Loading registrants...</c-text>
      </div>
    </div>
  );

  ManagerBubble: FunctionalComponent<ManagerBubbleInterface> = ({
    name,
    email,
  }) => (
    <div class="manager-bubble-container">
      <p>{name}</p>
      <button onClick={() => this.removeManager(email)}>
        <ion-icon name="close-outline"></ion-icon>
      </button>
    </div>
  );

  render() {
    return (
      <div class="wizard-container" onClick={() => this.clearManagerSearch()}>
        <div class="wizard-header">
          <div>
            <c-text type="wizardHeading">
              {this.wizardSteps[this.wizardStepCount].name}
            </c-text>
            <c-text type="wizardSubHeading">
              STEP {this.wizardStepCount + 1} OF {this.wizardSteps.length}
            </c-text>
          </div>
          {!this.isCreateButtonActive && (
            <c-button
              type="modalClose"
              name="closeModal"
              icon-name=""
              label=""
            ></c-button>
          )}
        </div>
        {this.wizardStepCount === 0 && <this.BasicStep></this.BasicStep>}
        {this.wizardStepCount === 1 && <this.ScheduleStep></this.ScheduleStep>}
        {this.wizardStepCount === 2 && <this.AssetsStep></this.AssetsStep>}
        {this.wizardStepCount === 3 && (
          <this.InvoicingStep></this.InvoicingStep>
        )}
        {this.wizardStepCount === 4 && <this.ManagersStep></this.ManagersStep>}
        {this.wizardStepCount === 5 && <this.PublishStep></this.PublishStep>}
      </div>
    );
  }
}
