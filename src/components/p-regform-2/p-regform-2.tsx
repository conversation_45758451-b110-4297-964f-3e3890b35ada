import {
  Component,
  FunctionalComponent,
  Listen,
  State,
  Prop,
  h,
} from "@stencil/core";
import { RouterHistory, MatchResults, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-regform-2",
  styleUrl: "p-regform-2.css",
})
export class PRegform_2 {
  /*----
  Props
  ----*/
  @Prop() history: RouterHistory;
  @Prop() match: MatchResults;

  /*----
  States
  ----*/
  @State() isFetchingData: boolean = true;
  @State() isModalVisible: boolean = false;
  @State() isToastActive: boolean = false;

  @State() isTicketsFetched: boolean = false;
  @State() pages: any = [];
  @State() ticketsInPage: any = [];
  @State() isRegSysFetched: boolean = false;
  @State() isInAction: boolean = false;

  /*-------------
  Event Listeners
  -------------*/
  @Listen("checkout-btn-click-event")
  checkoutBtnHandler() {
    this.history.push(`/checkout/${state.eventCodeForRegistration}`, {});
  }

  @Listen("member-discount-applied")
  membershipDiscountAppliedHandler(event) {
    event.target.closest("c-section-get-member-discount").remove();
    state.isMemberDiscountApplied = true;
  }

  @Listen("close-save-invoice-pref-modal")
  closeSaveInvoicePrefModal(event) {
    this.isModalVisible = false;
    this.showToast({
      type: event.detail.status,
      label: event.detail.msg,
    });
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    if (event.detail.cNavItemName === "back") {
      this.history.goBack();
      return;
    }
    state.configureActivePageId = event.detail.cNavItemName;
    state.configureActivePageLabel = event.detail.label;
    this.getTicketsByEventCodeAndPageId();
  }

  @Listen("dropdown-input-event")
  dropDownHandler(event) {
    let value = event.detail.value;
    state.configureActivePageId = value.split("---")[0];
    state.configureActivePageLabel = value.split("---")[1];
    this.getTicketsByEventCodeAndPageId();
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "addFullTicketToCart") {
      this.addFullTicketToCart(e.detail.value);
    } else if (e.detail.name === "addBasicTicketToCart") {
      let value = e.detail.value.split("---");
      this.addBasicTicketToCart(
        value[0],
        value[1] === "primary" ? true : false
      );
    } else if (e.detail.name === "addTrackSessionToCart") {
      this.addTrackSessionToCart(e.detail.value);
    } else if (e.detail.name === "removeFullTicketFromCart") {
      this.removeFullTicketFromCart(e.detail.value);
    } else if (e.detail.name === "removeBasicTicketFromCart") {
      let value = e.detail.value.split("---");
      this.removeBasicTicketFromCart(
        value[0],
        value[1] === "primary" ? true : false
      );
    } else if (e.detail.name === "removeTrackSessionFromCart") {
      this.removeTrackSessionFromCart(e.detail.value);
    }
  }

  // private toastType: string;
  // private toastLabel: string;
  // private toastDuration: number = 7;
  private navOpts = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "Tickets",
    },
  ];

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    if (this.match.params.eventCodeForRegistration) {
      state.eventCodeForRegistration =
        this.match.params.eventCodeForRegistration.trim();
    }
  }

  componentDidLoad() {
    this.getRegSys();
  }

  /*-----
  Methods
  -----*/
  addFullTicketToCart(ticketId: string) {
    let payload = {
      ticketId: ticketId,
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/add-to-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getCart();
          state.isPrimaryTicketInCart = true;
          this.updateTicketsByEventCodeAndPageId();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  addBasicTicketToCart(ticketId: string, isPrimary: boolean) {
    let payload = {
      ticketId: ticketId,
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/add-to-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getCart();
          if (isPrimary) {
            state.isPrimaryTicketInCart = true;
          }
          // this.getTicketsByEventCodeAndPageId();
          this.updateTicketsByEventCodeAndPageId();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  addTrackSessionToCart(data: string) {
    let ticketId: any = data.split("#")[0];
    let sessionId: any = data.split("#")[1];

    let payload = {
      ticketId: ticketId,
      sessionId: sessionId,
      eventCode: state.eventCodeForRegistration,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/add-to-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getCart();
          this.updateTicketsByEventCodeAndPageId();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  removeFullTicketFromCart(ticketId: string) {
    let payload = {
      ticketId: ticketId,
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/remove-from-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getCart();
          state.isPrimaryTicketInCart = false;
          this.updateTicketsByEventCodeAndPageId();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  removeBasicTicketFromCart(ticketId: string, isPrimary: boolean) {
    let payload = {
      ticketId: ticketId,
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/remove-from-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          if (isPrimary) {
            state.isPrimaryTicketInCart = false;
          }
          this.getCart();
          // this.getTicketsByEventCodeAndPageId();
          this.updateTicketsByEventCodeAndPageId();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  removeTrackSessionFromCart(data: string) {
    let ticketId: any = data.split("#")[0];
    let sessionId: any = data.split("#")[1];

    let payload = {
      ticketId: ticketId,
      sessionId: sessionId,
      eventCode: state.eventCodeForRegistration,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/remove-from-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getCart();
          this.updateTicketsByEventCodeAndPageId();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  getCart() {
    let payload = {
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.cartItems = response.data.payload.cartItems;
          state.cartTotal = response.data.payload.cartTotal;
          if (state.cartItems.length > 0) {
            state.isCheckoutBtnDisabled = false;
          } else {
            state.isCheckoutBtnDisabled = true;
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  getRegSys() {
    if (this.isRegSysFetched) this.isRegSysFetched = false;
    let payload = {
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getregsysbyeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.pages = response.data.payload.pages;
          state.configureActivePageId = this.pages[0].name;
          state.configureActivePageLabel = this.pages[0].label;
          state.configureActivePageIcon = this.pages[0].icon;

          this.pages.map((page: any) => {
            this.navOpts.push(page);
          });

          this.getTicketsByEventCodeAndPageId();
          this.getCart();
          this.isRegSysFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  getTicketsByEventCodeAndPageId() {
    this.isTicketsFetched = false;
    let payload = {
      eventCode: state.eventCodeForRegistration,
      pageId: state.configureActivePageId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getticketsbypageidandeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.ticketsInPage = response.data.payload;
          this.ticketsInPage = [...this.ticketsInPage];
          this.isTicketsFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  updateTicketsByEventCodeAndPageId() {
    let payload = {
      eventCode: state.eventCodeForRegistration,
      pageId: state.configureActivePageId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getticketsbypageidandeventcode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          // this.ticketsInPage = [];
          // this.ticketsInPage = [...this.ticketsInPage];
          this.ticketsInPage = response.data.payload;
          this.ticketsInPage = [...this.ticketsInPage];
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  showToast(obj) {
    console.log(obj);
  }

  /*------------------
  Functional Component
  ------------------*/
  NoPages: FunctionalComponent = () => (
    <c-sidebar type="left">
      <div class="empty-container">
        <c-text>No pages</c-text>
      </div>
    </c-sidebar>
  );

  NoTickets: FunctionalComponent = () => (
    <c-section>
      <div class="empty-container">
        <c-text>
          Tickets are yet to be uploaded in{" "}
          <u>{state.configureActivePageLabel}</u>
        </c-text>
      </div>
    </c-section>
  );

  PageList: FunctionalComponent = () => (
    <c-sidebar type="left">
      {!this.isInAction && (
        <c-vnav
          nav-opts-str={JSON.stringify(this.navOpts)}
          isConfigMode={false}
        ></c-vnav>
      )}
    </c-sidebar>
  );

  PageLoader: FunctionalComponent = () => (
    <c-sidebar type="left">
      <c-skel variant="leftNavigation"></c-skel>
    </c-sidebar>
  );

  TicketList: FunctionalComponent = () => (
    <c-section>
      {state.isMember && !state.isFullTicketPurchased ? (
        <c-banner>
          <c-text>
            Hi, {state.firstName}
            <br />
            As an HCIPAI Member, you're eligible for a discount on your ticket. Please apply your coupon at checkout.
          </c-text>
        </c-banner>
      ) : (
        ""
      )}

      {this.ticketsInPage.map(
        (ticket: any) =>
          ticket.ticketType === "fullTicket" &&
          ticket.isTicketVisible && (
            <c-ticket-full-ticket
              ticketId={ticket.ticketId}
              type={ticket.ticketType}
              ticketTitle={ticket.ticketTitle}
              persona={ticket.persona}
              tierString={JSON.stringify(ticket.ticketTier)}
              isVisible={ticket.isTicketVisible}
              isDisabled={ticket.isTicketDisabled}
              isPrimary={ticket.isTicketPrimary}
              isPrimaryDependent={ticket.isTicketPrimaryDependent}
              isTicketInCart={ticket.isTicketInCart}
              isTicketPurchased={ticket.isTicketPurchased}
              purchaseStatus={ticket.purchaseStatus}
              isConfigMode={false}
            ></c-ticket-full-ticket>
          )
      )}

      {this.ticketsInPage.map(
        (ticket: any) =>
          ticket.ticketType === "basicTicket" &&
          ticket.isTicketVisible && (
            <c-ticket-basic-ticket
              ticketId={ticket.ticketId}
              type={ticket.ticketType}
              ticketTitle={ticket.ticketTitle}
              persona={ticket.persona}
              tierString={JSON.stringify(ticket.ticketTier)}
              isVisible={ticket.isTicketVisible}
              isDisabled={ticket.isTicketDisabled}
              isPrimary={ticket.isTicketPrimary}
              isPrimaryDependent={ticket.isTicketPrimaryDependent}
              isTicketInCart={ticket.isTicketInCart}
              isTicketPurchased={ticket.isTicketPurchased}
              purchaseStatus={ticket.purchaseStatus}
              isConfigMode={false}
            ></c-ticket-basic-ticket>
          )
      )}

      {this.ticketsInPage.map(
        (ticket: any) =>
          ticket.ticketType === "trackTicket" &&
          ticket.isTicketVisible && (
            // Primary dependant track ticket but enable for VD
            <c-ticket-track-ticket
              ticketId={ticket.ticketId}
              type={ticket.ticketType}
              persona={ticket.persona}
              sessionString={JSON.stringify(ticket.sessions)}
              isVisible={ticket.isTicketVisible}
              isPrimary={ticket.isTicketPrimary}
              isPrimaryDependent={ticket.isTicketPrimaryDependent}
              isDisabled={
                !state.isPrimaryTicketInCart && !state.isPrimaryTicketPurchased
              }
            ></c-ticket-track-ticket>

            // Primary dependant track ticket
            // <c-ticket-track-ticket
            //   ticketId={ticket.ticketId}
            //   type={ticket.ticketType}
            //   persona={ticket.persona}
            //   sessionString={JSON.stringify(ticket.sessions)}
            //   isVisible={ticket.isTicketVisible}
            //   isPrimary={ticket.isTicketPrimary}
            //   isPrimaryDependent={ticket.isTicketPrimaryDependent}
            //   isDisabled={
            //     !state.isPrimaryTicketInCart && !state.isPrimaryTicketPurchased
            //   }
            // ></c-ticket-track-ticket>

            // Allows track purchase without primary
            // <c-ticket-track-ticket
            //   ticketId={ticket.ticketId}
            //   type={ticket.ticketType}
            //   persona={ticket.persona}
            //   sessionString={JSON.stringify(ticket.sessions)}
            //   isVisible={ticket.isTicketVisible}
            //   isPrimary={ticket.isTicketPrimary}
            //   isPrimaryDependent={ticket.isTicketPrimaryDependent}
            //   isDisabled={false}
            // ></c-ticket-track-ticket>
          )
      )}
    </c-section>
  );

  TicketLoader: FunctionalComponent = () => (
    <c-section>
      <c-skel variant="ticketList"></c-skel>
    </c-section>
  );

  render() {
    return (
      <c-page>
        <c-topbar></c-topbar>

        {this.isRegSysFetched && (
          <div class="hide-on-desktop show-on-mobile">
            <c-dropdown
              name="changeMobilePage"
              option-str={JSON.stringify(this.pages)}
            ></c-dropdown>
          </div>
        )}

        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}

        {this.isRegSysFetched ? (
          this.pages.length > 0 ? (
            <this.PageList></this.PageList>
          ) : (
            <this.NoPages></this.NoPages>
          )
        ) : (
          <this.PageLoader></this.PageLoader>
        )}

        {this.isTicketsFetched ? (
          this.ticketsInPage.length > 0 ? (
            <this.TicketList></this.TicketList>
          ) : (
            <this.NoTickets></this.NoTickets>
          )
        ) : (
          <this.TicketLoader></this.TicketLoader>
        )}

        <c-cart-preview></c-cart-preview>
        <c-mobile-cart></c-mobile-cart>
      </c-page>
    );
  }
}

injectHistory(PRegform_2);
