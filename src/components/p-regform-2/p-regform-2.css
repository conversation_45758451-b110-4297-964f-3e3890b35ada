p-regform-2 .left-sidebar c-vnav-item .configure-row {
  width: 180px;
}

p-regform-2 c-banner .banner-container {
  margin-bottom: 1em;
}

p-regform-2 .section-divider {
  color: rgba(0, 0, 0, 0.3);
  font-weight: 400;
  padding-bottom: 0.25em;
  margin-bottom: 1.25em;
  font-size: 1.25em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

p-regform-2 c-page .default-page {
  margin-top: 6em;
  padding-bottom: 6em;
}

p-regform-2 .disabled-track {
  pointer-events: none;
  opacity: 0.3;
}

p-regform-2 .empty-container {
  text-align: center;
}

p-regform-2 c-section section {
  width: 520px;
  margin-left: 24.5%;
}

p-regform-2 .show-on-desktop {
  display: block;
}

p-regform-2 .hide-on-desktop {
  display: none;
}

p-regform-2 .default-page c-dropdown select {
  width: 100%;
  margin-top: 1em;
}

@media only screen and (max-width: 768px) {
  p-regform-2 .default-page c-section section {
    margin: 1.5em auto;
  }

  p-regform-2 c-page .default-page {
    margin-top: 1em;
  }

  p-regform-2 c-section section {
    width: 100%;
    margin: 1.5em 0 0em 0;
  }

  p-regform-2 .hide-on-mobile {
    display: none;
  }

  p-regform-2 .show-on-mobile {
    display: block;
  }
}
