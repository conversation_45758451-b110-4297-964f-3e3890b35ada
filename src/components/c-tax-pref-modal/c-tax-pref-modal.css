c-tax-pref-modal .container {
  width: 22%;
  margin-left: 39%;
  margin-top: 10vh;
  position: fixed;
  background: white;
  font-size: 0.9em;
  padding: 1.5em 2em;
  border-radius: 0.25em;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05);
  z-index: 99999;
}

c-tax-pref-modal .row {
  display: flex;
  justify-content: space-between;
}

c-tax-pref-modal h1 {
  color: var(--accent-color);
  margin-bottom: 0.1em;
}

c-tax-pref-modal .mandatory {
  color: red;
}

c-tax-pref-modal .tax-jurisdiction-label {
  margin-bottom: 0.5em;
  margin-top: 1.5em;
}

c-tax-pref-modal .progress-bar {
  width: 100%;
  height: 5px;
  background: var(--accent-color-bg-light);
  border-radius: 0.25em;
  margin-top: 0.5em;
}

c-tax-pref-modal .step-1 {
  width: 50%;
  background: var(--accent-color);
}
c-tax-pref-modal .step-2 {
  width: 100%;
  background: var(--accent-color);
}

c-tax-pref-modal .invisible-btn {
  visibility: hidden;
}

c-tax-pref-modal .radio-btn-container {
  display: flex;
  justify-content: space-between;
}

c-tax-pref-modal .radio-btn-container input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}

c-tax-pref-modal .radio-btn-container label {
  display: inline-block;
  padding: 10px 20px;
  font-family: sans-serif, Arial;
  font-size: 16px;
  border: 2px solid var(--accent-color-bg-light);
  border-radius: 4px;
  transition: all 0.15s ease-in;
}

c-tax-pref-modal .radio-btn-container label:hover {
  /* border: 2px solid var(--accent-color);
  color: var(--accent-color); */
  border: 2px solid var(--accent-color);
  color: var(--fontcolor);
  cursor: pointer;
}

c-tax-pref-modal .radio-btn-container input[type="radio"]:focus + label {
  border: 2px dashed #444;
}

c-tax-pref-modal .radio-btn-container input[type="radio"]:checked + label {
  border-color: var(--accent-color);
  color: white;
  background: var(--accent-color);
}

c-tax-pref-modal select {
  border: 0;
  border: 1px solid rgba(0, 0, 0, 0.15);
  padding: 0.75em 0em 0.75em 0.75em;
  width: 100%;
  outline: none 0;
  border-radius: 0.25em;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 1em;
  background: transparent;
  background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: 98%;
  background-position-y: 12px;
  transition: all 0.15s ease-in;
}

c-tax-pref-modal option:checked {
  color: red;
}

c-tax-pref-modal select::-ms-expand {
  display: none;
}

c-tax-pref-modal select:hover {
  cursor: pointer;
  border: 1px solid var(--accent-color-bg);
}

c-tax-pref-modal footer {
  display: flex;
  flex-direction: row;
  padding-bottom: 0.5em;
}

c-tax-pref-modal .direction-btn {
  border: 0;
  box-sizing: border-box;
  background: none;
  border-radius: 0.25em;
  /* font-weight: 700; */
  padding: 0.75em 1em;
  color: var(--accent-color);
  font-size: 0.8em;
  transition: all 0.15s ease-in;
}

c-tax-pref-modal .direction-btn:hover {
  cursor: pointer;
  background: var(--accent-color-bg-lightest);
}

c-tax-pref-modal .sub-text {
  font-size: 0.8em;
  opacity: 0.6;
}

c-tax-pref-modal .sub-text-no-gst {
  padding-bottom: 0.5em;
}

c-tax-pref-modal .secondary-info-container {
  margin-top: 2em;
}

c-tax-pref-modal .secondary-info-container c-inputbox:nth-child(2) .inputbox {
  margin-bottom: 0;
}

c-tax-pref-modal footer {
  margin-top: 3em;
  display: flex;
  justify-content: space-between;
}

c-tax-pref-modal .step-desc {
  font-size: 1em;
  line-height: 1.5;
  padding-bottom: 1em;
}

c-tax-pref-modal .sub-text {
  font-size: 0.8em;
}
/* 
c-tax-pref-modal .radio-label-container {
  display: flex;
  align-items: baseline;
}

c-tax-pref-modal .radio-label-container label {
  margin-left: 0.5em;
} */

c-tax-pref-modal .radio-container {
  display: block;
  position: relative;
  padding-left: 1.75em;
  cursor: pointer;
  font-size: 1em;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

c-tax-pref-modal .radio-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

c-tax-pref-modal .radio-checkmark {
  position: absolute;
  top: 2px;
  left: 0;
  height: 1em;
  width: 1em;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.03);
  border-radius: 50%;
  transition: all 0.15s ease-in;
}

/* On mouse-over, add a grey background color */
c-tax-pref-modal .radio-container:hover input ~ .radio-checkmark {
  background-color: #ccc;
}

/* When the radio button is checked, add a blue background */
c-tax-pref-modal .radio-container input:checked ~ .radio-checkmark {
  background-color: var(--accent-color);
}

/* Create the indicator (the dot/circle - hidden when not checked) */
c-tax-pref-modal .radio-checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the indicator (dot/circle) when checked */
c-tax-pref-modal .radio-container input:checked ~ .radio-checkmark:after {
  display: block;
}

/* Style the indicator (dot/circle) */
c-tax-pref-modal .radio-container .radio-checkmark:after {
  top: 3.5px;
  left: 3.5px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
}

c-tax-pref-modal .billing-address-label {
  margin-top: 2em;
}

c-tax-pref-modal .hide {
  display: none;
}

c-tax-pref-modal .tax-details-list {
  list-style-type: none;
  margin-top: 1em;
  margin-bottom: 0.3em;
  padding: 0;
}

c-tax-pref-modal .tax-detail {
  display: flex;
  margin-bottom: 1em;
  color: var(--fontcolor);
}

c-tax-pref-modal .icon {
  width: 21px;
  height: 20px;
  text-align: center;
  border-radius: 100%;
  margin-right: 0.75em;
}

c-tax-pref-modal .green-tick {
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
}
