import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import {
  isInvoicePrefWithGstPrefValid,
  isInvoicePrefWithoutGstPrefValid,
} from "../../utils/";
import Store from "../../global/store";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-tax-pref-modal",
  styleUrl: "c-tax-pref-modal.css",
})
export class CTaxPrefModal {
  @State() currentStep: number = 0;
  @State() isInputboxDisabled: boolean = false;
  @State() isSaveInvoicePrefBtnDisabled: boolean = true;
  @State() isGSTInputsVisible: boolean = false;

  private isSaving: boolean = false;
  private isDropDownDisabled: boolean = false;
  private totalSteps: number = 2;

  @Event({
    eventName: "close-save-invoice-pref-modal",
    bubbles: true,
  })
  closeInvoicePrefModal: EventEmitter;

  @Listen("start-invoice-preference-survey")
  startInvoicePreferenceSurvey(event) {
    this.navBtnClickHandler(event, "next");
  }

  @Listen("save-invoice-preferences")
  saveInvoicePreferences() {
    this.isSaving = true;
    this.isInputboxDisabled = true;
    this.isSaveInvoicePrefBtnDisabled = true;
    this.isDropDownDisabled = true;
    let invoicePref = Store.getInvoicePref();
    let payload = {
      item: "invoicePreferences",
      value: invoicePref,
    };

    axios({
      method: "PATCH",
      baseURL: `${state.baseUrl}/profile`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(`${response.data.status}: ${response.data.msg}`);
        } else if (response.data.status === "Success") {
          this.closeInvoicePrefModal.emit(response.data);
        }
      })
      .catch((error) => {
        alert(error);
        this.changeComponentState("profile-update-error");
      });
  }

  @Listen("input-event")
  inputEventHandler() {
    this.changeComponentState("input-event");
  }

  handleDropDownInput(event, name) {
    if (name === "country") {
      if (event.target.value.trim() != "Choose Option") {
        Store.setCountry(event.target.value.trim());
        Store.setCurrency("inr");
        if (Store.getOrgInsti().length > 0 && Store.getJobDegree().length > 0) {
          this.progressControl("proceed");
        }
      } else {
        Store.setCountry("");
      }
    } else if (name === "taxjurisdiction") {
      Store.setTaxJurisdiction(event.target.value.trim());
    }
    this.changeComponentState("dropdown-event");
  }

  componentWillLoad() {
    let invoicePref = Store.getInvoicePref();
    if (Store.getGSTInvoicePreference()) {
      let { error } = isInvoicePrefWithGstPrefValid(invoicePref);
      if (error) {
        this.isSaveInvoicePrefBtnDisabled = true;
      } else {
        this.isSaveInvoicePrefBtnDisabled = false;
      }
    } else {
      let { error } = isInvoicePrefWithoutGstPrefValid(invoicePref);
      if (error) {
        this.isSaveInvoicePrefBtnDisabled = true;
      } else {
        this.isSaveInvoicePrefBtnDisabled = false;
      }
    }
  }

  changeComponentState(lastEventName: string) {
    let invoicePref = Store.getInvoicePref();
    if (
      lastEventName === "input-event" ||
      lastEventName === "dropdown-event" ||
      lastEventName === "radio-event"
    ) {
      if (Store.getGSTInvoicePreference()) {
        let { error } = isInvoicePrefWithGstPrefValid(invoicePref);
        if (error) {
          this.isSaveInvoicePrefBtnDisabled = true;
        } else {
          this.isSaveInvoicePrefBtnDisabled = false;
        }
      } else {
        let { error } = isInvoicePrefWithoutGstPrefValid(invoicePref);
        if (error) {
          this.isSaveInvoicePrefBtnDisabled = true;
        } else {
          this.isSaveInvoicePrefBtnDisabled = false;
        }
      }
    } else if (lastEventName === "account-setup-btn-click-event") {
      this.isSaving = true;
      this.isInputboxDisabled = true;
      this.isSaveInvoicePrefBtnDisabled = true;
      this.isDropDownDisabled = true;
    } else if (
      lastEventName === "account-setup-failed" ||
      "account-setup-success" ||
      "account-setup-error"
    ) {
      this.isSaving = false;
      this.isInputboxDisabled = false;
      this.isSaveInvoicePrefBtnDisabled = true;
      this.isDropDownDisabled = false;
      if (lastEventName === "account-setup-success") {
        Store.toggleIsRegistering();
      }
    }
  }

  progressControl(direction) {
    if (direction === "back") {
      if (this.currentStep - 1 >= 1) this.currentStep = this.currentStep - 1;
    } else if (direction === "next") {
      if (this.currentStep + 1 <= this.totalSteps)
        this.currentStep = this.currentStep + 1;
    }
  }

  handleGSTInvoicePreference(event) {
    if (event.target.value.trim() === "gst-invoice-preferred") {
      this.isGSTInputsVisible = true;
      Store.setGSTInvoicePreference(true);
    } else if (event.target.value.trim() === "gst-invoice-not-preferred") {
      this.isGSTInputsVisible = false;
      Store.setGSTInvoicePreference(false);
    }
    this.changeComponentState("radio-event");
  }

  navBtnClickHandler(event, direction) {
    event.preventDefault();
    this.progressControl(direction);
  }

  render() {
    return (
      <div class="container">
        <div class={this.currentStep === 0 ? "" : "hide"}>
          <header>
            {" "}
            <h1>
              Important <span class="mandatory">*</span>
            </h1>
          </header>
          <p class="step-desc">
            In order to prepare our invoices in accordance with the Goods &
            Services Tax of the Govt. of India, we need a few pieces of
            information. This will take less than a minute.
          </p>
          <c-btn
            name="invoicePreferenceSurveyStart"
            label="Continue"
            action-label=""
            is-in-action={false}
            is-disabled={false}
          ></c-btn>
          <br />
          <br />
        </div>
        <div>
          {this.currentStep > 0 ? (
            <header>
              <h1>Invoice Preference</h1>
              <div class="progress-bar">
                <div class={`progress-bar step-${this.currentStep}`}></div>
              </div>
            </header>
          ) : (
            ""
          )}
          <div class={this.currentStep === 1 ? "" : "hide"}>
            <p class="step-desc">
              Do you want GST invoices for your purchases to be issued in the
              name of your company/institute? <span class="mandatory">*</span>
              <br />
              <span class="sub-text">
                A valid GSTIN is required for this option. Kindly contact the
                appropriate department in your company/institute for further
                information
              </span>
            </p>
            <div class="row">
              <div class="radio-label-container">
                <label class="radio-container">
                  Yes
                  <input
                    type="radio"
                    name="gst"
                    value="gst-invoice-preferred"
                    onClick={(event) => this.handleGSTInvoicePreference(event)}
                  />
                  <span class="radio-checkmark"></span>
                </label>
              </div>
              <div class="radio-label-container">
                <label class="radio-container">
                  {" "}
                  No
                  <input
                    type="radio"
                    name="gst"
                    value="gst-invoice-not-preferred"
                    onClick={(event) => this.handleGSTInvoicePreference(event)}
                    checked
                  />
                  <span class="radio-checkmark"></span>
                </label>
              </div>
            </div>
            <div
              class={
                this.isGSTInputsVisible ? "secondary-info-container" : "hide"
              }
            >
              <c-inputbox
                type="text"
                name="businessName"
                placeholder="Business name (as in GST registration) *"
                is-disabled={this.isInputboxDisabled}
              ></c-inputbox>
              <c-inputbox
                type="text"
                name="taxID"
                placeholder="GSTIN of the business *"
                is-disabled={this.isInputboxDisabled}
              ></c-inputbox>
            </div>
            <footer>
              <button
                class={
                  this.currentStep - 1 === 0
                    ? "invisible-btn direction-btn"
                    : "direction-btn"
                }
                onClick={(event) => this.navBtnClickHandler(event, "back")}
              >
                Back
              </button>
              <button
                class={
                  this.currentStep === this.totalSteps
                    ? "invisible-btn direction-btn"
                    : "direction-btn"
                }
                onClick={(event) => this.navBtnClickHandler(event, "next")}
              >
                Next
              </button>
            </footer>
          </div>
          <div class={this.currentStep === 2 ? "" : "hide"}>
            <div class="billing-address-container">
              {Store.getGSTInvoicePreference() ? (
                <div>
                  {" "}
                  <p>
                    Your billing address <span class="mandatory">*</span>
                    <br />
                    <span class="sub-text">
                      Your company/institute address (as per GST registration)
                      will be your billing address
                    </span>
                  </p>
                  <c-inputbox
                    type="text"
                    name="billingAddressLine1"
                    placeholder="Billing Address Line 1 *"
                    is-disabled={this.isInputboxDisabled}
                  ></c-inputbox>
                  <c-inputbox
                    type="text"
                    name="billingAddressLine2"
                    placeholder="Billing Address Line 2 *"
                    is-disabled={this.isInputboxDisabled}
                  ></c-inputbox>
                  <c-inputbox
                    type="text"
                    name="billingAddressLine3"
                    placeholder="City, Pincode, State, Country *"
                    is-disabled={this.isInputboxDisabled}
                  ></c-inputbox>
                  <p class="tax-jurisdiction-label">
                    Where is this GST registration from?{" "}
                    <span class="mandatory">*</span>
                  </p>
                  <select
                    onInput={(event) =>
                      this.handleDropDownInput(event, "taxjurisdiction")
                    }
                    disabled={this.isDropDownDisabled}
                  >
                    <option value="inside-tax-jurisdiction-state">
                      Inside Maharashtra & Inside India
                    </option>
                    <option value="outside-tax-jurisdiction-state">
                      Outside Maharashtra & Inside India
                    </option>
                  </select>
                </div>
              ) : (
                <div>
                  <p class="tax-jurisdiction-label">
                    Where are you currently located?{" "}
                    <span class="mandatory">*</span>
                    <br />
                  </p>
                  <select
                    onInput={(event) =>
                      this.handleDropDownInput(event, "taxjurisdiction")
                    }
                    disabled={this.isDropDownDisabled}
                  >
                    <option value="inside-tax-jurisdiction-state">
                      Inside Maharashtra & Inside India
                    </option>
                    <option value="outside-tax-jurisdiction-state">
                      Outside Maharashtra & Inside India
                    </option>
                    <option value="outside-tax-jurisdiction-country">
                      Outside India
                    </option>
                  </select>
                  <p class="billing-address-label">
                    Your billing address? (optional)
                  </p>
                  <c-inputbox
                    type="text"
                    name="billingAddressLine1"
                    placeholder="Billing Address Line 1"
                    is-disabled={this.isInputboxDisabled}
                  ></c-inputbox>
                  <c-inputbox
                    type="text"
                    name="billingAddressLine2"
                    placeholder="Billing Address Line 2"
                    is-disabled={this.isInputboxDisabled}
                  ></c-inputbox>
                  <c-inputbox
                    type="text"
                    name="billingAddressLine3"
                    placeholder="City, Pincode, State, Country"
                    is-disabled={this.isInputboxDisabled}
                  ></c-inputbox>
                </div>
              )}
            </div>
            <br />
            <br />
            <c-btn
              name="saveInvoicePreferences"
              label="Save Preferences"
              action-label=""
              is-in-action={this.isSaving}
              is-disabled={this.isSaveInvoicePrefBtnDisabled}
            ></c-btn>
            <footer>
              <button
                class={
                  this.currentStep - 1 === 0
                    ? "invisible-btn direction-btn"
                    : "direction-btn"
                }
                onClick={(event) => this.navBtnClickHandler(event, "back")}
              >
                Back
              </button>
              <button
                class={
                  this.currentStep === this.totalSteps
                    ? "invisible-btn direction-btn"
                    : "direction-btn"
                }
                onClick={(event) => this.navBtnClickHandler(event, "next")}
              >
                Next
              </button>
            </footer>
          </div>
        </div>
      </div>
    );
  }
}
