import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  State,
  Listen,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";
import { gsap } from "gsap";

@Component({
  tag: "c-wizard-add-ticket",
  styleUrl: "c-wizard-add-ticket.css",
})
export class CWizardAddTicket {
  wizardContainer!: HTMLDivElement;

  /*------------------
  States
  ------------------*/
  @State() wizardState: string = "init";
  @State() wizardStepCount: number = 0;
  @State()
  isInitNextDisabled: boolean = false;
  // Full Ticket States
  @State() fullTicket_Basic_Next_Disabled: boolean = true;
  @State() fullTicket_AddTicket_Disabled: boolean = true;
  @State() fullTicket_AddTicket_InAction: boolean = false;
  @State() fullTicket_PricingTiers: any = [];
  // Partial Ticket States
  @State() partialTicket_Basic_Next_Disabled: boolean = true;
  @State() partialTicket_Access_Next_Disabled: boolean = true;
  @State() partialTicket_Pricing_Next_Disabled: boolean = true;
  @State() partialTicket_AccessDates: any = [];
  @State() partialTicket_PricingTiers: any = [];
  // Track Ticket States
  @State() trackTicket_SubTicket_Next_Disabled: boolean = true;
  @State() trackTicket_Days_Next_Disabled: boolean = true;
  @State() trackTicket_Sessions_Next_Disabled: boolean = true;
  @State() trackTicket_Subtickets: any = [];
  @State() trackTicket_Days: any = [];
  @State() trackTicket_Sessions: any = [];

  // Full ticket variables
  private fullTicketTitle: string = "";
  private fullTicketSteps: any = [
    {
      name: "Full ticket - Basic details",
      stepId: "fullTicket_Basic",
    },
    {
      name: "Full ticket - Pricing tiers",
      stepId: "fullTicket_Pricing",
    },
  ];
  // Partial ticket variables
  private partialTicketTitle: string = "";
  private partialTicketSteps: any = [
    {
      name: "Partial ticket - Basic details",
      stepId: "partialTicket_Basic",
    },
    {
      name: "Partial ticket - Access type",
      stepId: "partialTicket_Dates",
    },
    {
      name: "Partial ticket - Pricing",
      stepId: "partialTicket_Pricing",
    },
  ];
  // Track ticket variables
  private trackTicketSteps: any = [
    {
      name: "Track ticket - Subticket pricing",
      stepId: "trackTicket_Subtypes",
    },
    {
      name: "Track ticket - Days",
      stepId: "trackTicket_Days",
    },
    {
      name: "Track ticket - Sessions",
      stepId: "trackTicket_Sessions",
    },
  ];
  private isWizardWidthIncreased: boolean = false;

  /*------------------
  Event Emitters
  ------------------*/
  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  /*------------------
  Event Listeners
  ------------------*/
  @Listen("addPricingTier_fullTicket") addPricingTier(e) {
    this.fullTicket_PricingTiers.push(e.detail.tierObj);
    this.fullTicket_PricingTiers = [...this.fullTicket_PricingTiers];
    this.validateTicketInputs();
  }

  @Listen("addAccessDates_partialTicket") addAccessDates(e) {
    this.partialTicket_AccessDates.push(e.detail.accessDateObj);
    this.partialTicket_AccessDates = [...this.partialTicket_AccessDates];
    if (this.partialTicket_AccessDates.length > 0) {
      this.partialTicket_Access_Next_Disabled = false;
    } else {
      this.partialTicket_Access_Next_Disabled = true;
    }
  }

  @Listen("addSubTicket_trackTicket") addSubTicket(e) {
    this.trackTicket_Subtickets.push(e.detail.subTicketObj);
    this.trackTicket_Subtickets = [...this.trackTicket_Subtickets];
    if (this.trackTicket_Subtickets.length > 0) {
      this.trackTicket_SubTicket_Next_Disabled = false;
    } else {
      this.trackTicket_SubTicket_Next_Disabled = true;
    }
  }

  @Listen("addTrackDay_TrackTicket") addTrackDay(e) {
    this.trackTicket_Days.push(e.detail.trackDayObj);
    this.trackTicket_Days = [...this.trackTicket_Days];
    if (this.trackTicket_Days.length > 0) {
      this.trackTicket_Days_Next_Disabled = false;
    } else {
      this.trackTicket_Days_Next_Disabled = true;
    }
  }

  @Listen("tierChange")
  tierChangeListener(e) {
    e.preventDefault();
    this.validateTicketInputs();
  }

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
      this.resetComponent();
    } else if (e.detail.name === "initNext") {
      this.handleInitNext();
    } else if (e.detail.name === "backToInit") {
      this.handleBackToInit();
    } else if (e.detail.name === "wizardNext") {
      this.handleWizardNext();
    } else if (e.detail.name === "wizardBack") {
      this.handleWizardBack();
    } else if (e.detail.name === "createFullTicket") {
      this.createFullTicket();
    }
    this.adjustWizardWidth();
  }

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "ticketType") {
      state.configureActiveTicketType = event.detail.value;
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "fullTicketTitle") {
      this.fullTicketTitle = e.detail.value;
    } else if (e.detail.name === "partialTicketTitle") {
      this.partialTicketTitle = e.detail.value;
    }
    this.validateTicketInputs();
  }

  /*-----
  Methods
  -----*/
  adjustWizardWidth() {
    if (this.wizardState === "partialTicket" && this.wizardStepCount === 2) {
      this.increaseWizardWidth(60);
    } else {
      this.decreaseWizardWidth();
    }

    if (this.wizardState === "trackTicket" && this.wizardStepCount === 0) {
      this.increaseWizardWidth(50);
    } else {
      this.decreaseWizardWidth();
    }

    if (this.wizardState === "trackTicket" && this.wizardStepCount === 2) {
      this.increaseWizardWidth(60);
    } else {
      this.decreaseWizardWidth();
    }
  }

  createFullTicket() {
    this.fullTicket_AddTicket_InAction = true;

    let payload = {
      eventCode: state.eventCodeForConfiguration,
      pageId: state.configureActivePageId,
      ticketTitle: this.fullTicketTitle,
      ticketType: "fullTicket",
      tiers: JSON.stringify(this.fullTicket_PricingTiers),
    };

    console.log(payload);

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/create-full-ticket`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
          this.resetComponent();
        } else if (response.data.status === "Success") {
          this.closeModalEvent.emit();
          this.fullTicket_AddTicket_InAction = false;
          this.resetComponent();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  increaseWizardWidth(wizardWidth: number) {
    if (!this.isWizardWidthIncreased) {
      let tl = gsap.timeline();
      tl.to(this.wizardContainer, {
        width: `${wizardWidth}%`,
        duration: 0.15,
      });
      this.isWizardWidthIncreased = true;
    }
  }

  decreaseWizardWidth() {
    if (this.isWizardWidthIncreased) {
      let tl = gsap.timeline();
      tl.to(this.wizardContainer, {
        width: "35%",
        duration: 0.15,
      });
      this.isWizardWidthIncreased = false;
    }
  }

  handleInitNext() {
    this.wizardState = state.configureActiveTicketType;
  }

  handleBackToInit() {
    this.resetComponent();
  }

  handleWizardNext() {
    this.wizardStepCount = this.wizardStepCount + 1;
  }

  handleWizardBack() {
    this.wizardStepCount = this.wizardStepCount - 1;
  }

  render_WizardFooter() {
    if (this.wizardState === "fullTicket") {
      return (
        <div class="field-container-row">
          <c-button
            name={this.wizardStepCount === 0 ? "backToInit" : "wizardBack"}
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>

          {this.wizardStepCount === 0 && (
            <c-button
              name="wizardNext"
              isDisabled={this.fullTicket_Basic_Next_Disabled}
            >
              Next
            </c-button>
          )}

          {this.wizardStepCount === 1 && (
            <c-button
              name="createFullTicket"
              isDisabled={this.fullTicket_AddTicket_Disabled}
              isInAction={this.fullTicket_AddTicket_InAction}
              isInActionLabel="Adding.."
            >
              Add full ticket
            </c-button>
          )}
        </div>
      );
    } else if (this.wizardState === "partialTicket") {
      return (
        <div class="field-container-row">
          <c-button
            name={this.wizardStepCount === 0 ? "backToInit" : "wizardBack"}
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>

          {this.wizardStepCount === 0 && (
            <c-button
              name="wizardNext"
              isDisabled={this.partialTicket_Basic_Next_Disabled}
            >
              Next
            </c-button>
          )}

          {this.wizardStepCount === 1 && (
            <c-button
              name="wizardNext"
              isDisabled={this.partialTicket_Access_Next_Disabled}
            >
              Next
            </c-button>
          )}

          {this.wizardStepCount === 2 && (
            <c-button
              name="addPartialTicket"
              isDisabled={this.partialTicket_Pricing_Next_Disabled}
            >
              Add partial ticket
            </c-button>
          )}
        </div>
      );
    } else if (this.wizardState === "trackTicket") {
      return (
        <div class="field-container-row">
          <c-button
            name={this.wizardStepCount === 0 ? "backToInit" : "wizardBack"}
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>

          {this.wizardStepCount === 0 && (
            <c-button
              name="wizardNext"
              isDisabled={this.trackTicket_SubTicket_Next_Disabled}
            >
              Next
            </c-button>
          )}

          {this.wizardStepCount === 1 && (
            <c-button
              name="wizardNext"
              isDisabled={this.trackTicket_Days_Next_Disabled}
            >
              Next
            </c-button>
          )}

          {this.wizardStepCount === 2 && (
            <c-button
              name="addTrackTicket"
              isDisabled={this.partialTicket_Pricing_Next_Disabled}
            >
              Add track ticket
            </c-button>
          )}
        </div>
      );
    }
  }

  resetComponent() {
    this.wizardState = "init";
    this.wizardStepCount = 0;
    state.configureActiveTicketType = "fullTicket";
    this.isInitNextDisabled = false;

    // Reset fullTicket Steps
    this.fullTicketTitle = "";
    this.fullTicket_Basic_Next_Disabled = true;
    this.fullTicket_AddTicket_Disabled = true;
    this.fullTicket_AddTicket_InAction = false;
    this.fullTicket_PricingTiers = [];
    this.fullTicket_PricingTiers = [...this.fullTicket_PricingTiers];

    // Reset partialTicket Steps
    this.partialTicketTitle = "";
    this.partialTicket_Basic_Next_Disabled = true;
    this.partialTicket_Access_Next_Disabled = true;
    this.partialTicket_Pricing_Next_Disabled = true;
    this.partialTicket_AccessDates = [];
    this.partialTicket_AccessDates = [...this.partialTicket_AccessDates];
    this.partialTicket_PricingTiers = [];
    this.partialTicket_PricingTiers = [...this.partialTicket_PricingTiers];

    // Reset trackTicket Steps
    this.trackTicket_SubTicket_Next_Disabled = true;
    this.trackTicket_Days_Next_Disabled = true;
    this.trackTicket_Sessions_Next_Disabled = true;
    this.trackTicket_Subtickets = [];
    this.trackTicket_Subtickets = [...this.trackTicket_Subtickets];
    this.trackTicket_Days = [];
    this.trackTicket_Days = [...this.trackTicket_Days];
    this.trackTicket_Sessions = [];
    this.trackTicket_Sessions = [...this.trackTicket_Sessions];
  }

  validateTicketInputs() {
    if (state.configureActiveTicketType === "fullTicket") {
      this.validateTicketInputs_FullTicket();
    } else if (state.configureActiveTicketType === "partialTicket") {
      this.validateTicketInputs_PartialTicket();
    } else if (state.configureActiveTicketType === "trackTicket") {
      this.validateTicketInputs_TrackTicket();
    }
  }

  validateTicketInputs_FullTicket() {
    if (this.fullTicketTitle.length > 0) {
      this.fullTicket_Basic_Next_Disabled = false;
    } else {
      this.fullTicket_Basic_Next_Disabled = true;
    }

    if (
      this.fullTicketTitle.length > 0 &&
      this.fullTicket_PricingTiers.length > 0
    ) {
      this.fullTicket_AddTicket_Disabled = false;
    } else {
      this.fullTicket_AddTicket_Disabled = true;
    }
  }

  validateTicketInputs_PartialTicket() {
    if (this.partialTicketTitle.length > 0) {
      this.partialTicket_Basic_Next_Disabled = false;
    } else {
      this.partialTicket_Basic_Next_Disabled = true;
    }
  }

  validateTicketInputs_TrackTicket() {}

  /*------------------
  Functinal Components
  ------------------*/
  InitStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container radio-label-container">
        <c-radio
          name="ticketType"
          label1="Full ticket"
          label2=""
          label3="Allows access on all the event days. This is a primary ticket"
          val="fullTicket"
          isChecked={
            state.configureActiveTicketType === "fullTicket" ? true : false
          }
        ></c-radio>
      </div>
      <div class="field-container radio-label-container">
        <c-radio
          name="ticketType"
          label1="Partial ticket"
          label2=""
          label3="Allows access on partial event days. This is a primary ticket"
          val="partialTicket"
          isChecked={
            state.configureActiveTicketType === "partialTicket" ? true : false
          }
        ></c-radio>
      </div>
      <div class="field-container radio-label-container">
        <c-radio
          name="ticketType"
          label1="Track tickets"
          label2=""
          label3="Allows access to parallel events"
          val="trackTicket"
          isChecked={
            state.configureActiveTicketType === "trackTicket" ? true : false
          }
        ></c-radio>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <div></div>
          <c-button
            name="initNext"
            icon-name=""
            isDisabled={this.isInitNextDisabled}
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  // fullTicket
  FullTicket: FunctionalComponent = () => (
    <div class="wizard-content">
      {this.wizardStepCount === 0 && (
        <this.FullTicket_Basic></this.FullTicket_Basic>
      )}
      {this.wizardStepCount === 1 && (
        <this.FullTicket_Pricing></this.FullTicket_Pricing>
      )}
      <this.WizardFooter></this.WizardFooter>
    </div>
  );
  FullTicket_Basic: FunctionalComponent = () => (
    <div class="field-container">
      <c-text type="modalLabel" isMandatory={true}>
        TITLE
      </c-text>
      <c-textbox
        input-type="text"
        name="fullTicketTitle"
        placeholder={`e.g. India HCI ${
          new Date().getFullYear() + 1
        } Full Conference Ticket`}
        isDisabled={false}
        value={this.fullTicketTitle}
      ></c-textbox>
    </div>
  );
  FullTicket_Pricing: FunctionalComponent = () => (
    <div class="field-container">
      <c-ticket-price-input-full
        pricingTierString={
          this.fullTicket_PricingTiers.length > 0
            ? JSON.stringify(this.fullTicket_PricingTiers)
            : ""
        }
      ></c-ticket-price-input-full>
    </div>
  );

  // partialTicket
  PartialTicket: FunctionalComponent = () => (
    <div class="wizard-content">
      {this.wizardStepCount === 0 && (
        <this.PartialTicket_Basic></this.PartialTicket_Basic>
      )}
      {this.wizardStepCount === 1 && (
        <this.PartialTicket_Access></this.PartialTicket_Access>
      )}
      {this.wizardStepCount === 2 && (
        <this.PartialTicket_Pricing></this.PartialTicket_Pricing>
      )}
      <this.WizardFooter></this.WizardFooter>
    </div>
  );
  PartialTicket_Basic: FunctionalComponent = () => (
    <div class="field-container">
      <c-text type="modalLabel" isMandatory={true}>
        TITLE
      </c-text>
      <c-textbox
        input-type="text"
        name="partialTicketTitle"
        placeholder={`e.g. India HCI ${
          new Date().getFullYear() + 1
        } Partial Conference Ticket`}
        isDisabled={false}
        value={this.partialTicketTitle}
      ></c-textbox>
    </div>
  );
  PartialTicket_Access: FunctionalComponent = () => (
    <div class="field-container">
      <c-ticket-partial-access-dates
        accessDatesString={
          this.partialTicket_AccessDates.length > 0
            ? JSON.stringify(this.partialTicket_AccessDates)
            : ""
        }
      ></c-ticket-partial-access-dates>
    </div>
  );
  PartialTicket_Pricing: FunctionalComponent = () => (
    <div class="field-container">
      <c-ticket-price-input-partial
        accessDatesString={
          this.partialTicket_AccessDates.length > 0
            ? JSON.stringify(this.partialTicket_AccessDates)
            : ""
        }
      ></c-ticket-price-input-partial>
    </div>
  );

  // trackTicket
  TrackTicket: FunctionalComponent = () => (
    <div class="wizard-content">
      {this.wizardStepCount === 0 && (
        <this.TrackTicket_SubTicket></this.TrackTicket_SubTicket>
      )}
      {this.wizardStepCount === 1 && (
        <this.TrackTicket_Days></this.TrackTicket_Days>
      )}
      {this.wizardStepCount === 2 && (
        <this.TrackTicket_Sessions></this.TrackTicket_Sessions>
      )}
      <this.WizardFooter></this.WizardFooter>
    </div>
  );
  TrackTicket_SubTicket: FunctionalComponent = () => (
    <div class="field-container">
      <c-ticket-price-input-track
        subTicketsString={
          this.trackTicket_Subtickets.length > 0
            ? JSON.stringify(this.trackTicket_Subtickets)
            : ""
        }
      ></c-ticket-price-input-track>
    </div>
  );
  TrackTicket_Days: FunctionalComponent = () => (
    <div class="field-container">
      <c-ticket-track-days
        trackDaysString={
          this.trackTicket_Days.length > 0
            ? JSON.stringify(this.trackTicket_Days)
            : ""
        }
      ></c-ticket-track-days>
    </div>
  );
  TrackTicket_Sessions: FunctionalComponent = () => (
    <div class="field-container">
      <c-ticket-track-sessions
        trackDaysString={
          this.trackTicket_Days.length > 0
            ? JSON.stringify(this.trackTicket_Days)
            : ""
        }
        subTicketsString={
          this.trackTicket_Subtickets.length > 0
            ? JSON.stringify(this.trackTicket_Subtickets)
            : ""
        }
      ></c-ticket-track-sessions>
    </div>
  );

  WizardFooter: FunctionalComponent = () => (
    <div class="field-container last-field-container">
      {this.render_WizardFooter()}
    </div>
  );

  render() {
    return (
      <div
        class="wizard-container"
        ref={(el) => (this.wizardContainer = el as HTMLDivElement)}
      >
        <div class="wizard-header">
          <div>
            <c-text type="wizardHeading">
              {/* 
            {this.wizardState === "fullTicket" &&
              "Full conference ticket details"}
            {this.wizardState === "partialTicket" &&
              "Partial conference ticket details"}
            {this.wizardState === "trackTicket" && "Track details"} */}
              {this.wizardState === "init" && "Choose ticket type"}
              {this.wizardState === "fullTicket" &&
                this.fullTicketSteps[this.wizardStepCount].name}
              {this.wizardState === "partialTicket" &&
                this.partialTicketSteps[this.wizardStepCount].name}
              {this.wizardState === "trackTicket" &&
                this.trackTicketSteps[this.wizardStepCount].name}
            </c-text>
            <c-text type="wizardSubHeading">
              {this.wizardState === "fullTicket" &&
                `STEP ${this.wizardStepCount + 1} OF ${
                  this.fullTicketSteps.length
                }`}
              {this.wizardState === "partialTicket" &&
                `STEP ${this.wizardStepCount + 1} OF ${
                  this.partialTicketSteps.length
                }`}
              {this.wizardState === "trackTicket" &&
                `STEP ${this.wizardStepCount + 1} OF ${
                  this.trackTicketSteps.length
                }`}
            </c-text>
          </div>
          <c-button
            type="modalClose"
            name="closeModal"
            icon-name=""
            label=""
          ></c-button>
        </div>
        {this.wizardState === "init" && <this.InitStep></this.InitStep>}
        {this.wizardState === "fullTicket" && (
          <this.FullTicket></this.FullTicket>
        )}
        {this.wizardState === "partialTicket" && (
          <this.PartialTicket></this.PartialTicket>
        )}
        {this.wizardState === "trackTicket" && (
          <this.TrackTicket></this.TrackTicket>
        )}
      </div>
    );
  }
}
