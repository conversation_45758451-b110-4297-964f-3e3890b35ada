c-wizard-add-ticket .wizard-container {
  position: absolute;
  width: 35%;
  left: 0;
  right: 0;
  margin: 4em auto 0 auto;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  padding: 1em 1.5em 1.5em 1.5em;
  z-index: 999999;
  margin-bottom: 4em;
}

c-wizard-add-ticket .wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-add-ticket .wizard-content {
  box-sizing: border-box;
  background: white;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
  padding: 2em 1em;
  margin-top: 1.5em;
  font-size: 0.9em;
}

c-wizard-add-ticket .wizard-content .field-container:last-child {
  margin-bottom: 0;
}

c-wizard-add-ticket .field-container {
  margin-bottom: 1em;
}

c-wizard-add-ticket .field-container p {
  margin: 0;
}

c-wizard-add-ticket .radio-label-container {
  display: flex;
}

c-wizard-add-ticket .field-container-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-add-ticket c-radio .radio-btn-container {
  margin-top: 0;
  margin-bottom: 0.5em;
}

c-wizard-add-ticket c-radio .radio-btn-container .radio-btn-label-1 {
  font-weight: 700;
}

c-wizard-add-ticket c-radio .radio-btn-container .radio-btn-label-3 {
  margin-left: 2em;
  font-size: 0.9em;
}

c-wizard-add-ticket .wizard-content {
  box-sizing: border-box;
  background: white;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
  padding: 1em;
  margin-top: 1em;
}

c-wizard-add-ticket .field-container {
  margin-bottom: 1.5em;
}

c-wizard-add-ticket .field-container c-text .modal-label {
  font-size: 0.8em;
  margin-bottom: 0.5em;
}

c-wizard-add-ticket .flex-row {
  display: flex;
}

c-wizard-add-ticket .flex-row c-radio .radio-btn-container {
  font-weight: 400;
}

c-wizard-add-ticket .wizard-header c-button .modal-close {
  padding: 0;
}
