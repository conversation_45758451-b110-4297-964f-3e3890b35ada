import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  Prop,
  State,
  Host,
  Watch,
  h,
} from "@stencil/core";

@Component({
  tag: "c-ticket-track-days",
  styleUrl: "c-ticket-track-days.css",
})
export class CTicketTrackDays {
  /*-----------
  Event Emitter
  -----------*/
  @Event({
    eventName: "addTrackDay_TrackTicket",
    bubbles: true,
  })
  addTrackDay_TrackTicket: EventEmitter;

  /*-------------
  Event Listeners  
  -------------*/
  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "newTrackDay") {
      this.mode = "newTrackDay";
    } else if (e.detail.name === "cancelAccessDate") {
      this.mode = "viewTrackDays";
    } else if (e.detail.name === "createTrackDay") {
      this.createTrackDay();
    } else if (e.detail.name === "accessDay") {
      this.prepareForTrackDayEdit(e);
    }
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "trackDay") {
      this.newTrackDay = e.detail.value;
    }
    this.validateInputs();
  }

  /*---
  Props
  ---*/
  @Prop() trackDaysString: string = "";

  /*----
  States  
  ----*/
  @State()
  mode: string = "viewTrackDays";
  @State() isSaveTrackDayDisabled: boolean = true;
  @State() trackDays: any = [];

  /*------
  Watchers  
  ------*/
  @Watch("trackDaysString") trackDaysStringWatcher(
    newVal: string,
    oldVal: string
  ) {
    if (newVal != oldVal) {
      this.generateTrackDays();
    }
  }

  /*-------
  Variables  
  -------*/
  private trackDayId: string = "";
  private trackDay: string = "";
  private newTrackDay: string = "";

  /*---------------
  Lifecycle Methods  
  ---------------*/
  componentWillLoad() {
    if (this.trackDays.length > 0) {
      this.generateTrackDays();
    }
  }

  /*-------
  Functions  
  -------*/
  createTrackDay() {
    let obj = {
      id: Math.random().toString(),
      trackDay: this.newTrackDay,
    };
    this.addTrackDay_TrackTicket.emit({
      trackDayObj: obj,
    });
    this.isSaveTrackDayDisabled = true;
    this.mode = "viewTrackDays";
    this.resetComponent();
  }

  deleteTrackDay() {
    console.log("delete track day");
  }

  editTrackDay() {
    console.log("edit track day");
  }

  generateTrackDays() {
    this.trackDays = JSON.parse(this.trackDaysString);
    this.trackDays = [...this.trackDays];
  }

  prepareForTrackDayEdit(e) {
    let trackDayId: string = e.detail.value;
    this.trackDays.map((item: any) => {
      if (item.id === trackDayId) {
        this.trackDay = item.trackDay;
      }
    });
    console.log(this.trackDay);
    console.log(this.trackDayId);
    this.mode = "editTrackDay";
  }

  resetComponent() {
    this.trackDay = "";
    this.newTrackDay = "";
  }

  validateInputs() {
    if (this.mode === "newTrackDay") {
      this.validateInputs_NewTrackDay();
    } else if (this.mode === "editTrackDay") {
      this.validateInputs_EditTrackDay();
    }
  }

  validateInputs_NewTrackDay() {
    if (this.newTrackDay.length > 0) {
      this.isSaveTrackDayDisabled = false;
    } else {
      this.isSaveTrackDayDisabled = true;
    }
  }

  validateInputs_EditTrackDay() {
    let hasTrackDayChanged: boolean = false;

    if (this.newTrackDay != this.trackDay) {
      hasTrackDayChanged = true;
    } else {
      hasTrackDayChanged = false;
    }

    if (hasTrackDayChanged) {
      this.isSaveTrackDayDisabled = false;
    } else {
      this.isSaveTrackDayDisabled = true;
    }
  }

  /*-------------------
  Functional Components  
  -------------------*/
  ViewTrackItem: FunctionalComponent = () =>
    this.trackDays.length > 0 ? (
      <div class="access-days-button-container">
        {this.trackDays.map((item: any) => (
          <c-button type="accessDays" name="accessDay" value={item.id}>
            {item.trackDay}
          </c-button>
        ))}
      </div>
    ) : (
      <div class="no-tier-container">
        <c-text>Found 0 track days</c-text>
      </div>
    );

  InputTrackItem: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          CHOOSE A TRACK DAY
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="trackDay"
            pickTime={false}
            date={this.trackDay}
          ></c-date-picker>
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelAccessDate">
            Cancel
          </c-button>
          <div class="button-group">
            {this.mode === "editTrackDay" && (
              <c-button type="ghost_Danger_Small" name="deletePriceTier">
                Delete
              </c-button>
            )}
            {this.mode === "editTrackDay" && (
              <div>&nbsp;&nbsp;&nbsp;&nbsp;</div>
            )}
            <c-button
              type="ghost_Small"
              name={
                this.mode === "newTrackDay" ? "createTrackDay" : "editTrackDay"
              }
              isDisabled={this.isSaveTrackDayDisabled}
            >
              {this.mode === "newTrackDay" && "Add"}
              {this.mode === "editTrackDay" && "Save"}
            </c-button>
          </div>
        </c-row>
      </div>
    </div>
  );

  CreateTrackDay: FunctionalComponent = () => (
    <div class="ticket-price-table__footer">
      <c-button
        name="newTrackDay"
        type="newTrackDay"
        isDisabled={false}
        isInAction={false}
      ></c-button>
    </div>
  );

  render() {
    return (
      <Host>
        <div class="ticket-eligibility__top-row">
          <c-text type="modalLabel" isMandatory={true}>
            ADD DAYS ON WHICH TRACK EVENTS WILL BE HELD
          </c-text>
        </div>
        <div class="ticket-eligibility-table__body">
          {this.mode === "viewTrackDays" ? (
            <this.ViewTrackItem></this.ViewTrackItem>
          ) : (
            <this.InputTrackItem></this.InputTrackItem>
          )}
          {this.mode === "viewTrackDays" && (
            <this.CreateTrackDay></this.CreateTrackDay>
          )}
        </div>
      </Host>
    );
  }
}
