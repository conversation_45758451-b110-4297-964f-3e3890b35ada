import { Component, Prop, State, Host, Listen, h } from "@stencil/core";
import { MatchResults, RouterHistory, injectHistory } from "@stencil/router";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "p-dashboard",
  styleUrl: "p-dashboard.css",
})
export class PDashboard {
  /*----
  Props
  ----*/
  @Prop() match: MatchResults;
  @Prop() history: RouterHistory;

  /*----
  States
  ----*/
  @State() activeDashboardView: string = "overview";
  @State() isOverlayVisible: boolean = false;
  @State() isUserDetailsModalVisible: boolean = false;
  @State() isTicketDetailVisible: boolean = false;
  @State() isToastActive: boolean = false;
  @State() isEventNameFetched: boolean = false;

  /*-------------
  Event Listeners
  -------------*/
  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    // this.activeSection = event.detail.cNavItemName;
    if (event.detail.cNavItemName === "back") {
      this.history.goBack();
    } else {
      this.activeDashboardView = event.detail.cNavItemName;
    }
  }

  @Listen("hide-modal")
  hideModal() {
    state.expandedUserEmail = "";
    this.isOverlayVisible = false;
    this.isUserDetailsModalVisible = false;
    this.isTicketDetailVisible = false;
  }

  @Listen("expand-wc-details")
  showTicketDetailsHandler(e) {
    state.expandedTrackTicketTitle = e.detail.name;
    state.expandedTrackTicketSoldUnits = parseInt(e.detail.soldCount);
    this.isOverlayVisible = true;
    this.isTicketDetailVisible = true;
  }

  @Listen("coupon-creation-response")
  couponCtionResponseHandler(event) {
    this.hideOverlay(event);
    this.showToast({
      type: event.detail.status,
      label: event.detail.msg,
    });
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  @Listen("dropdown-input-event")
  dropDownHandler(event) {
    if (event.detail.filter === "changeMobilePage") {
      let value = event.detail.value;
      this.activeDashboardView = value.split("---")[0];
    }
  }

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "showDashboardViewOptions") {
      state.isMobileDashboardOptionsVisible = true;
    } else if (e.detail.name === "hideDashboardViewOptions") {
      state.isMobileDashboardOptionsVisible = false;
    }
  }

  private navOptsRM = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "eventName",
      isFirstLabel: false,
    },
    {
      type: "navItem",
      name: "overview",
      label: "Overview",
      state: "active",
      icon: "reader-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "sales",
      label: "Sales",
      state: "",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "OVERALL",
      isFirstLabel: false,
    },
    {
      type: "navItem",
      name: "registrants",
      label: "Accounts",
      state: "",
      icon: "people-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "members",
      label: "Members",
      state: "enabled",
      icon: "ribbon-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "verification",
      label: "Alerts",
      state: "enabled",
      icon: "alert-circle-outline",
      subText: "",
      route: "",
    },
  ];
  private navOptsAdmin = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "eventName",
      isFirstLabel: false,
    },
    {
      type: "navItem",
      name: "overview",
      label: "Overview",
      state: "active",
      icon: "reader-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "sales",
      label: "Sales",
      state: "",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "OVERALL",
      isFirstLabel: false,
    },
    {
      type: "navItem",
      name: "registrants",
      label: "Accounts",
      state: "",
      icon: "people-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "members",
      label: "Members",
      state: "enabled",
      icon: "ribbon-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "verification",
      label: "Alerts",
      state: "enabled",
      icon: "alert-circle-outline",
      subText: "",
      route: "",
    },
    {
      type: "label",
      label: "Admin",
      isFirstLabel: false,
      isAdminOption: true,
    },
    {
      type: "navItem",
      name: "coupons",
      label: "Coupons",
      state: "enabled",
      icon: "ticket-outline",
      subText: "",
      route: "",
      isAdminOption: true,
    },
    {
      type: "navItem",
      name: "invoices",
      label: "Invoices",
      state: "",
      icon: "document-text-outline",
      subText: "",
      route: "",
      isAdminOption: true,
    },
  ];
  private navOptsMobileRM = [
    {
      type: "navItem",
      name: "overview",
      label: "Overview",
      state: "active",
      icon: "reader-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "sales",
      label: "Sales",
      state: "",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "registrants",
      label: "Accounts",
      state: "",
      icon: "people-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "members",
      label: "Members",
      state: "enabled",
      icon: "ribbon-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "verification",
      label: "Alerts",
      state: "enabled",
      icon: "alert-circle-outline",
      subText: "",
      route: "",
    },
  ];
  private navOptsMobileAdmin = [
    {
      type: "navItem",
      name: "overview",
      label: "Overview",
      state: "active",
      icon: "reader-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "sales",
      label: "Sales",
      state: "",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "registrants",
      label: "Accounts",
      state: "",
      icon: "people-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "members",
      label: "Members",
      state: "enabled",
      icon: "ribbon-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "verification",
      label: "Alerts",
      state: "enabled",
      icon: "alert-circle-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "coupons",
      label: "Coupons",
      state: "enabled",
      icon: "ticket-outline",
      subText: "",
      route: "",
      isAdminOption: true,
    },
    {
      type: "navItem",
      name: "invoices",
      label: "Invoices",
      state: "",
      icon: "document-text-outline",
      subText: "",
      route: "",
      isAdminOption: true,
    },
  ];

  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    if (this.match.params.eventCodeForMonitoring) {
      state.eventCodeForMonitoring =
        this.match.params.eventCodeForMonitoring.trim();
      this.getEventName();
    }
  }

  getEventName() {
    let payload = {
      eventCode: state.eventCodeForMonitoring,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/geteventbycode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.eventNameForMonitoring = response.data.payload.name;
          this.isEventNameFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  /*-----
  Methods
  -----*/
  hideOverlay(event) {
    event.preventDefault();
    this.isUserDetailsModalVisible = false;
    this.isOverlayVisible = false;
    this.isTicketDetailVisible = false;
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  render() {
    return (
      <Host>
        {this.isToastActive && (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        )}

        {/* {this.isUserDetailsModalVisible && (
          <c-user-info-modal></c-user-info-modal>
        )} */}

        {this.isTicketDetailVisible && (
          <c-ticket-detail-modal></c-ticket-detail-modal>
        )}

        {this.isOverlayVisible && (
          <div
            class="dark-overlay"
            onClick={(event) => this.hideOverlay(event)}
          ></div>
        )}

        <c-topbar></c-topbar>

        <div class="show-on-mobile dropdown-menu-container">
          {state.isUserDataFetched && (
            <div>
              {state.isAdmin ? (
                <c-dropdown
                  name="changeMobilePage"
                  option-str={JSON.stringify(this.navOptsMobileAdmin)}
                ></c-dropdown>
              ) : (
                <c-dropdown
                  name="changeMobilePage"
                  option-str={JSON.stringify(this.navOptsMobileRM)}
                ></c-dropdown>
              )}
            </div>
          )}
          <c-button
            type="toggleMobileDashboardOptions"
            name={
              state.isMobileDashboardOptionsVisible
                ? "hideDashboardViewOptions"
                : "showDashboardViewOptions"
            }
            isDisabled={this.activeDashboardView === "overview" ? true : false}
          ></c-button>
        </div>

        <c-vnav
          nav-opts-str={
            state.isAdmin
              ? JSON.stringify(this.navOptsAdmin)
              : JSON.stringify(this.navOptsRM)
          }
        ></c-vnav>

        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}

        {this.isEventNameFetched && (
          <main>
            {this.activeDashboardView === "overview" && (
              <c-rm-overview-2></c-rm-overview-2>
            )}
            {this.activeDashboardView === "registrants" && (
              <c-rm-accounts></c-rm-accounts>
            )}
            {this.activeDashboardView === "sales" && <c-rm-sales></c-rm-sales>}
            {this.activeDashboardView === "members" && (
              <c-rm-members></c-rm-members>
            )}
            {this.activeDashboardView === "verification" && (
              <c-rm-alerts></c-rm-alerts>
            )}
            {this.activeDashboardView === "coupons" && (
              <c-rm-coupons></c-rm-coupons>
            )}
            {this.activeDashboardView === "invoices" && (
              <c-rm-invoices></c-rm-invoices>
            )}
          </main>
        )}
      </Host>
    );
  }
}

injectHistory(PDashboard);
