:host {
  display: block;
}

p-dashboard c-topbar .header-content {
  background: #f7f9fa;
  margin-top: 0;
  padding-top: 1em;
}

p-dashboard c-topbar header {
  margin-left: 1%;
}

p-dashboard .overview {
  width: 60%;
  margin: 1em auto 0 16.5%;
}

p-dashboard .dark-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  -webkit-animation: fadein 0.25s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: fadein 0.25s; /* Firefox < 16 */
  -ms-animation: fadein 0.25s; /* Internet Explorer */
  -o-animation: fadein 0.25s; /* Opera < 12.1 */
  animation: fadein 0.25s;
}

p-dashboard > main {
  position: absolute;
  top: 6em;
  margin-left: 17%;
  width: 57.5%;
  padding-bottom: 8em;
}

p-dashboard c-vnav nav {
  position: fixed;
  margin-top: 6em;
  margin-left: 1%;
}

p-dashboard .section-divider {
  margin-top: 0;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* p-dashboard c-user-info-modal .modal-container {
  position: absolute;
  width: 50%;
  background: white;
  margin: 10vh auto 10em 25%;
  padding: 1em 1.5em;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
}

p-dashboard c-user-info-modal .name {
  margin-bottom: 0;
}

p-dashboard c-user-info-modal .headline {
  margin: 0;
  padding: 0;
} */

p-dashboard .controls {
  position: fixed;
  width: 22.5%;
  height: 120px;
  background: orange;
  right: 0;
  top: 7em;
}

p-dashboard .show-on-desktop {
  display: block;
}

p-dashboard .dropdown-menu-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-sizing: border-box;
  max-width: 350px;
  width: 100%;
  margin: 0 auto;
  margin-top: 1.5em;
  margin-bottom: 1.5em;
}

p-dashboard .dropdown-menu-container c-dropdown select {
  display: block;
  width: 290px;
  margin-right: 0;
}

p-dashboard .show-on-mobile {
  display: none;
}

@media only screen and (max-width: 768px) {
  p-dashboard c-topbar header {
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
    margin-top: 1em;
    box-sizing: border-box;
  }

  p-dashboard .default-page c-dropdown select {
    width: 100%;
    margin-top: 1em;
  }

  p-dashboard .overview {
    width: 100%;
    margin: 0 auto;
  }

  p-dashboard c-vnav nav {
    display: none;
  }

  p-dashboard > main {
    position: static;
    box-sizing: border-box;
    max-width: 350px;
    width: 100%;
    margin: 0 auto;
  }

  p-dashboard .show-on-mobile {
    display: flex;
  }
}
