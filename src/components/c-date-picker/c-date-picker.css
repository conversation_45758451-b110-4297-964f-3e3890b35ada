c-date-picker input {
  box-sizing: border-box;
  width: 100%;
  outline: none;
  border: 0;
  border-radius: var(--site-border-radius);
  font-size: 0.9em;
  padding: 0.5em 0.25em 0.5em 0.5em;
  border: var(--site-border);
  transition: var(--site-transition);
}

c-date-picker .date-picker-container {
  width: 150px;
}

c-date-picker .time-picker-enabled {
  border-radius: 0.25em 0.25em 0 0;
  border-bottom: 0;
}

c-date-picker .time-picker {
  border-radius: 0 0 0.25em 0.25em;
}

c-date-picker input:hover,
input::-webkit-calendar-picker-indicator:hover {
  cursor: pointer;
}

c-date-picker input::-webkit-datetime-edit-day-field,
input::-webkit-datetime-edit-month-field,
input::-webkit-datetime-edit-year-field,
input::-webkit-datetime-edit-text,
input::-webkit-datetime-edit-hour-field,
input::-webkit-datetime-edit-minute-field,
input::-webkit-datetime-edit-ampm-field {
  color: rgba(0, 0, 0, 0.4);
}

c-date-picker input::-webkit-datetime-edit-text {
  padding: 0.25em;
}

input::-webkit-calendar-picker-indicator {
  filter: invert(0.6);
}

c-date-picker input:focus {
  border: var(--site-border-focus);
}
