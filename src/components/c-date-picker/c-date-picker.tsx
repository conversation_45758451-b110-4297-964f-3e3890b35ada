import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-date-picker",
  styleUrl: "c-date-picker.css",
})
export class CDatePicker {
  @Prop() name: string;
  @Prop() date: string;
  @Prop() time: string;
  @Prop() pickTime: boolean = true;

  @Event({
    eventName: "dateInput",
    bubbles: true,
  })
  dateInput: EventEmitter;

  @Event({
    eventName: "timeInput",
    bubbles: true,
  })
  timeInput: EventEmitter;

  handleDateInput(e) {
    this.dateInput.emit({
      name: this.name,
      value: e.target.value,
    });
  }

  handleTimeInput(e) {
    this.timeInput.emit({
      name: this.name,
      value: e.target.value,
    });
  }

  render() {
    return (
      <div class="date-picker-container">
        <input
          type="date"
          class={this.pickTime && "time-picker-enabled"}
          name={this.name}
          onInput={(e) => this.handleDateInput(e)}
          value={this.date}
        ></input>
        {this.pickTime && (
          <input
            type="time"
            class="time-picker"
            name={this.name}
            onInput={(e) => this.handleTimeInput(e)}
            value={this.time}
          ></input>
        )}
      </div>
    );
  }
}
