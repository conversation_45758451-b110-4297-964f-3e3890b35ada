import { Component, Listen, Prop, State, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-rzp-thanks",
  styleUrl: "p-rzp-thanks.css",
})
export class PRzpThanks {
  @Prop() history: RouterHistory;
  @State() compState = "init";

  @Listen("back-to-account")
  backToAccount(event) {
    event.preventDefault();
    this.backToAccountHandler();
  }

  backToAccountHandler() {
    this.history.push("/", {});
  }

  componentWillLoad() {
    if (!this.history.location.state) {
      this.history.push("/", {});
    }
  }

  componentDidLoad() {
    setTimeout(() => {
      this.checkPaymentStatus();
    }, 3000);
  }

  checkPaymentStatus() {
    let payload = this.history.location.state;
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/razorpay-confirm-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.changeComponentState("rzp-payment-failed");
        } else if (response.data.status === "Success") {
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
          state.isCouponsAvailable = false;
          state.paymentGateway = "razorpay";
          this.changeComponentState("rzp-payment-success");
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  changeComponentState(newState) {
    this.compState = newState;
  }

  render() {
    return (
      <div class="rzp-thanks-container">
        {this.compState === "rzp-payment-success" ? (
          <div>
            <h1 class="heading-success">
              <span class="tick">✓</span> Payment successful
            </h1>
            <p>
              We have recorded your purchases. A confirmation mail has been sent
              to your email.
            </p>
            <div class="footer-controls">
              <div></div>
              <c-btn
                name="backtoaccount"
                label="My account"
                action-label=".."
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            </div>
          </div>
        ) : (
          ""
        )}

        {this.compState === "rzp-payment-failed" ? (
          <div>
            <h1 class="heading-failed">Something went wrong</h1>
            <p>
              Our payment gateway (Razorpay) was unable to process your payment.
              Either the transaction failed entirely or there were processing
              delays.
            </p>
            <p>
              <strong>In case of payment failure</strong>, money should not be
              deducted from your account/card. You may go back to your account
              and try paying once again.
            </p>
            <p>
              <strong>But in the rare case of processing delays</strong>, money
              is probably deducted from your account/card. But don't worry, it
              usually takes a few minutes to hours for this processing to
              complete. Kindly refrain from purchasing further items and keep
              checking the "Purchases" page of your IndiaHCI account. Once the
              processing is complete, the items will be marked as "Paid" and
              will show up in the "Purchases" page. In case the processing is
              not successful within 5 days, the deducted amount will be refunded
              to your account/card.
            </p>

            <div class="footer-controls">
              <div></div>
              <c-btn
                name="backtoaccount"
                label="My account"
                action-label=".."
                is-in-action={false}
                is-disabled={false}
              ></c-btn>
            </div>
          </div>
        ) : (
          ""
        )}

        {this.compState === "init" ? (
          <div class="init-container">
            <c-spinner-dark></c-spinner-dark>
            &nbsp;&nbsp;
            <p class="processing-message">Processing payment..</p>
          </div>
        ) : (
          ""
        )}
      </div>
    );
  }
}

injectHistory(PRzpThanks);
