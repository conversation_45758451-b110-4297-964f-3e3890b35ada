p-rzp-thanks .rzp-thanks-container {
  background: white;
  width: 40%;
  padding: 2em 3em;
  border-radius: 0.25em;
  /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05); */
  border: 1px solid rgba(0, 0, 0, 0.1);

  margin: 0 auto;
  margin-top: 25vh;
}

p-rzp-thanks .rzp-thanks-container p {
  line-height: 1.5;
  font-size: 0.9em;
}

p-rzp-thanks .footer-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3em;
}

p-rzp-thanks .heading-failed {
  font-size: 1.5em;
  color: var(--danger-color);
}

p-rzp-thanks .heading-success {
  font-size: 1.5em;
  color: var(--accent-color);
}

p-rzp-thanks .heading-success .tick {
  color: white;
  padding: 0.1em 0.4em;
  font-size: 0.7em;
  background: var(--accent-green);
  border-radius: 100%;
}

p-rzp-thanks c-text-link a {
  font-size: 0.9em;
}

p-rzp-thanks .init-container {
  display: flex;
  align-items: center;
}

p-rzp-thanks .processing-message {
  font-size: 4em;
}

@media only screen and (max-width: 768px) {
  p-rzp-thanks .rzp-thanks-container {
    width: 80%;
    margin-top: 2em;
  }
}
