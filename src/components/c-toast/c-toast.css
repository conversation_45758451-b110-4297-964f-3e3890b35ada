c-toast .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1em;
  width: 32%;
  z-index: 999999;
  border-radius: 0.25em;
  position: fixed;
  top: 3vh;
  margin-left: 33%;
  -webkit-animation: showtoast 0.25s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: showtoast 0.25s; /* Firefox < 16 */
  -ms-animation: showtoast 0.25s; /* Internet Explorer */
  -o-animation: showtoast 0.25s; /* Opera < 12.1 */
  animation: showtoast 0.25s;
}

@keyframes showtoast {
  from {
    opacity: 0;
    top: -10vh;
  }
  to {
    opacity: 1;
    top: 2vh;
  }
}

c-toast .Success {
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
  border: 1px solid var(--accent-green-darker);
}

c-toast .Failed {
  color: var(--accent-pink-darker);
  border: 1px solid var(--accent-pink-darker);
  background: #ffebf4;
}

c-toast p {
  margin: 0;
  font-size: 0.9em;
}

c-toast button {
  background: none;
  border: none;
  font-size: 1.5em;
  transition: all 0.15s ease-in;
  outline: none;
}

c-toast button:hover {
  cursor: pointer;
  transform: scale(1.2);
}

c-toast .close-Success {
  color: var(--accent-green-darker);
  margin: 0;
  padding: 0;
}

c-toast .close-Failed {
  color: var(--accent-pink-darker);
  margin: 0;
  padding: 0;
}

c-toast .hidden {
  display: none;
}

@media only screen and (max-width: 768px) {
  c-toast .container {
    width: 82.5%;
    padding: 0.5em;
    margin: 0 auto;
  }
}
