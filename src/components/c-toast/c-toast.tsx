import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-toast",
  styleUrl: "c-toast.css",
})
export class CToast {
  @Prop() type: string;
  @Prop() label: string;
  @Prop() duration: number;
  private containerClass: string;

  componentWillLoad() {
    this.containerClass = `container ${this.type}`;
  }

  @Event({
    eventName: "hide-toast",
    bubbles: true,
  })
  hideToast: EventEmitter;

  componentDidLoad() {
    setTimeout(() => {
      this.hideToast.emit();
    }, this.duration * 1000);
  }

  handleBtnClick(event) {
    event.preventDefault();
    this.hideToast.emit();
  }

  render() {
    return (
      <div class={this.containerClass}>
        <p>{this.label}</p>
        <button
          class={`close-${this.type}`}
          onClick={(event) => this.handleBtnClick(event)}
        >
          ×
        </button>
      </div>
    );
  }
}
