import { Component, Listen, State, Prop, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import state from "../../global/state";

@Component({
  tag: "c-oauth",
  styleUrl: "c-oauth.css",
})
export class COauth {
  @Prop() history: RouterHistory;

  googleButtonDiv!: HTMLDivElement;

  @State() isGoogleOauthLoaded: boolean = false;

  private _window: any;

  @Listen("buttonClick")
  async buttonClickHandler(event) {
    if (event.detail.name === "googleOauth") {
      console.log("Google Oauth pressed");
    } else if (event.detail.name === "linkedinOauth") {
      console.log("LinkedIn Oauth login");
      window.open(`https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${state.linkedinClientId}&redirect_uri=${state.linkedinRedirectUri}&state=${state.linkedinState}&scope=r_liteprofile%20r_emailaddress
`);
    }
  }

  componentDidLoad() {
    this._window = window as any;
    this.initGoogleOauth();
  }

  loadScript(src) {
    return new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }

  async initGoogleOauth() {
    if (!this.isGoogleOauthLoaded) {
      const isScriptLoaded = await this.loadScript(state.googleScriptUrl);

      if (!isScriptLoaded) {
        console.log("Failed to load Google Oauth script");
        return;
      }

      await this._window.google.accounts.id.initialize({
        client_id: state.googleClientId,
        callback: (response) => {
          let payload = {
            googleJwt: response.credential,
          };
          this.history.push("/post-google-oauth", payload);
        },
      });

      await this._window.google.accounts.id.renderButton(this.googleButtonDiv, {
        type: "standard",
        theme: "outline",
        size: "large",
        width: 280,
      });

      this.isGoogleOauthLoaded = true;
    }
  }

  render() {
    return (
      <div class={`oauth-container ${!this.isGoogleOauthLoaded && "hidden"}`}>
        <c-button
          name="linkedinOauth"
          type="oauth"
          icon-url="https://res.cloudinary.com/layerpark/image/upload/v1596921819/social-icons/linkedin-white-transparent.svg"
        >
          Sign in with LinkedIn
        </c-button>
        <br />
        <div ref={(el) => (this.googleButtonDiv = el as HTMLDivElement)}></div>
        <c-divider type="oauth"></c-divider>
      </div>
    );
  }
}

injectHistory(COauth);
