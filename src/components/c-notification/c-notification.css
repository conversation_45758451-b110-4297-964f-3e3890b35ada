c-notification .notification-container {
  position: absolute;
  display: none;
  opacity: 0;
  padding: var(--site-padding);
  border-radius: var(--site-border-radius);
  background: black;
  font-size: 0.9em;
  z-index: 9999999;
}

c-notification .notification__top-centre {
  top: -20vh;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  text-align: center;
}

c-notification .error {
  color: var(--red-400);
  background: var(--red-50);
  border: 1px solid var(--red-100);
}

c-notification .success {
  color: var(--accent-green-darker);
  background: var(--accent-green-bg-lighter);
  border: 1px solid var(--accent-green);
}
