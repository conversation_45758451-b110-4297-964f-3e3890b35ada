import { Component, Prop, Watch, h } from "@stencil/core";
import { gsap } from "gsap";
import state from "../../global/state";

@Component({
  tag: "c-notification",
  styleUrl: "c-notification.css",
})
export class CNotification {
  @Prop() isActive: boolean;

  @Watch("isActive") isActiveWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      if (newVal) {
        this.generateCssClasses();
      }
    }
  }

  notificationContainer!: HTMLDivElement;

  private cssClasses: string = "notification-container";

  generateCssClasses() {
    if (state.notificationPosition === "bottomRight") {
      this.cssClasses = `${this.cssClasses} notification__bottom-right`;
    } else if (state.notificationPosition === "topCentre") {
      this.cssClasses = `${this.cssClasses} notification__top-centre`;
    }
    this.cssClasses = `${this.cssClasses} ${state.notificationType}`;
    this.showNotification();
  }

  hideNotification_TopCentre(notificationContainerEl: any) {
    let tl = gsap.timeline();
    tl.to(notificationContainerEl, {
      top: "-20vh",
      opacity: 0,
      duration: 0.25,
    });
    tl.to(notificationContainerEl, {
      display: "none",
    });
  }

  showNotification() {
    if (state.notificationPosition === "topCentre") {
      this.showNotification_TopCentre();
    }
  }

  hideNotification(notificationContainerEl: any) {
    if (state.notificationPosition === "topCentre") {
      this.hideNotification_TopCentre(notificationContainerEl);
    }
    state.notificationType = "";
    state.notificationMessage = ``;
    state.notificationPosition = "";
    state.isNotificationActive = false;
  }

  showNotification_TopCentre() {
    let tl = gsap.timeline();
    tl.to(this.notificationContainer, {
      display: "block",
    });
    tl.to(this.notificationContainer, {
      top: "2.5vh",
      opacity: 1,
      duration: 0.25,
    });
    setTimeout(() => {
      this.hideNotification(this.notificationContainer);
    }, 5000);
  }

  render() {
    return (
      <div
        class={this.cssClasses}
        ref={(el) => (this.notificationContainer = el as HTMLDivElement)}
      >
        <c-text>{state.notificationMessage}</c-text>
      </div>
    );
  }
}
