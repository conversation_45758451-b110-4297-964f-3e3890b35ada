import { Component, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-section-list",
  styleUrl: "c-section-list.css",
})
export class CSectionList {
  @State() isFetching: boolean = true;
  private membershipSection: string = "";

  componentDidLoad() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/getmembership`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
        } else if (response.data.status === "Success") {
          this.membershipSection = response.data.payload.membershipSection;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="section-container">
        {this.membershipSection === "professional-hcipai-member" ? (
          <c-section-get-member-discount></c-section-get-member-discount>
        ) : (
          ""
        )}
        {this.membershipSection === "professional" ? (
          <c-section-buy-membership></c-section-buy-membership>
        ) : (
          ""
        )}
        <div class="ticket-seperator"></div>
        <h1 class="section-title">Full Conference Ticket</h1>
        {!state.isGuest &&
        !state.isSponsor &&
        !state.isPrimaryTicketDiscounted ? (
          <c-section-ticket type="full-conference"></c-section-ticket>
        ) : (
          <c-injectedticket></c-injectedticket>
        )}
      </div>
    );
  }
}
