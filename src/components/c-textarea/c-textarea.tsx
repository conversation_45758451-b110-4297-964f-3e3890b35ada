import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-textarea",
  styleUrl: "c-textarea.css",
})
export class CTextarea {
  @Event({
    eventName: "textarea-input-event",
    bubbles: true,
  })
  textAreaInputEvent: EventEmitter;

  @Prop() rows: number;
  @Prop() cols: number;
  @Prop() placeholder: string;
  @Prop() name: string;

  handleInput = (event) => {
    this.textAreaInputEvent.emit({
      name: this.name,
      value: event.target.value.trim(),
      key: event.key,
    });
  };

  render() {
    return (
      <textarea
        rows={this.rows}
        cols={this.cols}
        placeholder={this.placeholder}
        onKeyUp={(event) => this.handleInput(event)}
      ></textarea>
    );
  }
}
