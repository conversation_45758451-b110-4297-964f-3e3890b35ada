c-textarea textarea {
  box-sizing: border-box;
  width: 100%;
  padding: 0.75em;
  font-size: 1em;
  border: 0;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.03);
  background: rgba(0, 0, 0, 0.03);
  transition: all 0.15s ease-in;
  white-space: pre-line;
}

c-textarea textarea:hover {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

input:focus,
input[type]:focus,
.uneditable-input:focus {
  border-color: var(--accent-color-bg);
  outline: 0 none;
}

::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
  font-size: 0.9em;
}

:-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.37);
  font-size: 0.9em;
}

::placeholder {
  color: rgba(0, 0, 0, 0.37);
  font-size: 0.9em;
}
