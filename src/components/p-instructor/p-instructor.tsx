import { Component, Prop, State, h } from "@stencil/core";
import { RouterHistory, MatchResults } from "@stencil/router";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-instructor",
  styleUrl: "p-instructor.css",
})
export class PInstructor {
  @Prop() match: MatchResults;
  @Prop() history: RouterHistory;

  @State() isDataFetched: boolean = false;

  private ticketId: string = "";
  private sessionId: string = "";
  private sessionTitle: string = "";
  private sessionParticipants: any = [];

  componentWillLoad() {
    this.ticketId = this.match.params.ticketId;
    this.sessionId = this.match.params.sessionId;
  }

  componentDidLoad() {
    this.fetchViewData();
  }

  fetchViewData() {
    let payload = {
      ticketId: this.ticketId,
      sessionId: this.sessionId,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-session-details`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.sessionTitle = response.data.payload.sessionTitle;
          this.sessionParticipants = response.data.payload.sessionParticipants;
          this.isDataFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="modal__container">
        {this.isDataFetched ? (
          <c-text>{this.sessionTitle}</c-text>
        ) : (
          <c-text>Fetching session details..</c-text>
        )}
        <br />
        <c-card>
          {this.isDataFetched ? (
            this.sessionParticipants.length > 0 ? (
              <ul>
                <c-text type="subtext">PARTICIPANT LIST</c-text>
                {this.sessionParticipants.map((participant: any) => (
                  <li>
                    <c-text>{participant.name}</c-text>
                    <c-link>{participant.email}</c-link>
                  </li>
                ))}
              </ul>
            ) : (
              <e-text>There are no participants yet</e-text>
            )
          ) : (
            <ul>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>
              </li>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>{" "}
              </li>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>{" "}
              </li>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>{" "}
              </li>
            </ul>
          )}
        </c-card>
      </div>
    );
  }
}
