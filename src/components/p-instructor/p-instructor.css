p-instructor .registrant-list {
  margin: 0;
  padding: 0;
  list-style-type: none;
  background: white;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

p-instructor .registrant-list-item {
  display: flex;
  justify-content: space-between;
  padding: 1em;
}

p-instructor .registrant-list-item span {
  width: 50%;
  text-align: left;
}

p-instructor .registrant-list-item:nth-child(even) {
  background: rgba(0, 0, 0, 0.03);
}

p-instructur .track-detail-container .heading {
  color: #593196;
}

p-instructor .registrant-list-item .item-1 {
  width: 10%;
}

p-instructor .registrant-list-item .item-2 {
  width: 40%;
}

p-instructor .registrant-list-item .item-3 {
  width: 40%;
}

.modal__container {
  width: 50%;
  background: var(--bg-color);
  z-index: 9999;
  position: absolute;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0.25em;
  padding: 1.5em;
  box-sizing: border-box;
  margin-top: 1em;
}

.modal__container c-card .basic-card-container {
  background: white;
  padding: 1.5em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
}

.modal__container c-text .subtext {
  margin: 0;
  padding: 0;
}

.modal__container c-card ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.modal__container c-card ul li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1em;
  padding-bottom: 1em;
}

.modal__container c-card ul li c-skel-line:first-child .skel-line {
  margin-bottom: 1em;
}

.modal__container c-card ul li:first-child {
  padding-top: 0;
}

.modal__container c-card ul li:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}
