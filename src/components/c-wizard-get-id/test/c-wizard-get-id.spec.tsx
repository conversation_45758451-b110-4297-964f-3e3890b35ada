import { newSpecPage } from '@stencil/core/testing';
import { CWizardGetId } from '../c-wizard-get-id';

describe('c-wizard-get-id', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CWizardGetId],
      html: `<c-wizard-get-id></c-wizard-get-id>`,
    });
    expect(page.root).toEqualHtml(`
      <c-wizard-get-id>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-wizard-get-id>
    `);
  });
});
