import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  State,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-wizard-get-id",
  styleUrl: "c-wizard-get-id.css",
})
export class CWizardGetId {
  @Listen("fileChangeEvent")
  fileChangeEventHandler(event) {
    if (event.detail.name === "idCardFront") {
      this.idCardFront = event.detail.file;
    } else if (event.detail.name === "idCardBack") {
      this.idCardBack = event.detail.file;
    }
    this.wizardStepCheck();
  }

  @Listen("fileRemovedEvent")
  fileRemovedEventHandler(event) {
    if (event.detail.name === "idCardFront") {
      this.idCardFront = "";
    } else if (event.detail.name === "idCardBack") {
      this.idCardBack = "";
    }
    this.wizardStepCheck();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "idCardExpiry") {
      this.idCardExpiry = e.detail.value;
      this.wizardStepCheck();
    }
  }

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "submitIdCard") {
      this.submitIdCard();
    } else if (e.detail.name === "convertAccountToPro") {
      this.convertAccountToPro();
    } else if (e.detail.name === "nextStep") {
      this.wizardStepCount = this.wizardStepCount + 1;
    } else if (e.detail.name === "previousStep") {
      this.wizardStepCount = this.wizardStepCount - 1;
    }
  }

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "idCardChoice") {
      this.idCardChoice = event.detail.value;
    }
  }

  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @State() wizardStepCount: number = 0;
  @State() isIdNextDisabled: boolean = true;
  @State() isChoiceNextDisabled: boolean = false;
  @State() isSubmitting: boolean = false;
  @State() idCardChoice: string = "uploadIdCard";

  private idCardFront: any;
  private idCardBack: any;
  private idCardExpiry: string = "";

  initWizard() {
    // Initializes states
    this.isIdNextDisabled = true;
    this.isChoiceNextDisabled = false;
    this.isSubmitting = false;

    //Inits Vars
    this.idCardFront = "";
    this.idCardBack = "";
    this.idCardExpiry = "";
  }

  wizardStepCheck() {
    if (this.idCardFront && this.idCardExpiry.length > 0) {
      this.isIdNextDisabled = false;
    } else {
      this.isIdNextDisabled = true;
    }
  }

  submitIdCard() {
    this.isIdNextDisabled = true;
    this.isSubmitting = true;

    let formData: any = new FormData();
    formData.append("idCardFront", this.idCardFront);
    if (this.idCardBack) {
      formData.append("idCardBack", this.idCardBack);
    }
    formData.append("idCardExpiry", this.idCardExpiry);

    axios({
      method: "POST",
      data: formData,
      baseURL: `${state.baseUrl}/upload-id-card`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert("An error occured while uploading ID card. Please try again");
          this.initWizard();
        } else if (response.data.status === "Success") {
          state.isIdCardVerified = false;
          this.closeModalEvent.emit();
        }
        this.isIdNextDisabled = false;
        this.isSubmitting = false;
      })
      .catch((error: any) => {
        alert(`${error}: Please try again`);
        this.initWizard();
        this.isIdNextDisabled = false;
        this.isSubmitting = false;
      });
  }

  convertAccountToPro() {
    this.isChoiceNextDisabled = true;
    this.isSubmitting = true;

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/convert-account-to-pro`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(`${response.data.msg}. Please try again`);
          this.initWizard();
        } else if (response.data.status === "Success") {
          this.closeModalEvent.emit();
        }
        this.isChoiceNextDisabled = false;
        this.isSubmitting = false;
      })
      .catch((error: any) => {
        alert(`${error}: Please try again`);
        this.initWizard();
        this.isChoiceNextDisabled = false;
        this.isSubmitting = false;
      });
  }

  ChoiceStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        {!state.isIdCardExists && (
          <c-text>
            Your {state.occupation} account does not have an ID card associated
            with it. What would you like to do?&nbsp;
            <span class="mandatory">*</span>
          </c-text>
        )}
        {state.isIdCardExists && state.isIdCardExpired ? (
          <c-text>
            Your {state.occupation} ID card has expired. What would you like to
            do?&nbsp;
            <span class="mandatory">*</span>
          </c-text>
        ) : (
          ""
        )}
        <c-radio
          name="idCardChoice"
          label1={
            state.isIdCardExists
              ? state.isIdCardExpired
                ? `Re-upload ${state.occupation} ID card`
                : ""
              : `Upload ${state.occupation} ID card`
          }
          label2=""
          label3=""
          val="uploadIdCard"
          isChecked={this.idCardChoice === "uploadIdCard" ? true : false}
        ></c-radio>
        <c-radio
          name="idCardChoice"
          label1="Convert to Professional account"
          label2=""
          label3=""
          val="convertAccountToPro"
          isChecked={this.idCardChoice === "convertAccountToPro" ? true : false}
        ></c-radio>
      </div>
      {this.idCardChoice === "convertAccountToPro" && (
        <div>
          <c-banner>
            <u>Please note</u>: Tickets prices are higher for Professionals
          </c-banner>
          <br />
        </div>
      )}

      <div class="field-container">
        <div class="field-container-row">
          <div></div>
          <c-button
            name={
              this.idCardChoice === "uploadIdCard"
                ? "nextStep"
                : "convertAccountToPro"
            }
            icon-name=""
            isDisabled={this.isChoiceNextDisabled}
            isInAction={this.isSubmitting}
            isInActionLabel="Submitting.."
          >
            {this.idCardChoice === "uploadIdCard" && "Next"}
            {this.idCardChoice === "convertAccountToPro" && "Convert"}
          </c-button>
        </div>
      </div>
    </div>
  );

  UploadStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text>
          Kindly upload your {state.occupation} ID card
          <br />
          <u>Max file size: 500KB</u>
        </c-text>
      </div>
      <div class="field-container">
        <c-text>
          Front side <span class="mandatory">*</span>
        </c-text>
        <c-uploader
          file-type="file"
          name="idCardFront"
          fileSizeLimitInKB={500}
        ></c-uploader>
      </div>
      <div class="field-container">
        <c-text>Back side (if applicable) </c-text>
        <c-uploader
          file-type="file"
          name="idCardBack"
          fileSizeLimitInKB={500}
        ></c-uploader>
      </div>
      <div class="field-container">
        <c-text>
          ID card expiry date <span class="mandatory">*</span>
        </c-text>
        <c-date-picker name="idCardExpiry" pickTime={false}></c-date-picker>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="submitIdCard"
            icon-name=""
            isDisabled={this.isIdNextDisabled}
            isInAction={this.isSubmitting}
            isInActionLabel="Submitting.."
          >
            Submit
          </c-button>
        </div>
      </div>
    </div>
  );

  render() {
    return (
      <div class="wizard-container">
        <div class="wizard-header">
          <c-text type="wizardHeading">
            Upload {state.occupation === "student" ? "Student" : "Professional"}{" "}
            ID card
          </c-text>
        </div>
        {this.wizardStepCount === 0 && <this.ChoiceStep></this.ChoiceStep>}
        {this.wizardStepCount === 1 && <this.UploadStep></this.UploadStep>}
      </div>
    );
  }
}
