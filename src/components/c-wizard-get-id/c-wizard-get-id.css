c-wizard-get-id .wizard-container {
  position: fixed;
  width: 30%;
  left: 0;
  right: 0;
  margin: 0 auto;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  padding: 1em 1.5em 1.5em 1.5em;
  z-index: 999999;
}

c-wizard-get-id .wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-get-id .wizard-content {
  box-sizing: border-box;
  background: white;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
  padding: 1em;
  margin-top: 1em;
}

c-wizard-get-id .wizard-content .field-container:last-child {
  margin-bottom: 0;
}

c-wizard-get-id .wizard-content .field-container:last-child {
  margin-bottom: 0;
}

c-wizard-get-id .field-container-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-get-id .field-container {
  margin-bottom: 1.5em;
}

c-wizard-get-id .date-picker-container {
  margin-top: 0.5em;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-wizard-get-id .wizard-container {
    width: 90%;
    max-width: 300px;
    margin-top: 1em;
    padding: 1em;
  }
}
