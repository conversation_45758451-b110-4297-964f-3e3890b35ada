.purchased-item-container {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  background: white;
  padding: 1em;
  margin-bottom: 1em;
  width: 82.5%;
  /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05); */
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  align-items: center;
}

c-purchased-item .details {
  /* width: 75%; */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.purchased-item-subtitle {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

.purchased-item-remove-btn {
  /* height: 15px;
  width: 15px; */
  border: 0;
  background: none;
  font-size: 1.5em;
  color: rgba(231, 76, 60, 1);
  transition: all 0.15s ease-in;
  margin: 0;
  padding: 0;
}
.purchased-item-remove-btn:hover {
  cursor: pointer;
  /* background: rgba(231, 76, 60, 0.1); */
  transform: scale(1.25);
}

.purchased-item-content-left {
  width: 75%;
  margin: 0;
}
.purchased-item-content-left .subtext {
  margin-top: 0;
}
.purchased-item-content-right {
  margin: 0;
  font-size: 0.9em;
}

c-purchased-item .purchased-item-title {
  margin: 0;
  font-size: 0.9em;
  line-height: 1.5;
  margin-top: 0.5em;
}

c-purchased-item .badge {
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.25em 1em;
  margin: 0;
  margin-bottom: 0.5em;
  border-radius: 0.25em;
  margin-right: 1em;
  width: 75px;
  text-align: center;
}

c-purchased-item .blue-badge {
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}
