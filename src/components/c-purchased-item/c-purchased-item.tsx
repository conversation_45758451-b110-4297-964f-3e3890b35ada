import { Component, Prop, Event, EventEmitter, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-purchased-item",
  styleUrl: "c-purchased-item.css",
})
export class CPurchasedItem {
  @Event({
    eventName: "empty-purchase-list",
    bubbles: true,
  })
  emptyPurchaseListHandler: EventEmitter;

  @Prop() ticketId: string;
  @Prop() heading: string;
  @Prop() subtitle: string;
  @Prop() currency: string;
  @Prop() price: string;
  @Prop() tier: string;

  componentWillLoad() {
    this.subtitle = this.lowerFirstLetter(this.subtitle);
  }

  lowerFirstLetter(str: string) {
    return str.charAt(0).toLowerCase() + str.slice(1);
  }

  removeTicketFromCart(e) {
    e.preventDefault();
    let payload = {
      ticketId: this.ticketId,
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/remove-from-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.getCart();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  getCart() {
    let payload = {
      eventCode: state.eventCodeForRegistration,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.updateCartTotal();
          state.cartItems = response.data.payload.cartItems;
          state.cartTotal = response.data.payload.cartTotal;
          if (state.cartItems.length === 0)
            this.emptyPurchaseListHandler.emit();
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  updateCartTotal() {
    let payload = {
      eventCode: state.eventCodeForRegistration,
      paymentGateway: state.paymentGateway,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-checkout-total`,
      withCredentials: true,
      data: payload,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.currency = response.data.payload.currency;
          state.gatewayFee = response.data.payload.gatewayFee;
          state.grandTotal = response.data.payload.grandTotal;
          if (response.data.payload.coupon.isApplied) {
            state.isCouponApplied = response.data.payload.coupon.isApplied;
            state.appliedCouponName = response.data.payload.coupon.name;
            state.appliedCouponDeductionType =
              response.data.payload.coupon.deductionType;
            state.appliedCouponDeductionValue =
              response.data.payload.coupon.deductionValue;
            state.cartTotalAfterDiscount =
              response.data.payload.coupon.cartTotalAfterDiscount;
            state.deductedAmount = response.data.payload.coupon.deductedAmount;
          }
          if (state.grandTotal === 0) {
            state.isFreePurchase = true;
          }
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="purchased-item-container">
        <div class="details">
          <div class="purchased-item-content-left">
            {/* {this.subtitle === "workshop" || this.subtitle === "course" ? (
              <span class="purchased-item-subtitle badge blue-badge">
                {this.subtitle}
              </span>
            ) : (
              <span class="purchased-item-subtitle badge blue-badge">
                {this.subtitle.toUpperCase()}{" "}
              </span>
            )} */}
            <c-text type="subtext">{this.subtitle.toUpperCase()}</c-text>
            <p class="purchased-item-title">
              {this.heading}{" "}
              {this.tier.length > 0 ? `- (${this.tier.toUpperCase()})` : ""}
            </p>
            {/* <c-text type="subtext">
              {" "}
              {this.tier.length > 0 ? `${this.tier.toUpperCase()}` : ""}
            </c-text> */}
          </div>
          <p class="purchased-item-content-right">
            <span>{this.currency}</span>
            <span>{this.price}</span>
          </p>
        </div>
        {/* <button
          class="purchased-item-remove-btn"
          onClick={(e) => this.removeTicketFromCart(e)}
        >
          ×
        </button> */}
      </div>
    );
  }
}
