import { Component, Event, EventEmitter, Listen, Prop, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-overview-info",
  styleUrl: "c-overview-info.css",
})
export class COverviewInfo {
  @Prop() label: string;
  @Prop() count: string;
  @Prop() highlight: string;
  @Prop() total: string;
  @Prop() isBtn: boolean = false;
  @Prop() width: number;
  @Prop() layout: string;

  @Event({
    eventName: "show-ticket-details",
    bubbles: true,
  })
  showTicketDetails: EventEmitter;

  @Listen("button-click")
  buttonClickHandler(event) {
    if (event.detail.name === "expandTrackDetails") {
      this.openModalInfo();
    }
  }

  openModalInfo() {
    this.showTicketDetails.emit();
    state.expandedTrackTicketTitle = this.label;
    state.expandedTrackTicketSoldUnits = parseInt(this.count);
  }

  render() {
    if (this.layout === "label-count") {
      return (
        <div class={`container container-${this.width}`}>
          <p class="elem-1">{this.label}</p>
          <p class="elem-2">
            <span class="count">{this.count}</span>
          </p>
        </div>
      );
    } else if (this.layout === "label-count-sales") {
      return (
        <div class={`container container-${this.width}`}>
          <p class="elem-1">
            {this.label} <span class="sale-count">x {this.count}</span>
          </p>
          {/* <p class="elem-3">
            <span class="count">x {this.count}</span>
          </p> */}
          <div class="row total-count-row">
            <p class="elem-2">
              <span class="total">{this.total}</span>
            </p>
          </div>
        </div>
      );
    } else if (this.layout === "label-count-expand") {
      return (
        <div>
          <div class={`container container-${this.width} label-count-expand`}>
            <p class="elem-1">{this.label}</p>

            {parseInt(this.count) > 0 ? (
              <c-btn
                name="expandTrackDetails"
                label={`View ${this.count} participants`}
                action-label=""
                type="ghost"
              ></c-btn>
            ) : (
              <span class="sale-count">No participants</span>
            )}
          </div>
          <div class="divider"></div>
        </div>
      );
    }
  }
}
