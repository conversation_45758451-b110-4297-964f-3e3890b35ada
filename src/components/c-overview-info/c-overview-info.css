c-overview-info .link-icon {
  width: 12px;
  height: 12px;
  margin-right: 0.5em;
}

c-overview-info .container {
  width: 260px;
  display: flex;
  justify-content: space-between;
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

c-overview-info .container-300 {
  width: 300px;
}

c-overview-info .container-320 {
  width: 320px;
}

c-overview-info p {
  margin-top: 0;
  margin-bottom: 0;
}

c-overview-info .link-btn {
  border: 0;
  background: none;
  outline: none;
  text-align: left;
  font-size: 1em;
  margin-bottom: 1em;
}

c-overview-info .link-btn:hover {
  cursor: pointer;
  color: var(--accent-color-dark);
  background: var(--accent-color-bg-lightest);
  border-radius: 0.25em;
}

c-overview-info .elem-1 {
  width: 70%;
}

c-overview-info .elem-2 {
  width: 60%;
  text-align: right;
}

c-overview-info .elem-3 {
  width: 20%;
  text-align: left;
}

c-overview-info .total-count-row {
  display: flex;
  justify-content: space-between;
}

c-overview-info .sale-count {
  color: rgba(0, 0, 0, 0.4);
}

c-overview-info .label-count-expand {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  width: 420px;
}

c-overview-info .label-count-expand .elem-1 {
  width: 60%;
}

c-overview-info .divider {
  width: 100%;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}
