import { Component, Prop, h, Listen, State, Watch } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-vnav",
  styleUrl: "c-vnav.css",
})
export class CVnav {
  @Prop() navOptsStr: string;
  @Prop() name: string;
  @Prop() isDisabled: boolean = false;
  @Prop() isConfigMode: boolean = false;
  @Prop() activeOption: string

  @State() activeNavName: string;
  @State() navOpts: any;

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    this.activeNavName = event.detail.cNavItemName;
    this.navOpts.map((obj) => {
      if (obj.state === "active") {
        obj.state = "enabled";
      }
      if (obj.name === this.activeNavName) {
        obj.state = "active";
      }
    });
  }

  @Watch("navOptsStr") navOptsWatcher(newVal: string, oldVal: string) {
    if (newVal != oldVal) {
      this.parseNavOptions();
    }
  }

  @Watch("activeOption") activeOptionWatcher(newVal: string, oldVal: string) {
    if (newVal != oldVal) {
      if (newVal != this.activeNavName) {
        this.activeNavName  = newVal;
        this.navOpts.map((obj) => {
          if (obj.state === "active") {
            obj.state = "enabled";
          }
          if (obj.name === this.activeNavName) {
            obj.state = "active";
          }
        });
      }
    }
  }

  componentWillLoad() {
    this.parseNavOptions();
  }

  parseNavOptions() {
    this.navOpts = JSON.parse(this.navOptsStr);
    this.navOpts = [...this.navOpts];
  }

  render() {
    return (
      <nav>
        {this.navOpts.map((opt) =>
          opt.type === "navItem" ? (
            <c-vnav-item
              navName={this.name}
              name={opt.name}
              label={opt.label}
              state={opt.state}
              icon={opt.icon}
              sub-text={opt.subText}
              route={opt.route}
              isDisabled={this.isDisabled}
              configure={opt.configure}
              navLen={this.navOpts.length}
              isConfigMode={this.isConfigMode}
            ></c-vnav-item>
          ) : opt.isFirstLabel ? (
            <c-text type="navLabel">
              {opt.label === "eventName"
                ? state.eventCodeForMonitoring.toUpperCase()
                : opt.label.toUpperCase()}
            </c-text>
          ) : (
            <div class="nav-label-container" style={{ marginTop: "1em" }}>
              <c-text type="navLabel">
                {" "}
                {opt.label === "eventName"
                  ? state.eventNameForMonitoring.toUpperCase()
                  : opt.label.toUpperCase()}
              </c-text>
            </div>
          )
        )}
      </nav>
    );
  }
}
