import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  Prop,
  State,
  Host,
  h,
  Watch,
} from "@stencil/core";

@Component({
  tag: "c-ticket-price-input-full",
  styleUrl: "c-ticket-price-input-full.css",
})
export class CTicketPriceInputFull {
  @Event({
    eventName: "tierChange",
    bubbles: true,
  })
  tierChangeEvent: EventEmitter;

  @Event({
    eventName: "addPricingTier_fullTicket",
    bubbles: true,
  })
  addPricingTier_fullTicket: EventEmitter;

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "newPricingTier") {
      this.mode = "newTierItem";
    } else if (e.detail.name === "cancelPriceTier") {
      this.mode = "viewTierItem";
    } else if (e.detail.name === "createPriceTier") {
      this.createPriceTier();
    } else if (e.detail.name === "deletePriceTier") {
      this.deletePriceTier();
    } else if (e.detail.name === "editPriceTier") {
      this.editPriceTier();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "tierName") {
      this.newTierName = e.detail.value;
    } else if (e.detail.name === "studentPrice") {
      this.newStudentPrice = e.detail.value;
    } else if (e.detail.name === "proPrice") {
      this.newProfessionalPrice = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "tierStartDate") {
      this.newTierStartDate = e.detail.value;
    } else if (e.detail.name === "tierEndDate") {
      this.newTierEndDate = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("ticketTierClicked") handleTicketTierClick(e) {
    this.tierId = e.detail.tierId;
    this.tierName = e.detail.tierName;
    this.tierStartDate = e.detail.tierStart;
    this.tierEndDate = e.detail.tierEnd;
    this.tierRange = e.detail.tierRange;
    this.studentPrice = e.detail.price_Student;
    this.professionalPrice = e.detail.price_Professional;
    console.log(this.tierId);
    console.log(this.tierRange);
    this.mode = "editTierItem";
  }

  @Prop() pricingTierString: string = "";

  @State()
  mode: string = "viewTierItem";
  @State() isSaveTierDisabled: boolean = true;
  @State() tierData: any = [];
  @State() pricingTiers: any = [];

  @Watch("pricingTierString") pricingTierStringWatcher(
    newVal: string,
    oldVal: string
  ) {
    if (newVal != oldVal) {
      this.pricingTiers = JSON.parse(this.pricingTierString);
      this.pricingTiers = [...this.pricingTiers];
    }
  }

  private tierId: string = "";
  private tierName: string = "";
  private tierStartDate: string = "";
  private tierEndDate: string = "";
  private tierRange: string = "";
  private studentPrice: number = 0;
  private professionalPrice: number = 0;

  private newTierName: string = "";
  private newTierStartDate: string = "";
  private newTierEndDate: string = "";
  private newStudentPrice: number = 0;
  private newProfessionalPrice: number = 0;

  componentWillLoad() {
    if (this.pricingTierString) {
      this.pricingTiers = JSON.parse(this.pricingTierString);
      this.pricingTiers = [...this.pricingTiers];
    }
  }

  createPriceTier() {
    let obj = {
      id: Math.random().toString(),
      name: this.newTierName,
      start: this.newTierStartDate,
      end: this.newTierEndDate,
      range: `${this.newTierStartDate}-${this.newTierEndDate}`,
      price: {
        student: this.newStudentPrice,
        professional: this.newProfessionalPrice,
      },
    };
    this.addPricingTier_fullTicket.emit({
      tierObj: obj,
    });
    this.isSaveTierDisabled = true;
    this.mode = "viewTierItem";
  }

  deletePriceTier() {
    this.tierChangeEvent.emit();
    console.log("delete price tier");
  }

  editPriceTier() {
    this.tierChangeEvent.emit();
    console.log("edit price tier");
  }

  validateInputs() {
    if (this.mode === "newTierItem") {
      this.validateInputs_NewTier();
    } else if (this.mode === "editTierItem") {
      this.validateInputs_EditTier();
    }
  }

  validateInputs_NewTier() {
    if (
      this.newTierName.length > 0 &&
      this.newTierStartDate.length > 0 &&
      this.newTierEndDate.length > 0 &&
      this.newStudentPrice > 0 &&
      this.newProfessionalPrice > 0
    ) {
      this.isSaveTierDisabled = false;
    } else {
      this.isSaveTierDisabled = true;
    }
  }

  validateInputs_EditTier() {
    let hasTierNameChanged: boolean = false;
    let hasTierStartDateChanged: boolean = false;
    let hasTierEndDateChanged: boolean = false;
    let hasStudentPriceChanged: boolean = false;
    let hasProfessionalPriceChanged: boolean = false;

    if (this.newTierName != this.tierName) {
      hasTierNameChanged = true;
    } else {
      hasTierNameChanged = false;
    }

    if (this.newTierStartDate != this.tierStartDate) {
      hasTierStartDateChanged = true;
    } else {
      hasTierStartDateChanged = false;
    }

    if (this.newTierEndDate != this.tierEndDate) {
      hasTierEndDateChanged = true;
    } else {
      hasTierEndDateChanged = false;
    }

    if (this.newStudentPrice != this.studentPrice) {
      hasStudentPriceChanged = true;
    } else {
      hasStudentPriceChanged = false;
    }

    if (this.newProfessionalPrice != this.professionalPrice) {
      hasProfessionalPriceChanged = true;
    } else {
      hasProfessionalPriceChanged = false;
    }

    if (
      hasTierNameChanged ||
      hasTierStartDateChanged ||
      hasTierEndDateChanged ||
      hasStudentPriceChanged ||
      hasProfessionalPriceChanged
    ) {
      this.isSaveTierDisabled = false;
    } else {
      this.isSaveTierDisabled = true;
    }
  }

  ViewTierItem: FunctionalComponent = () =>
    this.pricingTiers.length > 0 ? (
      this.pricingTiers.map((tier: any) => (
        <c-ticket-price-item-full
          tierId={tier.id}
          tierName={tier.name}
          tierStart={tier.start}
          tierEnd={tier.end}
          tierRange={tier.range}
          price_Student={tier.price.student}
          price_Professional={tier.price.professional}
        ></c-ticket-price-item-full>
      ))
    ) : (
      <div class="no-tier-container">
        <c-text>Found 0 pricing tiers</c-text>
      </div>
    );

  InputTierItem: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TIER NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="tierName"
          placeholder="e.g. Early, Late, Walk-in"
          isDisabled={false}
          value={this.mode === "editTierItem" ? this.tierName : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TIER START & END DATES
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="tierStartDate"
            pickTime={false}
            date={this.tierStartDate}
          ></c-date-picker>
          <c-date-picker
            name="tierEndDate"
            pickTime={false}
            date={this.tierEndDate}
          ></c-date-picker>
        </div>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          TIER PRICE
        </c-text>
        <div class="input-price-container">
          <div class="input-price-container__price">
            <c-text>₹</c-text>
            <c-textbox
              input-type="text"
              name="studentPrice"
              placeholder="Student price"
              isDisabled={false}
              value={
                this.mode === "editTierItem" ? this.studentPrice.toString() : ""
              }
            ></c-textbox>
          </div>
          &nbsp;&nbsp;
          <div class="input-price-container__price">
            <c-text>₹</c-text>
            <c-textbox
              input-type="text"
              name="proPrice"
              placeholder="Professional price"
              isDisabled={false}
              value={
                this.mode === "editTierItem"
                  ? this.professionalPrice.toString()
                  : ""
              }
            ></c-textbox>
          </div>
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelPriceTier">
            Cancel
          </c-button>
          <div class="button-group">
            {this.mode === "editTierItem" && (
              <c-button type="ghost_Danger_Small" name="deletePriceTier">
                Delete
              </c-button>
            )}
            &nbsp;&nbsp;&nbsp;&nbsp;
            <c-button
              type="ghost_Small"
              name={
                this.mode === "newTierItem"
                  ? "createPriceTier"
                  : "editPriceTier"
              }
              isDisabled={this.isSaveTierDisabled}
            >
              {this.mode === "newTierItem" && "Add"}
              {this.mode === "editTierItem" && "Save"}
            </c-button>
          </div>
        </c-row>
      </div>
    </div>
  );

  CreateTier: FunctionalComponent = () => (
    <div class="ticket-price-table__footer">
      <c-button
        name="newPricingTier"
        type="newPricingTier"
        isDisabled={false}
        isInAction={false}
      ></c-button>
    </div>
  );

  render() {
    return (
      <Host>
        <div class="ticket-price__top-row">
          {/* <c-text type="modalLabel" isMandatory={true}>
            PRICING TIERS
          </c-text> */}
          {/* <c-button
            name="newPricingTier"
            type="newPricingTier"
            isDisabled={false}
            isInAction={false}
          ></c-button> */}
        </div>
        <div class="ticket-price-table__header">
          <div class="ticket-price-table__header--item ticket-price-table__header--item--1">
            <c-text>Tier name</c-text>
          </div>
          <div class="ticket-price-table__header--item ticket-price-table__header--item--2">
            <c-text>Tier start & end</c-text>
          </div>
          <div class="ticket-price-table__header--item ticket-price-table__header--item--3">
            <c-text>Tier prices</c-text>
          </div>
        </div>
        <div class="ticket-price-table__body">
          {this.mode === "viewTierItem" ? (
            <this.ViewTierItem></this.ViewTierItem>
          ) : (
            <this.InputTierItem></this.InputTierItem>
          )}
          {this.mode === "viewTierItem" && <this.CreateTier></this.CreateTier>}
        </div>
      </Host>
    );
  }
}
