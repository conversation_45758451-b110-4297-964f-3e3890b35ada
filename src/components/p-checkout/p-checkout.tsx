import { Component, Listen, Prop, h } from "@stencil/core";
import { RouterHistory, MatchResults, injectHistory } from "@stencil/router";
import axios from "axios";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "p-checkout",
  styleUrl: "p-checkout.css",
})
export class PCheckout {
  /*----
  Props
  ----*/
  @Prop() history: RouterHistory;
  @Prop() match: MatchResults;

  private isRazorpayScriptLoaded: boolean = false;

  @Listen("empty-purchase-list")
  deletePurchasedItemHandler(event) {
    event.preventDefault();
    if (state.cartItems.length === 0) {
      state.isPrimaryTicketInCart = false;
      state.isPartialTicketInCart = false;
      state.isCouponApplied = false;
      state.appliedCouponName = "";
      state.appliedCouponDeductionType = "";
      state.appliedCouponDeductionValue = 0;
      state.isCouponInputEnabled = false;
      state.isFreePurchase = false;
      state.isCouponsAvailable = false;
      state.paymentGateway = "razorpay";
      this.clearCoupon();
    }
  }

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    if (event.detail.cNavItemName === "back") {
      this.history.goBack();
    }
  }

  clearCoupon() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/clearcoupon`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
        } else if (response.data.status === "Success") {
          this.history.push(
            `/registration/${state.eventCodeForRegistration}`,
            {}
          );
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  @Listen("pay-btn-click-event")
  payBtnClickHandler(event) {
    event.preventDefault();
    this.paymentHandler();
  }

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    if (this.match.params.eventCodeForRegistration) {
      state.eventCodeForRegistration =
        this.match.params.eventCodeForRegistration.trim();
    }
  }

  componentDidLoad() {
    if (!state.cartTotal) {
      this.history.goBack();
    }
  }

  paymentHandler() {
    if (state.isFreePurchase) {
      let payload = {
        isFreePurchase: true,
        eventCode: state.eventCodeForRegistration,
      };
      this.history.push("/free-purchase-thanks", payload);
    } else {
      if (state.paymentGateway === "bank") {
        let payload = {
          paymentGateway: "BankTransfer",
          eventCode: state.eventCodeForRegistration,
          bankTxCode: Store.getBankTxCode(),
        };
        this.history.push(
          `/bank-thanks/${state.eventCodeForRegistration}`,
          payload
        );
      } else if (state.paymentGateway === "razorpay") {
        this.displayRazorpay();
      }
    }
  }

  loadScript(src) {
    return new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }

  async displayRazorpay() {
    if (!this.isRazorpayScriptLoaded) {
      const res = await this.loadScript(
        "https://checkout.razorpay.com/v1/checkout.js"
      );
      if (!res) {
        alert("Razorpay SDK failed to load");
        return;
      }
    }

    let payload = {
      paymentGateway: "Razorpay",
      eventCode: state.eventCodeForRegistration,
    };

    let data: any;

    await axios({
      method: "POST",
      baseURL: `${state.baseUrl}/pay-with-razorpay-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          data = response.data.payload;
        }
      })
      .catch((error) => {
        alert(error);
      });

    // Test
    // let key: string = "rzp_test_L6Nh6GCncfTSDN";
    // Prod
    let key: string = "***********************";

    const options = {
      key: key,
      currency: data.currency,
      amount: data.amount.toString(),
      order_id: data.id,
      name: data.org,
      description: "",
      image:
        "https://res.cloudinary.com/layerpark/image/upload/v1701137316/events/india-hci-2024-2b28ee8b-054d-4bbf-9364-aba716edc553/Square.png",
      handler: (response) => {
        let obj = {
          paymentGateway: "Razorpay",
          paymentID: response.razorpay_payment_id,
          orderID: response.razorpay_order_id,
          signature: response.razorpay_signature,
        };
        if (obj.paymentID && obj.orderID && obj.signature) {
          this.history.push("/razorpay-thanks", obj);
        } else {
          alert("Payment Failed: Please try again!");
        }
      },
      prefill: {
        name: data.name,
        email: data.email,
        phone_number: data.mobile,
      },
      theme: {
        color: "#593196",
      },
      modal: {
        ondismiss: () => {
          state.isPaying = false;
          state.isPaymentBtnDisabled = false;
        },
      },
    };
    const _window = window as any;
    const paymentObject = new _window.Razorpay(options);
    paymentObject.open();
  }

  private navOpts = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
  ];

  render() {
    return (
      <c-page>
        <c-topbar></c-topbar>
        <c-sidebar type="left">
          <c-vnav
            nav-opts-str={JSON.stringify(this.navOpts)}
            isConfigMode={false}
          ></c-vnav>
        </c-sidebar>

        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}

        <main>
          <c-purchase-list></c-purchase-list>
          <c-checkout-options></c-checkout-options>
        </main>
      </c-page>
    );
  }
}

injectHistory(PCheckout);
