import {
  Component,
  Prop,
  h,
  State,
  Listen,
  FunctionalComponent,
} from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import state from "../../global/state";
import {
  getLinkedInProfile,
  linkedInSignup<PERSON>elper,
  linkAccountWithLinkedInHelper,
} from "../../global/helpers";
import {
  signupWithOauthPayloadGenerator,
  linkAccountWithOauthPayloadGenerator,
} from "../../global/generators";

interface ErrorProp {
  title: string;
  description: string;
}

interface LoaderProp {
  message: string;
}

@Component({
  tag: "p-post-linkedin-oauth",
  styleUrl: "p-post-linkedin-oauth.css",
})
export class PPostLinkedinOauth {
  @Prop() history: RouterHistory;
  @State() compState: string = "init";
  @State() linkAccountState: string = "init";

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "occupation") {
      state.occupation = event.detail.value;
    }
  }

  @Listen("buttonClick")
  async buttonClickHandler(event) {
    if (event.detail.name === "createAccountFromLinkedIn") {
      let linkedInSignupPayload = signupWithOauthPayloadGenerator();
      let { isUserSignedUp, signupMessage } = await linkedInSignupHelper(
        linkedInSignupPayload
      );
      if (!isUserSignedUp) {
        state.isSignupError = true;
        state.notificationType = "error";
        state.notificationMessage = signupMessage;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      if (state.eventCode.length > 0) {
        state.eventCodeForRegistration = state.eventCode;
        this.history.push(
          `/registration/${state.eventCodeForRegistration}`,
          {}
        );
      } else {
        this.history.push("/events", {});
      }
    } else if (event.detail.name === "linkAccountWithLinkedin") {
      let linkAccountWithLinkedInPayload =
        linkAccountWithOauthPayloadGenerator();
      let { isAccountLinked, message } = await linkAccountWithLinkedInHelper(
        linkAccountWithLinkedInPayload
      );
      if (!isAccountLinked) {
        state.isSignupError = true;
        state.notificationType = "error";
        state.notificationMessage = message;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      this.linkAccountState = "confirm";
    } else if (event.detail.name === "confirmAccountLinkingWithLinkedIn") {
      if (state.eventCode.length > 0) {
        state.eventCodeForRegistration = state.eventCode;
        this.history.push(
          `/registration/${state.eventCodeForRegistration}`,
          {}
        );
      } else {
        this.history.push("/events", {});
      }
    }
  }

  private authCode: string = "";
  private requestState: string = "";
  private authError: string = "";
  private authErrorMessage: string = "";

  Error: FunctionalComponent<ErrorProp> = ({ title, description }) => (
    <div class="error-container">
      <c-text type="errorHeading">{title}</c-text>
      <c-text>{description}</c-text>
      <br />
      <c-oauth></c-oauth>
      <div class="signin-container">
        <c-link url="/">Sign in with email and password</c-link>
      </div>
    </div>
  );

  Loader: FunctionalComponent<LoaderProp> = ({ message }) => (
    <div class="loader-container">
      <div class="loader-content">
        <c-spinner-dark></c-spinner-dark>
        <c-text>{message}</c-text>
      </div>
    </div>
  );

  NewAccount: FunctionalComponent = () => (
    <div>
      <c-row type="oauthProfileDetails">
        {/* <c-img type="oauthDp" src={state.dpUrl}></c-img> */}
        <div>
          <c-text>
            {state.firstName} {state.lastName}
          </c-text>
          <c-link url={`mailto:${state.email}`}>{state.email}</c-link>
        </div>
      </c-row>
      <div class="occupation-container">
        <c-text>Your occupation?</c-text>
        <c-row>
          <c-radio
            name="occupation"
            label1="Student"
            label2=""
            label3=""
            val="student"
            isChecked={false}
          ></c-radio>
          <c-radio
            name="occupation"
            label1="Professional"
            label2=""
            label3=""
            val="professional"
            isChecked={true}
          ></c-radio>
        </c-row>
      </div>
      <c-button
        type="solidWithIcon"
        name="createAccountFromLinkedIn"
        icon-name="log-in-outline"
        is-in-action={state.isLinkedinCreateAccountInAction}
        label="Create account"
      ></c-button>
    </div>
  );

  ConnectAccount: FunctionalComponent = () => (
    <div>
      {this.linkAccountState === "init" && (
        <this.LinkAccountInit></this.LinkAccountInit>
      )}
      {this.linkAccountState === "confirm" && (
        <this.LinkAccountConfirm></this.LinkAccountConfirm>
      )}
    </div>
  );

  LinkAccountInit: FunctionalComponent = () => (
    <div>
      <c-text type="cardHeading">Account already exists</c-text>
      <c-text>
        There is already an account associated with this email{" "}
        {/* <c-link url={`mailto:${state.email}`}>{state.email}</c-link> */}
      </c-text>
      <div class="existing-account-container">
        <c-text>
          {state.firstName} {state.lastName}
        </c-text>
        <c-link url={`mailto:${state.email}`}>{state.email}</c-link>
        <div class="auth-mechanism-container">
          <c-text>
            <ion-icon name="lock-open-outline"></ion-icon> &nbsp;Uses Email &
            Password for login
          </c-text>
        </div>
      </div>
      {/* <c-text>
        Kindly link your LinkedIn account with this existing account
      </c-text> */}
      <c-button
        type="solidWithIcon"
        name="linkAccountWithLinkedin"
        icon-name="link-outline"
        is-in-action={state.isLinkAccountWithLinkedInInAction}
        label="Link accounts"
      ></c-button>
      <c-divider type="single"></c-divider>
      <c-text type="cardSubSectionHeading">Benefits of linking</c-text>
      <div class="existing-account-container">
        <ul class="connection-benefits-list">
          <li>Prevents duplicate account</li>
          <li>Login using both LinkedIn & email + password</li>
        </ul>
      </div>
    </div>
  );

  LinkAccountConfirm: FunctionalComponent = () => (
    <div>
      <c-text type="cardHeading">Account linking successful</c-text>
      <c-text>We have linked your existing account with LinkedIn</c-text>
      {/* <div class="linked-account-container">
        <c-row type="oauthProfileDetails">
          <c-img type="oauthDp" src={state.dpUrl}></c-img>
          <div>
            <c-text>
              {state.firstName} {state.lastName}
            </c-text>
            <c-link url={`mailto:${state.email}`}>{state.email}</c-link>
          </div>
        </c-row>
      </div> */}
      <c-button
        type="solidWithIcon"
        name="confirmAccountLinkingWithLinkedIn"
        icon-name="arrow-forward-outline"
        is-in-action={state.isLinkAccountWithLinkedInInAction}
        label="Proceed to account"
      ></c-button>
    </div>
  );

  componentWillLoad() {
    state.occupation = "professional";
    this.authCode = this.history.location.query.code;
    this.requestState = this.history.location.query.state;
    this.authError = this.history.location.query.error;
    this.authErrorMessage = this.history.location.query.error_description;

    console.log(this.requestState);
    console.log(this.authErrorMessage);

    if (this.authError) {
      this.compState = "authorisationError";
    }

    // state.firstName = "Tuhin";
    // state.lastName = "Bhuyan";
    // state.email = "<EMAIL>";
    // state.dpUrl =
    //   "https://media-exp1.licdn.com/dms/image/C5103AQFkYeQbg8KWZQ/profile-displayphoto-shrink_400_400/0/*************?e=**********&v=beta&t=HoLV0IndPXB-RrVR7-FZW2qMOiw1PBEkFnEhxkHQkJ8";
    // this.compState = "connectAccounts";
    // this.linkAccountState = "confirm";

    this.fetchData();
  }

  async fetchData() {
    let {
      isLinkedInProfileFetched,
      firstName,
      lastName,
      email,
      dpUrl,
      isUserExists,
      isLinkedInConnected,
      remarks,
    } = await getLinkedInProfile(this.authCode);

    console.log(remarks);

    if (!isLinkedInProfileFetched) {
      this.compState = "error";
      return;
    }

    state.firstName = firstName;
    state.lastName = lastName;
    state.email = email.toLowerCase();
    state.dpUrl = dpUrl;
    state.isEmailVerified = true;
    // state.isAccountSetup = false;

    if (isUserExists) {
      if (isLinkedInConnected === true) {
        this.history.push("/events", {});
      } else {
        this.compState = "connectAccounts";
      }
    } else {
      this.compState = "createNewAccount";
    }
  }

  render() {
    return (
      <c-page type="postOauth">
        <c-card>
          {this.compState === "init" && (
            <this.Loader message="Fetching profile information.."></this.Loader>
          )}

          {this.compState === "authorisationError" && (
            <this.Error
              title="Please try again"
              description="We did not get any authorization to fetch your LinkedIn profile"
            ></this.Error>
          )}

          {this.compState === "error" && (
            <this.Error
              title="Please try again"
              description="There was an error while retrieving your profile information"
            ></this.Error>
          )}

          {this.compState === "connectAccounts" && (
            <this.ConnectAccount></this.ConnectAccount>
          )}

          {this.compState === "createNewAccount" && (
            <this.NewAccount></this.NewAccount>
          )}
        </c-card>
        {/* <c-notification isActive={state.isNotificationActive}></c-notification> */}
      </c-page>
    );
  }
}

injectHistory(PPostLinkedinOauth);
