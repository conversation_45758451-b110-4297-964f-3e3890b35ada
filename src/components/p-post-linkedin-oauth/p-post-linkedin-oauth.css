p-post-linkedin-oauth .loader-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 50vh;
}

p-post-linkedin-oauth .loader-content {
  text-align: center;
}

p-post-linkedin-oauth .error-container c-divider .oauth {
  margin-top: 1.5em;
  margin-bottom: 1em;
}

p-post-linkedin-oauth .signin-container {
  text-align: center;
}

p-post-linkedin-oauth .occupation-container {
  padding: var(--site-padding);
  border: var(--site-border);
  border-radius: var(--site-border-radius);
  margin-bottom: 1em;
  margin-top: 1em;
}

p-post-linkedin-oauth .occupation-container c-text p {
  margin-bottom: 0.5em;
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.5);
}

p-post-linkedin-oauth c-radio .radio-btn-container {
  margin: 0;
}

p-post-linkedin-oauth .existing-account-container {
  padding: var(--site-padding);
  border: var(--site-border);
  border-radius: var(--site-border-radius);
  margin-top: 1em;
  margin-bottom: 1em;
}
p-post-linkedin-oauth .existing-account-container:last-child {
  margin-bottom: 0;
  margin-top: 0.25em;
}

p-post-linkedin-oauth .auth-mechanism-container {
  padding: var(--site-padding);
  border-radius: var(--site-border-radius);
  background: rgba(0, 0, 0, 0.05);
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

p-post-linkedin-oauth .auth-mechanism-container c-text p {
  font-size: 0.8em;
}

p-post-linkedin-oauth .connection-benefits-list {
  display: block;
  margin: 0;
  padding: 0;
  font-size: 0.8em;
}

p-post-linkedin-oauth .connection-benefits-list > li {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

p-post-linkedin-oauth .connection-benefits-list > li:first-child {
  border-bottom: none;
  padding-bottom: 0.5em;
  border-bottom: var(--site-border);
}

p-post-linkedin-oauth .connection-benefits-list > li:last-child {
  border-bottom: none;
  padding-top: 0.5em;
}

p-post-linkedin-oauth c-button .default-button {
  margin-top: 0.75em;
}

p-post-linkedin-oauth .linked-account-container {
  padding: 0.75em 0 0.5em 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin: 1em 0;
}
