p-purchases .purchased-items-container {
  margin: 0 auto;
  width: 580px;
  margin: 5em 0 0 22.5%;
}

p-purchases .section-title {
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin-bottom: 0.5em;
  font-size: 1.5em;
}

p-purchases .registration-closed-container {
  background: linear-gradient(
    135deg,
    rgba(89, 49, 150, 1) 0%,
    rgba(106, 70, 161, 1) 35%,
    rgba(122, 90, 171, 1) 100%
  );
  padding: 1.25em 1.5em 1em 1.5em;
  border-radius: 0.4em;
  color: var(--accent-color-bg-lightest);
  margin-bottom: 4em;
}

p-purchases .registration-closure-notice {
  margin-top: 2em;
}

p-purchases c-page .default-page {
  margin-top: 6em;
}

@media only screen and (max-width: 768px) {
  p-purchases .hide-on-mobile {
    display: none;
  }

  p-purchases .purchased-items-container {
    width: 100%;
    margin: 1em 0 0 0;
  }

  p-purchases c-page .default-page {
    margin-top: 1em;
  }
}
