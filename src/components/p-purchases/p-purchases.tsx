import { Component, Prop, State, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "p-purchases",
  styleUrl: "p-purchases.css",
})
export class PPurchases {
  @Prop() history: RouterHistory;
  @State() isFetchingData: boolean = true;

  componentWillLoad() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/events`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Failed to fetch data");
        } else if (response.data.status === "Success") {
          Store.setFirstName(response.data.payload.profile.name.first);
          Store.setLastName(response.data.payload.profile.name.last);
          Store.setOccupation(response.data.payload.professional.occupation);
          Store.setIsMember(response.data.payload.membership.isMember);
          Store.setCurrency(response.data.payload.settings.currency.name);
          Store.setCurrencySymbol(
            response.data.payload.settings.currency.symbol
          );
          // this.cartLength = response.data.payload.cart.length;
          state.firstName = response.data.payload.profile.name.first;
          state.lastName = response.data.payload.profile.name.last;
          state.isRegistrationManager =
            response.data.payload.settings.isRegistrationManager;
          state.isAdmin = response.data.payload.settings.isAdmin;
          this.isFetchingData = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  componenWillLoad() {
    state.isMobileMenuOpen = false;
  }

  private navOpts = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "profile",
      label: "Profile",
      state: "",
      icon: "person-outline",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "purchases",
      label: "Purchases",
      state: "active",
      icon: "cart-outline",
      subText: "",
      route: "",
    },
  ];

  render() {
    return (
      <c-page>
        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}
        <c-sidebar type="left">
          <c-vnav nav-opts-str={JSON.stringify(this.navOpts)}></c-vnav>
        </c-sidebar>
        <c-topbar></c-topbar>
        <div class="purchased-items-container">
          <c-paid-items-list own-purchases={true}></c-paid-items-list>
        </div>
      </c-page>
    );
  }
}

injectHistory(PPurchases);
