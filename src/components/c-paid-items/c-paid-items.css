c-paid-items .container {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  margin-bottom: 2em;
}

c-paid-items header {
  border-radius: 0.25em 0.25em 0em 0em;
  padding: 1em;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0, 0.025);
}

c-paid-items .row__item--underlined {
  padding: 0em 1em;
}

c-paid-items .label-value {
  opacity: 0.8;
}

c-paid-items .label-value-bubble {
  padding: 0.15em 0.5em;
  font-size: 0.7em;
  border-radius: 0.25em;
}

c-paid-items .paid-item-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

c-paid-items .item-price {
  text-align: left;
  width: 50px;
}

c-paid-items .paid-item {
  padding: 0 1em;
}

c-paid-items .paid-item p {
  margin: 0;
  padding: 0;
  margin-bottom: 1em;
}

c-paid-items .paid-item p:last-child {
  margin: 0;
  padding: 0;
  margin-bottom: 0em;
}

c-paid-items .paid-item .sub-title {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.9em;
}

c-paid-items main {
  border-radius: 0.25em;
  padding: 1em 0;
}

c-paid-items .header-row {
  display: flex;
  justify-content: space-between;
}

c-paid-items .button-container {
  display: flex;
  justify-content: space-around;
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 0.5em;
  box-sizing: border-box;
}

c-paid-items .button-container button {
  cursor: pointer;
  background: none;
  border: 0;
  font-size: 0.8em;
  outline: none;
  border-radius: 100%;
}

c-paid-items .btn-show-details {
  color: var(--accent-color);
  transition: all 0.15s ease-in;
}

c-paid-items .btn-show-details:hover {
  transition: all 0.15s ease-in;
}

c-paid-items .btn-hide-details {
  color: rgba(231, 76, 60, 1);
  transition: all 0.15s ease-in;
}

c-paid-items .btn-hide-details:hover {
  transition: all 0.15s ease-in;
}

c-paid-items .seperator {
  width: 100%;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.15);
  margin-bottom: 1em;
}

c-paid-items .payment-summary-value {
  padding-bottom: 1em;
  color: #333;
}

c-paid-items .show {
  display: block;
}

c-paid-items .hide {
  display: none;
}

c-paid-items .header-skel {
  padding: 1.5em 0;
}

c-paid-items .main-skel {
  padding-top: 1em;
  padding-bottom: 3em;
}

c-paid-items .purchased {
  background: var(--accent-green-darker);
  color: white;
}

c-paid-items .under-verification {
  background: #ff9800;
  color: white;
}

c-paid-items .white-label {
  font-size: 0.8em;
  font-weight: 400;
  opacity: 0.7;
}

c-paid-items .grey-label {
  color: rgba(0, 0, 0, 0.4);
  font-size: 0.75em;
  font-weight: 700;
}

c-paid-items .hseperator {
  margin-top: 1em;
  margin-bottom: 1em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

c-paid-items .hseperator-no-bottom-margin {
  margin-bottom: 0;
}

c-paid-items .hseperator-no-top-margin {
  margin-top: 0;
}

c-paid-items .red {
  color: red;
}

c-paid-items .discount-statement {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

c-paid-items .discount-applied-bubble {
  margin: 0;
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.15em 0.5em;
  border-radius: 0.25em;
  /* background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker); */
  color: white;
  background: var(--accent-blue-darker);
}

c-paid-items .paid-item-date {
  font-size: 0.9em;
  margin: 0;
  padding-bottom: 1em;
  color: rgba(0, 0, 0, 0.6);
}

@media only screen and (max-width: 768px) {
  c-paid-items .header-row {
    display: block;
  }

  c-paid-items .paid-item p:last-child {
    margin-top: 1em;
  }
}
