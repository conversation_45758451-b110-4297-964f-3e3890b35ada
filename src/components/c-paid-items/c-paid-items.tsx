import { Component, State, h, Prop } from "@stencil/core";

@Component({
  tag: "c-paid-items",
  styleUrl: "c-paid-items.css",
})
export class CPaidItems {
  @State() isShowingDetails: boolean = false;

  @Prop() currency: string;
  @Prop() purchaseDate: string;
  private purchasedDateAdjusted: string;
  @Prop() purchaseStatus: string;

  @Prop() purchasedItems: string;
  private purchasedItemsArr = [];

  @Prop() isCouponApplied: boolean = false;
  @Prop() couponName: string = "";
  @Prop() couponDeductionType: string = "";
  @Prop() couponDeductionValue: number = 0;
  @Prop() cartAmountBeforeDeduction: number = 0;
  @Prop() deductedAmount: number = 0;
  @Prop() cartAmountAfterDeduction: number = 0;

  @Prop() cartTotal: string;
  @Prop() gatewayFee: string;
  @Prop() grandTotal: string;

  @Prop() transactionId: string;
  @Prop() paymentMethod: string;
  @Prop() purchaseId: string;

  private discountStatement: string = "";

  componentWillLoad() {
    this.purchasedItemsArr = JSON.parse(this.purchasedItems);
    let buffDate = new Date(this.purchaseDate);
    this.purchasedDateAdjusted = buffDate.toString().substring(4, 15);

    if (this.couponDeductionType === "fixed") {
      this.discountStatement = `${this.currency}${this.couponDeductionValue} off on above cart total`;
    } else if (this.couponDeductionType === "percentage") {
      this.discountStatement = `${this.couponDeductionValue}% of above cart total`;
    }
  }

  handleBtnClick(event) {
    event.preventDefault();
    if (this.isShowingDetails === true) {
      this.isShowingDetails = false;
    } else {
      this.isShowingDetails = true;
    }
  }
  render() {
    return (
      <div class="container">
        <main>
          <p class="paid-item paid-item-date">{this.purchasedDateAdjusted}</p>

          {this.purchasedItemsArr.map((item: any) => (
            <div class="paid-item">
              <div class="header-row">
                <p>
                  <span class="main-title">{item.title}</span>
                  <br />
                  <span class="sub-title">{item.tier.toUpperCase()} </span>
                </p>
                <p class="row__item--underlined">
                  <span class="item-price">
                    {this.currency}
                    {item.price}
                  </span>
                  <br />
                  <span
                    class={
                      this.purchaseStatus === "purchased"
                        ? "label-value-bubble purchased"
                        : "label-value-bubble under-verification"
                    }
                  >
                    <strong>
                      {this.purchaseStatus === "under-verification"
                        ? "Verifying"
                        : ""}
                      {this.purchaseStatus === "purchased" ? "Paid" : ""}
                    </strong>
                  </span>
                  <br />
                  {this.isCouponApplied && (
                    <span class="discount-applied-bubble">Discounted</span>
                  )}
                </p>
              </div>
            </div>
          ))}
        </main>
        <div class="button-container">
          <button
            class={
              this.isShowingDetails ? "btn-hide-details" : "btn-show-details"
            }
            onClick={(event) => this.handleBtnClick(event)}
          >
            {this.isShowingDetails ? "Less" : "More"}
          </button>
        </div>
        <footer class={this.isShowingDetails ? "show" : "hide"}>
          <div class="hseperator hseperator-no-top-margin"></div>
          <p class="row__item--underlined">
            <label class="white-label">
              CART TOTAL {this.isCouponApplied && "BEFORE DISCOUNT"}
            </label>{" "}
            <br />{" "}
            <span class="label-value">
              {this.currency}
              {this.isCouponApplied
                ? this.cartTotal
                : this.cartAmountBeforeDeduction}
            </span>
          </p>
          {this.isCouponApplied && (
            <p class="row__item--underlined">
              <label class="white-label">
                DISCOUNT
                {this.discountStatement && `(${this.discountStatement})`}-{" "}
                {this.couponName}
              </label>{" "}
              <br />
              <span class="red">
                -
                <span class="item-price">
                  {this.currency}
                  {this.deductedAmount}
                </span>
              </span>
            </p>
          )}
          {this.isCouponApplied && (
            <p class="row__item--underlined">
              <label class="white-label">CART TOTAL AFTER DISCOUNT</label>{" "}
              <br />{" "}
              <span class="label-value">
                {this.currency}
                {this.cartAmountAfterDeduction}
              </span>
            </p>
          )}
          <p class="row__item--underlined">
            <label class="white-label">GATEWAY FEE</label> <br />{" "}
            <span class="label-value">
              {this.currency}
              {this.gatewayFee}
            </span>
          </p>
          <p class="row__item--underlined">
            <label class="white-label">GRAND TOTAL</label> <br />{" "}
            <span class="label-value">
              {this.currency}
              {this.grandTotal}
            </span>
          </p>

          <div class="hseperator"></div>
          <p class="row__item--underlined">
            <label class="white-label">PAYMENT METHOD</label> <br />{" "}
            <span class="label-value">{this.paymentMethod}</span>
          </p>
          <p class="row__item--underlined">
            <label class="white-label">TRANSACTION ID</label> <br />{" "}
            <span class="label-value">{this.transactionId}</span>
          </p>
          <p class="row__item--underlined">
            <label class="white-label">ORDER ID</label> <br />{" "}
            <span class="label-value">{this.purchaseId}</span>
          </p>
        </footer>
      </div>
    );
  }
}
