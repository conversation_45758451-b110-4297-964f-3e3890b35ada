import { Component, Prop, State, h, Host, Watch } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-ticket-track-ticket",
  styleUrl: "c-ticket-track-ticket.css",
  shadow: true,
})
export class CTicketTrackTicket {
  /*---
  Props
  ----*/
  @Prop() ticketId: string;
  @Prop() type: string;
  @Prop() persona: string;
  @Prop() sessionString: string;
  @Prop() isVisible: boolean;
  @Prop() isPrimary: boolean;
  @Prop() isPrimaryDependent: boolean;
  @Prop() isDisabled: boolean = false;

  @State() sessions: any;

  @Watch("sessionString") sessionStringWatcher(
    newVal: boolean,
    oldVal: boolean
  ) {
    if (newVal != oldVal) {
      this.parseSessions();
    }
  }

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    this.parseSessions();
  }

  parseSessions() {
    this.sessions = JSON.parse(this.sessionString);
    this.sessions = [...this.sessions];
  }

  checkIfSessionDisabled() {
    return false;
  }

  render() {
    return (
      <Host>
        {this.isDisabled ? (
          <c-banner>
            <c-text>
              To buy workshop or course pass, kindly{" "}
              <u>purchase a full conference ticket or conference day pass</u>
            </c-text>
          </c-banner>
        ) : (
          <c-banner theme="danger">
            {state.eventCodeForRegistration ===
            "visual-discourse-2024-b5dbe345-8dd9-4ec3-bdb9-4de7388f7dee" ? (
              <div>
                <span>
                  <strong>Please note:</strong>
                </span>
                <ul style={{ padding: "0em 1em", margin: "0em" }}>
                  <li>You can attend only 1 workshop</li>
                  <li>
                    People with a Full Conference Ticket or One day pass (6th
                    Nov) will get a free workshop ticket
                  </li>
                </ul>
                <br />
                <span>
                  <strong>How to get free workshop ticket?</strong>
                </span>
                <ul style={{ padding: "0em 1em", margin: "0em" }}>
                  <li>
                    Purchase one of the above mentioned tickets first. A coupon
                    will be added to your account
                  </li>
                  <li>Add your desired workshop to cart and go to checkout</li>
                  <li>
                    You will see the coupon on checkout page. Redeem the coupon
                    and get your workshop ticket for free
                  </li>
                </ul>
              </div>
            ) : (
              <p>
                Kindly refer to the{" "}
                <a
                  href="https://www.2024.indiahci.org/programme/schedule"
                  target="_blank"
                >
                  conference schedule
                </a>{" "}
                before purchasing workshops and courses
              </p>
            )}
          </c-banner>
        )}

        <div class={`sessions-container ${this.isDisabled && "disabled"}`}>
          {this.sessions.map((session) => (
            // <div>
            //   <e-text>{session.title}</e-text>
            //   <br />
            // </div>
            <c-ticket-track-session
              ticketId={this.ticketId}
              sessionId={session.id}
              ticketTitle={session.title}
              type={session.type}
              quantity={session.quantity}
              startsOn={session.startsOn}
              endsOn={session.endsOn}
              studentPrice={session.studentPrice}
              professionalPrice={session.professionalPrice}
              url={session.url}
              instructors={session.instructors}
              isVisible={session.isVisible}
              isDisabled={session.isDisabled}
              isSoldOut={session.isSoldOut}
              persona={this.persona}
              isTicketInCart={session.isTicketInCart}
              isTicketPurchased={session.isTicketPurchased}
              purchaseStatus={session.purchaseStatus}
            ></c-ticket-track-session>
          ))}
        </div>
      </Host>
    );
  }
}
