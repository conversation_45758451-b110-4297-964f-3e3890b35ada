import { newSpecPage } from '@stencil/core/testing';
import { CTicketTrackTicket } from '../c-ticket-track-ticket';

describe('c-ticket-track-ticket', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CTicketTrackTicket],
      html: `<c-ticket-track-ticket></c-ticket-track-ticket>`,
    });
    expect(page.root).toEqualHtml(`
      <c-ticket-track-ticket>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-ticket-track-ticket>
    `);
  });
});
