c-wizard-account-setup .wizard-container {
  position: fixed;
  width: 30%;
  left: 0;
  right: 0;
  margin: 0 auto;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  padding: 1em 1.5em 1.5em 1.5em;
  z-index: 999999;
}

c-wizard-account-setup .wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-account-setup .wizard-content {
  box-sizing: border-box;
  background: white;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
  padding: 1em;
  margin-top: 1em;
}

c-wizard-account-setup .wizard-content .field-container:last-child {
  margin-bottom: 0;
}

c-wizard-account-setup .wizard-content .field-container:last-child {
  margin-bottom: 0;
}

c-wizard-account-setup .field-container {
  margin-bottom: 1.5em;
}

c-wizard-account-setup .field-container .modal-label {
  margin-bottom: 0.5em;
}

c-wizard-account-setup .field-container-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-account-setup .field-container-row .mdash {
  color: rgba(0, 0, 0, 0.1);
}

c-wizard-account-setup c-radio .radio-btn-container {
  margin-bottom: 0;
}

c-wizard-account-setup .wizard-sub-heading {
  margin-top: 0.5em;
}

c-wizard-account-setup .gst-details-container {
  margin-bottom: 1.5em;
}

c-wizard-account-setup .gst-details-container .modal-label {
  padding-top: 0.5em;
}

c-wizard-account-setup c-dropdown select {
  width: 100%;
}

c-wizard-account-setup .billing-address-input-group c-textbox input {
  margin-bottom: 1em;
}

c-wizard-account-setup .date-picker-container {
  margin-top: 0.5em;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-wizard-account-setup .wizard-container {
    width: 90%;
    max-width: 300px;
    margin-top: 1em;
    padding: 1em;
  }
}
