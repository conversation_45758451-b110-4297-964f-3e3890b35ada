import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  State,
  h,
} from "@stencil/core";
import state from "../../global/state";
import { countries } from "../../global/app";
import axios from "axios";

@Component({
  tag: "c-wizard-account-setup",
  styleUrl: "c-wizard-account-setup.css",
})
export class CWizardAccountSetup {
  private orgInsti: string = "";
  private jobDegree: string = "";
  private nativeCountry: string = "";
  private mobileIsdCode: string = "91";
  private mobileCountry: string = "India";
  private mobileNumber: string = "";
  private gstPreference: string = "no";
  private businessName: string = "";
  private businessTaxId: string = "";
  private taxJurisdiction: string = "";
  private billingAddressLine1: string = "";
  private billingAddressLine2: string = "";
  private billingAddressLine3: string = "";
  private nativeCountries: any = [];
  private idCardFront: any;
  private idCardBack: any;
  private idCardExpiry: string = "";

  private wizardSteps: any = [
    { name: "Account setup" },
    { name: "GST details" },
    { name: "Invoicing details" },
  ];

  private studentWizardSteps: any = [
    { name: "Account setup" },
    { name: "Student ID details" },
    { name: "GST details" },
    { name: "Invoicing details" },
  ];

  private taxJurisdiction_Individual_Options: any = [
    {
      label: "-",
      value: "",
    },
    {
      label: "Inside Maharashtra & Inside India",
      value: "inside-tax-jurisdiction-state",
    },
    {
      label: "Outside Maharashtra & Inside India",
      value: "outside-tax-jurisdiction-state",
    },
    {
      label: "Outside India",
      value: "outside-tax-jurisdiction-country",
    },
  ];
  private taxJurisdiction_Company_Options: any = [
    {
      label: "-",
      value: "",
    },
    {
      label: "Inside Maharashtra & Inside India",
      value: "inside-tax-jurisdiction-state",
    },
    {
      label: "Outside Maharashtra & Inside India",
      value: "outside-tax-jurisdiction-state",
    },
    {
      label: "Outside India",
      value: "outside-tax-jurisdiction-country",
    },
  ];

  @State() wizardStepCount: number = 0;
  @State() isAffiliationNextDisabled: boolean = true;
  @State() isGSTDetailsNextDisabled: boolean = false;
  @State() isGSTInvoiceRequired: boolean = false;
  @State() isInvoicingNextDisabled: boolean = true;
  @State() isIdNextDisabled: boolean = true;
  @State() isSubmitting: boolean = false;

  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "nextStep") {
      this.incrementStep();
    } else if (e.detail.name === "previousStep") {
      this.decrementStep();
    } else if (e.detail.name === "setupAccount") {
      if (state.occupation === "student") {
        this.setupStudentAccount();
      } else {
        this.setupProfessionalAccount();
      }
    }
  }

  @Listen("dropdown-input-event")
  dropDownEventHandler(event) {
    if (event.detail.filter === "nativeCountry") {
      this.nativeCountry = event.detail.value;
    } else if (event.detail.filter === "taxJurisdiction") {
      this.taxJurisdiction = event.detail.value;
    }
    this.wizardStepCheck();
  }

  @Listen("phoneInputEvent") phoneInputEventHandler(e) {
    this.mobileIsdCode = e.detail.isdCode;
    this.mobileNumber = e.detail.mobileNumber;
    this.mobileCountry = e.detail.mobileCountry;
  }

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "gstPreference") {
      this.gstPreference = event.detail.value;
      if (this.gstPreference === "yes") {
        this.isGSTInvoiceRequired = true;
        this.isGSTDetailsNextDisabled = true;
      } else if (this.gstPreference === "no") {
        this.businessName = "";
        this.businessTaxId = "";
        this.isGSTInvoiceRequired = false;
        this.isGSTDetailsNextDisabled = false;
      }
    }
  }

  @Listen("textInput") handleTextInput(e) {
    if (e.detail.name === "orgInsti") {
      this.orgInsti = e.detail.value;
    } else if (e.detail.name === "jobDegree") {
      this.jobDegree = e.detail.value;
    } else if (e.detail.name === "businessName") {
      this.businessName = e.detail.value;
    } else if (e.detail.name === "businessTaxId") {
      this.businessTaxId = e.detail.value;
    } else if (e.detail.name === "billingAddressLine1") {
      this.billingAddressLine1 = e.detail.value;
    } else if (e.detail.name === "billingAddressLine2") {
      this.billingAddressLine2 = e.detail.value;
    } else if (e.detail.name === "billingAddressLine3") {
      this.billingAddressLine3 = e.detail.value;
    }

    this.wizardStepCheck();
  }

  @Listen("fileChangeEvent")
  fileChangeEventHandler(event) {
    if (event.detail.name === "idCardFront") {
      this.idCardFront = event.detail.file;
    } else if (event.detail.name === "idCardBack") {
      this.idCardBack = event.detail.file;
    }
    this.wizardStepCheck();
  }

  @Listen("fileRemovedEvent")
  fileRemovedEventHandler(event) {
    if (event.detail.name === "idCardFront") {
      this.idCardFront = "";
    } else if (event.detail.name === "idCardBack") {
      this.idCardBack = "";
    }
    this.wizardStepCheck();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "idCardExpiry") {
      this.idCardExpiry = e.detail.value;
      this.wizardStepCheck();
    }
  }

  componentWillLoad() {
    this.generateNativeCountries();
  }

  decrementStep() {
    this.wizardStepCount = this.wizardStepCount - 1;
    this.resetFinalStep();
  }

  incrementStep() {
    this.wizardStepCount = this.wizardStepCount + 1;
    this.resetFinalStep();
  }

  initWizard() {
    // Initializes states
    this.wizardStepCount = 0;
    this.isAffiliationNextDisabled = true;
    this.isGSTDetailsNextDisabled = false;
    this.isGSTInvoiceRequired = false;
    this.isInvoicingNextDisabled = true;
    this.isSubmitting = false;
    this.isIdNextDisabled = true;

    //Inits Vars
    this.orgInsti = "";
    this.jobDegree = "";
    this.nativeCountry = "";
    this.mobileIsdCode = "91";
    this.mobileCountry = "India";
    this.mobileNumber = "";
    this.gstPreference = "no";
    this.businessName = "";
    this.businessTaxId = "";
    this.taxJurisdiction = "";
    this.billingAddressLine1 = "";
    this.billingAddressLine2 = "";
    this.billingAddressLine3 = "";
    this.idCardFront = "";
    this.idCardBack = "";
    this.idCardExpiry = "";

    //Generate countries
    this.generateNativeCountries();
  }

  generateNativeCountries() {
    countries.map((country: any) => {
      let obj = {
        label: country.name,
        value: country.name,
      };
      this.nativeCountries.push(obj);
    });
    this.nativeCountry = this.nativeCountries[0].value;
  }

  resetFinalStep() {
    this.isInvoicingNextDisabled = true;
    this.taxJurisdiction = "";
  }

  setupStudentAccount() {
    this.isInvoicingNextDisabled = true;
    this.isSubmitting = true;

    let formData: any = new FormData();
    formData.append("orgInsti", this.orgInsti);
    formData.append("jobDegree", this.jobDegree);
    formData.append("country", this.nativeCountry);
    formData.append("idCardFront", this.idCardFront);
    if (this.idCardBack) {
      formData.append("idCardBack", this.idCardBack);
    }
    formData.append("idCardExpiry", this.idCardExpiry);
    formData.append("mobileIsdCode", this.mobileIsdCode);
    formData.append("mobileCountry", this.mobileCountry);
    formData.append("mobileNumber", this.mobileNumber);
    formData.append("isGSTInvoicePreferred", this.isGSTInvoiceRequired);
    formData.append("businessName", this.businessName);
    formData.append("taxID", this.businessTaxId);
    formData.append("taxJurisdiction", this.taxJurisdiction);
    formData.append("billingAddressLine1", this.billingAddressLine1);
    formData.append("billingAddressLine2", this.billingAddressLine2);
    formData.append("billingAddressLine3", this.billingAddressLine3);

    axios({
      method: "POST",
      data: formData,
      baseURL: `${state.baseUrl}/accountsetupv2-student`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(`${response.data.msg}. Please try again`);
          this.initWizard();
        } else if (response.data.status === "Success") {
          state.isAccountSetup = true;
          state.isIdCardVerified = false;
          this.closeModalEvent.emit();
        }
        this.isInvoicingNextDisabled = false;
        this.isSubmitting = false;
      })
      .catch((error: any) => {
        alert(`${error}: Please try again`);
        this.initWizard();
        this.isInvoicingNextDisabled = false;
        this.isSubmitting = false;
      });
  }

  setupProfessionalAccount() {
    this.isInvoicingNextDisabled = true;
    this.isSubmitting = true;

    let payload: any = {
      orgInsti: this.orgInsti,
      jobDegree: this.jobDegree,
      country: this.nativeCountry,
      mobileIsdCode: this.mobileIsdCode,
      mobileCountry: this.mobileCountry,
      mobileNumber: this.mobileNumber,
      isGSTInvoicePreferred: this.isGSTInvoiceRequired,
      businessName: this.businessName,
      taxID: this.businessTaxId,
      taxJurisdiction: this.taxJurisdiction,
      billingAddressLine1: this.billingAddressLine1,
      billingAddressLine2: this.billingAddressLine2,
      billingAddressLine3: this.billingAddressLine3,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/accountsetupv2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("An error occured while setting up account. Please try again");
          this.initWizard();
        } else if (response.data.status === "Success") {
          state.isAccountSetup = true;
          this.closeModalEvent.emit();
        }
        this.isInvoicingNextDisabled = false;
        this.isSubmitting = false;
      })
      .catch((error) => {
        alert(`${error}: Please try again`);
        this.isInvoicingNextDisabled = false;
        this.isSubmitting = false;
        this.initWizard();
      });
  }

  wizardStepCheck() {
    if (
      this.orgInsti.length > 0 &&
      this.jobDegree.length > 0 &&
      this.nativeCountry.length > 0
    ) {
      this.isAffiliationNextDisabled = false;
    } else {
      this.isAffiliationNextDisabled = true;
    }

    if (this.businessName.length > 0 && this.businessTaxId.length > 0) {
      this.isGSTDetailsNextDisabled = false;
    } else {
      this.isGSTDetailsNextDisabled = true;
    }

    if (this.taxJurisdiction.length > 0) this.isInvoicingNextDisabled = false;
    else this.isInvoicingNextDisabled = true;

    if (this.idCardFront && this.idCardExpiry.length > 0) {
      this.isIdNextDisabled = false;
    } else {
      this.isIdNextDisabled = true;
    }
  }

  BasicStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {state.occupation === "professional" ? "ORGANISATION" : "INSTITUTION"}
        </c-text>
        <c-textbox
          input-type="text"
          name="orgInsti"
          placeholder={
            state.occupation === "professional"
              ? "Company / institute name"
              : "Institute name"
          }
          isDisabled={false}
          value={this.orgInsti}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {state.occupation === "professional" ? "JOB ROLE" : "DEGREE"}
        </c-text>
        <c-textbox
          input-type="text"
          name="jobDegree"
          placeholder={
            state.occupation === "professional"
              ? "e.g. UI designer"
              : "e.g. Mdes, PhD etc"
          }
          isDisabled={false}
          value={this.jobDegree}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          YOUR NATIVE COUNTRY?
        </c-text>
        <c-dropdown
          name="nativeCountry"
          option-str={JSON.stringify(this.nativeCountries)}
          value={this.nativeCountry}
        ></c-dropdown>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={false}>
          MOBILE No. (Optional)
        </c-text>
        <c-inputphone
          is-disabled={false}
          numpad-placeholder="Your 10-digit mobile number"
          inputboxValue={this.mobileNumber}
          selectValue={this.mobileIsdCode}
        ></c-inputphone>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <div></div>
          <c-button
            name="nextStep"
            icon-name=""
            isDisabled={this.isAffiliationNextDisabled}
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  IdStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text>
          Kindly upload your {state.occupation} ID card
          <br />
          <u>Max file size: 500KB</u>
        </c-text>
      </div>
      <div class="field-container">
        <c-text>
          Front side <span class="mandatory">*</span>
        </c-text>
        <c-uploader
          file-type="file"
          name="idCardFront"
          fileSizeLimitInKB={500}
        ></c-uploader>
      </div>
      <div class="field-container">
        <c-text>Back side (if applicable) </c-text>
        <c-uploader
          file-type="file"
          name="idCardBack"
          fileSizeLimitInKB={500}
        ></c-uploader>
      </div>
      <div class="field-container">
        <c-text>
          ID card expiry date <span class="mandatory">*</span>
        </c-text>
        <c-date-picker name="idCardExpiry" pickTime={false}></c-date-picker>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="nextStep"
            icon-name=""
            isDisabled={this.isIdNextDisabled}
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  GstDetailsStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text>
          <c-text>
            Do you want GST invoice for your purchases?{" "}
            <span class="mandatory">*</span>
          </c-text>
        </c-text>
        <c-text type="wizardSubHeading">
          For individuals who intends to seek refund from employer for their
          purchases
        </c-text>
        <c-row>
          <c-radio
            name="gstPreference"
            label1="Yes"
            label2=""
            label3=""
            val="yes"
            isChecked={this.gstPreference === "yes" ? true : false}
          ></c-radio>
          <c-radio
            name="gstPreference"
            label1="No"
            label2=""
            label3=""
            val="no"
            isChecked={this.gstPreference === "no" ? true : false}
          ></c-radio>
        </c-row>
      </div>

      {this.isGSTInvoiceRequired ? (
        <div class="gst-details-container">
          <c-divider type="single"></c-divider>
          <div class="field-container">
            <c-text type="modalLabel" isMandatory={this.isGSTInvoiceRequired}>
              ENTER BUSINESS NAME & GSTIN
            </c-text>
            <c-textbox
              input-type="text"
              name="businessName"
              placeholder="Business name"
              isDisabled={false}
              value={this.isGSTInvoiceRequired ? this.businessName : ""}
            ></c-textbox>
            <br />
            <br />
            <c-textbox
              input-type="text"
              name="businessTaxId"
              placeholder="Business GSTIN"
              isDisabled={false}
              value={this.isGSTInvoiceRequired ? this.businessTaxId : ""}
            ></c-textbox>
          </div>
        </div>
      ) : (
        <br />
      )}

      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="nextStep"
            icon-name=""
            isDisabled={
              this.isGSTInvoiceRequired ? this.isGSTDetailsNextDisabled : false
            }
          >
            Next
          </c-button>
        </div>
      </div>
    </div>
  );

  InvoicingStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {this.isGSTInvoiceRequired
            ? "WHERE IS YOUR COMPANY REGISTERED?"
            : "WHERE ARE YOU CURRENTLY LOCATED?"}
        </c-text>
        <c-dropdown
          name="taxJurisdiction"
          option-str={
            this.isGSTInvoiceRequired
              ? JSON.stringify(this.taxJurisdiction_Company_Options)
              : JSON.stringify(this.taxJurisdiction_Individual_Options)
          }
          value={this.taxJurisdiction}
        ></c-dropdown>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={this.isGSTInvoiceRequired}>
          {this.isGSTInvoiceRequired
            ? "COMPANY ADDRESS"
            : "BILLING ADDRESS (Optional)"}
        </c-text>
        <div class="billing-address-input-group">
          <c-textbox
            input-type="text"
            name="billingAddressLine1"
            placeholder="Billing Address Line 1"
            isDisabled={false}
            value={this.billingAddressLine1}
          ></c-textbox>
          <c-textbox
            input-type="text"
            name="billingAddressLine2"
            placeholder="Billing Address Line 2"
            isDisabled={false}
            value={this.billingAddressLine2}
          ></c-textbox>
          <c-textbox
            input-type="text"
            name="billingAddressLine3"
            placeholder="City, Pincode, State, Country"
            isDisabled={false}
            value={this.billingAddressLine3}
          ></c-textbox>
        </div>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <c-button
            name="previousStep"
            type="back"
            icon-name=""
            isDisabled={false}
          >
            Back
          </c-button>
          <c-button
            name="setupAccount"
            icon-name=""
            isDisabled={this.isInvoicingNextDisabled}
            isInAction={this.isSubmitting}
            isInActionLabel="Submitting.."
          >
            Submit
          </c-button>
        </div>
      </div>
    </div>
  );

  render() {
    return (
      <div class="wizard-container">
        <div class="wizard-header">
          <div>
            <c-text type="wizardHeading">
              {state.occupation === "student"
                ? this.studentWizardSteps[this.wizardStepCount].name
                : this.wizardSteps[this.wizardStepCount].name}
            </c-text>
            <c-text type="wizardSubHeading">
              STEP {this.wizardStepCount + 1} OF{" "}
              {state.occupation === "student"
                ? this.studentWizardSteps.length
                : this.wizardSteps.length}
            </c-text>
          </div>
        </div>

        {state.occupation === "student" ? (
          <div>
            {this.wizardStepCount === 0 && <this.BasicStep></this.BasicStep>}
            {this.wizardStepCount === 1 && <this.IdStep></this.IdStep>}
            {this.wizardStepCount === 2 && (
              <this.GstDetailsStep></this.GstDetailsStep>
            )}
            {this.wizardStepCount === 3 && (
              <this.InvoicingStep></this.InvoicingStep>
            )}
          </div>
        ) : (
          <div>
            {this.wizardStepCount === 0 && <this.BasicStep></this.BasicStep>}
            {this.wizardStepCount === 1 && (
              <this.GstDetailsStep></this.GstDetailsStep>
            )}
            {this.wizardStepCount === 2 && (
              <this.InvoicingStep></this.InvoicingStep>
            )}
          </div>
        )}
      </div>
    );
  }
}
