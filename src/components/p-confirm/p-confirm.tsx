import {
  Component,
  Listen,
  Prop,
  FunctionalComponent,
  h,
  State,
} from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import axios from "axios";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "p-confirm",
  styleUrl: "p-confirm.css",
})
export class PConfirm {
  @Prop() history: RouterHistory;
  private isRazorpayScriptLoaded: boolean = false;

  @Listen("empty-purchase-list")
  deletePurchasedItemHandler(event) {
    event.preventDefault();
    if (state.cartItems.length === 0) {
      state.isPrimaryTicketInCart = false;
      state.isPartialTicketInCart = false;
      state.isFreePurchase = false;
      state.paymentGateway = "razorpay";
    }
  }

  @Listen("pay-btn-click-event")
  payBtnClickHandler(event) {
    event.preventDefault();
    this.paymentHandler();
  }

  @Listen("calculateMembershipCheckoutTotal")
  calculateMembershipCheckoutTotalHandler() {
    this.calculateMembershipCheckoutTotal();
  }

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    if (event.detail.cNavItemName === "back") {
      this.history.goBack();
    }
  }

  @State()
  membershipData: any;
  @State() membershipSubTitle: string = "";
  @State() membershipTitle: string = "";
  @State() membershipCurrency: string = "";
  @State() membershipPrice: number = 0;

  componentWillLoad() {
    if (state.membershipInCart.length > 0) {
      this.addMembershipToCart();
    } else {
      this.goToHome();
    }
  }

  async addMembershipToCart() {
    let payload: any = {
      membershipType: state.membershipInCart,
      paymentGateway: state.paymentGateway,
    };

    await axios({
      method: "POST",
      baseURL: `${state.baseUrl}/add-membership-to-legacy-cart`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          let membershipData = response.data.payload;
          this.membershipTitle = membershipData.title;
          this.membershipCurrency = membershipData.currency;
          this.membershipSubTitle = membershipData.subTitle;
          this.membershipPrice = membershipData.price;
          state.currency = membershipData.currency;
          state.cartTotal = membershipData.cartTotal;
          state.gatewayFee = membershipData.gatewayFee;
          state.grandTotal = membershipData.grandTotal;
          state.isMembershipSaved = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  async calculateMembershipCheckoutTotal() {
    let payload: any = {
      membershipType: state.membershipInCart,
      paymentGateway: state.paymentGateway,
    };

    await axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-membership-checkout-total`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          state.currency = response.data.payload.currency;
          state.cartTotal = response.data.payload.cartTotal;
          state.gatewayFee = response.data.payload.gatewayFee;
          state.grandTotal = response.data.payload.grandTotal;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  paymentHandler() {
    if (state.paymentGateway === "bank") {
      let payload = {
        paymentGateway: "BankTransfer",
        bankTxCode: Store.getBankTxCode(),
      };
      this.history.push("/membership-confirmation", payload);
    } else if (state.paymentGateway === "razorpay") {
      this.displayRazorpay();
    }
  }

  loadScript(src) {
    return new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }

  async displayRazorpay() {
    if (!this.isRazorpayScriptLoaded) {
      const res = await this.loadScript(
        "https://checkout.razorpay.com/v1/checkout.js"
      );
      if (!res) {
        alert("Razorpay SDK failed to load");
        return;
      }
    }

    let payload = {
      paymentGateway: "Razorpay",
    };

    let data: any;
    await axios({
      method: "POST",
      baseURL: `${state.baseUrl}/membership-razorpay-prep`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          data = response.data.payload;
        }
      })
      .catch((error) => {
        alert(error);
      });

    // Test
    // let key: string = "rzp_test_L6Nh6GCncfTSDN";
    // Prod
    let key: string = "rzp_live_rMYujqF2bz67uJ";

    const options = {
      key: key,
      currency: data.currency,
      amount: data.amount.toString(),
      order_id: data.id,
      name: data.org,
      description: "",
      image:
        "https://res.cloudinary.com/layerpark/image/upload/v1662136677/hcipai/main/hcipai-logo_nihn9o.png",
      handler: (response) => {
        let obj = {
          paymentGateway: "Razorpay",
          paymentID: response.razorpay_payment_id,
          orderID: response.razorpay_order_id,
          signature: response.razorpay_signature,
        };
        if (obj.paymentID && obj.orderID && obj.signature) {
          this.history.push("/membership-confirmation", obj);
        } else {
          alert("Payment Failed: Please try again!");
        }
      },
      prefill: {
        name: data.name,
        email: data.email,
        phone_number: data.mobile,
      },
      theme: {
        color: "#593196",
      },
      modal: {
        ondismiss: () => {
          state.isPaying = false;
          state.isPaymentBtnDisabled = false;
        },
      },
    };
    const _window = window as any;
    const paymentObject = new _window.Razorpay(options);
    paymentObject.open();
  }

  goToHome() {
    this.history.push("/events", {});
  }

  MembershipItem: FunctionalComponent = () => (
    <div class="purchase-list-container">
      <h1>Membership checkout</h1>
      <p class="one-line">
        By paying, you accept our{" "}
        <c-text-link
          url="https://indiahci.org/cancellation-refund.html"
          label="cancellation & refund policy"
        ></c-text-link>
      </p>
      <div class="purchased-item-container">
        <div class="details">
          <div class="purchased-item-content-left">
            {/* <span class="purchased-item-subtitle badge blue-badge">
              {this.membershipSubTitle.toUpperCase()}{" "}
            </span> */}
            <c-text type="subtext">
              {this.membershipSubTitle.toUpperCase()}
            </c-text>
            <p class="purchased-item-title">{this.membershipTitle}</p>
          </div>
          <p class="purchased-item-content-right">
            <span>{this.membershipCurrency}</span>
            <span>{this.membershipPrice}</span>
          </p>
        </div>
      </div>
    </div>
  );

  private navOpts = [
    {
      type: "navItem",
      name: "back",
      label: "Back",
      state: "",
      icon: "chevron-back-outline",
      subText: "",
      route: "",
    },
  ];

  render() {
    return (
      <c-page>
        <c-topbar></c-topbar>
        <c-sidebar type="left">
          <c-vnav
            nav-opts-str={JSON.stringify(this.navOpts)}
            isConfigMode={false}
          ></c-vnav>
        </c-sidebar>

        {state.isMobileMenuOpen && <c-mobile-menu></c-mobile-menu>}

        {state.isMembershipSaved && (
          <main>
            <this.MembershipItem></this.MembershipItem>
            <c-payment-options></c-payment-options>
          </main>
        )}
      </c-page>
    );
  }
}

injectHistory(PConfirm);
