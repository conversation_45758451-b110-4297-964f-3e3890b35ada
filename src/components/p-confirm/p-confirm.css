p-confirm main {
  width: 70%;
  max-width: 768px;
  margin: 0 auto 0 auto;
  margin-right: 14%;
  display: flex;
  justify-content: space-between;
}

p-confirm .purchased-item-container {
  /* display: flex; */
  /* height: 50px; */
  /* justify-content: space-between; */
  /* align-items: baseline; */
  background: white;
  padding: 1em;
  margin-bottom: 1em;
  /* width: 50%; */
  /* box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05); */
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  align-items: center;
  margin-right: 4em;
  width: 82.5%;
}

p-confirm .details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.purchased-item-subtitle {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.6);
}

.purchased-item-remove-btn {
  /* height: 15px;
  width: 15px; */
  border: 0;
  background: none;
  font-size: 1.5em;
  color: rgba(231, 76, 60, 1);
  transition: all 0.15s ease-in;
  margin: 0;
  padding: 0;
}
.purchased-item-remove-btn:hover {
  cursor: pointer;
  /* background: rgba(231, 76, 60, 0.1); */
  transform: scale(1.25);
}

.purchased-item-content-left {
  width: 75%;
  margin: 0;
}
.purchased-item-content-left .subtext {
  margin-top: 0;
}
.purchased-item-content-right {
  margin: 0;
  font-size: 0.9em;
}

p-confirm .purchased-item-title {
  margin: 0;
  font-size: 0.9em;
  line-height: 1.5;
  margin-top: 0.5em;
}

p-confirm .badge {
  font-size: 0.7em;
  font-weight: 700;
  padding: 0.25em 1em;
  margin: 0;
  margin-bottom: 0.5em;
  border-radius: 0.25em;
  margin-right: 1em;
  width: 75px;
  text-align: center;
}

p-confirm .blue-badge {
  background: var(--accent-blue-bg-lighter);
  color: var(--accent-blue-darker);
}

p-confirm h1 {
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 0em;
  margin-top: 0;
  font-size: 1.5em;
  font-weight: 400;
}

p-confirm .one-line {
  font-size: 0.8em;
  margin-top: 0;
  padding-bottom: 1em;
}

@media only screen and (max-width: 768px) {
  p-confirm main {
    display: block;
    width: 100%;
    margin: 1.5em auto 0 auto;
    padding: 0;
  }

  p-confirm .purchased-item-container {
    display: none;
  }
}
