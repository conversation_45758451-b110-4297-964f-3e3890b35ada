import { Component, Prop, h } from "@stencil/core";
import { RouterHistory, MatchResults } from "@stencil/router";

@Component({
  tag: "p-test",
  styleUrl: "p-test.css",
})
export class PTest {
  @Prop() history: RouterHistory;
  @Prop() match: MatchResults;

  componentWillLoad() {
    console.log(`eventId: ${this.match.params.eventCode}`);
  }

  render() {
    return (
      <div>
        <p>This is a test</p>
      </div>
    );
  }
}
