import { Component, Prop, State, h } from "@stencil/core";

@Component({
  tag: "c-alerts-filter",
  styleUrl: "c-alerts-filter.css",
})
export class CAlertsFilter {
  @Prop() alertCount: string;
  @State() isFetching: boolean = true;
  private alertStatus = [
    {
      label: "Alert Status",
      value: "",
    },
    {
      label: "Active",
      value: "active",
    },
    {
      label: "Closed",
      value: "closed",
    },
  ];
  private alertTypes = [
    {
      label: "Alert Types",
      value: "",
    },
    {
      label: "Bank Transfer",
      value: "Bank Transfer",
    },
    {
      label: "Razorpay Payment Failure",
      value: "Razorpay Payment Failure",
    },
    {
      label: "Incomplete Registration",
      value: "Partial Registration",
    },
  ];
  render() {
    return (
      <div class="container">
        <div class="filter-container">
          <c-dropdown
            name="alertStatus"
            option-str={JSON.stringify(this.alertStatus)}
          ></c-dropdown>
          <c-dropdown
            name="alertType"
            option-str={JSON.stringify(this.alertTypes)}
          ></c-dropdown>
        </div>
        <p class="filter-desc">
          Showing <strong>{this.alertCount}</strong> active alerts
        </p>
      </div>
    );
  }
}
