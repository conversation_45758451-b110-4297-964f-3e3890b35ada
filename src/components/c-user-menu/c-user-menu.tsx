import { Component, State, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-user-menu",
  styleUrl: "c-user-menu.css",
})
export class CUserMenu {
  @State() isUserMenuHidden: boolean = true;

  handleUserMenuBtnClick(event) {
    event.preventDefault();
    this.isUserMenuHidden = !this.isUserMenuHidden;
  }

  @State() isLoginOutBtnDisabled: boolean = false;
  private isLoggingOut: boolean = false;

  render() {
    return (
      <div>
        <button
          onClick={(event) => {
            this.handleUserMenuBtnClick(event);
          }}
          class="user-menu-btn"
        >
          {state.dpUrl.length > 0 && (
            <c-img type="menuDp" src={state.dpUrl}></c-img>
          )}
          {state.firstName} <span class="caret-down"></span>
        </button>
        <div
          class={
            this.isUserMenuHidden ? "hide-user-menu user-menu" : "user-menu"
          }
        >
          <div class="user-menu-dropdown">
            <ul class="user-menu-list">
              <li>
                {" "}
                <stencil-route-link class="user-menu-links" url="/">
                  Home
                </stencil-route-link>
              </li>
              <li>
                {" "}
                <stencil-route-link class="user-menu-links" url="/profile">
                  Profile
                </stencil-route-link>
              </li>
              <li>
                {" "}
                <stencil-route-link class="user-menu-links" url="/purchases">
                  Purchases
                </stencil-route-link>
              </li>
              {state.isRegistrationManager ? (
                <li>
                  {" "}
                  <stencil-route-link class="user-menu-links" url="/dashboard">
                    RM Access
                  </stencil-route-link>
                </li>
              ) : (
                ""
              )}
            </ul>
            <c-btn
              name="logout"
              label="Logout"
              action-label="Logging.."
              is-in-action={this.isLoggingOut}
              is-disabled={this.isLoginOutBtnDisabled}
            ></c-btn>
          </div>
        </div>
      </div>
    );
  }
}
