.user-menu {
  position: absolute;
  width: 150px;
  right: 1%;
}

.user-menu-dropdown {
  border-radius: 0.25em;
  padding: 1em;
  background: #351d5a;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 6px 6px rgba(0, 0, 0, 0.05);
}

c-user-menu .user-menu-btn {
  display: flex;
  align-items: center;
  color: var(--fontcolor);
  background: none;
  border-radius: var(--border-radius);
  border: 0;
  padding: 0.9em 1em;
  font-size: 1em;
  box-sizing: border-box;
  transition: all 0.15s ease-in;
  outline: none;
}
c-user-menu .user-menu-btn:hover {
  cursor: pointer;
  background: rgba(0, 0, 0, 0.03);
}

c-user-menu c-btn button {
  width: 100%;
  color: white;
  background: #fd346c;
  border-radius: var(--border-radius);
  border: 0;
  padding: 0.7em 1em;
  font-size: 0.9em;
  box-sizing: border-box;
  outline: none;
}
c-user-menu c-btn button:hover {
  background: #e42f61;
}

c-user-menu .caret-down {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 4px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}

.dp-thumbnail {
  height: 36px;
  width: 36px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 100%;
}

.triangle {
  position: absolute;
  width: 0;
  height: 0;
  margin-top: -9px;
  margin-left: 68px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #351d5a;
  border-radius: 0.25em;
}

.hide-user-menu {
  display: none;
}

.user-menu-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  margin-top: 1em;
  margin-bottom: 1em;
}

.user-menu-list li {
  /* margin-bottom: 1em; */
}

.user-menu-links a {
  display: block;
  text-decoration: none;
  color: var(--accent-color-bg);
  padding: 0.7em 1em;
  border-radius: 0.25em;
  font-size: 0.9em;
}
.user-menu-links a:hover {
  background: var(--accent-color);
}

.user-menu-seperator {
  height: 1px;
  width: 85%;
  margin: 0 auto;
  background: var(--accent-color-light);
  margin-top: 2em;
  margin-bottom: 2em;
}
