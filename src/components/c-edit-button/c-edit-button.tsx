import { Component, Event, EventEmitter, h } from "@stencil/core";

@Component({
  tag: "c-edit-button",
  styleUrl: "c-edit-button.css",
})
export class CEditButton {
  @Event({
    eventName: "profile-edit-btn-click",
    bubbles: true,
  })
  editBtnClickEvent: EventEmitter;

  handleEditBtnClick(event) {
    event.preventDefault();
    this.editBtnClickEvent.emit();
  }

  render() {
    return (
      <button>
        <div
          class="edit-icon"
          onClick={(event) => this.handleEditBtnClick(event)}
        ></div>
      </button>
    );
  }
}
