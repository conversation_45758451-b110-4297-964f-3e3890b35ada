import {
  Component,
  Event,
  EventEmitter,
  Listen,
  Host,
  Prop,
  State,
  FunctionalComponent,
  Watch,
  h,
} from "@stencil/core";

@Component({
  tag: "c-uploader",
  styleUrl: "c-uploader.css",
})
export class CUploader {
  @Event({
    eventName: "fileChangeEvent",
    bubbles: true,
  })
  fileChangeEvent: EventEmitter;

  @Event({
    eventName: "fileRemovedEvent",
    bubbles: true,
  })
  fileRemovedEvent: EventEmitter;

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "cancelFileInput") {
      this.removeFile();
    }
  }

  fileInputEL!: HTMLInputElement;

  @Prop() name: string;
  @Prop() fileType: string;
  @Prop() resetUploader: boolean = false;
  @Prop() fileSizeLimitInKB: number = 0;

  private fileSizeDivisor: number = 1000;

  @Watch("resetUploader")
  resetUploaderWatcher(newVal: number, oldVal: number) {
    if (newVal != oldVal) {
      if (newVal) {
        this.removeFile();
      }
    }
  }

  @State() isFileAdded: boolean = false;

  removeFile() {
    this.fileInputEL.value = "";
    this.isFileAdded = false;
    this.fileRemovedEvent.emit({
      name: this.name,
    });
  }

  fileChangeHandler() {
    let file: any = this.fileInputEL.files[0];
    if (this.fileSizeLimitInKB > 0) {
      if (file.size / this.fileSizeDivisor > this.fileSizeLimitInKB) {
        this.removeFile();
        alert(`❌ File size exceeds ${this.fileSizeLimitInKB}KB`);
        return;
      }
    }
    this.isFileAdded = true;
    this.fileChangeEvent.emit({
      name: this.name,
      file: file,
      size: file.size,
    });
  }

  ImageUploader: FunctionalComponent = () => (
    <div class="image-uploader-container">
      <input
        type="file"
        name={this.name}
        accept="image/*"
        onChange={() => this.fileChangeHandler()}
        ref={(el) => (this.fileInputEL = el as HTMLInputElement)}
      />
      {this.isFileAdded && (
        <c-button
          type="cancelFileInput"
          name="cancelFileInput"
          icon-name=""
          label=""
        ></c-button>
      )}
    </div>
  );

  FileUploader: FunctionalComponent = () => (
    <div class="image-uploader-container">
      <input
        type="file"
        name={this.name}
        accept="image/*,.pdf"
        onChange={() => this.fileChangeHandler()}
        ref={(el) => (this.fileInputEL = el as HTMLInputElement)}
      />
      {this.isFileAdded && (
        <c-button
          type="cancelFileInput"
          name="cancelFileInput"
          icon-name=""
          label=""
        ></c-button>
      )}
    </div>
  );

  render() {
    if (this.fileType === "image") {
      return (
        <Host>
          <this.ImageUploader></this.ImageUploader>
        </Host>
      );
    } else {
      return (
        <Host>
          <this.FileUploader></this.FileUploader>
        </Host>
      );
    }
  }
}
