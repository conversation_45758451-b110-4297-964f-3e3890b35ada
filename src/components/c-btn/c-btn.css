c-btn button {
  box-sizing: border-box;
  width: 100%;
  padding: 0.7em 1em;
  font-size: 0.9em;
  border: 0;
  border-radius: var(--border-radius);
  background: var(--accent-color);
  color: var(--accent-color-bg-lightest);
  transition: all 0.15s ease-in;
}

c-btn button:hover {
  cursor: pointer;
  background: var(--accent-color-dark);
}

c-btn button:active {
  cursor: pointer;
  background: var(--accent-color-darker);
}

c-btn .disabled {
  opacity: 0.3;
  color: var(--accent-color-bg-lightest);
}

c-btn .disabled:hover {
  opacity: 0.3;
  cursor: auto;
}

c-btn .ghost {
  background: white;
  color: var(--accent-color);
  border: 1px solid var(--accent-color-bg);
}

c-btn .ghost:hover {
  background: var(--accent-color-bg-lightest);
  color: var(--accent-color);
}

c-btn .transparent {
  background: none;
  border: 0;
  color: var(--accent-color);
}

c-btn .transparent:hover {
  background: none;
  border: 0;
  color: var(--accent-color);
  background: var(--accent-color-bg-lightest);
}

c-btn .icon-only-ghost {
  padding: 3px 5px 1px 5px;
  background: none;
  border: none;
  opacity: 0.8;
}

c-btn .icon-only-ghost:hover {
  background: var(--accent-color-bg-lightest);
  opacity: 0.8;
}

c-btn .icon-only-ghost:active {
  opacity: 0.5;
}
