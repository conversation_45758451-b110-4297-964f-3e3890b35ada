import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-btn",
  styleUrl: "c-btn.css",
})
export class CBtn {
  @Prop() name: string;
  @Prop() label: string;
  @Prop() value: string;
  @Prop() type: string;
  @Prop() actionLabel: string;
  @Prop() isInAction: boolean;
  @Prop() isDisabled: boolean;
  @Prop() icon: boolean = false;
  @Prop() iconUrl: string;

  @Event({
    eventName: "create-coupon",
    bubbles: true,
  })
  createCouponClickEvent: EventEmitter;

  @Event({
    eventName: "login-btn-click-event",
    bubbles: true,
  })
  loginBtnClickEvent: EventEmitter;

  @Event({
    eventName: "signup-btn-click-event",
    bubbles: true,
  })
  signUpBtnClickEvent: EventEmitter;

  @Event({
    eventName: "verify-email-btn-click",
    bubbles: true,
  })
  verifyEmailBtnClick: EventEmitter;

  @Event({
    eventName: "account-setup-btn-click-event",
    bubbles: true,
  })
  accountSetupBtnClick: EventEmitter;

  @Event({
    eventName: "logout-btn-click-event",
    bubbles: true,
  })
  logoutBtnClick: EventEmitter;

  @Event({
    eventName: "checkout-btn-click-event",
    bubbles: true,
  })
  checkOutBtnClick: EventEmitter;

  @Event({
    eventName: "pay-btn-click-event",
    bubbles: true,
  })
  payBtnClick: EventEmitter;

  @Event({
    eventName: "back-to-account",
    bubbles: true,
  })
  backToAccount: EventEmitter;

  @Event({
    eventName: "apply-member-discount",
    bubbles: true,
  })
  applyMemberDiscount: EventEmitter;

  @Event({
    eventName: "send-password-reset-code",
    bubbles: true,
  })
  sendPasswordResetCode: EventEmitter;

  @Event({
    eventName: "verify-password-reset-code",
    bubbles: true,
  })
  verifyPasswordResetCode: EventEmitter;

  @Event({
    eventName: "reset-password",
    bubbles: true,
  })
  resetPassword: EventEmitter;

  @Event({
    eventName: "back-to-login",
    bubbles: true,
  })
  backToLogin: EventEmitter;

  @Event({
    eventName: "save-profile-edits",
    bubbles: true,
  })
  saveProfileEdits: EventEmitter;

  @Event({
    eventName: "hide-overlay",
    bubbles: true,
  })
  hideOverlay: EventEmitter;

  @Event({
    eventName: "show-modal",
    bubbles: true,
  })
  showModal: EventEmitter;

  @Event({
    eventName: "hide-modal",
    bubbles: true,
  })
  hideModal: EventEmitter;

  @Event({
    eventName: "bank-transactions-btn-click",
    bubbles: true,
  })
  verifyBankTransaction: EventEmitter;

  @Event({
    eventName: "download-legacy-member-data",
    bubbles: true,
  })
  downloadLegacyMemberData: EventEmitter;

  @Event({
    eventName: "start-invoice-preference-survey",
    bubbles: true,
  })
  startInvoicePreferenceSurvey: EventEmitter;

  @Event({
    eventName: "save-invoice-preferences",
    bubbles: true,
  })
  saveInvoicePreferences: EventEmitter;

  @Event({
    eventName: "download-purchase-data",
    bubbles: true,
  })
  downloadPurchaseData: EventEmitter;

  @Event({
    eventName: "download-track-registrants",
    bubbles: true,
  })
  downloadTrackRegistrants: EventEmitter;

  @Event({
    eventName: "resolve-alert",
    bubbles: true,
  })
  resolveAlert: EventEmitter;

  @Event({
    eventName: "button-click",
    bubbles: true,
  })
  buttonClickEvent: EventEmitter;

  @Event({
    eventName: "uploadInvoiceGroup",
    bubbles: true,
  })
  uploadInvoiceGroup: EventEmitter;

  @Event({
    eventName: "downloadAccounts",
    bubbles: true,
  })
  downloadAccounts: EventEmitter;

  handleBtnClick(event) {
    event.preventDefault();
    if (this.name === "login") {
      this.loginBtnClickEvent.emit();
    } else if (this.name === "signup") {
      this.signUpBtnClickEvent.emit();
    } else if (this.name === "verifyEmail") {
      this.verifyEmailBtnClick.emit();
    } else if (this.name === "accountSetup") {
      this.accountSetupBtnClick.emit();
    } else if (this.name === "logout") {
      this.logoutBtnClick.emit();
    } else if (this.name === "checkout") {
      this.checkOutBtnClick.emit();
    } else if (this.name === "pay") {
      this.payBtnClick.emit();
    } else if (this.name === "backtoaccount") {
      this.backToAccount.emit();
    } else if (this.name === "applymemberdiscount") {
      this.applyMemberDiscount.emit();
    } else if (this.name === "sendpasswordresetcode") {
      this.sendPasswordResetCode.emit();
    } else if (this.name === "verifypasswordresetcode") {
      this.verifyPasswordResetCode.emit();
    } else if (this.name === "resetpassword") {
      this.resetPassword.emit();
    } else if (this.name === "backtologin") {
      this.backToLogin.emit();
    } else if (this.name === "saveProfileEdits") {
      this.saveProfileEdits.emit();
    } else if (this.name === "hideOverlay") {
      this.hideOverlay.emit();
    } else if (this.name === "verifyBankTransaction") {
      this.verifyBankTransaction.emit({
        orderID: this.value,
      });
    } else if (this.name === "downloadLegacyMemberData") {
      this.downloadLegacyMemberData.emit();
    } else if (this.name === "invoicePreferenceSurveyStart") {
      this.startInvoicePreferenceSurvey.emit();
    } else if (this.name === "saveInvoicePreferences") {
      this.saveInvoicePreferences.emit();
    } else if (this.name === "downloadPurchaseData") {
      this.downloadPurchaseData.emit();
    } else if (this.name === "downloadTrackRegistrants") {
      this.downloadTrackRegistrants.emit();
    } else if (this.name === "resolveAlert") {
      this.resolveAlert.emit({
        alertId: this.value,
      });
    } else if (this.name === "createCoupon") {
      this.createCouponClickEvent.emit();
    } else if (this.name === "uploadInvoiceGroup") {
      this.uploadInvoiceGroup.emit();
    } else if (this.name === "downloadAccounts") {
      this.downloadAccounts.emit();
    } else {
      this.buttonClickEvent.emit({
        name: this.name,
        value: this.value,
      });
    }
  }

  render() {
    return (
      <button
        class={`${this.type} ${this.isDisabled ? "disabled" : ""} `}
        disabled={this.isDisabled}
        onClick={(event) => this.handleBtnClick(event)}
      >
        {this.isInAction ? <c-spinner></c-spinner> : this.label}
      </button>
    );
  }
}
