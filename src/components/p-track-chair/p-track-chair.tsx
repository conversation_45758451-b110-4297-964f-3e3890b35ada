import { Component, Listen, Prop, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-track-chair",
  styleUrl: "p-track-chair.css",
})
export class PTrackChair {
  @Prop() width: string;
  @State() isFetching: boolean = true;
  @State() isOverlayVisible: boolean = false;
  @State() isTicketDetailVisible: boolean = false;

  private overviewDetails: any;
  componentDidLoad() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/trackoverview`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.overviewDetails = response.data.payload;
          state.notificationCount = this.overviewDetails.notificationCount;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  @Listen("show-ticket-details")
  showTicketDetailsHandler() {
    this.isOverlayVisible = true;
    this.isTicketDetailVisible = true;
  }

  @Listen("hide-modal")
  hideModal() {
    state.expandedUserEmail = "";
    this.isOverlayVisible = false;
    this.isTicketDetailVisible = false;
  }

  hideOverlay(event) {
    event.preventDefault();
    this.isOverlayVisible = false;
    this.isTicketDetailVisible = false;
  }

  render() {
    return (
      <div class="container">
        {this.isTicketDetailVisible ? (
          <c-ticket-detail-modal></c-ticket-detail-modal>
        ) : (
          ""
        )}
        {this.isOverlayVisible ? (
          <div
            class="dark-overlay"
            onClick={(event) => this.hideOverlay(event)}
          ></div>
        ) : (
          ""
        )}
        <section class="overview-section">
          <h1 class="overview-section-header">Track Participants</h1>
          <p class="subheading">FOR WORKSHOP & COURSE CHAIRS</p>
          <br />
          {this.isFetching ? (
            <div>
              <c-skel-card></c-skel-card>
            </div>
          ) : (
            <div class="row">
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1 header-row">
                      <span>Workshops </span>
                      <span class="count">
                        Total participants:{" "}
                        {this.overviewDetails.purchases.workshops.totalCount}
                      </span>
                    </p>
                  </div>
                </div>
                <div class="card-content no-footer-card">
                  <div>
                    {this.overviewDetails.purchases.workshops.unitSale.map(
                      (workshop) => (
                        <c-overview-info
                          highlight=""
                          is-btn={true}
                          label={workshop.name}
                          count={workshop.sold}
                          total={`₹${workshop.total}`}
                          layout="label-count-expand"
                          width={400}
                        ></c-overview-info>
                      )
                    )}
                  </div>
                </div>
              </div>
              <div class="vseperator"></div>
              <div class="card">
                <div class="card-header">
                  <div class="row label-total-row">
                    <p class="elem-1 header-row">
                      <span> Courses </span>
                      <span class="count">
                        Total Participants:{" "}
                        {this.overviewDetails.purchases.courses.totalCount}
                      </span>
                    </p>
                  </div>
                </div>
                <div class="card-content no-footer-card">
                  <div>
                    {this.overviewDetails.purchases.courses.unitSale.map(
                      (course) => (
                        <c-overview-info
                          highlight=""
                          is-btn={true}
                          label={course.name}
                          count={course.sold}
                          total={`₹${course.total}`}
                          layout="label-count-expand"
                          width={400}
                        ></c-overview-info>
                      )
                    )}
                  </div>
                </div>
                {/* <div class="card-footer">
                  <div class="row label-total-row">
                    <p class="elem-1">Total</p>
                    <div class="total-count-row">
                      <p class="elem-2">
                        ₹{this.overviewDetails.purchases.courses.totalSale}
                      </p>
                      <p class="elem-3">
                        ({this.overviewDetails.purchases.courses.totalCount})
                      </p>
                    </div>
                  </div>
                </div> */}
              </div>
            </div>
          )}
        </section>
      </div>
    );
  }
}
