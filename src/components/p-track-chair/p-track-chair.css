p-track-chair .container {
  display: flex;
  justify-content: space-around;
  margin-top: 1em;
  margin-bottom: 5em;
}

p-track-chair .overview-section-header {
  color: rgba(0, 0, 0, 0.4);
  font-size: 1.5em;
  margin-bottom: 0.25em;
  font-weight: 400;
}

p-track-chair .vseperator {
  margin-left: 0.75em;
  margin-right: 0.75em;
}

p-track-chair .row {
  display: flex;
}

p-track-chair .header-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

p-track-chair .card-header {
  background: rgba(238, 234, 245, 0.5);
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em 0.25em 0 0;
  font-size: 0.9em;
  font-weight: 700;
  color: var(--accent-color);
  display: flex;
  justify-content: space-between;
}

p-track-chair .card-header p {
  margin-top: 0;
  margin-bottom: 0;
}

p-track-chair .card-content {
  display: flex;
  justify-content: space-between;
  padding: 0em 1em;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em;
  font-size: 0.9em;
}

p-track-chair .overview-section {
  margin-bottom: 2.5em;
}

p-track-chair .label-total-row {
  width: 100%;
  justify-content: space-between;
}

p-track-chair .elem-2 {
  width: 60%;
  text-align: right;
}

p-track-chair .elem-3 {
  width: 20%;
  text-align: left;
}

p-track-chair .total-count-row {
  display: flex;
  justify-content: space-between;
}

p-track-chair .wide-card {
  width: 350px;
}

p-track-chair .card-footer {
  background: white;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
  border-radius: 0em 0em 0.25em 0.25em;
  font-size: 0.9em;
  display: flex;
  justify-content: space-between;
}

p-track-chair .card-footer p {
  margin: 0;
  padding: 0;
}

p-track-chair .round-bottom {
  border-radius: 0 0 0.25em 0.25em;
}

p-track-chair .card-subheading p {
  font-size: 0.7em;
  margin: 0;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: 0;
}

p-track-chair .sale-count {
  color: rgba(0, 0, 0, 0.4);
}

p-track-chair .no-footer-card {
  border-radius: 0 0 0.25em 0.25em;
}

p-track-chair .dark-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  -webkit-animation: fadein 0.25s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: fadein 0.25s; /* Firefox < 16 */
  -ms-animation: fadein 0.25s; /* Internet Explorer */
  -o-animation: fadein 0.25s; /* Opera < 12.1 */
  animation: fadein 0.25s;
}

p-track-chair c-ticket-detail-modal .main-container {
  margin-left: 25%;
}

p-track-chair
  c-ticket-detail-modal
  .main-container
  div
  .track-info-container
  c-btn
  button {
  display: none;
}

p-track-chair .subheading {
  font-size: 0.9em;
  margin-top: 0;
}

p-track-chair .count {
  font-weight: 400;
}
