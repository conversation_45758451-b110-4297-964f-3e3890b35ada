import { Component, Event, EventEmitter, Prop, h } from "@stencil/core";

@Component({
  tag: "c-btn-light",
  styleUrl: "c-btn-light.css",
})
export class CBtnLight {
  @Prop() name: string;
  @Prop() label: string;
  @Prop() actionLabel: string;
  @Prop() isInAction: boolean;
  @Prop() isDisabled: boolean;

  @Event({
    eventName: "resend-verification-code-event",
    bubbles: true,
  })
  resendVerificationCodeClickEvent: EventEmitter;

  @Event({
    eventName: "resend-password-reset-code-event",
    bubbles: true,
  })
  resendPasswordResetCodeClickEvent: EventEmitter;

  @Event({
    eventName: "back",
    bubbles: true,
  })
  backBtnClickEvent: EventEmitter;

  @Event({
    eventName: "profile-edit-close",
    bubbles: true,
  })
  profileEditCloseEvent: EventEmitter;

  handleBtnClick(event) {
    event.preventDefault();
    if (this.name === "resendVerificationCodeBtn") {
      this.resendVerificationCodeClickEvent.emit();
    } else if (this.name === "resendpasswordresetcode") {
      this.resendPasswordResetCodeClickEvent.emit();
    } else if (this.name === "back") {
      this.backBtnClickEvent.emit();
    } else if (this.name === "closeProfileEdits") {
      this.profileEditCloseEvent.emit();
    }
  }

  render() {
    return (
      <button
        class={this.isDisabled ? "disabled" : ""}
        disabled={this.isDisabled}
        onClick={(event) => this.handleBtnClick(event)}
      >
        {" "}
        {this.isInAction ? <c-spinner-dark></c-spinner-dark> : this.label}
      </button>
    );
  }
}
