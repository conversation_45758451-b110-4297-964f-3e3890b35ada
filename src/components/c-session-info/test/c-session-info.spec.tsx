import { newSpecPage } from '@stencil/core/testing';
import { CSessionInfo } from '../c-session-info';

describe('c-session-info', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CSessionInfo],
      html: `<c-session-info></c-session-info>`,
    });
    expect(page.root).toEqualHtml(`
      <c-session-info>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-session-info>
    `);
  });
});
