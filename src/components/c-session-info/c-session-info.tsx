import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  Prop,
  h,
} from "@stencil/core";

import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-session-info",
  styleUrl: "c-session-info.css",
})
export class CSessionInfo {
  /*------------------
  Event Emitters
  ------------------*/
  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Prop() data: any;

  @State() isDataFetched: boolean = false;

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    }
  }

  componentDidLoad() {
    this.fetchViewData();
  }

  private sessionTitle: string = "";
  private sessionParticipants: any = [];

  fetchViewData() {
    let payload = {
      ticketId: this.data.ticketId,
      sessionId: this.data.sessionId,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-session-details`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.sessionTitle = response.data.payload.sessionTitle;
          this.sessionParticipants = response.data.payload.sessionParticipants;
          this.isDataFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="modal__container">
        <c-row>
          {this.isDataFetched ? (
            <c-text>{this.sessionTitle}</c-text>
          ) : (
            <c-text>Fetching session details..</c-text>
          )}
          <c-button
            type="modalClose"
            name="closeModal"
            icon-name=""
            label=""
          ></c-button>
        </c-row>
        <c-card>
          {this.isDataFetched ? (
            this.sessionParticipants.length > 0 ? (
              <ul>
                <c-text type="subtext">PARTICIPANT LIST</c-text>
                {this.sessionParticipants.map((participant: any) => (
                  <li>
                    <c-text>{participant.name}</c-text>
                    <c-link>{participant.email}</c-link>
                  </li>
                ))}
              </ul>
            ) : (
              <e-text>There are no participants yet</e-text>
            )
          ) : (
            <ul>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>
              </li>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>{" "}
              </li>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>{" "}
              </li>
              <li>
                <c-skel-line color="gray" width={50}></c-skel-line>
                <c-skel-line color="gray" width={25}></c-skel-line>{" "}
              </li>
            </ul>
          )}
        </c-card>
        {this.isDataFetched && (
          <footer>
            <c-text-link
              url={`/instructor/${this.data.ticketId}/${this.data.sessionId}`}
              label="Share this list with instructors →"
            ></c-text-link>
          </footer>
        )}
      </div>
    );
  }
}
