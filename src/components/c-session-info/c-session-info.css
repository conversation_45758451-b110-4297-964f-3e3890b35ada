.modal__container {
  width: 100%;
  background: var(--bg-color);
  z-index: 9999;
  position: absolute;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0.25em;
  padding: 1.5em;
  box-sizing: border-box;
}

.modal__container c-card .basic-card-container {
  background: white;
  padding: 1.5em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
}

.modal__container c-text .subtext {
  margin: 0;
  padding: 0;
}

.modal__container c-card ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.modal__container c-card ul li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1em;
  padding-bottom: 1em;
}

.modal__container c-card ul li c-skel-line:first-child .skel-line {
  margin-bottom: 1em;
}

.modal__container c-card ul li:first-child {
  padding-top: 0;
}

.modal__container c-card ul li:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}

.modal__container c-row .row-container {
  margin-bottom: 1em;
}

.modal__container c-button .modal-close {
  padding: 0;
}

.modal__container footer {
  margin-top: 2em;
  display: flex;
  justify-content: space-around;
}

@media only screen and (max-width: 768px) {
  .modal__container {
    position: fixed;
    width: 90%;
    padding: 1em;
    top: 3em;
    max-height: 85vh;
    overflow: scroll;
  }

  .modal__container c-card .basic-card-container {
    padding: 1em;
  }

  .modal__container .row-container {
    align-items: unset;
  }
}
