import { Component, Prop, FunctionalComponent, Watch, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-ticket-full-ticket",
  styleUrl: "c-ticket-full-ticket.css",
})
export class CTicketFullTicket {
  /*---
  Props
  ----*/
  @Prop() ticketId: string;
  @Prop() mode: string;
  @Prop() type: string;
  @Prop() ticketTitle: string;
  @Prop() persona: string;
  @Prop() tierString: string;
  @Prop() isVisible: boolean;
  @Prop() isDisabled: boolean;
  @Prop() isPrimary: boolean;
  @Prop() isPrimaryDependent: boolean;
  @Prop() isConfigMode: boolean = false;
  @Prop() isTicketInCart: boolean = false;
  @Prop() isTicketPurchased: boolean = false;
  @Prop() purchaseStatus: string = "";

  /*------
  Watchers
  ------*/
  @Watch("isTicketInCart") isTicketInCartWatcher(
    newVal: boolean,
    oldVal: boolean
  ) {
    if (newVal != oldVal) {
      state.isPrimaryTicketInCart = this.isTicketInCart;
    }
  }

  /*-------
  Variables
  -------*/
  private tierName: string = "";
  private tierEndDate: string = "";
  private tierStudentPrice: number = 0;
  private tierProfessionalPrice: number = 0;

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    this.setGlobalStates();
    this.generatePriceFromTierDetails();
  }

  generatePriceFromTierDetails() {
    let tierDetails = JSON.parse(this.tierString);
    this.tierName = tierDetails.name;
    this.tierEndDate = tierDetails.endDate;
    this.tierStudentPrice = tierDetails.studentPrice;
    this.tierProfessionalPrice = tierDetails.professionalPrice;
  }

  setGlobalStates() {
    state.isFullTicketPurchased = this.isTicketPurchased;
    state.isPrimaryTicketInCart = this.isTicketInCart;
    state.isPrimaryTicketPurchased = this.isTicketPurchased;
  }

  /*-------------------
  Functional Components
  -------------------*/
  AdminFullTicketView: FunctionalComponent = () => (
    <div class="ticket-container">
      <div class="ticket-container__details">
        <h1>{this.ticketTitle}</h1>
        <div class="ticket-container__details__perks">
          <c-text>✅ Full access on all event days</c-text>
          <c-text>✅ Includes lunch</c-text>
          {state.eventCodeForRegistration ===
          "hci-for-social-good-eccc4fb3-19b6-4608-85e4-9e731edd10ce" ? (
            <c-text>✅ Free workshop access</c-text>
          ) : (
            <c-text>⚠️ Does not include workshops/courses</c-text>
          )}
          <c-text>⚠️ Does not include accomodation</c-text>
        </div>
      </div>
      <div class="ticket-container__price">
        <div class="ticket-container__price__tier-name">
          <c-text type="subtext">{this.tierName.toUpperCase()}</c-text>
        </div>
        <div class="ticket-container__price__item">
          <c-text type="heading">₹{this.tierStudentPrice}</c-text>
          <p class="bubble green-bubble">Student</p>
        </div>
        <div class="ticket-container__price__item">
          <c-text type="heading">₹{this.tierProfessionalPrice}</c-text>
          <p class="bubble blue-bubble">Professional</p>
        </div>
        <div class="ticket-container__price__deadline">
          <c-text type="subtext">
            Prices will increase on{" "}
            {new Date(this.tierEndDate).toString().substring(4, 15)} (IST)
          </c-text>
        </div>
        <div class="ticket-container__price__footer">
          <c-button type="ghost_Danger_Small_onDark" name="deleteFullTicket">
            Delete
          </c-button>
          <c-button type="ghost_Small_onDark" name="editFullTicket">
            Edit
          </c-button>
        </div>
      </div>
    </div>
  );

  DefaultFullTicketView: FunctionalComponent = () => (
    <div class="ticket-container">
      <div class="ticket-container__details">
        <h1>{this.ticketTitle}</h1>
        <div class="ticket-container__details__perks">
          {state.eventCodeForRegistration ===
          "post-chi-2025-5d6e4f92-f05e-43b0-87a3-1d5a5d9204b5" ? (
            <c-text>✅ Full access on the event day</c-text>
          ) : (
            <c-text>✅ Full access on all event days</c-text>
          )}
          {state.eventCodeForRegistration !=
          "post-chi-2025-5d6e4f92-f05e-43b0-87a3-1d5a5d9204b5" && (
            <c-text>✅ Includes lunch</c-text>
          )}
          {state.eventCodeForRegistration !=
          "post-chi-2025-5d6e4f92-f05e-43b0-87a3-1d5a5d9204b5" && (
            <c-text>⚠️ Does not include workshops/courses</c-text>
          )}
          <c-text>⚠️ Does not include accomodation</c-text>
          {state.eventCodeForRegistration ===
          "post-chi-2025-5d6e4f92-f05e-43b0-87a3-1d5a5d9204b5" && (
            <div><br/><br/></div>
          )}
        </div>
      </div>
      <div class="ticket-container__price">
        <div class="ticket-container__price__tier-name">
          <c-text type="subtext">{this.tierName.toUpperCase()}</c-text>
        </div>
        {this.persona === "student" && (
          <div class="ticket-container__price__item">
            <c-text type="heading">₹{this.tierStudentPrice}</c-text>
            <p class="bubble green-bubble">Student</p>
          </div>
        )}
        {this.persona === "professional" && (
          <div class="ticket-container__price__item">
            <c-text type="heading">₹{this.tierProfessionalPrice}</c-text>
            <p class="bubble blue-bubble">Professional</p>
          </div>
        )}

        <div class="ticket-container__price__deadline">
          {!this.isTicketPurchased && (
            <div>
              {state.eventCodeForRegistration != "post-chi-2025-5d6e4f92-f05e-43b0-87a3-1d5a5d9204b5" && 
              <div>
                <c-text type="subtext">
                  Price includes GST
                  <br />
                  This price will increase on{" "}
                  {new Date(this.tierEndDate).toString().substring(4, 15)} (IST)
                </c-text>
              </div>
              }
            </div>
          )}
        </div>
        <div class="ticket-container__price__footer">
          {this.isTicketPurchased ? (
            <div>
              {this.purchaseStatus === "purchased" && (
                <p class="ticket-purchased-confirmation">Purchased</p>
              )}
              {this.purchaseStatus === "under-verification" && (
                <p class="ticket-under-verification">Under verification</p>
              )}
            </div>
          ) : // <p class="ticket-purchased-confirmation">Purchased</p>
          this.isTicketInCart ? (
            <c-button
              type="ghost_Danger_Small_onDark"
              name="removeFullTicketFromCart"
              value={this.ticketId}
            >
              Remove from cart
            </c-button>
          ) : (
            <c-button
              type="ghost_Small_onDark"
              name="addFullTicketToCart"
              value={this.ticketId}
            >
              Add to cart
            </c-button>
          )}
        </div>
      </div>
    </div>
  );

  render() {
    return (
      <c-card>
        {this.isConfigMode ? (
          <this.AdminFullTicketView></this.AdminFullTicketView>
        ) : (
          <this.DefaultFullTicketView></this.DefaultFullTicketView>
        )}
      </c-card>
    );
  }
}
