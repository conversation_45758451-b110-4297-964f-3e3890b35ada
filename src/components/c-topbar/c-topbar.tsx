import { Component, Listen, Prop, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-topbar",
  styleUrl: "c-topbar.css",
})
export class CTopbar {
  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "openMobileMenu") {
      state.isMobileMenuOpen = true;
    }
  }

  @Prop() logoType: string;
  render() {
    return (
      <header>
        <c-row>
          <c-site-logo
            link="https://indiahci.org"
            src="https://res.cloudinary.com/layerpark/image/upload/v1619012555/hcipai-transparent-logo.png"
          ></c-site-logo>
          <div class="hide-on-mobile">
            <c-account-control></c-account-control>
          </div>
          <div class="show-on-mobile">
            <c-button type="ghost_Small" name="openMobileMenu">
              <ion-icon name="menu-outline"></ion-icon>
            </c-button>
          </div>
        </c-row>
      </header>
    );
  }
}
