c-topbar header {
  position: fixed;
  width: 98%;
  top: 1em;
}

c-topbar .show-on-mobile {
  display: none;
}

/* CSS for responsive design */
@media only screen and (max-width: 768px) {
  c-topbar .hide-on-mobile {
    display: none;
  }

  c-topbar .show-on-mobile {
    display: block;
  }

  c-topbar header {
    /* width: 90%; */
    position: static;
    width: 100%;
    max-width: 350px;
  }

  c-topbar c-button .ghost-small {
    border: 0;
    padding: 0;
  }

  c-topbar c-button ion-icon {
    font-size: 2em;
  }
}
