import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  State,
  h,
} from "@stencil/core";
import { gsap } from "gsap";
import axios from "axios";
import state from "../../global/state";

interface ManagerBubbleInterface {
  name: string;
  email: string;
}

@Component({
  tag: "c-wizard-edit-event",
  styleUrl: "c-wizard-edit-event.css",
})
export class CWizardEditEvent {
  private eventName: string = "";
  private eventTagline: string = "";
  private eventVenueLabel: string = "";
  private eventVenueUrl: string = "";
  private eventWebsite: string = "";
  private eventStartDatetime: string = "";
  private eventStartDate: string = "";
  private eventStartTime: string = "";
  private eventEndDatetime: string = "";
  private eventEndDate: string = "";
  private eventEndTime: string = "";
  private eventInvoicePrefix: string = "";
  private eventLogoUrl: any;
  private eventBannerUrl: any;
  private eventPosterUrl: any;
  private registrationStartDatetime: string = "";
  private registrationStartDate: string = "";
  private registrationStartTime: string = "";
  private registrationEndDatetime: string = "";
  private registrationEndDate: string = "";
  private registrationEndTime: string = "";
  private fetchedRegistrantArr: any = [];
  private filteredRegistrantArr: any = [];
  private delaySearch: any;

  private newEventName: string = "";
  private newEventTagline: string = "";
  private newEventVenueLabel: string = "";
  private newEventVenueUrl: string = "";
  private newEventWebsite: string = "";
  private newEventStartDate: string = "";
  private newEventStartTime: string = "";
  private newEventEndDate: string = "";
  private newEventEndTime: string = "";
  private newEventLogo: any;
  private newEventBanner: any;
  private newEventPoster: any;
  private newRegistrationStartDate: string = "";
  private newRegistrationStartTime: string = "";
  private newRegistrationEndDate: string = "";
  private newRegistrationEndTime: string = "";
  // private initManagerList: any = [];

  private hasEventNameChanged: boolean = false;
  private hasEventTaglineChanged: boolean = false;
  private hasEventVenueLabelChanged: boolean = false;
  private hasEventVenueUrlChanged: boolean = false;
  private hasEventWebsiteChanged: boolean = false;
  private hasEventStartDateChanged: boolean = false;
  private hasEventStartTimeChanged: boolean = false;
  private hasEventEndDateChanged: boolean = false;
  private hasEventEndTimeChanged: boolean = false;
  private hasRegistrationStartDateChanged: boolean = false;
  private hasRegistrationStartTimeChanged: boolean = false;
  private hasRegistrationEndDateChanged: boolean = false;
  private hasRegistrationEndTimeChanged: boolean = false;
  private hasEventInvoicePrefixChanged: boolean = false;
  private hasEventLogoChanged: boolean = false;
  private hasEventBannerChanged: boolean = false;
  private hasEventPosterChanged: boolean = false;
  private hasEventManagersChanged: boolean = false;

  private editOpts: any = [
    {
      type: "navItem",
      name: "eventBasic",
      label: "Basic",
      state: "active",
      icon: "",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "eventSchedule",
      label: "Schedule",
      state: "",
      icon: "",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "eventInvoicing",
      label: "Invoicing",
      state: "",
      icon: "",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "eventAssets",
      label: "Assets",
      state: "",
      icon: "",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "eventManagers",
      label: "Managers",
      state: "",
      icon: "",
      subText: "",
      route: "",
    },
  ];

  /* -------------
  COMPONENT STATES
  ------------- */
  @State() managers: any = [];
  @State() isRegistrantsFetched: boolean = false;
  @State() registrantArr: any = [];
  @State() isRegistrantNameFilled: boolean = false;
  @State() invoicePrefix: string = "";
  @State() newInvoicePrefix: string = "";
  @State() progressBarText: string = "Preparing..";
  @State() activeEditOption: string = this.editOpts[0].name;
  @State() isEventDataFetched: boolean = false;
  @State() isUpdatingEventDetails: boolean = false;
  @State() unsavedChanges: any = [];
  @State() isBasicUpdateButtonDisabled: boolean = true;
  @State() isScheduleUpdateButtonDisabled: boolean = true;
  @State() isInvoicingUpdateButtonDisabled: boolean = true;
  @State() isAssetsUpdateButtonDisabled: boolean = true;
  @State() isManagersUpdateButtonDisabled: boolean = true;
  @State() isBasicUpdateButtonInAction: boolean = false;
  @State() isScheduleUpdateButtonInAction: boolean = false;
  @State() isInvoicingUpdateButtonInAction: boolean = false;
  @State() isAssetsUpdateButtonInAction: boolean = false;
  @State() isManagersUpdateButtonInAction: boolean = false;
  @State() resetUploader: boolean = false;

  progressBarEl!: HTMLDivElement;
  notificationEL!: HTMLDivElement;
  disableFieldContainer!: HTMLDivElement;
  wizardNavigation!: HTMLDivElement;

  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Event({
    eventName: "fetchNewData",
    bubbles: true,
  })
  fetchNewDataEvent: EventEmitter;

  @Event({
    eventName: "eventEdited",
    bubbles: true,
  })
  eventEditedEvent: EventEmitter;

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(e) {
    if (e.detail.navName === "editEventWizard") {
      this.activeEditOption = e.detail.cNavItemName;
    }
  }

  @Listen("buttonClick")
  handleButtonClick(e) {
    if (e.detail.name === "closeModal") {
      state.editEventCode = "";
      state.editEventName = "";
      this.closeModalEvent.emit();
    } else if (e.detail.name === "updateBasicDetails") {
      this.updateBasicDetails();
    } else if (e.detail.name === "updateSchedule") {
      this.updateSchedule();
    } else if (e.detail.name === "updateInvoicing") {
      this.updateInvoicing();
    } else if (e.detail.name === "updateAssets") {
      this.updateAssets();
    } else if (e.detail.name === "updateManagers") {
      this.updateManagers();
    }
  }

  updateBasicDetails() {
    let basicDetailsUpdateObj: any = {
      eventCode: state.editEventCode,
      eventName: this.newEventName ? this.newEventName : this.eventName,
      eventTagline: this.newEventTagline
        ? this.newEventTagline
        : this.eventTagline,
      eventVenueLabel: this.newEventVenueLabel
        ? this.newEventVenueLabel
        : this.eventVenueLabel,
      eventVenueUrl: this.newEventVenueUrl
        ? this.newEventVenueUrl
        : this.eventVenueUrl,
      eventWebsite: this.newEventWebsite
        ? this.newEventWebsite
        : this.eventWebsite,
    };

    this.isBasicUpdateButtonInAction = true;
    gsap.to([this.disableFieldContainer, this.wizardNavigation], {
      opacity: 0.3,
      pointerEvents: "none",
      duration: 0.25,
    });

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/editeventbasics`,
      data: basicDetailsUpdateObj,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchEventDetails();
          this.eventEditedEvent.emit();
          this.isBasicUpdateButtonInAction = false;
          this.removeFromUnsavedChangesArray("Basic");
          gsap.to(this.notificationEL, { opacity: 1, duration: 0.25 });
          gsap.to([this.disableFieldContainer, this.wizardNavigation], {
            opacity: 1,
            pointerEvents: "auto",
            duration: 0.25,
          });
          setTimeout(() => {
            gsap.to(this.notificationEL, {
              opacity: 0,
              duration: 0.25,
            });
          }, 3000);
        }
      })
      .catch((error: any) => {
        alert(error);
      });
  }

  updateSchedule() {
    let scheduleUpdateObj: any = {
      eventCode: state.editEventCode,
      eventStartDate: this.newEventStartDate
        ? this.newEventStartDate
        : this.eventStartDate,
      eventStartTime: this.newEventStartTime
        ? this.newEventStartTime
        : this.eventStartTime,
      eventEndDate: this.newEventEndDate
        ? this.newEventEndDate
        : this.eventEndDate,
      eventEndTime: this.newEventEndTime
        ? this.newEventEndTime
        : this.eventEndTime,
      registrationStartDate: this.newRegistrationStartDate
        ? this.newRegistrationStartDate
        : this.registrationStartDate,
      registrationStartTime: this.newRegistrationStartTime
        ? this.newRegistrationStartTime
        : this.registrationStartTime,
      registrationEndDate: this.newRegistrationEndDate
        ? this.newRegistrationEndDate
        : this.registrationEndDate,
      registrationEndTime: this.newRegistrationEndTime
        ? this.newRegistrationEndTime
        : this.registrationEndTime,
    };

    this.isScheduleUpdateButtonInAction = true;
    gsap.to([this.disableFieldContainer, this.wizardNavigation], {
      opacity: 0.3,
      pointerEvents: "none",
      duration: 0.25,
    });

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/editeventschedule`,
      data: scheduleUpdateObj,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchEventDetails();
          this.eventEditedEvent.emit();
          this.isScheduleUpdateButtonInAction = false;
          this.removeFromUnsavedChangesArray("Schedule");
          gsap.to(this.notificationEL, { opacity: 1, duration: 0.25 });
          gsap.to([this.disableFieldContainer, this.wizardNavigation], {
            opacity: 1,
            pointerEvents: "auto",
            duration: 0.25,
          });
          setTimeout(() => {
            gsap.to(this.notificationEL, {
              opacity: 0,
              duration: 0.25,
            });
          }, 3000);
        }
      })
      .catch((error: any) => {
        alert(error);
      });
  }

  updateInvoicing() {
    let invoiceUpdateObj: any = {
      eventCode: state.editEventCode,
      invoicePrefix: this.newInvoicePrefix
        ? this.newInvoicePrefix
        : this.invoicePrefix,
    };

    this.isInvoicingUpdateButtonInAction = true;
    gsap.to([this.disableFieldContainer, this.wizardNavigation], {
      opacity: 0.3,
      pointerEvents: "none",
      duration: 0.25,
    });

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/editeventinvoicing`,
      data: invoiceUpdateObj,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchEventDetails();
          this.eventEditedEvent.emit();
          this.isInvoicingUpdateButtonInAction = false;
          this.removeFromUnsavedChangesArray("Invoicing");
          gsap.to(this.notificationEL, { opacity: 1, duration: 0.25 });
          gsap.to([this.disableFieldContainer, this.wizardNavigation], {
            opacity: 1,
            pointerEvents: "auto",
            duration: 0.25,
          });
          setTimeout(() => {
            gsap.to(this.notificationEL, {
              opacity: 0,
              duration: 0.25,
            });
          }, 3000);
        }
      })
      .catch((error: any) => {
        alert(error);
      });
  }

  updateAssets() {
    let formData: any = new FormData();
    formData.append("eventCode", state.editEventCode);

    if (this.newEventLogo) {
      formData.append("eventLogo", this.newEventLogo);
    }
    if (this.newEventBanner) {
      formData.append("eventBanner", this.newEventBanner);
    }
    if (this.newEventPoster) {
      formData.append("eventPoster", this.newEventPoster);
    }

    this.isAssetsUpdateButtonInAction = true;
    this.resetUploader = true;

    gsap.to([this.disableFieldContainer, this.wizardNavigation], {
      opacity: 0.3,
      pointerEvents: "none",
      duration: 0.25,
    });

    axios({
      method: "POST",
      data: formData,
      baseURL: `${state.baseUrl}/editeventassets`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          gsap.to(this.notificationEL, { opacity: 1, duration: 0.25 });
          gsap.to([this.disableFieldContainer, this.wizardNavigation], {
            opacity: 1,
            pointerEvents: "auto",
            duration: 0.25,
          });
          this.fetchEventDetails();
          this.eventEditedEvent.emit();
          this.isAssetsUpdateButtonInAction = false;
          this.resetUploader = false;
          this.removeFromUnsavedChangesArray("Assets");
          setTimeout(() => {
            gsap.to(this.notificationEL, {
              opacity: 0,
              duration: 0.25,
            });
          }, 3000);
        }
      })
      .catch((error: any) => {
        alert(error);
      });
  }

  updateManagers() {
    let managerUpdateObj: any = {
      eventCode: state.editEventCode,
      managers: this.managers,
    };

    this.isManagersUpdateButtonInAction = true;
    gsap.to([this.disableFieldContainer, this.wizardNavigation], {
      opacity: 0.3,
      pointerEvents: "none",
      duration: 0.25,
    });

    axios({
      method: "POST",
      data: managerUpdateObj,
      baseURL: `${state.baseUrl}/editeventmanagers`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response: any) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchEventDetails();
          this.eventEditedEvent.emit();
          this.isManagersUpdateButtonInAction = false;
          this.removeFromUnsavedChangesArray("eventManagers");
          gsap.to(this.notificationEL, { opacity: 1, duration: 0.25 });
          gsap.to([this.disableFieldContainer, this.wizardNavigation], {
            opacity: 1,
            pointerEvents: "auto",
            duration: 0.25,
          });
          setTimeout(() => {
            gsap.to(this.notificationEL, {
              opacity: 0,
              duration: 0.25,
            });
          }, 3000);
        }
      })
      .catch((error: any) => {
        alert(error);
      });
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "eventStart") {
      this.newEventStartDate = e.detail.value;
      this.newEventStartDate != this.eventStartDate
        ? (this.hasEventStartDateChanged = true)
        : (this.hasEventStartDateChanged = false);
    } else if (e.detail.name === "eventEnd") {
      this.newEventEndDate = e.detail.value;
      this.newEventEndDate != this.eventEndDate
        ? (this.hasEventEndDateChanged = true)
        : (this.hasEventEndDateChanged = false);
    } else if (e.detail.name === "registrationStart") {
      this.newRegistrationStartDate = e.detail.value;
      this.newRegistrationStartDate != this.registrationStartDate
        ? (this.hasRegistrationStartDateChanged = true)
        : (this.hasRegistrationStartDateChanged = false);
    } else if (e.detail.name === "registrationEnd") {
      this.newRegistrationEndDate = e.detail.value;
      this.newRegistrationEndDate != this.registrationEndDate
        ? (this.hasRegistrationEndDateChanged = true)
        : (this.hasRegistrationEndDateChanged = false);
    }
    this.changeUpdateButtonState();
  }

  @Listen("timeInput") handleTimeInput(e) {
    if (e.detail.name === "eventStart") {
      this.newEventStartTime = e.detail.value;
      this.newEventStartTime != this.eventStartTime
        ? (this.hasEventStartTimeChanged = true)
        : (this.hasEventStartTimeChanged = false);
    } else if (e.detail.name === "eventEnd") {
      this.newEventEndTime = e.detail.value;
      this.newEventEndTime != this.eventEndTime
        ? (this.hasEventEndTimeChanged = true)
        : (this.hasEventEndTimeChanged = false);
    } else if (e.detail.name === "registrationStart") {
      this.newRegistrationStartTime = e.detail.value;
      this.newRegistrationStartTime != this.registrationStartTime
        ? (this.hasRegistrationStartTimeChanged = true)
        : (this.hasRegistrationStartTimeChanged = false);
    } else if (e.detail.name === "registrationEnd") {
      this.newRegistrationEndTime = e.detail.value;
      this.newRegistrationEndTime != this.registrationEndTime
        ? (this.hasRegistrationEndTimeChanged = true)
        : (this.hasRegistrationEndTimeChanged = false);
    }
    this.changeUpdateButtonState();
  }

  @Listen("textInput") handleTextInput(e) {
    if (e.detail.name === "eventName") {
      this.newEventName = e.detail.value;
      this.newEventName != this.eventName
        ? (this.hasEventNameChanged = true)
        : (this.hasEventNameChanged = false);
    } else if (e.detail.name === "eventTagline") {
      this.newEventTagline = e.detail.value;
      this.newEventTagline != this.eventTagline
        ? (this.hasEventTaglineChanged = true)
        : (this.hasEventTaglineChanged = false);
    } else if (e.detail.name === "eventVenueLabel") {
      this.newEventVenueLabel = e.detail.value;
      this.newEventVenueLabel != this.eventVenueLabel
        ? (this.hasEventVenueLabelChanged = true)
        : (this.hasEventVenueLabelChanged = false);
    } else if (e.detail.name === "eventVenueUrl") {
      this.newEventVenueUrl = e.detail.value;
      this.newEventVenueUrl != this.eventVenueUrl
        ? (this.hasEventVenueUrlChanged = true)
        : (this.hasEventVenueUrlChanged = false);
    } else if (e.detail.name === "eventWebsite") {
      this.newEventWebsite = e.detail.value;
      this.newEventWebsite != this.eventWebsite
        ? (this.hasEventWebsiteChanged = true)
        : (this.hasEventWebsiteChanged = false);
    } else if (e.detail.name === "invoicePrefix") {
      this.newInvoicePrefix = e.detail.value;
      this.newInvoicePrefix != this.eventInvoicePrefix
        ? (this.hasEventInvoicePrefixChanged = true)
        : (this.hasEventInvoicePrefixChanged = false);
    } else if (e.detail.name === "registrantSearch") {
      this.isRegistrantNameFilled = true;
      if (e.detail.value.length > 0) {
        this.generateRegistrantListFromSearchString(e.detail.value);
      } else {
        this.registrantArr = [];
      }
    }
    this.changeUpdateButtonState();
  }

  @Listen("managerSelected") managerSelectedHandler(e) {
    let obj = {
      fullName: e.detail.managerName,
      email: e.detail.managerEmail,
    };
    let isManagerInArray: boolean = false;
    this.managers.map((manager: any) => {
      if (obj.email === manager.email) {
        isManagerInArray = true;
      }
    });
    if (!isManagerInArray) {
      this.managers.push(obj);
    }
    this.clearManagerSearch();
    this.checkIfManagersChanged();
  }

  @Listen("fileChangeEvent")
  fileChangeEventHandler(event) {
    if (event.detail.name === "eventLogo") {
      this.newEventLogo = event.detail.file;
      this.hasEventLogoChanged = true;
    } else if (event.detail.name === "eventBanner") {
      this.newEventBanner = event.detail.file;
      this.hasEventBannerChanged = true;
    } else if (event.detail.name === "eventPoster") {
      this.newEventPoster = event.detail.file;
      this.hasEventPosterChanged = true;
    }
    this.changeUpdateButtonState();
  }

  @Listen("fileRemovedEvent")
  fileRemovedEventHandler(event) {
    if (event.detail.name === "eventLogo") {
      this.newEventLogo = "";
      this.hasEventLogoChanged = false;
    } else if (event.detail.name === "eventBanner") {
      this.newEventBanner = "";
      this.hasEventBannerChanged = false;
    } else if (event.detail.name === "eventPoster") {
      this.newEventPoster = "";
      this.hasEventPosterChanged = false;
    }
    this.changeUpdateButtonState();
  }

  changeUpdateButtonState() {
    if (this.activeEditOption === "eventBasic") {
      if (
        this.hasEventNameChanged ||
        this.hasEventTaglineChanged ||
        this.hasEventVenueLabelChanged ||
        this.hasEventVenueUrlChanged ||
        this.hasEventWebsiteChanged
      ) {
        this.pushIntoUnsavedChangesArray("Basic");
        this.isBasicUpdateButtonDisabled = false;
      } else {
        this.removeFromUnsavedChangesArray("Basic");
        this.isBasicUpdateButtonDisabled = true;
      }
    } else if (this.activeEditOption === "eventSchedule") {
      if (
        this.hasEventStartDateChanged ||
        this.hasEventEndDateChanged ||
        this.hasRegistrationStartDateChanged ||
        this.hasRegistrationEndDateChanged ||
        this.hasEventStartTimeChanged ||
        this.hasEventEndTimeChanged ||
        this.hasRegistrationStartTimeChanged ||
        this.hasRegistrationEndTimeChanged
      ) {
        this.pushIntoUnsavedChangesArray("Schedule");
        this.isScheduleUpdateButtonDisabled = false;
      } else {
        this.removeFromUnsavedChangesArray("Schedule");
        this.isScheduleUpdateButtonDisabled = true;
      }
    } else if (this.activeEditOption === "eventInvoicing") {
      if (this.hasEventInvoicePrefixChanged) {
        this.pushIntoUnsavedChangesArray("Invoicing");
        this.isInvoicingUpdateButtonDisabled = false;
      } else {
        this.removeFromUnsavedChangesArray("Invoicing");
        this.isInvoicingUpdateButtonDisabled = true;
      }
    } else if (this.activeEditOption === "eventAssets") {
      if (
        this.hasEventLogoChanged ||
        this.hasEventBannerChanged ||
        this.hasEventPosterChanged
      ) {
        this.pushIntoUnsavedChangesArray("Assets");
        this.isAssetsUpdateButtonDisabled = false;
      } else {
        this.removeFromUnsavedChangesArray("Assets");
        this.isAssetsUpdateButtonDisabled = true;
      }
    } else if (this.activeEditOption === "eventManagers") {
      if (this.hasEventManagersChanged) {
        this.pushIntoUnsavedChangesArray("Managers");
        this.isManagersUpdateButtonDisabled = false;
      } else {
        this.removeFromUnsavedChangesArray("Managers");
        this.isManagersUpdateButtonDisabled = true;
      }
    }
    this.unsavedChanges = [...this.unsavedChanges];
  }

  pushIntoUnsavedChangesArray(itemName: string) {
    let isItemInArray: boolean = false;
    this.unsavedChanges.map((change: any) => {
      if (change === itemName) {
        isItemInArray = true;
      }
    });
    if (!isItemInArray) this.unsavedChanges.push(itemName);
  }

  removeFromUnsavedChangesArray(itemName: string) {
    this.unsavedChanges = this.unsavedChanges.filter((e) => e !== itemName);
  }

  componentDidLoad() {
    this.fetchRegistrants();
    this.fetchEventDetails();
  }

  checkIfManagersChanged() {
    // if (this.managers.length != this.initManagerList.length) {
    //   this.hasEventManagersChanged = true;
    // } else {
    //   let duplicateCount: number = 0;
    //   this.managers.map((manager: any) => {
    //     this.initManagerList.map((initManager: any) => {
    //       if (manager.email === initManager.email) {
    //         duplicateCount = duplicateCount + 1;
    //       }
    //     });
    //   });
    //   console.log(`duplicateCount: ${duplicateCount}`);
    //   if (duplicateCount > 0) {
    //     this.hasEventManagersChanged = false;
    //   } else {
    //     this.hasEventManagersChanged = true;
    //   }
    // }
    this.hasEventManagersChanged = true;
    this.changeUpdateButtonState();
  }

  clearManagerSearch() {
    this.registrantArr = [];
    this.isRegistrantNameFilled = false;
  }

  fetchRegistrants() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/get-all-accounts`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.fetchedRegistrantArr = response.data.payload;
          this.isRegistrantsFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  fetchEventDetails() {
    let payload = {
      eventCode: state.editEventCode,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/geteventbycode`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.eventName = response.data.payload.name;
          this.eventTagline = response.data.payload.tagline;
          this.eventVenueLabel = response.data.payload.venueLabel;
          this.eventVenueUrl = response.data.payload.venueUrl;
          this.eventWebsite = response.data.payload.website;
          this.eventLogoUrl = response.data.payload.logoUrl;
          this.eventBannerUrl = response.data.payload.bannerUrl;
          this.eventPosterUrl = response.data.payload.posterUrl;
          this.eventInvoicePrefix = response.data.payload.invoicePrefix;
          this.eventStartDatetime = response.data.payload.startsOn;
          this.eventStartDate = this.generateDateString(
            this.eventStartDatetime
          );
          this.eventStartTime = this.generateTimeString(
            this.eventStartDatetime
          );
          this.eventEndDatetime = response.data.payload.endsOn;
          this.eventEndDate = this.generateDateString(this.eventEndDatetime);
          this.eventEndTime = this.generateTimeString(this.eventEndDatetime);
          this.registrationStartDatetime =
            response.data.payload.registrationStartsOn;
          this.registrationStartDate = this.generateDateString(
            this.registrationStartDatetime
          );
          this.registrationStartTime = this.generateTimeString(
            this.registrationStartDatetime
          );
          this.registrationEndDatetime =
            response.data.payload.registrationEndsOn;
          this.registrationEndDate = this.generateDateString(
            this.registrationEndDatetime
          );
          this.registrationEndTime = this.generateTimeString(
            this.registrationEndDatetime
          );
          this.managers = response.data.payload.managers;
          this.managers = [...this.managers];
          // this.initManagerList = this.managers;
          this.isEventDataFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  generateDateString(isoString: string) {
    let date = new Date(isoString);
    let yearNo: number = date.getFullYear();
    let monthNo: number = date.getMonth() + 1;
    let dayNo: number = date.getDate();

    let year: string = "";
    let month: string = "";
    let day: string = "";

    if (dayNo < 10) {
      day = `0${dayNo}`;
    } else {
      day = `${dayNo}`;
    }

    if (monthNo < 10) {
      month = `0${monthNo}`;
    } else {
      month = `${monthNo}`;
    }

    year = `${yearNo}`;

    return `${year}-${month}-${day}`;
  }

  generateTimeString(isoString: string) {
    let date = new Date(isoString);
    let options = { hour12: false };
    let timeString: string = date.toLocaleTimeString("en-IN", options);
    timeString = timeString.substring(0, 5);
    return timeString;
  }

  handleDataFetchSuccess() {
    this.fetchNewDataEvent.emit();
    this.closeModalEvent.emit();
  }

  handleDataFetchFailure() {
    alert("Failed to create event. Please try again");
    this.initComponent();
  }

  initComponent() {
    this.managers = [];
    this.isRegistrantsFetched = false;
    this.registrantArr = [];
    this.isRegistrantNameFilled = false;
    this.invoicePrefix = "";
    this.progressBarText = "Preparing..";
    this.eventName = "";
    this.eventTagline = "";
    this.eventVenueLabel = "";
    this.eventVenueUrl = "";
    this.eventWebsite = "";
    this.eventStartDate = "";
    this.eventStartTime = "";
    this.eventEndDate = "";
    this.eventEndTime = "";
    this.eventLogoUrl = "";
    this.eventBannerUrl = "";
    this.eventPosterUrl = "";
    this.registrationStartDate = "";
    this.registrationStartTime = "";
    this.registrationEndDate = "";
    this.registrationEndTime = "";
    this.fetchedRegistrantArr = [];
    this.filteredRegistrantArr = [];

    this.isBasicUpdateButtonDisabled = false;
    this.isScheduleUpdateButtonDisabled = false;
    this.isInvoicingUpdateButtonDisabled = false;
    this.isAssetsUpdateButtonDisabled = false;
    this.isManagersUpdateButtonDisabled = false;
  }

  generateRegistrantListFromSearchString(searchString: string) {
    clearTimeout(this.delaySearch);
    this.delaySearch = setTimeout(() => {
      this.filteredRegistrantArr = this.fetchedRegistrantArr.filter(
        (registrant) => {
          let fullName = registrant.firstName + " " + registrant.lastName;
          return (
            registrant.firstName
              .toLowerCase()
              .includes(searchString.toLowerCase()) ||
            registrant.lastName
              .toLowerCase()
              .includes(searchString.toLowerCase()) ||
            registrant.email
              .toLowerCase()
              .includes(searchString.toLowerCase()) ||
            fullName.toLowerCase().includes(searchString.toLowerCase())
          );
        }
      );
      this.registrantArr = "";
      this.registrantArr = this.filteredRegistrantArr;
    }, 500);
  }

  removeManager(email: string) {
    let buffManagers: any = [];
    this.managers.map((manager) => {
      if (manager.email != email) {
        buffManagers.push(manager);
      }
    });
    this.managers = buffManagers;
    this.checkIfManagersChanged();
  }

  updateDetails(type: string) {
    console.log(`${type} details save!`);
  }

  BasicStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div
        class="disable-field-container"
        ref={(el) => (this.disableFieldContainer = el as HTMLInputElement)}
      >
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            NAME
          </c-text>
          <c-textbox
            input-type="text"
            name="eventName"
            placeholder={`e.g. India HCI ${new Date().getFullYear() + 1}`}
            isDisabled={false}
            value={this.eventName}
          ></c-textbox>
        </div>
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            TAGLINE
          </c-text>
          <c-textbox
            input-type="text"
            name="eventTagline"
            placeholder={`e.g. Design for disruption`}
            isDisabled={false}
            value={this.eventTagline}
          ></c-textbox>
        </div>
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            VENUE
          </c-text>
          <div class="double-textbox">
            <c-textbox
              input-type="text"
              name="eventVenueLabel"
              placeholder={`Name e.g. IIT Bombay`}
              isDisabled={false}
              value={this.eventVenueLabel}
            ></c-textbox>
            <c-textbox
              input-type="text"
              name="eventVenueUrl"
              placeholder={`Google map url`}
              isDisabled={false}
              value={this.eventVenueUrl}
            ></c-textbox>
          </div>
        </div>
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            WEBSITE
          </c-text>
          <c-textbox
            input-type="text"
            name="eventWebsite"
            placeholder={`e.g. indiahci.org/2020`}
            isDisabled={false}
            value={this.eventWebsite}
          ></c-textbox>
        </div>
      </div>
      <div class="field-container">
        <div class="field-container-row">
          <this.Notification></this.Notification>
          <c-button
            name="updateBasicDetails"
            icon-name=""
            isInAction={this.isBasicUpdateButtonInAction}
            isDisabled={this.isBasicUpdateButtonDisabled}
          >
            Save
          </c-button>
        </div>
      </div>
    </div>
  );

  ScheduleStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div
        class="disable-field-container"
        ref={(el) => (this.disableFieldContainer = el as HTMLInputElement)}
      >
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            EVENT DATES
          </c-text>
          <div class="field-container-row">
            <c-date-picker
              name="eventStart"
              date={this.eventStartDate}
              time={this.eventStartTime}
            ></c-date-picker>
            <span class="mdash">&mdash;</span>
            <c-date-picker
              name="eventEnd"
              date={this.eventEndDate}
              time={this.eventEndTime}
            ></c-date-picker>
          </div>
        </div>
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            REGISTRATION DATES
          </c-text>
          <div class="field-container-row">
            <c-date-picker
              name="registrationStart"
              date={this.registrationStartDate}
              time={this.registrationStartTime}
            ></c-date-picker>
            <span class="mdash">&mdash;</span>
            <c-date-picker
              name="registrationEnd"
              date={this.registrationEndDate}
              time={this.registrationEndTime}
            ></c-date-picker>
          </div>
        </div>
      </div>

      <div class="field-container">
        <div class="field-container-row">
          <this.Notification></this.Notification>
          <c-button
            name="updateSchedule"
            icon-name=""
            isInAction={this.isBasicUpdateButtonInAction}
            isDisabled={this.isScheduleUpdateButtonDisabled}
          >
            Save
          </c-button>
        </div>
      </div>
    </div>
  );

  InvoicingStep: FunctionalComponent = () => (
    <div class="wizard-content">
      <div
        class="disable-field-container"
        ref={(el) => (this.disableFieldContainer = el as HTMLInputElement)}
      >
        <div class="field-container">
          <c-text type="modalLabel" isMandatory={true}>
            INVOICE PREFIX
          </c-text>
          <c-textbox
            input-type="text"
            name="invoicePrefix"
            placeholder={`e.g. IHCI${new Date().getFullYear() + 1}`}
            isDisabled={false}
            value={this.eventInvoicePrefix}
          ></c-textbox>
          <div class="subtext-container invoice-subtext-container">
            {this.invoicePrefix && (
              <c-text type="subtext">
                Example of invoice number: {this.invoicePrefix}-0001
              </c-text>
            )}
          </div>
        </div>
      </div>

      <div class="field-container">
        <div class="field-container-row">
          <this.Notification></this.Notification>
          <c-button
            name="updateInvoicing"
            icon-name=""
            isInAction={this.isBasicUpdateButtonInAction}
            isDisabled={this.isInvoicingUpdateButtonDisabled}
          >
            Save
          </c-button>
        </div>
      </div>
    </div>
  );

  AssetsStep: FunctionalComponent = () => (
    <div class="wizard-content step-2-content">
      <div ref={(el) => (this.disableFieldContainer = el as HTMLInputElement)}>
        <div class="field-container assets-field-container">
          <c-text type="modalLabel">
            LOGO{" "}
            <span class="modal-label-light">
              (Max size = 1MB, Aspect Ratio = Any)
            </span>{" "}
          </c-text>
          <c-uploader
            file-type="image"
            name="eventLogo"
            resetUploader={this.resetUploader}
          ></c-uploader>
          <c-link
            type="textWithIcon"
            url={this.eventLogoUrl}
            iconName="image-outline"
          >
            View existing logo
          </c-link>
        </div>
        <div class="field-container assets-field-container">
          <c-text type="modalLabel">
            BANNER{" "}
            <span class="modal-label-light">
              (Max size = 1MB, Aspect Ratio = 4:3)
            </span>{" "}
          </c-text>
          <c-uploader
            file-type="image"
            name="eventBanner"
            resetUploader={this.resetUploader}
          ></c-uploader>
          <c-link
            type="textWithIcon"
            url={this.eventBannerUrl}
            iconName="image-outline"
          >
            View existing banner
          </c-link>
        </div>
        <div class="field-container assets-field-container">
          <c-text type="modalLabel">
            POSTER{" "}
            <span class="modal-label-light">
              (Max size = 1MB, Aspect Ratio = 3:2)
            </span>{" "}
          </c-text>
          <c-uploader
            file-type="image"
            name="eventPoster"
            resetUploader={this.resetUploader}
          ></c-uploader>
          <c-link
            type="textWithIcon"
            url={this.eventPosterUrl}
            iconName="image-outline"
          >
            View existing poster
          </c-link>
        </div>
      </div>

      <div class="field-container">
        <div class="field-container-row">
          <this.Notification></this.Notification>
          <c-button
            name="updateAssets"
            icon-name=""
            isInAction={this.isAssetsUpdateButtonInAction}
            isDisabled={this.isAssetsUpdateButtonDisabled}
          >
            Save
          </c-button>
        </div>
      </div>
    </div>
  );

  ManagersStep: FunctionalComponent = () => (
    <div onClick={() => this.clearManagerSearch()}>
      {this.isRegistrantsFetched ? (
        <div class="wizard-content" onClick={() => this.clearManagerSearch()}>
          <div
            class="disable-field-container"
            ref={(el) => (this.disableFieldContainer = el as HTMLInputElement)}
          >
            <div class="field-container">
              <c-text type="modalLabel">SEARCH REGISTERED USERS</c-text>
              <c-textbox
                input-type="text"
                name="registrantSearch"
                placeholder="Enter name or email"
                isDisabled={false}
                isTextboxFilled={this.isRegistrantNameFilled}
                value=""
              ></c-textbox>
              <c-list
                type="searchDropList"
                name="managerSearchDropList"
                listItemsAsString={JSON.stringify(this.registrantArr)}
              ></c-list>
            </div>
            <div class="field-container">
              <c-text type="modalLabel">MANAGERS</c-text>
              {this.managers.length > 0 ? (
                <div>
                  <div class="manager-list-container">
                    {this.managers.map((manager) => (
                      <this.ManagerBubble
                        name={manager.fullName}
                        email={manager.email}
                      ></this.ManagerBubble>
                    ))}
                  </div>
                  <div class="subtext-container">
                    <ion-icon name="alert-circle-outline"></ion-icon>
                    <c-text type="subtext">
                      The above individuals will be notified via email
                    </c-text>
                  </div>
                </div>
              ) : (
                <div class="subtext-container">
                  <c-text type="subtext">0 managers added</c-text>
                </div>
              )}
            </div>
          </div>
          <div class="field-container">
            <div class="field-container-row">
              <this.Notification></this.Notification>
              <c-button
                name="updateManagers"
                icon-name=""
                isInAction={this.isManagersUpdateButtonInAction}
                isDisabled={this.isManagersUpdateButtonDisabled}
              >
                Save
              </c-button>
            </div>
          </div>
        </div>
      ) : (
        <this.LoadingScreen></this.LoadingScreen>
      )}
    </div>
  );

  ProgressBar: FunctionalComponent = () => (
    <div class="progress-container">
      <c-text>{this.progressBarText}</c-text>
      <div class="progress-bar-container">
        <div
          class="progress-bar"
          ref={(el) => (this.progressBarEl = el as HTMLInputElement)}
        ></div>
        <div class="progress-bar-background"></div>
      </div>
    </div>
  );

  LoadingScreen: FunctionalComponent = () => (
    <div class="loading-screen-container">
      <div class="spinner-container">
        <c-spinner-dark></c-spinner-dark>
        &nbsp;&nbsp;
        <c-text>Loading event details...</c-text>
      </div>
    </div>
  );

  Notification: FunctionalComponent = () => (
    <div
      class="update-confirmation-container"
      ref={(el) => (this.notificationEL = el as HTMLInputElement)}
    >
      <c-badge color="blue" label="Basic details saved"></c-badge>
    </div>
  );

  ManagerBubble: FunctionalComponent<ManagerBubbleInterface> = ({
    name,
    email,
  }) => (
    <div class="manager-bubble-container">
      <p>{name}</p>
      <button onClick={() => this.removeManager(email)}>
        <ion-icon name="close-outline"></ion-icon>
      </button>
    </div>
  );

  render() {
    return (
      <div
        class="edit-event-wizard-container"
        onClick={() => this.clearManagerSearch()}
      >
        <div class="edit-event-wizard-header">
          <div>
            <c-text type="wizardHeading">Edit {state.editEventName}</c-text>
          </div>
          <c-button
            type="modalClose"
            name="closeModal"
            icon-name=""
            label=""
          ></c-button>
        </div>
        {this.isEventDataFetched ? (
          <div class="edit-event-wizard-content-container">
            <div
              class="edit-event-wizard-navigation"
              ref={(el) => (this.wizardNavigation = el as HTMLInputElement)}
            >
              <c-vnav
                name="editEventWizard"
                nav-opts-str={JSON.stringify(this.editOpts)}
                isDisabled={this.isUpdatingEventDetails}
              ></c-vnav>
              {this.unsavedChanges.length > 0 && (
                <div class="unsaved-changes-container">
                  <c-text type="subtext">Unsaved changes in</c-text>
                  <div class="unsaved-changes--bubble-container">
                    {this.unsavedChanges.map((change: string) => (
                      <span class="unsaved-change-bubble">{change}</span>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div class="edit-event-wizard-content">
              {this.activeEditOption === "eventBasic" && (
                <this.BasicStep></this.BasicStep>
              )}
              {this.activeEditOption === "eventSchedule" && (
                <this.ScheduleStep></this.ScheduleStep>
              )}
              {this.activeEditOption === "eventInvoicing" && (
                <this.InvoicingStep></this.InvoicingStep>
              )}
              {this.activeEditOption === "eventAssets" && (
                <this.AssetsStep></this.AssetsStep>
              )}
              {this.activeEditOption === "eventManagers" && (
                <this.ManagersStep></this.ManagersStep>
              )}
            </div>
          </div>
        ) : (
          <this.LoadingScreen></this.LoadingScreen>
        )}
      </div>
    );
  }
}
