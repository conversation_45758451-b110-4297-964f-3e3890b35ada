c-wizard-edit-event .edit-event-wizard-container {
  position: fixed;
  width: 45%;
  left: 0;
  right: 0;
  margin: 4em auto 0 auto;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  padding: 0.5em 1.5em 1.5em 1.5em;
  z-index: 999999;
}

c-wizard-edit-event .edit-event-wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

c-wizard-edit-event .edit-event-wizard-content-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

c-wizard-edit-event .edit-event-wizard-navigation {
  width: 25%;
  margin-top: 1em;
}

c-wizard-edit-event .edit-event-wizard-content {
  width: 65%;
}

c-wizard-edit-event .wizard-content {
  box-sizing: border-box;
  background: white;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--site-border-radius);
  padding: 1em;
  margin-top: 1em;
}

c-wizard-edit-event .wizard-content .field-container:last-child {
  margin-bottom: 0;
}

c-wizard-edit-event .field-container {
  margin-bottom: 1.5em;
}

c-wizard-edit-event .field-container .modal-label {
  margin-bottom: 0.5em;
}

c-wizard-edit-event .field-container-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

c-wizard-edit-event .field-container-row .mdash {
  color: rgba(0, 0, 0, 0.1);
}

c-wizard-edit-event .step-2-content .modal-label {
  margin-bottom: 0;
}

c-wizard-edit-event .step-2-content .subtext {
  margin-top: 0;
}

c-wizard-edit-event .loading-screen-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 200px;
}

c-wizard-edit-event .spinner-container {
  display: flex;
  align-items: center;
}

c-wizard-edit-event .spinner-container c-spinner-dark .spinner-icon {
  margin-top: 7px;
}

c-wizard-edit-event .manager-list-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.25em;
}

c-wizard-edit-event .manager-bubble-container {
  display: flex;
  border: 1px solid var(--accent-color-bg);
  border-radius: 0.25em;
  margin: 0 0.5em 0.5em 0;
}

c-wizard-edit-event .manager-bubble-container p {
  padding: 0;
  margin: 0;
  padding: 0.25em 0.5em;
  font-size: 0.9em;
  background: var(--accent-color-bg-lightest);
  color: var(--accent-color);
  border-radius: 0.25em 0em 0em 0.25em;
}

c-wizard-edit-event .manager-bubble-container button {
  cursor: pointer;
  background: none;
  border: 0;
  outline: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-left: 1px solid var(--accent-color-bg);
  background: var(--accent-color-bg-lightest);
  border-radius: 0em 0.25em 0.25em 0em;
  transition: var(--site-transition);
}

c-wizard-edit-event .manager-bubble-container button:hover {
  background: var(--accent-color-bg-lighter);
}

c-wizard-edit-event .subtext-container {
  display: flex;
  align-items: center;
}

c-wizard-edit-event .subtext-container c-text .subtext {
  margin-top: 0;
  margin-left: 0.25em;
}

c-wizard-edit-event .invoice-subtext-container {
  margin-top: 0.5em;
}

c-wizard-edit-event .publish-options-container {
  /* display: flex; */
  font-size: 0.9em;
}

c-wizard-edit-event .double-textbox c-textbox:first-child input {
  border-radius: 0.25em 0.25em 0em 0em;
  border-bottom: 1px solid white;
}

c-wizard-edit-event .double-textbox c-textbox:first-child input:focus {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
}

c-wizard-edit-event
  .double-textbox
  c-textbox:first-child
  input:focus
  > c-wizard-edit-event
  .double-textbox
  c-textbox:last-child
  input {
  border-top: 0;
}

c-wizard-edit-event .double-textbox c-textbox:last-child input {
  border-radius: 0em 0em 0.25em 0.25em;
}

c-wizard-edit-event .double-textbox c-textbox:last-child input:focus {
}

c-wizard-edit-event .progress-container {
  padding: 3em 0em 5em 0;
  text-align: center;
}

c-wizard-edit-event .progress-container c-text p {
  margin-bottom: 0.5em;
}

c-wizard-edit-event .progress-bar-container {
  position: relative;
}

c-wizard-edit-event .progress-bar {
  position: absolute;
  width: 0%;
  height: 10px;
  background: var(--blue-300);
  border-radius: 0.5em;
}

c-wizard-edit-event .progress-bar-background {
  position: absolute;
  width: 100%;
  height: 10px;
  border-radius: 0.5em;
  background: rgba(0, 0, 0, 0.1);
}

c-wizard-edit-event .assets-field-container c-link a {
  font-size: 0.7em;
  margin-top: 0.3em;
}

c-wizard-edit-event .update-confirmation-container {
  opacity: 0;
}

c-wizard-edit-event .unsaved-changes-container {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 0.5em;
  padding-top: 0.5em;
}

c-wizard-edit-event .unsaved-changes--bubble-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.5em;
}

c-wizard-edit-event .unsaved-change-bubble {
  font-size: 0.8em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.5em;
  border-radius: 0.5em;
  margin-bottom: 0.75em;
  margin-right: 0.75em;
}

c-wizard-edit-event .disable-field-container {
  margin-bottom: 2em;
}

c-wizard-edit-event .progress-container {
  margin: 3em 1em;
}
