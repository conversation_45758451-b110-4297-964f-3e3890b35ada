import { Component, Prop, State, Listen, h } from "@stencil/core";
import Store from "../../global/store";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-section-ticket",
  styleUrl: "c-section-ticket.css",
})
export class CSectionTicket {
  @Prop() type: string;
  @State() isFetching: boolean = true;

  private ticketBtnStatus: string;
  private tier: string;
  private availableTill: string;
  private purchaseDate: string;

  @Listen("full-ticket-in-cart")
  fullTicketInCartHandler(event) {
    event.preventDefault();
    state.isFullTicketInCart = true;
  }
  @Listen("full-ticket-not-in-cart")
  fullTicketNotInCartHandler(event) {
    event.preventDefault();
    state.isFullTicketInCart = false;
  }

  componentDidLoad() {
    let payload = {
      type: this.type,
    };
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/getticketdetails`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
        } else if (response.data.status === "Success") {
          state.fullConferenceTicketID = response.data.payload.ticketID;
          state.fullConferenceTicketSubTitle = response.data.payload.subTitle;
          this.tier = response.data.payload.tier;
          state.fullConferenceTicketPrice = response.data.payload.price;
          this.purchaseDate = response.data.payload.purchaseDate;
          this.availableTill = response.data.payload.availableTill;
          if (this.availableTill.length === 0) {
            state.isFullTicketPurchased = true;
          }
          this.ticketBtnStatus = response.data.payload.ticketBtnStatus;
          if (this.ticketBtnStatus === "remove-from-cart") {
            state.isFullTicketInCart = true;
          } else {
            state.isFullTicketInCart = false;
          }
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        <div class="ticket-left">
          <p class="ticket-left-text ticket-heading">
            <strong>
              {this.isFetching ? (
                <c-skel-line color="accent"></c-skel-line>
              ) : (
                this.tier.toUpperCase()
              )}
            </strong>
            <br />
            <span class="ticket-left-text">
              {this.isFetching ? (
                <c-skel-line color="accent"></c-skel-line>
              ) : (
                state.fullConferenceTicketSubTitle
              )}
            </span>
          </p>

          <p class="ticket-left-text ticket-price">
            {this.isFetching ? (
              <c-skel-line color="accent" width={50}></c-skel-line>
            ) : (
              `${Store.getCurrencySymbol()}${state.fullConferenceTicketPrice}`
            )}
          </p>
          <p class="ticket-left-text ticket-tax-details">Incl. Taxes (GST)</p>
        </div>

        <div class="ticket-right">
          {this.isFetching ? (
            <ul class="ticket-details-list">
              <li class="ticket-detail">
                <c-skel-line color="gray" width={75}></c-skel-line>
              </li>
              <li class="ticket-detail">
                <c-skel-line color="gray" width={75}></c-skel-line>
              </li>
              <li class="ticket-detail">
                <c-skel-line color="gray" width={75}></c-skel-line>
              </li>
            </ul>
          ) : (
            <ul class="ticket-details-list">
              <li class="ticket-detail">
                <div class="icon green-tick">✓</div>{" "}
                <span>Attend on all conference days</span>
              </li>
              <li class="ticket-detail">
                <div class="icon green-tick">✓</div>{" "}
                <span>Access all the keynotes</span>
              </li>
              <li class="ticket-detail">
                <div class="icon warning">
                  <strong>!</strong>
                </div>{" "}
                <span>Does not include workshops & courses</span>
              </li>
            </ul>
          )}

          <div class="ticket-footer">
            <p class="ticket-end-date">
              {this.isFetching ? (
                <c-skel-line color="gray" width={75}></c-skel-line>
              ) : (
                <div>
                  {this.availableTill.length > 0 ? (
                    <span>
                      <strong> {this.tier} ends on</strong>
                      <br /> {this.availableTill} (IST)
                    </span>
                  ) : (
                    <span>
                      <strong>Purchased on</strong>
                      <br />
                      {new Date(this.purchaseDate).toString().substring(4, 15)}
                    </span>
                  )}
                </div>
              )}
            </p>
          </div>
        </div>
      </div>
    );
  }
}
