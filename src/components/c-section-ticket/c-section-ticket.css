c-section-ticket {
  width: 100%;
}

c-section-ticket .container {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  border-radius: 0.4em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: white;
}

c-section-ticket .ticket-left {
  width: 30%;
  background: rgb(89, 49, 150);
  background: linear-gradient(
    135deg,
    rgba(89, 49, 150, 1) 0%,
    rgba(71, 39, 120, 1) 35%,
    rgba(53, 29, 90, 1) 100%
  );
  padding: 1em;
  border-radius: 0.4em 0 0 0.4em;
}

c-section-ticket .ticket-right {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 60%;
}

c-section-ticket .ticket-left-text {
  color: var(--accent-color-bg-lighter);
  margin-top: 0;
  font-size: 0.9em;
}

c-section-ticket .ticket-tax-details {
  font-size: 0.7em;
  color: var(--accent-color-bg-light);
  margin-bottom: 0;
  padding-bottom: 0;
}

c-section-ticket .ticket-price {
  font-size: 1.5em;
  margin-top: 0;
  color: var(--accent-color-bg-lightest);
  margin-bottom: 0;
}

c-section-ticket .ticket-details-list {
  list-style-type: none;
  margin-top: 1em;
  /* margin-bottom: 2.75em; */
  margin-bottom: 0.3em;
  padding: 0;
}

c-section-ticket .ticket-detail {
  display: flex;
  margin-bottom: 1em;
  font-size: 0.9em;
  color: var(--fontcolor);
}

c-section-ticket .ticket-footer {
  display: flex;
  justify-content: space-between;
  width: 95%;
  align-items: center;
  margin-top: 0.8em;
  padding-bottom: 0.75em;
}

c-section-ticket .ticket-end-date {
  font-size: 0.8em;
  color: rgba(0, 0, 0, 0.4);
}

c-section-ticket .icon {
  width: 21px;
  height: 20px;
  text-align: center;
  border-radius: 100%;
  background: red;
  margin-right: 0.75em;
}

c-section-ticket .green-tick {
  background: var(--accent-green-bg-lighter);
  color: var(--accent-green-darker);
}

c-section-ticket .warning {
  background: var(--accent-golden-bg-lighter);
  color: var(--accent-golden-darker);
}
