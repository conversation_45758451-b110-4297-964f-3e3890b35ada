import { Component, h } from "@stencil/core";
import state from "../../global/state";

@Component({
  tag: "c-cart-list",
  styleUrl: "c-cart-list.css",
})
export class CCartList {
  render() {
    return (
      <ul class="item-list">
        {state.cartItems.length > 0 ? (
          state.cartItems.map((item) => (
            <li class="cart-list-item">
              <c-cart-item
                heading={item.title}
                sub-title={item.subTitle}
                currency="₹"
                price={item.price}
              ></c-cart-item>
            </li>
          ))
        ) : (
          <li class="no-border-list-item">You cart has 0 items</li>
        )}
      </ul>
    );
  }
}
