import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  Prop,
  State,
  Host,
  h,
  Watch,
} from "@stencil/core";

@Component({
  tag: "c-ticket-price-input-track",
  styleUrl: "c-ticket-price-input-track.css",
})
export class CTicketPriceInputTrack {
  /*-----------
  Event Emitter
  -----------*/
  @Event({
    eventName: "addSubTicket_trackTicket",
    bubbles: true,
  })
  addSubTicket_trackTicket: EventEmitter;

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "newSubTickets") {
      this.mode = "newSubTicketItem";
    } else if (e.detail.name === "cancelPriceTier") {
      this.mode = "viewSubTicket";
    } else if (e.detail.name === "createSubTicket") {
      this.createSubTicket();
    } else if (e.detail.name === "deletePriceTier") {
      this.deletePriceTier();
    } else if (e.detail.name === "editPriceTier") {
      this.editPriceTier();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "subTicketName") {
      this.newSubTicketName = e.detail.value;
    } else if (e.detail.name === "studentPrice") {
      this.newStudentPrice = e.detail.value;
    } else if (e.detail.name === "proPrice") {
      this.newProfessionalPrice = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("ticketTierClicked") handleTicketTierClick(e) {
    this.subTicketId = e.detail.subTicketId;
    this.subTicketName = e.detail.subTicketName;
    this.studentPrice = e.detail.price_Student;
    this.professionalPrice = e.detail.price_Professional;
    console.log(this.subTicketId);
    this.mode = "editSubTicket";
  }

  /*---
  Props
  ---*/
  @Prop() subTicketsString: string = "";

  /*----
  States  
  ----*/
  @State()
  mode: string = "viewSubTicket";
  @State() isSaveSubTicketDisabled: boolean = true;
  @State() subTickets: any = [];

  /*------
  Watchers  
  ------*/
  @Watch("subTicketsString") subTicketsStringWatcher(
    newVal: string,
    oldVal: string
  ) {
    if (newVal != oldVal) {
      this.generateSubTicketsList();
    }
  }

  /*-------
  Variables  
  -------*/
  private subTicketId: string = "";
  private subTicketName: string = "";
  private studentPrice: number = 0;
  private professionalPrice: number = 0;

  private newSubTicketName: string = "";
  private newStudentPrice: number = 0;
  private newProfessionalPrice: number = 0;

  /*---------------
  Lifecycle Methods  
  ---------------*/
  componentWillLoad() {
    if (this.subTicketsString.length > 0) {
      this.generateSubTicketsList();
    }
  }

  /*-------
  Functions  
  -------*/
  createSubTicket() {
    let obj = {
      id: Math.random().toString(),
      subTicketName: this.newSubTicketName,
      subTicketPrice: {
        student: this.newStudentPrice,
        professional: this.newProfessionalPrice,
      },
    };
    this.addSubTicket_trackTicket.emit({
      subTicketObj: obj,
    });
    this.mode = "viewSubTicket";
    this.resetComponent();
  }

  deletePriceTier() {
    console.log("delete price tier");
  }

  editPriceTier() {
    console.log("edit price tier");
  }

  generateSubTicketsList() {
    this.subTickets = JSON.parse(this.subTicketsString);
    this.subTickets = [...this.subTickets];
    console.log(this.subTickets);
  }

  resetComponent() {
    this.isSaveSubTicketDisabled = true;
  }

  validateInputs() {
    if (this.mode === "newSubTicketItem") {
      this.validateInputs_NewSubTicket();
    } else if (this.mode === "editSubTicket") {
      this.validateInputs_EditSubTicket();
    }
  }

  validateInputs_NewSubTicket() {
    if (
      this.newSubTicketName.length > 0 &&
      this.newStudentPrice > 0 &&
      this.newProfessionalPrice > 0
    ) {
      this.isSaveSubTicketDisabled = false;
    } else {
      this.isSaveSubTicketDisabled = true;
    }
  }

  validateInputs_EditSubTicket() {
    let hasSubTicketNameChanged: boolean = false;
    let hasStudentPriceChanged: boolean = false;
    let hasProfessionalPriceChanged: boolean = false;

    if (this.newSubTicketName != this.subTicketName) {
      hasSubTicketNameChanged = true;
    } else {
      hasSubTicketNameChanged = false;
    }

    if (this.newStudentPrice != this.studentPrice) {
      hasStudentPriceChanged = true;
    } else {
      hasStudentPriceChanged = false;
    }

    if (this.newProfessionalPrice != this.professionalPrice) {
      hasProfessionalPriceChanged = true;
    } else {
      hasProfessionalPriceChanged = false;
    }

    if (
      hasSubTicketNameChanged ||
      hasStudentPriceChanged ||
      hasProfessionalPriceChanged
    ) {
      this.isSaveSubTicketDisabled = false;
    } else {
      this.isSaveSubTicketDisabled = true;
    }
  }

  /*-------------------
  Functional Components 
  -------------------*/
  ViewSubTicket: FunctionalComponent = () =>
    this.subTickets.length > 0 ? (
      this.subTickets.map((tier: any) => (
        <c-ticket-price-item-track
          subTicketId={tier.id}
          name={tier.subTicketName}
          price_Student={tier.subTicketPrice.student}
          price_Professional={tier.subTicketPrice.professional}
        ></c-ticket-price-item-track>
      ))
    ) : (
      <div class="no-tier-container">
        <c-text>Found 0 sub-tickets</c-text>
      </div>
    );

  InputSubTicket: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          SUB-TICKET NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="subTicketName"
          placeholder="e.g. Workshop, Course, Symposium"
          isDisabled={false}
          value={this.mode === "editSubTicket" ? this.subTicketName : ""}
        ></c-textbox>
      </div>

      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          SUB-TICKET PRICE
        </c-text>
        <div class="input-price-container">
          <div class="input-price-container__price">
            <c-text>₹</c-text>
            <c-textbox
              input-type="text"
              name="studentPrice"
              placeholder="Student price"
              isDisabled={false}
              value={
                this.mode === "editSubTicket"
                  ? this.studentPrice.toString()
                  : ""
              }
            ></c-textbox>
          </div>
          &nbsp;&nbsp;
          <div class="input-price-container__price">
            <c-text>₹</c-text>
            <c-textbox
              input-type="text"
              name="proPrice"
              placeholder="Professional price"
              isDisabled={false}
              value={
                this.mode === "editSubTicket"
                  ? this.professionalPrice.toString()
                  : ""
              }
            ></c-textbox>
          </div>
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelPriceTier">
            Cancel
          </c-button>
          <div class="button-group">
            {this.mode === "editSubTicket" && (
              <c-button type="ghost_Danger_Small" name="deletePriceTier">
                Delete
              </c-button>
            )}
            &nbsp;&nbsp;&nbsp;&nbsp;
            <c-button
              type="ghost_Small"
              name={
                this.mode === "newSubTicketItem"
                  ? "createSubTicket"
                  : "editPriceTier"
              }
              isDisabled={this.isSaveSubTicketDisabled}
            >
              {this.mode === "newSubTicketItem" && "Create"}
              {this.mode === "editSubTicket" && "Save"}
            </c-button>
          </div>
        </c-row>
      </div>
    </div>
  );

  CreateSubTickets: FunctionalComponent = () => (
    <div class="ticket-price-table__footer">
      <c-button
        name="newSubTickets"
        type="newSubTickets"
        isDisabled={false}
        isInAction={false}
      ></c-button>
    </div>
  );

  render() {
    return (
      <Host>
        <div class="ticket-price-table__header">
          <div class="ticket-price-table__header--item ticket-price-table__header--item--1">
            <c-text>Sub-ticket name</c-text>
          </div>
          <div class="ticket-price-table__header--item ticket-price-table__header--item--2">
            <c-text>Sub-ticket price</c-text>
          </div>
        </div>
        <div class="ticket-price-table__body">
          {this.mode === "viewSubTicket" ? (
            <this.ViewSubTicket></this.ViewSubTicket>
          ) : (
            <this.InputSubTicket></this.InputSubTicket>
          )}
          {this.mode === "viewSubTicket" && (
            <this.CreateSubTickets></this.CreateSubTickets>
          )}
        </div>
      </Host>
    );
  }
}
