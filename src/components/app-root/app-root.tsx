import { Component, Host, Prop, Listen, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import { isUserLogged } from "../../utils/";
import axios from "axios";
import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "app-root",
  styleUrl: "app-root.css",
  shadow: true,
})
export class AppRoot {
  @Prop() history: RouterHistory;

  @Listen("getAccountInfoEvent")
  getAccountInfoHandler() {
    this.fetchUserData();
  }

  @Listen("logout-btn-click-event")
  logoutEventHandler() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/logout`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Failed to Logout");
        } else if (response.data.status === "Success") {
          this.history.push("/auth", {});
          state.isMobileMenuOpen = false;
          state.email = "";
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "logoutUser") {
      axios({
        method: "POST",
        baseURL: `${state.baseUrl}/logout`,
        withCredentials: true,
        responseType: "json",
      })
        .then((response) => {
          if (response.data.status === "Failed") {
            alert("Failed to Logout");
          } else if (response.data.status === "Success") {
            this.history.push("/auth", {});
            state.isMobileMenuOpen = false;
            state.email = "";
          }
        })
        .catch((error) => {
          alert(error);
        });
    } else if (e.detail.name === "goToProfile") {
      this.history.push("/profile", {});
    }
  }

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    if (
      event.detail.cNavItemName === "upcomingEvents" ||
      event.detail.cNavItemName === "archivedEvents" ||
      event.detail.cNavItemName === "membership"
    ) {
      let stateObj = { activeSection: event.detail.cNavItemName };
      this.history.push("/events", stateObj);
    } else if (event.detail.cNavItemName === "mobileProfile") {
      console.log(event.detail.cNavItemName);
      this.history.push("/profile", {});
    } else if (event.detail.cNavItemName === "mobilePurchases") {
      this.history.push("/purchases", {});
    } else if (event.detail.cNavItemName === "mobileBack") {
      this.history.goBack();
    }
    state.isMobileMenuOpen = false;
  }

  @Listen("closeModal")
  closeModalEventHandler() {
    this.fetchUserData();
  }

  componentDidLoad() {
    if (isUserLogged()) {
      this.fetchUserData();
    }
  }

  fetchUserData() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/home`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert("Failed to fetch data");
        } else if (response.data.status === "Success") {
          Store.setFirstName(response.data.payload.profile.name.first);
          Store.setLastName(response.data.payload.profile.name.last);
          Store.setOccupation(response.data.payload.professional.occupation);
          Store.setIsMember(response.data.payload.membership.isMember);
          Store.setCurrency(response.data.payload.settings.currency.name);
          Store.setCurrencySymbol(
            response.data.payload.settings.currency.symbol
          );
          // Profile details
          state.firstName = response.data.payload.profile.name.first;
          state.lastName = response.data.payload.profile.name.last;
          state.email = response.data.payload.profile.email;
          state.occupation = response.data.payload.professional.occupation;
          // Membership details
          state.isMember = response.data.payload.membership.isMember;
          state.membershipId = response.data.payload.membership.id;
          state.membershipType = response.data.payload.membership.type;
          state.membershipEndDate = response.data.payload.membership.endDate;
          // Access rights details
          state.isRegistrationManager =
            response.data.payload.settings.isRegistrationManager;
          state.isAdmin = response.data.payload.settings.isAdmin;
          //Open account setup modal
          state.isAccountSetup = response.data.isAccountSetupComplete;
          state.isEmailVerified = response.data.isEmailVerified;
          // ID card stuff
          state.isIdCardExists = response.data.isIdCardExists;
          state.isIdCardExpired = response.data.isIdCardExpired;
          state.isIdCardVerified = response.data.isIdCardVerified;
          state.isUserDataFetched = true;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <Host>
        {!state.isAccountSetup && (
          <c-modal name={"openAccountSetupModal"} is-active={true}></c-modal>
        )}

        {state.occupation === "student" &&
        !state.isIdCardExists &&
        state.isAccountSetup ? (
          <c-modal name={"getId"} is-active={true}></c-modal>
        ) : (
          ""
        )}

        {state.occupation === "student" &&
        state.isIdCardExpired &&
        state.isAccountSetup ? (
          <c-modal name={"getId"} is-active={true}></c-modal>
        ) : (
          ""
        )}

        {!state.isEmailVerified && (
          <c-banner
            variant="verifyEmail"
            theme="danger"
            position="bottom"
          ></c-banner>
        )}

        <stencil-router>
          <stencil-route-switch scrollTopOffset={0}>
            {/* <this.RestrictedRoute url="/" component="p-auth" exact={true} /> */}
            {/* <this.RestrictedRoute url="/auth" component="p-auth" /> */}
            <this.RestrictedRoute url="/auth/:eventCode?" component="p-auth" />

            <this.RestrictedRoute
              url="/post-linkedin-oauth"
              component="p-post-linkedin-oauth"
            />
            <this.RestrictedRoute
              url="/post-google-oauth"
              component="p-post-google-oauth"
            />

            {/* <this.PrivateRoute
              url="/registration/:eventCode"
              component="p-regform"
            /> */}

            <this.PrivateRoute
              url="/registration/:eventCodeForRegistration"
              component="p-regform-2"
            />

            <this.PrivateRoute url="/events" component="p-home" />
            {/* <this.PrivateRoute url="/test" component="p-home" /> */}

            <this.PrivateRoute url="/confirm" component="p-confirm" />
            <this.PrivateRoute
              url="/checkout/:eventCodeForRegistration"
              component="p-checkout"
            />

            <this.PrivateRoute url="/profile" component="p-profile" />
            <this.PrivateRoute url="/purchases" component="p-purchases" />
            <this.PrivateRoute
              url="/bank-thanks/:eventCodeForRegistration"
              component="p-bank-thanks"
            />
            <this.PrivateRoute
              url="/membership-confirmation"
              component="p-membership-thanks"
            />
            <this.PrivateRoute
              url="/razorpay-thanks"
              component="p-rzp-thanks"
            />
            <this.PrivateRoute
              url="/free-purchase-thanks"
              component="p-free-purchase-thanks"
            />
            <this.RegManagerRoute
              url="/dashboard/:eventCodeForMonitoring"
              component="p-dashboard"
            />
            <this.AdminRoute
              url="/configure/:eventCodeForConfiguration"
              component="p-configure"
            ></this.AdminRoute>
            <stencil-route
              url="/instructor/:ticketId/:sessionId"
              component="p-instructor"
            />
            <stencil-route url="/accesslist" component="p-accesslist" />
            <stencil-route
              url="/track-participants"
              component="p-track-chair"
            />
            <stencil-route
              url="/track-overview/:eventCode"
              component="p-track-overview"
            />
            <stencil-route
              url="/dashboard/overview"
              component="p-rm-overview"
            />
            <stencil-route
              url="/dashboard/purchases"
              component="p-rm-purchases"
            />
            <stencil-route
              url="/dashboard/registrants"
              component="p-rm-registrants"
            />
            <stencil-route url="/dashboard/coupons" component="p-rm-coupons" />
            <stencil-route url="/dashboard/members" component="p-rm-members" />
            <stencil-route url="/dashboard/alerts" component="p-rm-alerts" />
            <stencil-route
              url="/attendee-list/:eventCode"
              component="p-attendee-list"
            />

            {/* Catch-all Route */}
            <stencil-route component="p-catch-all" />
          </stencil-route-switch>
        </stencil-router>
      </Host>
    );
  }

  PrivateRoute = ({ component, ...props }: { [key: string]: any }) => {
    const Component = component;
    return (
      <stencil-route
        {...props}
        routeRender={(routeRenderProps) => {
          if (isUserLogged()) {
            return (
              <Component
                {...props}
                {...props.componentProps}
                {...routeRenderProps}
              ></Component>
            );
          }
          return <stencil-router-redirect url="/"></stencil-router-redirect>;
        }}
      />
    );
  };

  RestrictedRoute = ({ component, ...props }: { [key: string]: any }) => {
    const Component = component;
    return (
      <stencil-route
        {...props}
        routeRender={(routeRenderProps) => {
          if (!isUserLogged()) {
            return (
              <Component
                {...props}
                {...props.componentProps}
                {...routeRenderProps}
              ></Component>
            );
          }
          return (
            <stencil-router-redirect url="/events"></stencil-router-redirect>
          );
        }}
      />
    );
  };

  // signUpProtected = ({ component, ...props }: { [key: string]: any }) => {
  //   const Component = component;
  //   return (
  //     <stencil-route
  //       {...props}
  //       routeRender={() => {
  //         if (!isUserLogged() && Store.getIsRegistering()) {
  //           return <Component {...props} {...props.componentProps}></Component>;
  //         }
  //         return (
  //           <stencil-router-redirect url="/auth"></stencil-router-redirect>
  //         );
  //       }}
  //     />
  //   );
  // };

  RegManagerRoute = ({ component, ...props }: { [key: string]: any }) => {
    const Component = component;
    return (
      <stencil-route
        {...props}
        routeRender={(routeRenderProps) => {
          if (isUserLogged()) {
            return (
              <Component
                {...props}
                {...props.componentProps}
                {...routeRenderProps}
              ></Component>
            );
          }
          return <stencil-router-redirect url="/"></stencil-router-redirect>;
        }}
      />
    );
  };

  AdminRoute = ({ component, ...props }: { [key: string]: any }) => {
    const Component = component;
    return (
      <stencil-route
        {...props}
        routeRender={(routeRenderProps) => {
          if (isUserLogged()) {
            return (
              <Component
                {...props}
                {...props.componentProps}
                {...routeRenderProps}
              ></Component>
            );
          }
          return (
            <stencil-router-redirect url="/events"></stencil-router-redirect>
          );
        }}
      />
    );
  };
}

injectHistory(AppRoot);
