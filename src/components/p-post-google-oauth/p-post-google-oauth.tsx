import {
  Component,
  Prop,
  h,
  State,
  Listen,
  FunctionalComponent,
} from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import {
  getGoogleProfile,
  linkAccountWithGoogleHelper,
  googleSignupHelper,
} from "../../global/helpers";
import state from "../../global/state";

import {
  linkAccountWithOauthPayloadGenerator,
  signupWithOauthPayloadGenerator,
} from "../../global/generators";

interface ErrorProp {
  title: string;
  description: string;
}

interface LoaderProp {
  message: string;
}

@Component({
  tag: "p-post-google-oauth",
  styleUrl: "p-post-google-oauth.css",
})
export class PPostGoogleOauth {
  @Prop() history: RouterHistory;
  @State() compState: string = "init";
  @State() linkAccountState: string = "init";

  @Listen("radio-change-event")
  radioChangeEventHandler(event) {
    if (event.detail.name === "occupation") {
      state.occupation = event.detail.value;
    }
  }

  @Listen("buttonClick")
  async buttonClickHandler(event) {
    if (event.detail.name === "createAccountFromGoogle") {
      let googleSignupPayload = signupWithOauthPayloadGenerator();
      let { isUserSignedUp, signupMessage } = await googleSignupHelper(
        googleSignupPayload
      );
      if (!isUserSignedUp) {
        state.isSignupError = true;
        state.notificationType = "error";
        state.notificationMessage = signupMessage;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      if (state.eventCode.length > 0) {
        state.eventCodeForRegistration = state.eventCode;
        this.history.push(
          `/registration/${state.eventCodeForRegistration}`,
          {}
        );
      } else {
        this.history.push("/events", {});
      }
    } else if (event.detail.name === "linkAccountWithGoogle") {
      let linkAccountWithGooglePayload = linkAccountWithOauthPayloadGenerator();
      let { isAccountLinked, message } = await linkAccountWithGoogleHelper(
        linkAccountWithGooglePayload
      );
      if (!isAccountLinked) {
        state.isSignupError = true;
        state.notificationType = "error";
        state.notificationMessage = message;
        state.notificationPosition = "topCentre";
        state.isNotificationActive = true;
        return;
      }
      this.linkAccountState = "confirm";
    } else if (event.detail.name === "confirmAccountLinkingWithGoogle") {
      if (state.eventCode.length > 0) {
        state.eventCodeForRegistration = state.eventCode;
        this.history.push(
          `/registration/${state.eventCodeForRegistration}`,
          {}
        );
      } else {
        this.history.push("/events", {});
      }
    }
  }

  private googleJwt: string = "";

  Error: FunctionalComponent<ErrorProp> = ({ title, description }) => (
    <div class="error-container">
      <c-text type="errorHeading">{title}</c-text>
      <c-text>{description}</c-text>
      <br />
      <c-oauth></c-oauth>
      <div class="signin-container">
        <c-link url="/">Sign in with email and password</c-link>
      </div>
    </div>
  );

  Loader: FunctionalComponent<LoaderProp> = ({ message }) => (
    <div class="loader-container">
      <div class="loader-content">
        <c-spinner-dark></c-spinner-dark>
        <c-text>{message}</c-text>
      </div>
    </div>
  );

  NewAccount: FunctionalComponent = () => (
    <div>
      <c-row type="oauthProfileDetails">
        {/* <c-img type="oauthDp" src={state.dpUrl}></c-img> */}
        <div>
          <c-text>
            {state.firstName} {state.lastName}
          </c-text>
          <c-link url={`mailto:${state.email}`}>{state.email}</c-link>
        </div>
      </c-row>
      <div class="occupation-container">
        <c-text>Your occupation?</c-text>
        <c-row>
          <c-radio
            name="occupation"
            label1="Student"
            label2=""
            label3=""
            val="student"
            isChecked={false}
          ></c-radio>
          <c-radio
            name="occupation"
            label1="Professional"
            label2=""
            label3=""
            val="professional"
            isChecked={true}
          ></c-radio>
        </c-row>
      </div>
      <c-button
        type="solidWithIcon"
        name="createAccountFromGoogle"
        icon-name="log-in-outline"
        is-in-action={state.isGoogleCreateAccountInAction}
        label="Create account"
      ></c-button>
    </div>
  );

  ConnectAccount: FunctionalComponent = () => (
    <div>
      {this.linkAccountState === "init" && (
        <this.LinkAccountInit></this.LinkAccountInit>
      )}
      {this.linkAccountState === "confirm" && (
        <this.LinkAccountConfirm></this.LinkAccountConfirm>
      )}
    </div>
  );

  LinkAccountInit: FunctionalComponent = () => (
    <div>
      <c-text type="cardHeading">Account already exists</c-text>
      <c-text>
        There is already an account associated with this email{" "}
        {/* <c-link url={`mailto:${state.email}`}>{state.email}</c-link> */}
      </c-text>
      <div class="existing-account-container">
        <c-text>
          {state.firstName} {state.lastName}
        </c-text>
        <c-link url={`mailto:${state.email}`}>{state.email}</c-link>
        <div class="auth-mechanism-container">
          <c-text>
            <ion-icon name="lock-open-outline"></ion-icon> &nbsp;Uses Email &
            Password for login
          </c-text>
        </div>
      </div>
      {/* <c-text>
        Kindly link your Google account with this existing account
      </c-text> */}
      <c-button
        type="solidWithIcon"
        name="linkAccountWithGoogle"
        icon-name="link-outline"
        is-in-action={state.isLinkAccountWithGoogleInAction}
        label="Link accounts"
      ></c-button>
      <c-divider type="single"></c-divider>
      <c-text type="cardSubSectionHeading">Benefits of linking</c-text>
      <div class="existing-account-container">
        <ul class="connection-benefits-list">
          <li>Prevents duplicate account</li>
          <li>Login using both Google & email + password</li>
        </ul>
      </div>
    </div>
  );

  LinkAccountConfirm: FunctionalComponent = () => (
    <div>
      <c-text type="cardHeading">Account linking successful</c-text>
      <c-text>We have linked your existing account with Google</c-text>
      {/* <div class="linked-account-container">
        <c-row type="oauthProfileDetails">
          <c-img type="oauthDp" src={state.dpUrl}></c-img>
          <div>
            <c-text>
              {state.firstName} {state.lastName}
            </c-text>
            <c-link url={`mailto:${state.email}`}>{state.email}</c-link>
          </div>
        </c-row>
      </div> */}
      <c-button
        type="solidWithIcon"
        name="confirmAccountLinkingWithGoogle"
        icon-name="arrow-forward-outline"
        is-in-action={state.isLinkAccountWithGoogleInAction}
        label="Proceed to account"
      ></c-button>
    </div>
  );

  componentWillLoad() {
    this.googleJwt = this.history.location.state.googleJwt;
    this.fetchData();
  }

  async fetchData() {
    let {
      isGoogleProfileFetched,
      firstName,
      lastName,
      email,
      isEmailVerified,
      dpUrl,
      isUserExists,
      isGoogleConnected,
    } = await getGoogleProfile(this.googleJwt);

    if (!isGoogleProfileFetched) {
      this.compState = "error";
      return;
    }

    state.firstName = firstName;
    state.lastName = lastName;
    state.email = email.toLowerCase();
    state.dpUrl = dpUrl;
    state.isEmailVerified = isEmailVerified;
    // state.isAccountSetup = false;

    if (isUserExists) {
      if (isGoogleConnected === true) {
        this.history.push("/events", {});
      } else {
        this.compState = "connectAccounts";
      }
    } else {
      this.compState = "createNewAccount";
    }
  }

  render() {
    return (
      <c-page type="postOauth">
        <c-card>
          {this.compState === "init" && (
            <this.Loader message="Fetching profile information.."></this.Loader>
          )}

          {this.compState === "error" && (
            <this.Error
              title="Please try again"
              description="There was an error while retrieving your profile information"
            ></this.Error>
          )}

          {this.compState === "connectAccounts" && (
            <this.ConnectAccount></this.ConnectAccount>
          )}

          {this.compState === "createNewAccount" && (
            <this.NewAccount></this.NewAccount>
          )}
        </c-card>
        {/* <c-notification isActive={state.isNotificationActive}></c-notification> */}
      </c-page>
    );
  }
}

injectHistory(PPostGoogleOauth);
