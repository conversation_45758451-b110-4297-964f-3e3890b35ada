import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "c-create-invoice-modal",
  styleUrl: "c-create-invoice-modal.css",
})
export class CCreateInvoiceModal {
  invoiceGroupDataInput!: HTMLInputElement;

  @Event({
    eventName: "closeModal",
    bubbles: true,
  })
  closeModalEvent: EventEmitter;

  @Event({
    eventName: "invoiceGroupUploadSuccess",
    bubbles: true,
  })
  invoiceGroupUploadSuccess_EventEmitter: EventEmitter;

  @Event({
    eventName: "invoiceGroupUploadFailed",
    bubbles: true,
  })
  invoiceGroupUploadFailed_EventEmitter: EventEmitter;

  @Listen("input-event") inputHandler(event) {
    if (event.detail.name === "invoiceGroupName") {
      this.invoiceGroupName = event.detail.value.trim();
    }
    this.updateUploadButtonStatus();
  }

  @Listen("buttonClick") buttonClickHandler(e) {
    if (e.detail.name === "closeModal") {
      this.closeModalEvent.emit();
    } else if (e.detail.name === "uploadInvoiceGroupFile") {
      this.uploadInvoiceGroupFile();
    }
  }

  @State()
  isUploadButtonDisabled: boolean = true;
  @State() isUploadingFile: boolean = false;

  private invoiceGroupName: string = "";
  private invoiceGroupDataFile: any;
  private isFileAdded: boolean = false;
  private isInvoiceGroupNameAdded: boolean = false;

  fileInputChangeHandler() {
    this.invoiceGroupDataFile = this.invoiceGroupDataInput.files[0];
    this.updateUploadButtonStatus();
  }

  updateUploadButtonStatus() {
    if (this.invoiceGroupName.length === 0) {
      this.isInvoiceGroupNameAdded = false;
    } else {
      this.isInvoiceGroupNameAdded = true;
    }

    if (this.invoiceGroupDataFile) {
      this.isFileAdded = true;
    } else {
      this.isFileAdded = false;
    }

    if (this.isInvoiceGroupNameAdded && this.isFileAdded) {
      this.isUploadButtonDisabled = false;
    } else {
      this.isUploadButtonDisabled = true;
    }
  }

  uploadInvoiceGroupFile() {
    this.isUploadingFile = true;
    let formData = new FormData();
    formData.append("invoiceGroupData", this.invoiceGroupDataFile);
    formData.append("invoiceGroupName", this.invoiceGroupName);

    axios({
      method: "POST",
      data: formData,
      baseURL: `${state.baseUrl}/upload-invoice-group-data`,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Success") {
          this.invoiceGroupUploadSuccess_EventEmitter.emit({
            type: response.data.status,
            label: response.data.msg,
          });
        } else if (response.data.status === "Failed") {
          this.invoiceGroupUploadFailed_EventEmitter.emit({
            type: response.data.status,
            label: response.data.msg,
          });
        }
        this.isUploadingFile = false;
      })
      .catch((error) => {
        alert(`${error} : Please try again`);
        this.isUploadingFile = false;
      });
  }

  private monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  render() {
    return (
      <div class="modal-container">
        <c-row>
          <h3 class="section-header">Upload Invoice Group</h3>
          <c-button
            type="modalClose"
            name="closeModal"
            icon-name=""
            label=""
          ></c-button>
        </c-row>
        <div class="card">
          <div class="field-container-2">
            <p class="label">
              INVOICE GROUP NAME <span class="mandatory-field-star">*</span>
            </p>
            <c-inputbox
              type="text"
              name="invoiceGroupName"
              placeholder={`e.g. ${
                this.monthNames[new Date().getMonth()]
              } ${new Date().getFullYear()}`}
              is-disabled={false}
            ></c-inputbox>
          </div>
          <div class="field-container-2">
            <p class="label label-top">
              CHOOSE INVOICE GROUP .CSV FILE{" "}
              <span class="mandatory-field-star">*</span>
            </p>
            <div class="file-input__container">
              <input
                type="file"
                onChange={() => this.fileInputChangeHandler()}
                ref={(el) =>
                  (this.invoiceGroupDataInput = el as HTMLInputElement)
                }
              />
            </div>
          </div>
          <div class="wizard-controls">
            <div></div>
            <c-button
              name="uploadInvoiceGroupFile"
              action-label=""
              value=""
              isInAction={this.isUploadingFile}
              isDisabled={this.isUploadButtonDisabled}
            >
              Upload
            </c-button>
          </div>
        </div>
      </div>
    );
  }
}
