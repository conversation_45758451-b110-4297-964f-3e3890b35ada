import { newSpecPage } from '@stencil/core/testing';
import { CCreateInvoiceModal } from '../c-create-invoice-modal';

describe('c-create-invoice-modal', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CCreateInvoiceModal],
      html: `<c-create-invoice-modal></c-create-invoice-modal>`,
    });
    expect(page.root).toEqualHtml(`
      <c-create-invoice-modal>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-create-invoice-modal>
    `);
  });
});
