c-create-invoice-modal .modal-container {
  background: var(--bg-color);
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 9999999;
  position: fixed;
  top: 2em;
  box-sizing: border-box;
  max-height: 80vh;
  overflow: auto;
  margin: 4em auto 0 auto;
  left: 0;
  right: 0;
  padding: 1em;
  width: 60vw;
}

c-create-invoice-modal .card {
  background: white;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25em;
  /* height: 50vh; */
}

c-create-invoice-modal .label {
  font-size: 0.7em;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.4);
  margin: 0;
  margin-bottom: 0.25em;
}

c-create-invoice-modal .mandatory-field-star {
  color: red;
}

c-create-invoice-modal .section-header {
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  margin: 0;
  margin-bottom: 1em;
}

c-create-invoice-modal .file-input__container {
  background: rgba(0, 0, 0, 0.03);
  padding: 1em;
  border-radius: 0.25em;
  margin-bottom: 1em;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

c-create-invoice-modal .field-container-2 {
  margin-bottom: 0.75em;
}

c-create-invoice-modal .wizard-controls {
  display: flex;
  justify-content: space-between;
}

c-create-invoice-modal c-button .modal-close {
  padding: 0;
}

@media only screen and (max-width: 768px) {
  c-create-invoice-modal .modal-container {
    width: 90vw;
    top: 2em;
    margin: 0 auto;
  }
}
