import {
  Component,
  State,
  Prop,
  Watch,
  FunctionalComponent,
  h,
} from "@stencil/core";
import state from "../../global/state";

interface LabelProps {
  value: string;
  icon?: string;
}

@Component({
  tag: "c-control-bar",
  styleUrl: "c-control-bar.css",
})
export class CControlBar {
  @Prop() name: string;
  @Prop() invoiceYearsString: any;
  @Prop() count: number = 0;

  @State() itemCount: number = 0;
  @State() isCountFetched: boolean = false;

  @Watch("count")
  countWatcher(newVal: number, oldVal: number) {
    if (newVal != oldVal) {
      if (!isNaN(newVal)) {
        this.itemCount = newVal;
        this.isCountFetched = true;
      } else {
        this.isCountFetched = false;
      }
    }
  }

  private registrantCategoryOptions = [
    {
      label: "Persona (All)",
      value: "",
    },
    {
      label: "Professionals",
      value: "professional",
    },
    {
      label: "Students",
      value: "student",
    },
  ];
  private registrantMembershipOptions = [
    {
      label: "Membership (All)",
      value: "",
    },
    {
      label: "Members",
      value: "member",
    },
    {
      label: "Non-members",
      value: "non-member",
    },
  ];
  private registrantPurchaseOptions = [
    {
      label: "Purchase Status (All)",
      value: "",
    },
    {
      label: "With Purchases",
      value: "withPurchases",
    },
    {
      label: "Without Purchases",
      value: "withoutPurchases",
    },
  ];
  // private couponDeductionOptions = [
  //   {
  //     label: "Deduction (All)",
  //     value: "",
  //   },
  //   {
  //     label: "Fixed",
  //     value: "fixed",
  //   },
  //   {
  //     label: "Percentage",
  //     value: "percentage",
  //   },
  // ];
  private couponAccessOptions = [
    {
      label: "Access (All)",
      value: "",
    },
    {
      label: "Open to all",
      value: "open",
    },
    {
      label: "Restricted (Emails)",
      value: "emaillist",
    },
  ];
  private memberOptions = [
    {
      label: "Category (All)",
      value: "",
    },
    {
      label: "Annual",
      value: "annual",
    },
    {
      label: "Lifetime",
      value: "lifetime",
    },
  ];
  private alertStatus = [
    {
      label: "Status (All)",
      value: "",
    },
    {
      label: "Active",
      value: "active",
    },
    {
      label: "Closed",
      value: "closed",
    },
  ];
  private alertTypes = [
    {
      label: "Category (All)",
      value: "",
    },
    {
      label: "Bank Transfer",
      value: "Bank Transfer",
    },
    {
      label: "Razorpay Payment Failure",
      value: "Razorpay Payment Failure",
    },
    {
      label: "Incomplete Registration",
      value: "Partial Registration",
    },
    {
      label: "ID Verification",
      value: "Student ID Verification",
    },
  ];
  private adminHomeOptions = [
    {
      label: "Events (All)",
      value: "",
    },
    {
      label: "Published",
      value: "publishedEvents",
    },
    {
      label: "Unpublished",
      value: "unpublishedEvents",
    },
  ];

  private Label: FunctionalComponent<LabelProps> = ({ value, icon }) => (
    <div class="label-container">
      <ion-icon name={icon}></ion-icon>
      <p>{value}</p>
    </div>
  );

  // private ItemCount: FunctionalComponent = () => (
  //   <div class="control-group control-group__item-count">
  //     {this.isCountFetched ? (
  //       <p class="show-count">
  //         {this.name != "registrants" && (
  //           <div>
  //             Total {this.itemCount} {this.name === "purchases" && "sales"}
  //             {this.name === "coupons" && "coupons"}
  //             {this.name === "members" && "members"}
  //             {this.name === "alerts" && "alerts"}
  //           </div>
  //         )}
  //       </p>
  //     ) : (
  //       <c-skel-line color="gray" width={100}></c-skel-line>
  //     )}
  //   </div>
  // );

  private SkelControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <c-skel-line color="gray" width={50}></c-skel-line>
        <br />
        <c-skel-line color="gray" width={100}></c-skel-line>
      </div>
      <div class="control-group">
        <c-skel-line color="gray" width={50}></c-skel-line>
        <br />
        <c-skel-line color="gray" width={100}></c-skel-line>
      </div>
      <div class="control-group">
        <c-skel-line color="gray" width={50}></c-skel-line>
        <br />
        <c-skel-line color="gray" width={100}></c-skel-line>
      </div>
      <c-divider type="single"></c-divider>
      <div class="control-group">
        <c-skel-line color="gray" width={100}></c-skel-line>
      </div>
    </div>
  );

  private PurchasesControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Generate" icon="link-outline"></this.Label>
        <c-text-link
          url={`/attendee-list/${state.eventCodeForMonitoring}`}
          label="Attendee List"
        ></c-text-link>
      </div>
      <div class="control-group">
        <this.Label value="Export" icon="link-outline"></this.Label>
        <c-date-picker
          name="salesStartDate"
          pickTime={false}
          date=""
        ></c-date-picker>
        <c-date-picker
          name="salesEndDate"
          pickTime={false}
          date=""
        ></c-date-picker>
        <c-btn
          name="downloadPurchaseData"
          value=""
          type="ghost"
          label="Export Sales"
          action-label=""
          is-in-action={false}
          is-disabled={this.count > 0 ? false : true}
          isDisabled={true}
        ></c-btn>
      </div>
      {/* <c-divider type="single"></c-divider>
      <this.ItemCount></this.ItemCount> */}
    </div>
  );

  private RegistrantsControlBar: FunctionalComponent = () => (
    <div class="control-bar-container control-bar-container__registrants">
      <div class="control-group">
        <this.Label value="Search" icon="search-outline"></this.Label>
        <c-textbox
          input-type="text"
          name="registrantSearch"
          placeholder="Enter name or email"
          isDisabled={false}
          value=""
        ></c-textbox>
      </div>
      <div class="control-group">
        <this.Label value="Filters" icon="filter-outline"></this.Label>
        <c-dropdown
          name="categoryFilter"
          option-str={JSON.stringify(this.registrantCategoryOptions)}
        ></c-dropdown>{" "}
        <br />
        <c-dropdown
          name="membershipFilter"
          option-str={JSON.stringify(this.registrantMembershipOptions)}
        ></c-dropdown>
        <br />
        <c-dropdown
          name="purchaseStatusFilter"
          option-str={JSON.stringify(this.registrantPurchaseOptions)}
        ></c-dropdown>
        <c-btn
          name="downloadAccounts"
          value=""
          type="ghost"
          label="Export Accounts"
          action-label=""
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
      {/* <this.ItemCount></this.ItemCount> */}
    </div>
  );

  private CouponsControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Filter" icon="filter-outline"></this.Label>
        {/* <c-dropdown
          name="couponDeductionFilter"
          option-str={JSON.stringify(this.couponDeductionOptions)}
        ></c-dropdown> */}
        <c-dropdown
          name="couponAccessFilter"
          option-str={JSON.stringify(this.couponAccessOptions)}
        ></c-dropdown>
      </div>
      <div class="control-group">
        <this.Label value="Create" icon="create-outline"></this.Label>
        <c-btn
          name="createCoupon"
          value=""
          type="ghost"
          label="Create new coupon"
          action-label=""
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
      {/* <c-divider type="single"></c-divider>
      <this.ItemCount></this.ItemCount> */}
    </div>
  );

  private MembersControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Filter" icon="filter-outline"></this.Label>
        <c-dropdown
          name="memberFilter"
          option-str={JSON.stringify(this.memberOptions)}
        ></c-dropdown>
        <c-btn
          name="downloadLegacyMemberData"
          value=""
          type="ghost"
          label="Export Members"
          action-label=""
          is-in-action={false}
          is-disabled={this.count > 0 ? false : true}
        ></c-btn>
      </div>
      <div class="control-group">
        {/* <this.Label value="Export" icon="download-outline"></this.Label> */}
      </div>
      {/* <c-divider type="single"></c-divider>
      <this.ItemCount></this.ItemCount> */}
    </div>
  );

  private AlertsControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Filter" icon="filter-outline"></this.Label>
        <c-dropdown
          name="alertStatus"
          option-str={JSON.stringify(this.alertStatus)}
        ></c-dropdown>
        <c-dropdown
          name="alertType"
          option-str={JSON.stringify(this.alertTypes)}
        ></c-dropdown>
      </div>
      {/* <c-divider type="single"></c-divider>
      <this.ItemCount></this.ItemCount> */}
    </div>
  );

  private AdminHomeControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Filter" icon="filter-outline"></this.Label>
        <c-dropdown
          name="eventsFilter"
          option-str={JSON.stringify(this.adminHomeOptions)}
        ></c-dropdown>
      </div>
      <div class="control-group">
        <this.Label value="Create" icon="create-outline"></this.Label>
        <c-btn
          name="openCreateEventModal"
          value=""
          type="ghost"
          label="Create new event"
          action-label=""
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
      {/* <div class="control-group">
        <this.Label value="Access" icon="key-outline"></this.Label>
        <c-btn
          name="goToDashboard"
          value=""
          type="ghost"
          label="Admin dashboard"
          action-label=""
          is-in-action={false}
          is-disabled={true}
        ></c-btn>
      </div> */}
    </div>
  );

  private ConfigureControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Preview" icon="eye-outline"></this.Label>
        <c-btn
          name="previewRegForm"
          value=""
          type="ghost"
          label="Preview"
          action-label=""
          is-in-action={false}
          is-disabled={false}
        ></c-btn>
      </div>
    </div>
  );

  @State() isInvoiceUploadDisabled: boolean = true;
  private InvoiceControlBar: FunctionalComponent = () => (
    <div class="control-bar-container">
      <div class="control-group">
        <this.Label value="Filter" icon="filter-outline"></this.Label>
        <c-dropdown
          name="invoiceYearsFilter"
          option-str={this.invoiceYearsString}
          isDisabled={false}
        ></c-dropdown>
        <div style={{ marginTop: "1em" }}>
          <this.Label value="Upload" icon="cloud-upload-outline"></this.Label>
          <c-btn
            name="uploadInvoiceGroup"
            value=""
            type="ghost"
            label="Upload invoice data"
            action-label=""
            is-in-action={false}
            is-disabled={false}
          ></c-btn>
        </div>
      </div>
    </div>
  );

  render() {
    if (this.name === "purchases") {
      return <this.PurchasesControlBar></this.PurchasesControlBar>;
    } else if (this.name === "registrants") {
      return <this.RegistrantsControlBar></this.RegistrantsControlBar>;
    } else if (this.name === "coupons") {
      return <this.CouponsControlBar></this.CouponsControlBar>;
    } else if (this.name === "members") {
      return <this.MembersControlBar></this.MembersControlBar>;
    } else if (this.name === "alerts") {
      return <this.AlertsControlBar></this.AlertsControlBar>;
    } else if (this.name === "skel") {
      return <this.SkelControlBar></this.SkelControlBar>;
    } else if (this.name === "home") {
      return <this.AdminHomeControlBar></this.AdminHomeControlBar>;
    } else if (this.name === "configure") {
      return <this.ConfigureControlBar></this.ConfigureControlBar>;
    } else if (this.name === "invoices") {
      return <this.InvoiceControlBar></this.InvoiceControlBar>;
    }
  }
}
