c-control-bar .control-bar-container {
  position: fixed;
  background: white;
  top: 0;
  left: 79%;
  padding: 1em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.5em;
  width: 180px;
  margin-top: 6em;
}

c-control-bar .control-bar-container c-dropdown select {
  margin-right: 0;
  margin-bottom: 1em;
}

c-control-bar .control-bar-container c-dropdown:last-child select {
  margin-bottom: 0;
}

c-control-bar .label-container {
  display: flex;
  align-items: center;
  opacity: 0.6;
  font-size: 0.9em;
  margin-bottom: 0.5em;
}

c-control-bar .label-container p {
  margin: 0;
  font-size: 1em;
  margin-left: 0.5em;
}

c-control-bar .control-group {
  margin-bottom: 1.75em;
}

c-control-bar .control-bar-container__registrants .control-group:last-child {
  margin: 0;
  border: 0;
  padding: 0;
}

c-control-bar .control-group:last-child {
  margin-bottom: 0;
}

c-control-bar .control-group .date-picker-container {
  width: 100%;
  margin-bottom: 1em;
}

c-control-bar .show-count {
  margin: 0;
  padding: 0;
  font-size: 0.8em;
}

c-control-bar c-text-link a {
  display: block;
  background: white;
  color: var(--accent-color);
  padding: 0.5em 1em;
  font-size: 0.9em;
  border: 0;
  border-radius: var(--border-radius);
  border: 1px solid var(--accent-color-bg);
  text-align: center;
}

c-control-bar .file-upload__input {
  display: block;
  background: rgba(0, 0, 0, 0.08);
  padding: 1em 0.5em 1em 0.75em;
  border-radius: 0.5em 0.5em 0 0;
  font-size: 0.8em;
}

@media only screen and (max-width: 768px) {
  c-control-bar .control-bar-container {
    display: block;
    position: static;
    margin: 0 0 1.5em 0;
    width: 100%;
    box-sizing: border-box;
  }

  c-control-bar .control-bar-container c-dropdown select {
    margin-right: 0;
    margin-bottom: 1em;
    width: 100%;
  }
}
