import { Component, Host, Prop, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
import { isUserLogged } from "../../utils/";

@Component({
  tag: "p-catch-all",
  styleUrl: "p-catch-all.css",
  shadow: true,
})
export class PCatchAll {
  @Prop() history: RouterHistory;

  componentDidLoad() {
    if (isUserLogged()) {
      this.history.push("/events", {});
    } else {
      this.history.push("/auth", {});
    }
  }

  render() {
    return <Host></Host>;
  }
}

injectHistory(PCatchAll);
