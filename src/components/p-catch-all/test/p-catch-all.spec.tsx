import { newSpecPage } from '@stencil/core/testing';
import { PCatchAll } from '../p-catch-all';

describe('p-catch-all', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PCatchAll],
      html: `<p-catch-all></p-catch-all>`,
    });
    expect(page.root).toEqualHtml(`
      <p-catch-all>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </p-catch-all>
    `);
  });
});
