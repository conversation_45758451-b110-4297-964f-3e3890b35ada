import { Component, Prop, Watch, h, State } from "@stencil/core";

@Component({
  tag: "c-ticket-track-session",
  styleUrl: "c-ticket-track-session.css",
  shadow: true,
})
export class CTicketTrackSession {
  /*---
  Props
  ----*/
  @Prop() ticketId: string;
  @Prop() sessionId: string;
  @Prop() ticketTitle: string;
  @Prop() type: string;
  @Prop() quantity: number;
  @Prop() startsOn: string;
  @Prop() endsOn: string;
  @Prop() studentPrice: number;
  @Prop() professionalPrice: number;
  @Prop() url: string;
  @Prop() instructors: string;
  @Prop() isVisible: boolean;
  @Prop() isDisabled: boolean;
  @Prop() isSoldOut: boolean;
  @Prop() persona: string;
  @Prop() isTicketInCart: boolean = false;
  @Prop() isTicketPurchased: boolean = false;
  @Prop() purchaseStatus: string = "";

  /*---
  State
  ----*/
  @State() isInCart: boolean = false;

  /*------
  Watchers
  ------*/
  @Watch("isTicketInCart") isTicketInCartWatcher(
    newVal: boolean,
    oldVal: boolean
  ) {
    console.log(`newVal: ${newVal}`);
    if (newVal != oldVal) {
      this.isInCart = this.isTicketInCart;
    }
  }

  /*---------------
  Lifecycle Methods
  ---------------*/
  componentWillLoad() {
    this.isInCart = this.isTicketInCart;
  }

  render() {
    return (
      <c-card>
        <div class={`ticket-container ${this.isDisabled && "disabled"}`}>
          <div class="ticket-container__details">
            <h1>
              <c-text type="subtext">{this.type.toUpperCase()}</c-text>
              {this.ticketTitle}
            </h1>
            <c-text theme="danger">{this.quantity} seats left</c-text>
          </div>
          <div class="ticket-container__price">
            {this.persona === "student" && (
              <div class="ticket-container__price__item">
                <c-text type="heading">₹{this.studentPrice}</c-text>
                <p class="bubble green-bubble">Student</p>
              </div>
            )}
            {this.persona === "professional" && (
              <div class="ticket-container__price__item">
                <c-text type="heading">₹{this.professionalPrice}</c-text>
                <p class="bubble blue-bubble">Professional</p>
              </div>
            )}
            <div class="ticket-container__price__footer ticket-container__price__footer--default">
              {this.isTicketPurchased ? (
                <div>
                  {this.purchaseStatus === "purchased" && (
                    <p class="ticket-purchased-confirmation">Purchased</p>
                  )}
                  {this.purchaseStatus === "under-verification" && (
                    <p class="ticket-under-verification">Under verification</p>
                  )}
                </div>
              ) : // <p class="ticket-purchased-confirmation">Purchased</p>
              this.isSoldOut ? (
                <e-text type="type">Sold out</e-text>
              ) : this.isInCart ? (
                <c-button
                  type="ghost_Danger_Small_onDark"
                  name="removeTrackSessionFromCart"
                  value={`${this.ticketId}#${this.sessionId}`}
                >
                  Remove from cart
                </c-button>
              ) : (
                <c-button
                  type="ghost_Small_onDark"
                  name="addTrackSessionToCart"
                  value={`${this.ticketId}#${this.sessionId}`}
                >
                  Add to cart
                </c-button>
              )}
            </div>
          </div>
        </div>
      </c-card>
    );
  }
}
