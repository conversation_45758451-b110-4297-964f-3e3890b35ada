import { newSpecPage } from '@stencil/core/testing';
import { CTicketTrackSession } from '../c-ticket-track-session';

describe('c-ticket-track-session', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [CTicketTrackSession],
      html: `<c-ticket-track-session></c-ticket-track-session>`,
    });
    expect(page.root).toEqualHtml(`
      <c-ticket-track-session>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </c-ticket-track-session>
    `);
  });
});
