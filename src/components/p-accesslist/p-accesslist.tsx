import { Component, State, h } from "@stencil/core";
import axios from "axios";
import state from "../../global/state";

@Component({
  tag: "p-accesslist",
  styleUrl: "p-accesslist.css",
})
export class PAccesslist {
  @State() isFetching: boolean = true;
  private payload;

  componentWillLoad() {
    axios({
      method: "GET",
      baseURL: `${state.baseUrl}/getaccesslist`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          alert(response.data.msg);
        } else if (response.data.status === "Success") {
          this.payload = response.data.payload;
          this.isFetching = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="container">
        {this.isFetching ? (
          <c-skel-card></c-skel-card>
        ) : (
          <div class="track-detail-container">
            <p>Total: {this.payload.total}</p>
            <table class="access-list">
              {this.payload.registrants.map((registrant) => (
                <tr class="access-list-item">
                  <td class="item-1">
                    {registrant.firstName} {registrant.lastName}
                  </td>
                  <td class="item-2">{registrant.email}</td>
                  <td class="item-3">{registrant.mobile}</td>
                  <td class="item-4">
                    {new Date(registrant.purchasedOn)
                      .toString()
                      .substring(4, 21)}
                  </td>
                </tr>
              ))}
            </table>
          </div>
        )}
      </div>
    );
  }
}
