p-accesslist .container {
  width: 840px;
  margin: 0 auto;
  margin-top: 2em;
  border-radius: 0.25em;
  padding: 2em;
  margin-bottom: 8em;
}

p-accesslist .access-list {
  width: 100%;
  margin: 0;
  padding: 0;
  list-style-type: none;
  background: white;
  border-radius: 0.25em;
  border: 1px solid rgba(0, 0, 0, 0.1);
  table-layout: fixed;
}

p-accesslist .access-list td {
  word-wrap: break-word;
}

p-accesslist .access-list-item {
  display: flex;
  justify-content: space-between;
  padding: 1em;
}

/* p-accesslist .access-list-item:nth-child(even) {
  background: rgba(0, 0, 0, 0.03);
} */

p-accesslist .track-detail-container .heading {
  color: #593196;
}

p-accesslist .item-1 {
  width: 25%;
}
p-accesslist .item-2 {
  width: 37%;
}
p-accesslist .item-3 {
  width: 18%;
}
p-accesslist .item-4 {
  width: 15%;
}
