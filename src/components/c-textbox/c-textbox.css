c-textbox input {
  box-sizing: border-box;
  width: 100%;
  outline: none;
  border: 0;
  border-radius: var(--site-border-radius);
  font-size: 0.9em;
  padding: 0.7em 0.5em;
  border: var(--site-border);
  transition: var(--site-transition);
}

c-textbox input::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

c-textbox input:focus {
  border: var(--site-border-focus);
}

/* Floating Label Textbox */
c-textbox .floating-label-container {
  height: 3em;
  margin: 1em 0;
  position: relative;
  width: 100%;
}

c-textbox .floating-label-container input,
c-textbox .floating-label-container label {
  cursor: text;
  font-size: 1em;
  padding: var(--site-padding);
  position: absolute;
  transition: all 0.15s ease;
  width: 100%;
}

c-textbox .floating-label-container input {
  border: var(--site-border);
  border-radius: var(--site-border-radius);
}

c-textbox .floating-label-container label {
  color: rgba(0, 0, 0, 0.4);
  padding: var(--site-padding);
}

c-textbox .floating-label-container input:focus {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

c-textbox .floating-label-container input.filled ~ label,
c-textbox .floating-label-container input:focus ~ label {
  font-size: 0.7em;
  font-weight: 400;
  position: absolute;
  top: -1.5em;
  display: block;
  padding: 0;
  padding-left: 1.25em;
}

c-textbox .error input {
  border: 1px solid var(--red-400);
}
