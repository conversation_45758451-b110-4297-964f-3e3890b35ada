import { Component, Event, EventEmitter, Prop, Watch, h } from "@stencil/core";

@Component({
  tag: "c-textbox",
  styleUrl: "c-textbox.css",
})
export class CTextbox {
  @Prop() type: string;
  @Prop() inputType: string;
  @Prop() name: string;
  @Prop() placeholder: string;
  @Prop() isDisabled: boolean;
  @Prop() isError: boolean;
  @Prop() value: string;
  @Prop() isInFocus: boolean;

  @Prop() isTextboxFilled: boolean = false;

  inputBox!: HTMLInputElement;

  @Event({
    eventName: "textInput",
    bubbles: true,
  })
  textInput: EventEmitter;

  @Watch("isTextboxFilled")
  isTextboxFilledWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      if (!this.isTextboxFilled) {
        this.inputBox.value = "";
      }
    }
  }

  @Watch("isInFocus") isInFocusWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      if (newVal) {
        this.focusInputBox();
      } else {
        this.blurInputBox();
      }
    }
  }

  focusInputBox() {
    this.inputBox.focus();
  }

  blurInputBox() {
    this.inputBox.blur();
  }

  handleInput(event) {
    if (event.target.value.length > 0) {
      this.isTextboxFilled = true;
    } else {
      this.isTextboxFilled = false;
    }

    if (event.target.value) {
      if (event.target.value.trim()) {
        this.textInput.emit({
          name: this.name,
          value: event.target.value.trim(),
        });
      }
    } else {
      this.textInput.emit({
        name: this.name,
        value: "",
      });
    }
  }

  componentDidLoad() {
    if (this.isInFocus) this.inputBox.focus();
  }

  render() {
    if (this.type === "floatingLabel") {
      return (
        <div class={`floating-label-container ${this.isError && "error"}`}>
          <input
            id={this.name}
            class={this.isTextboxFilled ? "textbox filled" : "textbox"}
            type={this.inputType}
            name={this.name}
            placeholder=""
            onInput={(event) => this.handleInput(event)}
            disabled={this.isDisabled}
            value={this.value}
            ref={(el) => (this.inputBox = el as HTMLInputElement)}
          />
          <label htmlFor={this.name}>{this.placeholder}</label>
        </div>
      );
    } else {
      return (
        <input
          class="textbox"
          type={this.inputType}
          name={this.name}
          placeholder={this.placeholder}
          onInput={(event) => this.handleInput(event)}
          disabled={this.isDisabled}
          value={this.value}
          ref={(el) => (this.inputBox = el as HTMLInputElement)}
        />
      );
    }
  }
}
