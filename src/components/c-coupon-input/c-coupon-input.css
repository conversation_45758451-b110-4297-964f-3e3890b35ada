c-coupon-input .copy {
  font-size: 0.9em;
  margin: 0;
  margin-bottom: 0em;
}

c-coupon-input .failed-message {
  color: red;
  margin-top: 0.25em;
  margin-bottom: 0;
}

c-coupon-input .success-message {
  color: green;
  font-weight: 700;
}

c-coupon-input c-inputbox .inputbox {
  margin-top: 0.5em;
  margin-bottom: 0em;
  padding: 0.5em 0 0.5em 0.75em;
  font-size: 0.9em;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid var(--accent-color-bg-lighter);
  width: 200px;
}

c-coupon-input c-radio .radio-btn-container {
  margin-top: 0;
  margin-right: 2em;
  margin-bottom: 0.25em;
}

c-coupon-input .fade {
  opacity: 0.1;
}

c-coupon-input .row {
  display: flex;
  align-items: baseline;
}

c-coupon-input .spinner-active c-inputbox .inputbox {
  width: 200px;
  margin-right: 1em;
}

c-coupon-input c-spinner-dark .spinner-icon {
  height: 15px;
  width: 15px;
}

c-coupon-input .coupon-applied-container c-btn button {
  font-size: 0.8em;
  border: 0;
  padding: 0.5em;
  color: var(--accent-green-darker);
  background: #eafaf1;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 1em;
}

c-coupon-input .coupon-applied-container c-btn button:hover {
  color: var(--accent-green-darker);
  background: #d5f5e3;
}

c-coupon-input .row-space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
