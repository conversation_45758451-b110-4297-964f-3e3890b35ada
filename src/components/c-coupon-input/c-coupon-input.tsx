import {
  Component,
  State,
  Event,
  EventEmitter,
  Listen,
  h,
} from "@stencil/core";
import state from "../../global/state";
import axios from "axios";

@Component({
  tag: "c-coupon-input",
  styleUrl: "c-coupon-input.css",
})
export class CCouponInput {
  @Event({
    eventName: "get-cart",
    bubbles: true,
  })
  getCartEventEmitter: EventEmitter;

  // @Listen("radio-change-event")
  // radioChangeEventHandler(event) {
  //   if (event.detail.name === "couponPosession") {
  //     if (event.detail.value === "yes") {
  //       state.isCouponInputEnabled = true;
  //     } else if (event.detail.value === "no") {
  //       state.isCouponInputEnabled = false;
  //     }
  //   }
  // }

  @Listen("checkbox-input-event")
  checkboxEventHandler(event) {
    if (event.detail.name === "couponPossession") {
      state.isCouponInputEnabled = event.detail.value;
    }
  }

  @Listen("input-event")
  inputEventHandler(event) {
    if (event.detail.name === "couponCodeInput") {
      this.couponCode = event.detail.value.trim();
      if (this.couponCode.length === 8) {
        this.applyCoupon(this.couponCode);
      }
    }
  }

  @Listen("button-click")
  buttonClickHandler(event) {
    if (event.detail.name === "clearCoupon") {
      this.clearCoupon();
    }
  }

  @State() componentState: string = "init";
  private couponCode: string;

  applyCoupon(couponCode) {
    let payload = {
      eventCode: state.eventCodeForRegistration,
      couponCode: couponCode,
      cartTotal: state.cartTotal,
    };

    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/check-and-apply-coupon-v2`,
      data: payload,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.componentState = "failed";
        } else if (response.data.status === "Success") {
          this.getCartEventEmitter.emit();
          this.componentState = "init";
          state.isCouponApplied = true;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  clearCoupon() {
    axios({
      method: "POST",
      baseURL: `${state.baseUrl}/clearcoupon`,
      withCredentials: true,
      responseType: "json",
    })
      .then((response) => {
        if (response.data.status === "Failed") {
          this.componentState = "failed";
        } else if (response.data.status === "Success") {
          this.getCartEventEmitter.emit();
          state.isCouponApplied = false;
          state.appliedCouponName = "";
          state.appliedCouponDeductionType = "";
          state.appliedCouponDeductionValue = 0;
          state.isCouponInputEnabled = false;
          state.isFreePurchase = false;
        }
      })
      .catch((error) => {
        alert(error);
      });
  }

  render() {
    return (
      <div class="coupon-input-container">
        <p class="copy">Do you have a coupon code?</p>
        <div
          class={`row ${
            this.componentState === "fetching" && "spinner-active"
          }`}
        >
          <c-inputbox
            type="text"
            name="couponCodeInput"
            placeholder="Enter Coupon Code"
            is-disabled={this.componentState === "fetching" ? true : false}
            value=""
          ></c-inputbox>

          {this.componentState === "fetching" && (
            <c-spinner-dark></c-spinner-dark>
          )}
        </div>

        {this.componentState === "failed" ? (
          <p class="copy failed-message">Invalid coupon code</p>
        ) : (
          ""
        )}
      </div>
    );
  }
}
