import { Component, Listen, State, Prop, h } from "@stencil/core";
import { RouterHistory, injectHistory } from "@stencil/router";
// import { App } from "../../global/app";
// import axios from "axios";
// import Store from "../../global/store";
import state from "../../global/state";

@Component({
  tag: "p-regform",
  styleUrl: "p-regform.css",
})
export class PRegform {
  @Prop() history: RouterHistory;
  @State() isFetchingData: boolean = true;
  @State() isModalVisible: boolean = false;
  @State() isToastActive: boolean = false;
  @State() activeView: string = "tickets";

  @Listen("vnav-route-change")
  vNavRouteChangeHandler(event) {
    this.activeView = event.detail.cNavItemName;
  }

  private toastType: string;
  private toastLabel: string;
  private toastDuration: number = 7;

  // private navOpts = [
  //   {
  //     name: "tickets",
  //     label: "Conference Tickets",
  //     state: "active",
  //     subText: "",
  //     route: "",
  //   },
  //   {
  //     name: "workshops&courses",
  //     label: "Workshops & Courses",
  //     state: "",
  //     subText: "",
  //     route: "",
  //   },
  // ];

  private navOpts = [
    {
      type: "navItem",
      name: "tickets",
      label: "Conference Tickets",
      state: "active",
      subText: "",
      route: "",
    },
    {
      type: "navItem",
      name: "workshops&courses",
      label: "Workshops & Courses",
      state: "disabled",
      subText: "REGISTRATION CLOSED",
      route: "",
    },
  ];

  componentWillLoad() {
    // this.history.push("/events", {});
    // state.isFreePurchase = false;
    // state.isCouponApplied = false;
    // axios({
    //   method: "GET",
    //   baseURL: `${state.baseUrl}/events`,
    //   withCredentials: true,
    //   responseType: "json",
    // })
    //   .then((response) => {
    //     if (response.data.status === "Failed") {
    //       alert("Failed to fetch data");
    //     } else if (response.data.status === "Success") {
    //       Store.setFirstName(response.data.payload.profile.name.first);
    //       Store.setLastName(response.data.payload.profile.name.last);
    //       Store.setOccupation(response.data.payload.professional.occupation);
    //       Store.setIsMember(response.data.payload.membership.isMember);
    //       Store.setCurrency(response.data.payload.settings.currency.name);
    //       Store.setCurrencySymbol(
    //         response.data.payload.settings.currency.symbol
    //       );
    //       state.firstName = response.data.payload.profile.name.first;
    //       state.lastName = response.data.payload.profile.name.last;
    //       state.isRegistrationManager =
    //         response.data.payload.settings.isRegistrationManager;
    //       state.isSponsor = response.data.payload.settings.isSponsor;
    //       state.isGuest = response.data.payload.settings.isGuest;
    //       state.isPrimaryTicketDiscounted =
    //         response.data.payload.settings.isPrimaryTicketDiscounted;
    //       if (!response.data.payload.settings.billing) {
    //         this.isModalVisible = true;
    //       }
    //       this.isFetchingData = false;
    //     }
    //   })
    //   .catch((error) => {
    //     alert(error);
    //   });
  }

  @Listen("checkout-btn-click-event")
  checkoutBtnHandler() {
    this.history.push("/confirm", {});
  }

  @Listen("member-discount-applied")
  membershipDiscountAppliedHandler(event) {
    event.target.closest("c-section-get-member-discount").remove();
    state.isMemberDiscountApplied = true;
  }

  @Listen("close-save-invoice-pref-modal")
  closeSaveInvoicePrefModal(event) {
    this.isModalVisible = false;
    this.showToast({
      type: event.detail.status,
      label: event.detail.msg,
    });
  }

  @Listen("hide-toast")
  hideToastHandler() {
    this.isToastActive = false;
  }

  showToast(obj) {
    this.toastType = obj.type;
    this.toastLabel = obj.label;
    this.isToastActive = true;
  }

  render() {
    return (
      <div>
        {this.isToastActive ? (
          <c-toast
            type={this.toastType}
            label={this.toastLabel}
            duration={this.toastDuration}
          ></c-toast>
        ) : (
          ""
        )}
        {this.isModalVisible ? <c-tax-pref-modal></c-tax-pref-modal> : ""}
        <div class={this.isModalVisible ? "dark-overlay" : ""}></div>
        <c-topbar></c-topbar>
        <main>
          <c-vnav nav-opts-str={JSON.stringify(this.navOpts)}></c-vnav>
          {this.activeView === "tickets" && <c-section-list></c-section-list>}

          <c-cart-preview></c-cart-preview>
        </main>
      </div>
    );
  }
}

injectHistory(PRegform);
