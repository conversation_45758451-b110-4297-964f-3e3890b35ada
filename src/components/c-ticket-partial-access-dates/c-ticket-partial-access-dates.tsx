import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Listen,
  Prop,
  State,
  Watch,
  Host,
  h,
} from "@stencil/core";

@Component({
  tag: "c-ticket-partial-access-dates",
  styleUrl: "c-ticket-partial-access-dates.css",
})
export class CTicketPartialAccessDates {
  /*-----------
  Event Emitter
  -----------*/
  @Event({
    eventName: "addAccessDates_partialTicket",
    bubbles: true,
  })
  addAccessDates_partialTicket: EventEmitter;

  /*-------------
  Event Listeners  
  -------------*/
  @Listen("accessDateClicked") handleTicketTierClick(e) {
    this.accessName_Old = e.detail.name;
    this.accessDateId_Old = e.detail.id;
    this.accessIsMultiDay_Old = e.detail.isMultiDay;
    this.accessStartDate_Old = e.detail.startDate;
    this.accessEndDate_Old = e.detail.endDate;
    this.mode = "editAccessDate";
    console.log(this.accessDateId_Old);
  }

  @Listen("buttonClick")
  buttonClickHandler(e) {
    if (e.detail.name === "newAccessDate") {
      this.mode = "newAccessDate";
    } else if (e.detail.name === "cancelAccessDate") {
      this.resetComponent();
      this.mode = "viewAccessDates";
    } else if (e.detail.name === "createAccessDate") {
      this.createAccessDate();
    } else if (e.detail.name === "deleteAccessDate") {
      this.deleteAccessDate();
    } else if (e.detail.name === "editAccessDate") {
      this.editAccessDate();
    }
  }

  @Listen("textInput")
  textInputHandler(e) {
    if (e.detail.name === "accessName") {
      this.accessName = e.detail.value;
    }
    this.validateInputs();
  }

  @Listen("radio-change-event")
  radioChangeEventHandler(e) {
    if (e.detail.name === "accessType") {
      if (e.detail.value === "singleDay") {
        this.isMultiDayAccess = false;
        this.accessEndDate = "";
      } else if (e.detail.value === "multiDay") {
        this.isMultiDayAccess = true;
      }
    }
    this.validateInputs();
  }

  @Listen("dateInput") handleDateInput(e) {
    if (e.detail.name === "accessStartDate") {
      this.accessStartDate = e.detail.value;
    } else if (e.detail.name === "accessEndDate") {
      this.accessEndDate = e.detail.value;
    }
    this.validateInputs();
  }

  /*---
  Props
  ---*/
  @Prop() accessDatesString: string = "";

  /*----
  States  
  ----*/
  @State()
  mode: string = "viewAccessDates";
  @State() isSaveAccessDateDisabled: boolean = true;
  @State() accessDates: any = [];
  @State() isMultiDayAccess: boolean = false;

  /*------
  Watchers  
  ------*/
  @Watch("accessDatesString") accessDatesStringWatcher(
    newVal: string,
    oldVal: string
  ) {
    if (newVal != oldVal) {
      this.generateAccessDateList();
    }
  }

  /*-------
  Variables  
  -------*/
  private accessName: string = "";
  private accessStartDate: string = "";
  private accessEndDate: string = "";

  private accessDateId_Old: string = "";
  private accessName_Old: string = "";
  private accessIsMultiDay_Old: boolean = false;
  private accessStartDate_Old: string = "";
  private accessEndDate_Old: string = "";

  /*---------------
  Lifecycle Methods  
  ---------------*/
  componentWillLoad() {
    if (this.accessDatesString.length > 0) {
      this.generateAccessDateList();
    }
  }

  /*-------
  Functions  
  -------*/
  createAccessDate() {
    let obj = {
      id: Math.random().toString(),
      accessName: this.accessName,
      isMultiDayAccess: this.isMultiDayAccess,
      accessStartDate: this.accessStartDate,
      accessEndDate: this.accessEndDate,
    };
    this.addAccessDates_partialTicket.emit({
      accessDateObj: obj,
    });
    this.isSaveAccessDateDisabled = true;
    this.mode = "viewAccessDates";
    this.resetComponent();
  }

  deleteAccessDate() {
    console.log("delete access date");
  }

  editAccessDate() {
    console.log("edit access date");
  }

  generateAccessDateList() {
    this.accessDates = JSON.parse(this.accessDatesString);
    this.accessDates = [...this.accessDates];
  }

  resetComponent() {
    this.accessName = "";
    this.isMultiDayAccess = false;
    this.accessStartDate = "";
    this.accessEndDate = "";

    this.accessName_Old = "";
    this.accessIsMultiDay_Old = false;
    this.accessStartDate_Old = "";
    this.accessEndDate_Old = "";
  }

  validateInputs() {
    if (this.mode === "newAccessDate") {
      this.validateInputs_NewAccessDate();
    } else if (this.mode === "editAccessDate") {
      this.validateInputs_EditAccessDate();
    }
  }

  validateInputs_NewAccessDate() {
    if (this.isMultiDayAccess) {
      if (
        this.accessName.length > 0 &&
        this.accessStartDate.length > 0 &&
        this.accessEndDate.length > 0
      ) {
        this.isSaveAccessDateDisabled = false;
      } else {
        this.isSaveAccessDateDisabled = true;
      }
    } else {
      if (this.accessName.length > 0 && this.accessStartDate.length > 0) {
        this.isSaveAccessDateDisabled = false;
      } else {
        this.isSaveAccessDateDisabled = true;
      }
    }
  }

  validateInputs_EditAccessDate() {
    let hasAccessNameChanged: boolean = false;
    let hasAccessStartDateChanged: boolean = false;
    let hasAccessEndDateChanged: boolean = false;

    if (this.accessName != this.accessName_Old) {
      hasAccessNameChanged = true;
    } else {
      hasAccessNameChanged = false;
    }

    if (this.accessStartDate != this.accessStartDate_Old) {
      hasAccessStartDateChanged = true;
    } else {
      hasAccessStartDateChanged = false;
    }

    if (this.accessEndDate != this.accessEndDate_Old) {
      hasAccessEndDateChanged = true;
    } else {
      hasAccessEndDateChanged = false;
    }

    if (
      hasAccessNameChanged ||
      hasAccessStartDateChanged ||
      hasAccessEndDateChanged
    ) {
      this.isSaveAccessDateDisabled = false;
    } else {
      this.isSaveAccessDateDisabled = true;
    }
  }

  /*-------------------
  Functional Components 
  -------------------*/
  ViewAccessDate: FunctionalComponent = () =>
    this.accessDates.length > 0 ? (
      this.accessDates.map((date: any) => (
        <c-ticket-partial-access-date-item
          name={date.accessName}
          dateId={date.id}
          isMultiDay={date.isMultiDayAccess}
          startDate={date.accessStartDate}
          endDate={date.accessEndDate}
        ></c-ticket-partial-access-date-item>
      ))
    ) : (
      <div class="no-access-dates-container">
        <c-text>Found 0 dates</c-text>
      </div>
    );

  InputAccessDate: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          ACCESS NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="accessName"
          placeholder="e.g. 1 Day conference ticket, Workshop/Course pass"
          isDisabled={false}
          value={this.mode === "editAccessDate" ? this.accessName : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        {/* <c-text>Do you want to create a multi-day access?</c-text> */}
        <c-text type="modalLabel" isMandatory={true}>
          ALLOW MULTI-DAY ACCESS?
        </c-text>
        <div class="radio-group">
          <c-radio
            name="accessType"
            label1="Yes"
            label2=""
            label3=""
            val="multiDay"
            isChecked={this.isMultiDayAccess}
          ></c-radio>
          <c-radio
            name="accessType"
            label1="No"
            label2=""
            label3=""
            val="singleDay"
            isChecked={!this.isMultiDayAccess}
          ></c-radio>
        </div>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {this.isMultiDayAccess
            ? "SELECT ACCESS START & END DATES"
            : "SELECT ACCESS DATE"}
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="accessStartDate"
            pickTime={false}
            date={this.accessStartDate}
          ></c-date-picker>
          {this.isMultiDayAccess && (
            <c-date-picker
              name="accessEndDate"
              pickTime={false}
              date={this.accessEndDate}
            ></c-date-picker>
          )}
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelAccessDate">
            Cancel
          </c-button>
          <div class="button-group">
            <c-button
              type="ghost_Small"
              name="createAccessDate"
              isDisabled={this.isSaveAccessDateDisabled}
            >
              Create
            </c-button>
          </div>
        </c-row>
      </div>
    </div>
  );

  EditAccessDate: FunctionalComponent = () => (
    <div class="input-item-container">
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          ACCESS NAME
        </c-text>
        <c-textbox
          input-type="text"
          name="accessName"
          placeholder="e.g. 1 Day conference ticket, Workshop/Course pass"
          isDisabled={false}
          value={this.mode === "editAccessDate" ? this.accessName_Old : ""}
        ></c-textbox>
      </div>
      <div class="field-container">
        <c-text type="modalLabel" isMandatory={true}>
          {this.isMultiDayAccess
            ? "SELECT ACCESS START & END DATES"
            : "SELECT ACCESS DATE"}
        </c-text>
        <div class="input-date-picker-container">
          <c-date-picker
            name="accessStartDate"
            pickTime={false}
            date={this.accessStartDate_Old}
          ></c-date-picker>
          {this.accessIsMultiDay_Old && (
            <c-date-picker
              name="accessEndDate"
              pickTime={false}
              date={this.accessEndDate_Old}
            ></c-date-picker>
          )}
        </div>
      </div>
      <div class="field-container last-field-container">
        <c-row>
          <c-button type="link_Small" name="cancelAccessDate">
            Cancel
          </c-button>
          <c-row>
            <c-button type="ghost_Danger_Small" name="deleteAccessDate">
              Delete
            </c-button>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <c-button
              type="ghost_Small"
              name="editAccessDate"
              isDisabled={this.isSaveAccessDateDisabled}
            >
              Save
            </c-button>
          </c-row>
        </c-row>
      </div>
    </div>
  );

  CreateAccessDate: FunctionalComponent = () => (
    <div class="ticket-price-table__footer">
      <c-button
        name="newAccessDate"
        type="newAccessDate"
        isDisabled={false}
        isInAction={false}
      ></c-button>
    </div>
  );

  render() {
    return (
      <Host>
        <div class="ticket-eligibility__top-row">
          <c-text type="modalLabel" isMandatory={true}>
            CONFERENCE ACCESS DATES
          </c-text>
        </div>
        <div class="ticket-eligibility-table__header">
          <div class="ticket-eligibility-table__header--item ticket-eligibility-table__header--item--1">
            <c-text>Access name</c-text>
          </div>
          <div class="ticket-eligibility-table__header--item ticket-eligibility-table__header--item--2">
            <c-text>Access start</c-text>
          </div>
          <div class="ticket-eligibility-table__header--item ticket-eligibility-table__header--item--3">
            <c-text>Access end</c-text>
          </div>
        </div>
        <div class="ticket-eligibility-table__body">
          {this.mode === "viewAccessDates" && (
            <this.ViewAccessDate></this.ViewAccessDate>
          )}
          {this.mode === "newAccessDate" && (
            <this.InputAccessDate></this.InputAccessDate>
          )}
          {this.mode === "editAccessDate" && (
            <this.EditAccessDate></this.EditAccessDate>
          )}
          {this.mode === "viewAccessDates" && (
            <this.CreateAccessDate></this.CreateAccessDate>
          )}
        </div>
      </Host>
    );
  }
}
