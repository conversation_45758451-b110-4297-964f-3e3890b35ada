import Joi from "@hapi/joi";

const isLoginInputValid = (loginInput) => {
  const schema = Joi.object({
    email: Joi.string()
      .email({ tlds: { allow: false } })
      .min(5)
      .max(128)
      .trim()
      .required(),
    password: Joi.string().min(8).max(1024).required(),
    isRememberMe: Joi.boolean().required(),
  });

  return schema.validate(loginInput);
};

export default isLoginInputValid;
