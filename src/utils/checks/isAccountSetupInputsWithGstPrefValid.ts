import Joi from "@hapi/joi";

const isAccountSetupInputsWithGstPrefValid = (accountSetupInput) => {
  const schema = Joi.object({
    checkpointID: Joi.string().trim().min(5).max(128).required(),
    occupation: Joi.string().trim().min(1).max(1024).required(),
    isHCIPAIMember: Joi.boolean(),
    orgInsti: Joi.string().trim().min(1).max(1024).required(),
    jobDegree: Joi.string().trim().min(1).max(1024).required(),
    country: Joi.string().trim().min(1).max(1024).required(),
    currency: Joi.string().trim().min(1).max(1024).required(),
    isGSTInvoicePreferred: Joi.boolean().required(),
    businessName: Joi.string().required(),
    taxID: Joi.string().required(),
    taxJurisdiction: Joi.string().min(1).required(),
    billingAddressLine1: Joi.string().min(1).required(),
    billingAddressLine2: Joi.string().min(1).required(),
    billingAddressLine3: Joi.string().min(1).required(),
  });

  return schema.validate(accountSetupInput);
};

export default isAccountSetupInputsWithGstPrefValid;
