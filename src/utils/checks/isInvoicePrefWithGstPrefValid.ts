import Joi from "@hapi/joi";

const isInvoicePrefWithGstPrefValid = (accountSetupInput) => {
  const schema = Joi.object({
    isGSTInvoicePreferred: Joi.boolean().required(),
    businessName: Joi.string().required(),
    taxID: Joi.string().required(),
    taxJurisdiction: Joi.string().min(1).required(),
    billingAddressLine1: Joi.string().min(1).required(),
    billingAddressLine2: Joi.string().min(1).required(),
    billingAddressLine3: Joi.string().min(1).required(),
  });

  return schema.validate(accountSetupInput);
};

export default isInvoicePrefWithGstPrefValid;
