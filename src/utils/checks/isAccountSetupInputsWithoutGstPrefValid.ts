import Joi from "@hapi/joi";

const isAccountSetupInputsWithoutGstPrefValid = (accountSetupInput) => {
  const schema = Joi.object({
    checkpointID: Joi.string().trim().min(5).max(128).required(),
    occupation: Joi.string().trim().min(1).max(1024).required(),
    isHCIPAIMember: Joi.boolean(),
    orgInsti: Joi.string().trim().min(1).max(1024).required(),
    jobDegree: Joi.string().trim().min(1).max(1024).required(),
    country: Joi.string().trim().min(1).max(1024).required(),
    currency: Joi.string().trim().min(1).max(1024).required(),
    isGSTInvoicePreferred: Joi.boolean().required(),
    businessName: Joi.string().allow(""),
    taxID: Joi.string().allow(""),
    taxJurisdiction: Joi.string().min(1).required(),
    billingAddressLine1: Joi.string().min(1).allow(""),
    billingAddressLine2: Joi.string().min(1).allow(""),
    billingAddressLine3: Joi.string().min(1).allow(""),
  });

  return schema.validate(accountSetupInput);
};

export default isAccountSetupInputsWithoutGstPrefValid;
