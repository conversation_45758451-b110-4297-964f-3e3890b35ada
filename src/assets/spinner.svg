<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
  style="margin: auto; background: none; display: block; shape-rendering: auto;" width="201px" height="201px"
  viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
  <circle cx="50" cy="50" fill="none" stroke="#ffffff" stroke-width="7" r="45"
    stroke-dasharray="212.05750411731105 72.68583470577035" transform="rotate(48.0749 50 50)">
    <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="0.9615384615384615s"
      values="0 50 50;360 50 50" keyTimes="0;1"></animateTransform>
  </circle>
  <!-- [ldio] generated by https://loading.io/ -->
</svg>