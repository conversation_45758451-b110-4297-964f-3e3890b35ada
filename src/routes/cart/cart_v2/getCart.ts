import { Router } from "express";
import { userModel, ticketModel_v2 } from "../../../models";
import { isUserLogged, getUserID } from "../../../middleware";
import moment from "moment";

const router = Router();

router.post("/get-cart", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  let persona = userObj.professional.occupation;

  /*-----------------------
  Extract & validate inputs
  -----------------------*/
  let { eventCode } = req.body;
  let cartv2: any = userObj?.cartv2;
  let cartv2ForEventCode: any = [];

  cartv2.map((cartItem: any) => {
    if (cartItem.eventCode === eventCode) {
      cartv2ForEventCode.push(cartItem);
    }
  });

  let cartItemIDs: any = [];
  let trackSessionsIdsInCart: any = [];

  cartv2ForEventCode.forEach((item: any) => {
    cartItemIDs.push(item.ticketId);
    if (item.sessionId) {
      trackSessionsIdsInCart.push(item.sessionId);
    }
  });

  let cartItemDetails = await ticketModel_v2
    .find()
    .where("ticketId")
    .in(cartItemIDs)
    .exec();

  let cartItems: any = [];
  let cartTotal: number = 0;
  cartItemDetails.forEach((ticket: any) => {
    let obj = {};
    let price: number = 0;
    let tierName: string = "";
    let ticketTitle: string = "";
    let ticketSubtitle: string = "";

    if (ticket.ticketType === "fullTicket") {
      ticketTitle = ticket.fullTicketDetails.title;
      let ticketPricingTiers: any = ticket.fullTicketDetails.tiers;
      let tierStudentPrice: number = 0;
      let tierProfessionalPrice: number = 0;
      ticketPricingTiers.map((tier: any) => {
        let currentTime: string = moment().toISOString();
        let isBetween: any = moment(currentTime).isBetween(
          tier.tierStartDate,
          tier.tierEndDate
        );

        if (isBetween) {
          tierName = tier.tierName;
          tierStudentPrice = tier.studentPrice;
          tierProfessionalPrice = tier.professionalPrice;
        }
      });

      if (persona === "student") {
        price = tierStudentPrice;
        ticketSubtitle = "For students";
      } else if (persona === "professional") {
        price = tierProfessionalPrice;
        ticketSubtitle = "For professionals";
      }
    } else if (ticket.ticketType === "basicTicket") {
      ticketTitle = ticket.basicTicketDetails.title;
      let ticketPricingTiers: any = ticket.basicTicketDetails.tiers;
      let tierStudentPrice: number = 0;
      let tierProfessionalPrice: number = 0;
      ticketPricingTiers.map((tier: any) => {
        let currentTime: string = moment().toISOString();
        let isBetween: any = moment(currentTime).isBetween(
          tier.tierStartDate,
          tier.tierEndDate
        );

        if (isBetween) {
          tierName = tier.tierName;
          tierStudentPrice = tier.studentPrice;
          tierProfessionalPrice = tier.professionalPrice;
        }
      });

      if (persona === "student") {
        price = tierStudentPrice;
        ticketSubtitle = "-";
      } else if (persona === "professional") {
        price = tierProfessionalPrice;
        ticketSubtitle = "-";
      }
    } else if (ticket.ticketType === "trackTicket") {
      let trackSessions: any = ticket.trackTicketDetails.sessions;
      trackSessionsIdsInCart.map((trackSessionIdInCart: any) => {
        trackSessions.map((trackSession: any) => {
          if (trackSessionIdInCart === trackSession.id) {
            let sessionPrice: number =
              persona === "professional"
                ? trackSession.professionalPrice
                : trackSession.studentPrice;
            let obj = {
              ticketID: ticket.ticketId,
              sessionId: trackSessionIdInCart,
              title: trackSession.title,
              subTitle: trackSession.type,
              tier: "",
              currency: "₹",
              price: sessionPrice,
            };

            cartItems.push(obj);
            cartTotal = cartTotal + sessionPrice;
          }
        });
      });
    }

    if (ticket.ticketType != "trackTicket") {
      obj = {
        ticketID: ticket.ticketId,
        title: ticketTitle,
        subTitle: ticketSubtitle,
        tier: tierName,
        currency: "₹",
        price: price,
      };

      cartItems.push(obj);
      cartTotal = cartTotal + price;
    }
  });

  let payload = {
    cartTotal: cartTotal,
    cartItems: cartItems,
  };

  let resp = {
    status: "Success",
    msg: "Cart items retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
