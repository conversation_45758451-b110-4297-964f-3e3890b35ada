// @ts-nocheck

import { Router } from "express";
import { userModel, ticketModel_v2 } from "../../../models";
import { isUserLogged, getUserID } from "../../../middleware";

const router = Router();

router.post("/remove-from-cart", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ---------------------------------
  Extract ticket details from req.body
  --------------------------------- */
  let { ticketId, sessionId, eventCode } = req.body;

  /* ---------------------
  Check if ticketId exists
  --------------------- */

  let ticketObj = await ticketModel_v2.findOne({
    ticketId: ticketId,
    eventCode: eventCode,
  });
  if (!ticketObj) {
    let resp = {
      status: "Failed",
      msg: "Ticket doesn't exist",
    };
    return res.status(400).json(resp);
  }
  let isTicketPrimary: boolean = ticketObj.isTicketPrimary;

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  let isTicketInCart: boolean = false;

  userObj?.cartv2.forEach((ticket: any) => {
    if (ticket.ticketId === ticketId) {
      isTicketInCart = true;
    }
  });

  if (!isTicketInCart) {
    let resp = {
      status: "Failed",
      msg: "Ticket is not in cart",
    };
    return res.status(400).json(resp);
  }

  /* -------------------------------------
  Remove ticket from cart
  ------------------------------------- */
  let pullObj: any;

  if (isTicketPrimary) {
    pullObj = {
      eventCode: eventCode,
    };
  } else {
    if (sessionId) {
      pullObj = {
        ticketId: ticketId,
        eventCode: eventCode,
        sessionId: sessionId,
      };
    } else {
      pullObj = {
        ticketId: ticketId,
        eventCode: eventCode,
      };
    }
  }

  userModel.findOneAndUpdate(
    { _userID: _userID },
    { $pull: { cartv2: pullObj } },
    (error, success) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "Could not remove ticket to cart",
        };
        return res.json(resp);
      } else {
        let resp = {
          status: "Success",
          msg: "Ticket removed from cart",
        };
        return res.json(resp);
      }
    }
  );
});

export default router;
