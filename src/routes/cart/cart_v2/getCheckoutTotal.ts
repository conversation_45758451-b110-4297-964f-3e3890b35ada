import { Router } from "express";
import { userModel, ticketModel_v2, couponModel } from "../../../models";
import { isUserLogged, getUserID } from "../../../middleware";
import { validateCheckoutInputs } from "../../../validation";
import moment from "moment";

const router = Router();

router.post(
  "/get-checkout-total",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*-----------------------
    Check if user is LoggedIn
    -----------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }
    /*--------------------------------
    Check if checkout inputs are valid
    --------------------------------*/
    let { error } = validateCheckoutInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.status(400).send(resp);
    }

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    let persona = userObj.professional.occupation;

    /*-----------------------
    Extract & validate inputs
    -----------------------*/
    let { eventCode, paymentGateway } = req.body;
    let cartv2: any = userObj?.cartv2;
    let cartv2ForEventCode: any = [];

    cartv2.map((cartItem: any) => {
      if (cartItem.eventCode === eventCode) {
        cartv2ForEventCode.push(cartItem);
      }
    });
    let cartItemIDs: any = [];
    let trackSessionsIdsInCart: any = [];

    cartv2ForEventCode.forEach((item: any) => {
      cartItemIDs.push(item.ticketId);
      if (item.sessionId) {
        trackSessionsIdsInCart.push(item.sessionId);
      }
    });

    let cartItemDetails = await ticketModel_v2
      .find()
      .where("ticketId")
      .in(cartItemIDs)
      .exec();

    let cartItems: any = [];
    let cartTotal: number = 0;

    cartItemDetails.forEach((ticket: any) => {
      let obj = {};
      let price: number = 0;
      let tierName: string = "";
      let ticketTitle: string = "";
      let ticketSubtitle: string = "";

      if (ticket.ticketType === "fullTicket") {
        ticketTitle = ticket.fullTicketDetails.title;
        let ticketPricingTiers: any = ticket.fullTicketDetails.tiers;
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierName = tier.tierName;
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (persona === "student") {
          price = tierStudentPrice;
          ticketSubtitle = "For students";
        } else if (persona === "professional") {
          price = tierProfessionalPrice;
          ticketSubtitle = "For professionals";
        }
      } else if (ticket.ticketType === "basicTicket") {
        ticketTitle = ticket.basicTicketDetails.title;
        let ticketPricingTiers: any = ticket.basicTicketDetails.tiers;
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierName = tier.tierName;
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (persona === "student") {
          price = tierStudentPrice;
          ticketSubtitle = "-";
        } else if (persona === "professional") {
          price = tierProfessionalPrice;
          ticketSubtitle = "-";
        }
      } else if (ticket.ticketType === "trackTicket") {
        let trackSessions: any = ticket.trackTicketDetails.sessions;
        trackSessionsIdsInCart.map((trackSessionIdInCart: any) => {
          trackSessions.map((trackSession: any) => {
            if (trackSessionIdInCart === trackSession.id) {
              let sessionPrice: number =
                persona === "professional"
                  ? trackSession.professionalPrice
                  : trackSession.studentPrice;
              let obj = {
                ticketID: ticket.ticketId,
                sessionId: trackSessionIdInCart,
                title: trackSession.title,
                subTitle: trackSession.type,
                tier: "",
                currency: "₹",
                price: sessionPrice,
              };

              cartItems.push(obj);
              cartTotal = cartTotal + sessionPrice;
            }
          });
        });
      }

      if (ticket.ticketType != "trackTicket") {
        obj = {
          ticketID: ticket.ticketId,
          title: ticketTitle,
          subTitle: ticketSubtitle,
          tier: tierName,
          currency: "₹",
          price: price,
        };

        cartItems.push(obj);
        cartTotal = cartTotal + price;
      }
    });

    /* ------------------------
    Discount coupon calculation
     ------------------------ */
    let isCouponApplied: boolean = false;
    if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
    let couponName: string = "";
    let couponDeductionType: string = "";
    let subDeductionType: string = "";
    let couponDeductionValue: number = 0;
    let subDeductionValue: number = 0;
    let couponTicketTypeIds: any;
    let percentageDeductionValue: number = 0;
    let deductedAmount: number = 0;
    let oldCartTotal: number = 0;
    oldCartTotal = cartTotal;

    if (isCouponApplied) {
      let couponObj: any = await couponModel.findOne({
        id: userObj.coupon.id,
      });
      couponName = couponObj.name;
      couponDeductionType = couponObj.deduction.type;
      couponDeductionValue = couponObj.deduction.value;
      let couponTicketTypeIdsUnprocessed: any =
        couponObj.deduction.ticketTypes.items;
      let couponTicketTypeIds: any = [];
      couponTicketTypeIdsUnprocessed.map((ticket: any) => {
        couponTicketTypeIds.push(ticket.ticketId);
      });
      if (couponDeductionType === "fixed") {
        cartTotal = cartTotal - couponDeductionValue;
        deductedAmount = couponDeductionValue;
      } else if (couponDeductionType === "percentage") {
        percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
        cartTotal = cartTotal - percentageDeductionValue;
        deductedAmount = percentageDeductionValue;
      } else if (couponDeductionType === "ticketType") {
        subDeductionType = couponObj.deduction.ticketTypes.subDeductionType;
        subDeductionValue = couponObj.deduction.ticketTypes.subDeductionValue;
        let ticketDetailsArr: any = await ticketModel_v2
          .find()
          .where("ticketId")
          .in(couponTicketTypeIds)
          .exec();

        let finalProcessingArr: any = [];
        cartItemDetails.map((cartItemDetail: any) => {
          ticketDetailsArr.map((ticketDetail: any) => {
            if (cartItemDetail.ticketId === ticketDetail.ticketId) {
              finalProcessingArr.push(cartItemDetail);
            }
          });
        });

        let ticketDeductionValue: number = 0;
        finalProcessingArr.map((ticket: any) => {
          let price: number = 0;
          let ticketPricingTiers: any;

          if (ticket.ticketType === "fullTicket") {
            ticketPricingTiers = ticket.fullTicketDetails.tiers;
          } else if (ticket.ticketType === "basicTicket") {
            ticketPricingTiers = ticket.basicTicketDetails.tiers;
          }

          let tierStudentPrice: number = 0;
          let tierProfessionalPrice: number = 0;

          ticketPricingTiers.map((tier: any) => {
            let currentTime: string = moment().toISOString();
            let isBetween: any = moment(currentTime).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );
            if (isBetween) {
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });

          if (persona === "student") {
            price = tierStudentPrice;
          } else if (persona === "professional") {
            price = tierProfessionalPrice;
          }

          if (subDeductionType === "fixed") {
            ticketDeductionValue = price - subDeductionValue;
            deductedAmount = deductedAmount + subDeductionValue;
          } else if (subDeductionType === "percentage") {
            percentageDeductionValue = (subDeductionValue / 100) * price;
            ticketDeductionValue = price - percentageDeductionValue;
            deductedAmount = deductedAmount + percentageDeductionValue;
          }
        });
        cartTotal = cartTotal - deductedAmount;
      }
    }

    /* --------------------
    Grand Total Calculation
    -------------------- */
    let gatewayPerc = 0;
    let grandTotal = 0;
    let gatewayFee = 0;
    if (paymentGateway === "razorpay") {
      gatewayPerc = 4;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "bank") {
      gatewayPerc = 0;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "instamojo") {
      gatewayPerc = 3;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else {
      let resp = {
        status: "Failed",
        msg: "Invalid payment gateway",
      };
      return res.status(400).json(resp);
    }

    let payload = {
      gatewayPerc: gatewayPerc,
      currency: "₹",
      cartTotal: isCouponApplied ? oldCartTotal : cartTotal,
      gatewayFee: Math.round(gatewayFee),
      grandTotal: Math.round(grandTotal),
      coupon: {
        isApplied: isCouponApplied,
        name: couponName,
        deductionType: couponDeductionType,
        subDeductionType: subDeductionType,
        deductionValue: couponDeductionValue,
        subDeductionValue: subDeductionValue,
        deductedAmount: deductedAmount,
        cartTotalAfterDiscount: isCouponApplied ? cartTotal : 0,
      },
    };

    let resp = {
      status: "Success",
      msg: "Cart items retrieved",
      payload: payload,
    };
    return res.json(resp);
  }
);

export default router;
