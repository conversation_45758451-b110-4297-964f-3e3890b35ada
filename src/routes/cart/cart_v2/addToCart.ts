// @ts-nocheck

import { Router } from "express";
import { userModel, ticketModel_v2 } from "../../../models";
import { isUserLogged, getUserID } from "../../../middleware";
import { validateAddToCartInputs_v2 } from "../../../validation";

const router = Router();

router.post("/add-to-cart", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Check if cart Inputs are valid
  ---------------------------- */
  let { error } = validateAddToCartInputs_v2(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* ---------------------------------
  Extract ticket details from req.body
  --------------------------------- */
  let { ticketId, sessionId, eventCode } = req.body;

  /* ---------------------
  Check if ticketId exists
  --------------------- */
  let ticketObj = await ticketModel_v2.findOne({
    ticketId: ticketId,
  });

  if (!ticketObj) {
    let resp = {
      status: "Failed",
      msg: "Ticket doesn't exist",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  let isTicketInCart: boolean = false;

  userObj?.cartv2.forEach((ticket: any) => {
    if (ticket.ticketId === ticketId) {
      isTicketInCart = true;
    }
  });

  if (isTicketInCart && !sessionId) {
    let resp = {
      status: "Failed",
      msg: "Ticket already in cart",
    };
    return res.status(400).json(resp);
  }

  // if (ticketObj.ticketType === "membership") {
  //   let filter = {
  //     _userID: _userID,
  //   };
  //   let update = {
  //     "membership.isInCart": true,
  //   };
  //   await userModel.findOneAndUpdate(filter, update);
  // }

  /* -------------------------------------
  Add user email to membership coupon list
  ------------------------------------- */

  userModel.findOneAndUpdate(
    { _userID: _userID },
    {
      $push: {
        cartv2: {
          ticketId: ticketId,
          eventCode: eventCode,
          sessionId: sessionId ? sessionId : "",
        },
      },
    },
    (error, success) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "Could not add ticket to cart",
        };
        return res.json(resp);
      } else {
        let resp = {
          status: "Success",
          msg: "Ticket added to cart",
        };
        return res.json(resp);
      }
    }
  );
});

export default router;
