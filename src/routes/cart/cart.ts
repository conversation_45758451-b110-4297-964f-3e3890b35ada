// @ts-nocheck

import { Router } from "express";
import { userModel, ticketModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateAddToCartInputs } from "../../validation";
import moment from "moment";

const router = Router();

/* ===============
ADD TICKET TO CART
=============== */
router.post("/cart", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Check if cart Inputs are valid
  ---------------------------- */
  let { error } = validateAddToCartInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* ---------------------------------
  Extract ticket details from req.body
  --------------------------------- */
  let { ticketID } = req.body;
  ticketID = ticketID.trim();

  /* ---------------------
  Check if ticketID exists
  --------------------- */
  let ticketObj = await ticketModel.findOne({
    ticketID: ticketID,
  });
  if (!ticketObj) {
    let resp = {
      status: "Failed",
      msg: "Ticket doesn't exist",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  let isTicketInCart: boolean = false;

  userObj?.cart.forEach((ticket: any) => {
    if (ticket.ticketID === ticketID) {
      isTicketInCart = true;
    }
  });

  if (isTicketInCart) {
    let resp = {
      status: "Failed",
      msg: "Ticket already in cart",
    };
    return res.status(400).json(resp);
  } else {
    if (ticketObj.type === "membership") {
      let filter = {
        _userID: _userID,
      };
      let update = {
        "membership.isInCart": true,
      };
      await userModel.findOneAndUpdate(filter, update);
      if (userObj.cart.length > 0) {
        userModel.update(
          { _userID: _userID },
          { $set: { cart: [] } },
          async (err, affected) => {
            if (err) {
              let resp = {
                status: "Failed",
                msg: "Clearing cart failed!",
              };
              return res.status(400).json(resp);
            } else if (affected) {
              const newFullConferenceTicket: any = await ticketModel.findOne({
                subType: "full-conference-hcipai-professional",
              });
              const newFullConferenceTicketID =
                newFullConferenceTicket.ticketID;
              userModel.findOneAndUpdate(
                { _userID: _userID },
                { $push: { cart: { ticketID: newFullConferenceTicketID } } },
                function (error, success) {
                  if (error) {
                    let resp = {
                      status: "Failed",
                      msg: "Updating cart failed!",
                      payload: userObj,
                    };
                    return res.json(resp);
                  }
                }
              );
            }
          }
        );
      }
    }
    userModel.findOneAndUpdate(
      { _userID: _userID },
      { $push: { cart: { ticketID: ticketID } } },
      function (error, success) {
        if (error) {
          let resp = {
            status: "Failed",
            msg: "Could not save ticket to cart",
            payload: userObj,
          };
          return res.json(resp);
        } else {
          let resp = {
            status: "Success",
            msg: "Ticket saved to cart",
          };
          return res.json(resp);
        }
      }
    );
  }
});

/* ==================
DELETE TICKET TO CART
================== */
router.delete("/cart", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Check if login Inputs are valid
  ---------------------------- */
  let { error } = validateAddToCartInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* ---------------------------------
  Extract ticket details from req.body
  --------------------------------- */
  let { ticketID } = req.body;
  ticketID = ticketID.trim();

  /* ---------------------
  Check if ticketID exists
  --------------------- */
  let ticketObj = await ticketModel.findOne({
    ticketID: ticketID,
  });
  if (!ticketObj) {
    let resp = {
      status: "Failed",
      msg: "Ticket doesn't exist",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  let isTicketInCart: boolean = false;

  userObj?.cart.forEach((ticket: any) => {
    if (ticket.ticketID === ticketID) {
      isTicketInCart = true;
    }
  });

  if (!isTicketInCart) {
    let resp = {
      status: "Failed",
      msg: "Ticket is not in cart",
    };
    return res.status(400).json(resp);
  } else {
    if (ticketObj.type === "membership") {
      let filter = {
        _userID: _userID,
      };
      let update = {
        "membership.isInCart": false,
      };
      await userModel.findOneAndUpdate(filter, update);
      if (userObj.cart.length > 1) {
        userModel.update(
          { _userID: _userID },
          { $set: { cart: [] } },
          async (err, affected) => {
            if (err) {
              let resp = {
                status: "Failed",
                msg: "Clearing cart failed",
              };
              return res.status(400).json(resp);
            } else if (affected) {
              const newFullConferenceTicket: any = await ticketModel.findOne({
                subType: "full-conference-professional",
              });
              const newFullConferenceTicketID =
                newFullConferenceTicket.ticketID;
              userModel.findOneAndUpdate(
                { _userID: _userID },
                { $push: { cart: { ticketID: newFullConferenceTicketID } } },
                function (error, success) {
                  if (error) {
                    let resp = {
                      status: "Failed",
                      msg: "Updating cart failed!",
                      payload: userObj,
                    };
                    return res.json(resp);
                  } else if (affected) {
                    let resp = {
                      status: "Success",
                      msg: "Ticket removed from cart",
                    };
                    return res.json(resp);
                  }
                }
              );
            }
          }
        );
      } else {
        userModel.findOneAndUpdate(
          { _userID: _userID },
          { $pull: { cart: { ticketID: ticketID } } },
          function (error, success) {
            if (error) {
              let resp = {
                status: "Failed",
                msg: "Could not remove ticket to cart",
                payload: userObj,
              };
              return res.json(resp);
            } else {
              let resp = {
                status: "Success",
                msg: "Ticket removed from cart",
              };
              return res.json(resp);
            }
          }
        );
      }
    } else if (
      ticketObj.type === "full-conference" ||
      ticketObj.type === "single-selector"
    ) {
      userModel.update(
        { _userID: _userID },
        { $set: { cart: [] } },
        async (err, affected) => {
          if (err) {
            let resp = {
              status: "Failed",
              msg: "Clearing cart failed",
            };
            return res.status(400).json(resp);
          } else if (affected) {
            let resp = {
              status: "Success",
              msg: "Ticket removed from cart",
            };
            return res.json(resp);
          }
        }
      );
    } else {
      userModel.findOneAndUpdate(
        { _userID: _userID },
        { $pull: { cart: { ticketID: ticketID } } },
        function (error, success) {
          if (error) {
            let resp = {
              status: "Failed",
              msg: "Could not remove ticket to cart",
              payload: userObj,
            };
            return res.json(resp);
          } else {
            let resp = {
              status: "Success",
              msg: "Ticket removed from cart",
            };
            return res.json(resp);
          }
        }
      );
    }
  }
});

/* ============
GET ENTIRE CART
============ */
router.get("/cart", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  let persona = userObj.professional.occupation;

  let cartItemIDs: any = [];
  userObj?.cart.forEach((item: any) => {
    cartItemIDs.push(item.ticketID);
  });

  let cartItemDetails = await ticketModel
    .find()
    .where("ticketID")
    .in(cartItemIDs)
    .exec();

  let cartItems: any = [];
  let cartTotal: number = 0;
  cartItemDetails.forEach((item) => {
    let price = 0;
    let tier = "";
    let currency = "₹";
    let priceArr = item.price;
    let subType = item.subType;
    priceArr.forEach((item: any) => {
      let startTx = item.startDate;
      let endTx = item.endDate;
      let startDate = new Date(parseInt(startTx) * 1000).toISOString();
      let endDate = new Date(parseInt(endTx) * 1000).toISOString();
      let nowDate = moment().toISOString();
      let isBetween = moment(nowDate).isBetween(startDate, endDate);
      if (isBetween) {
        if (subType === "course") {
          if (persona === item.name.toLowerCase()) {
            tier = item.name;
            price = item.price;
            cartTotal = cartTotal + item.price.inr;
          }
        } else {
          tier = item.name;
          price = item.price;
          cartTotal = cartTotal + item.price.inr;
        }
      }
    });
    let buff = {
      ticketID: item.ticketID,
      title: item.title,
      subTitle: item.subTitle,
      tier: tier,
      currency: currency,
      price: price,
    };
    cartItems.push(buff);
  });

  let payload = {
    cartTotal: cartTotal,
    cartItems: cartItems,
  };

  let resp = {
    status: "Success",
    msg: "Cart items retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
