import { Router } from "express";
import { userModel, ticketModel, couponModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateCartTotalInputs } from "../../validation";
import moment from "moment";
const router = Router();

router.post("/getcarttotal", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Check if cart Inputs are valid
  ---------------------------- */
  let { error } = validateCartTotalInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  let persona = userObj.professional.occupation;

  let cartItemIDs: any = [];
  userObj?.cart.forEach((item: any) => {
    cartItemIDs.push(item.ticketID);
  });

  let cartItemDetails = await ticketModel
    .find()
    .where("ticketID")
    .in(cartItemIDs)
    .exec();

  let cartItems: any = [];
  let cartTotal: number = 0;
  cartItemDetails.forEach((item) => {
    let price = 0;
    let tier = "";
    let currency = "₹";
    let priceArr = item.price;
    let subType = item.subType;
    priceArr.forEach((item: any) => {
      let startTx = item.startDate;
      let endTx = item.endDate;
      let startDate = new Date(parseInt(startTx) * 1000).toISOString();
      let endDate = new Date(parseInt(endTx) * 1000).toISOString();
      let nowDate = moment().toISOString();
      let isBetween = moment(nowDate).isBetween(startDate, endDate);
      if (isBetween) {
        if (subType === "course") {
          if (persona === item.name.toLowerCase()) {
            tier = item.name;
            price = item.price;
            cartTotal = cartTotal + item.price.inr;
          }
        } else {
          tier = item.name;
          price = item.price;
          cartTotal = cartTotal + item.price.inr;
        }
      }
    });
    let buff = {
      ticketID: item.ticketID,
      title: item.title,
      subTitle: item.subTitle,
      tier: tier,
      currency: currency,
      price: price,
    };
    cartItems.push(buff);
  });

  /* ------------------------
  Discount coupon calculation
  ------------------------ */
  let isCouponApplied: boolean = false;
  if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
  let couponName: string = "";
  let couponDeductionType: string = "";
  let couponDeductionValue: number = 0;
  let couponTicketSubTypes: any;
  let percentageDeductionValue: number = 0;
  let deductedAmount: number = 0;
  let oldCartTotal: number = 0;
  oldCartTotal = cartTotal;

  if (isCouponApplied) {
    let couponObj: any = await couponModel.findOne({
      id: userObj.coupon.id,
    });
    couponName = couponObj.name;
    couponDeductionType = couponObj.deduction.type;
    couponDeductionValue = couponObj.deduction.value;
    couponTicketSubTypes = couponObj.deduction.ticketSubTypes;

    if (couponDeductionType === "fixed") {
      cartTotal = cartTotal - couponDeductionValue;
      deductedAmount = couponDeductionValue;
    } else if (couponDeductionType === "percentage") {
      percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
      cartTotal = cartTotal - percentageDeductionValue;
      deductedAmount = percentageDeductionValue;
    } else if (couponDeductionType === "ticketType") {
      let subTypesArr: any = [];
      couponTicketSubTypes.map((ticket: any) => {
        subTypesArr.push(ticket.subType);
      });
      let ticketDetailsArr = await ticketModel
        .find()
        .where("subType")
        .in(subTypesArr)
        .exec();
      let finalProcessingArr: any = [];
      cartItemDetails.map((cartItemDetail: any) => {
        ticketDetailsArr.map((ticketDetail: any) => {
          if (cartItemDetail.subType === ticketDetail.subType) {
            finalProcessingArr.push(cartItemDetail);
          }
        });
      });
      let ticketDeductionValue: number = 0;
      finalProcessingArr.map((ticket: any) => {
        let price = 0;
        let priceArr = ticket.price;
        let subType = ticket.subType;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let nowDate = moment().toISOString();
          let isBetween = moment(nowDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (subType === "course") {
              if (persona === item.name.toLowerCase()) {
                price = item.price.inr;
              }
            } else {
              price = item.price.inr;
            }
          }
          if (price > ticketDeductionValue) {
            ticketDeductionValue = price;
          }
        });
      });
      cartTotal = cartTotal - ticketDeductionValue;
      deductedAmount = ticketDeductionValue;
    }
  }

  /* --------------------
  Grand Total Calculation
  -------------------- */
  const { paymentGateway } = req.body;
  let gatewayPerc = 0;
  let grandTotal = 0;
  let gatewayFee = 0;
  if (paymentGateway === "razorpay") {
    gatewayPerc = 4;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "bank") {
    gatewayPerc = 0;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "instamojo") {
    gatewayPerc = 3;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else {
    let resp = {
      status: "Failed",
      msg: "Invalid payment gateway",
    };
    return res.status(400).json(resp);
  }

  let payload = {
    gatewayPerc: gatewayPerc,
    currency: "₹",
    cartTotal: isCouponApplied ? oldCartTotal : cartTotal,
    gatewayFee: gatewayFee,
    grandTotal: grandTotal,
    coupon: {
      isApplied: isCouponApplied,
      name: couponName,
      deductionType: couponDeductionType,
      deductionValue: couponDeductionValue,
      deductedAmount: deductedAmount,
      cartTotalAfterDiscount: isCouponApplied ? cartTotal : 0,
    },
  };

  let resp = {
    status: "Success",
    msg: "Cart items retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
