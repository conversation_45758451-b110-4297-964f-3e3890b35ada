import { Router } from "express";
import { isUserLogged } from "../../middleware";
import { logoutHelper } from "../../helpers";

const router = Router();
router.post("/logout", isUserLogged, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You must be logged in to log out",
    };
    return res.status(400).json(resp);
  }
  await logoutHelper(req, res);
});

export default router;
