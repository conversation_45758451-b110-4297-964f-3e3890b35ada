import { Router } from "express";
import { userModel } from "../../models";
import { loginHelper } from "../../helpers";
import { isUserLogged } from "../../middleware";
import { validateLoginInputs } from "../../validation";
import * as crypto from "crypto-js";

const router = Router();

router.post("/login", isUserLogged, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* ----------------------------
  Check if login Inputs are valid
  ---------------------------- */
  let { error } = validateLoginInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.send(resp);
  }

  /* ----------------------------
  Check if user exists and if the
  login credentials are OK!
  ---------------------------- */
  const { email, password } = req.body;
  let signature = crypto.SHA3(email, { outputLength: 256 }).toString();
  // const userObj = await userModel.findOne({ signature });
  const userObj = await userModel.findOne({ "profile.email": email });

  if (!userObj || !(await userObj.passwordMatch(password))) {
    let resp = {
      status: "Failed",
      msg: "User not found or invalid login credentials",
    };
    return res.json(resp);
  }

  /* ----------------------------
  Check if user exists and if the
  login credentials are OK!
  ---------------------------- */
  loginHelper(req, res, userObj!._userID);
  let resp = {
    status: "Success",
    msg: "User logged in",
  };
  res.json(resp);
});

export default router;
