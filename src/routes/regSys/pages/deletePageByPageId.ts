// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { regSysModel, userModel } from "../../../models";
import { validateDeletePageInputs } from "../../../validation";

const router = Router();

router.post(
  "/deletepagebypageid",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*------------------
    Check is user logged
    ------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*--------------------
    Check if user is Admin
    --------------------*/
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /*-----------------------
    Extract & validate inputs
    -----------------------*/
    let { eventCode, pageForDeletionId } = req.body;
    let { error } = validateDeletePageInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/getpagesbyeventcode - Invalid inputs to retrieve pages`);
      return res.json(resp);
    }

    /*-------------
    Retrieve RegSys
    -------------*/
    let regSysObj: any = await regSysModel
      .findOne({ eventCode: eventCode })
      .exec();

    /*---------------------------
    Sends "regSys not found" error
    ---------------------------*/
    if (!regSysObj) {
      let resp = {
        status: "Failed",
        msg: "Failed: RegSys not found",
      };
      return res.json(resp);
    }

    /*-----------------------
    Check if page has tickets
    -----------------------*/
    let pages: any = regSysObj.pages;
    let concernedPage: any;
    pages.map((page: any) => {
      if (page.id === pageForDeletionId) {
        concernedPage = page;
      }
    });

    // if (concernedPage.ticketsInPage.length > 0) {
    //   let resp = {
    //     status: "Success",
    //     isPageEmpty: false,
    //     msg: "To delete this page, first remove the tickets in it",
    //   };
    //   return res.json(resp);
    // }

    /*-------------------- 
    Pull page out of event
    --------------------*/
    let filter = {
      eventCode: eventCode,
    };

    let update = { pages: { id: pageForDeletionId } };

    regSysModel.findOneAndUpdate(
      filter,
      { $pull: update },
      (error: any, success: any) => {
        if (error) {
          let resp = {
            status: "Failed",
            msg: "Failed to remove page from event",
            payload: userObj,
          };
          return res.json(resp);
        }
        if (success) {
          let resp = {
            status: "Success",
            isPageEmpty: true,
            msg: "Page removed from event",
          };
          return res.json(resp);
        }
      }
    );
  }
);

export default router;
