// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { regSysModel, userModel } from "../../../models";
import { validateCreatePageInputs } from "../../../validation";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post(
  "/addpagetoeventcode",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*------------------
    Check is user logged
    ------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*--------------------
    Check if user is Admin
    --------------------*/
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /*-----------------------
    Extract & validate inputs
    -----------------------*/
    let { eventCode, pageLabel, pageIcon } = req.body;
    let { error } = validateCreatePageInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(
        `/addpagetoeventcode - Invalid inputs to create page in event`
      );
      return res.json(resp);
    }

    /*--------
    Peparation 
    --------*/
    let pageId: string = await uuidv4();

    let pageIconName: string = "";
    let iconNameArray: any = pageIcon.split('"');

    if (iconNameArray.length > 1) {
      pageIconName = iconNameArray[1];
    } else {
      pageIconName = pageIcon;
    }

    let pageObj: any = {
      id: pageId,
      label: pageLabel,
      icon: pageIconName,
    };

    /*-------------------- 
    Push page into event 
    --------------------*/
    let filter = {
      eventCode: eventCode,
    };
    let update = {
      pages: pageObj,
    };

    regSysModel.findOneAndUpdate(
      filter,
      { $push: update },
      (error: any, success: any) => {
        if (error) {
          let resp = {
            status: "Failed",
            msg: "Could not create page",
          };
          return res.json(resp);
        }
        if (success) {
          let resp = {
            status: "Success",
            msg: `Page created for ${eventCode}`,
          };
          return res.status(200).json(resp);
        }
      }
    );
  }
);

export default router;
