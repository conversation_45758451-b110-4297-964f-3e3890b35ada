// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { regSysModel, userModel } from "../../../models";
import { validateUpdatePageInputs } from "../../../validation";

const router = Router();

router.post("/updatepagebyid", isUserLogged, getUserID, async (req, res) => {
  /*------------------
  Check is user logged
  ------------------*/
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /*--------------------
  Check if user is Admin
  --------------------*/
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }
  if (!userObj.settings.isAdmin) {
    let resp = {
      status: "Failed",
      msg: "Access Denied - You are not an admin",
    };
    return res.json(resp);
  }

  /*-----------------------
  Extract & validate inputs
  -----------------------*/
  let { eventCode, pageId, pageLabel, pageIcon } = req.body;
  let { error } = validateUpdatePageInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(`/updatepagebyid - Invalid inputs to update page in event`);
    return res.json(resp);
  }

  /*--------
  Peparation 
  --------*/
  let pageIconName: string = "";
  let iconNameArray: any = pageIcon.split('"');
  if (iconNameArray.length > 1) {
    pageIconName = iconNameArray[1];
  } else {
    pageIconName = pageIcon;
  }

  /*-------------------- 
  Update page in event 
  --------------------*/
  regSysModel.findOneAndUpdate(
    { eventCode: eventCode, "pages.id": pageId },
    {
      $set: {
        "pages.$.label": pageLabel,
        "pages.$.icon": pageIconName,
      },
    },
    (error: any, success: any) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "Could not update page",
        };
        return res.json(resp);
      }
      if (success) {
        let resp = {
          status: "Success",
          msg: `Page update for ${eventCode}`,
        };
        return res.status(200).json(resp);
      }
    }
  );
});

export default router;
