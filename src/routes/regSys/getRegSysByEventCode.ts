import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { regSysModel, userModel } from "../../models";
import { validateGetEventByCodeInputs } from "../../validation";
import moment from "moment";

const router = Router();

router.post(
  "/getregsysbyeventcode",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*------------------
    Check is user logged
    ------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*--------------------
    Check if user is Admin
    --------------------*/
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    // if (!userObj.settings.isAdmin) {
    //   let resp = {
    //     status: "Failed",
    //     msg: "Access Denied - You are not an admin",
    //   };
    //   return res.json(resp);
    // }

    /*-----------------------
    Extract & validate inputs
    -----------------------*/
    let { eventCode } = req.body;
    let { error } = validateGetEventByCodeInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/getregsysbyeventcode - Invalid inputs to retrieve event`);
      return res.json(resp);
    }

    /*-------------
    Retrieve RegSys
    -------------*/
    let regSysObj: any = await regSysModel
      .findOne({ eventCode: eventCode })
      .exec();

    /*---------------------------
    Sends "regSys not found" error
    ---------------------------*/
    if (!regSysObj) {
      let resp = {
        status: "Failed",
        msg: "Failed: RegSys not found",
      };
      return res.json(resp);
    }

    /*---------------------------
    Create payload
    ---------------------------*/
    let pages: any = [];
    let pagesRaw: any = regSysObj.pages;
    let isFirstPage: boolean = true;

    pagesRaw.map((page: any) => {
      let isScheduled: boolean = page.meta.schedule.isScheduled;
      let enableOn: string = page.meta.schedule.enableOn;
      let disableOn: string = page.meta.schedule.disableOn;
      let isPublished: string = page.meta.isPublished;
      let subText: string = "";
      let isDisabled: boolean = false;
      let isActive: boolean = false;

      if (isScheduled) {
        let nowIsBefore: boolean = moment().isBefore(enableOn);
        let nowIsBetween = moment().isBetween(enableOn, disableOn);
        let nowIsAfter: boolean = moment().isAfter(disableOn);

        if (nowIsBefore) {
          isDisabled = true;
          subText = `Opens on ${moment(enableOn)} (IST)`;
        }
        if (nowIsBetween) {
          isDisabled = false;
          subText = `Closes on ${moment(disableOn)} (IST)`;
        }
        if (nowIsAfter) {
          isDisabled = true;
          subText = `Closed on ${moment(disableOn)} (IST)`;
        }
      }

      if (isFirstPage) {
        isActive = true;
        isFirstPage = false;
      }

      let obj = {
        type: "navItem",
        name: page.id,
        label: page.label,
        state: isActive ? "active" : "",
        icon: page.icon,
        subText: subText,
        route: "",
        isDisabled: isDisabled,
        isPublished: isPublished,
        configure: false,
      };
      pages.push(obj);
    });

    /*==========================*/
    /* Get ticket details       */
    /*==========================*/

    let payload: any = {
      pages: pages,
    };

    /*-------------------
    Send success response
    -------------------*/
    let resp = {
      status: "Success",
      msg: "RegSys details fetched",
      payload: payload,
    };
    return res.status(200).json(resp);
  }
);

export default router;
