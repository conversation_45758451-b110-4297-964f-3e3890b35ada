import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { userModel, ticketModel_v2 } from "../../../models";
import { validateGetTicketsByPageIdAndEventCodeInputs } from "../../../validation";
import moment from "moment";

const router = Router();

router.post(
  "/getticketsbypageidandeventcode",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*------------------
    Check is user logged
    ------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*--------------------
    Check if user is Admin
    --------------------*/
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    let persona: string = userObj.professional.occupation;
    // if (!userObj.settings.isAdmin) {
    //   let resp = {
    //     status: "Failed",
    //     msg: "Access Denied - You are not an admin",
    //   };
    //   return res.json(resp);
    // }

    const cartv2: any = userObj.cartv2;
    let purchasedTicketIds: any = [];
    let purchasedTicketStatus: any = [];
    let purchasedSessionIds: any = [];
    const purchases: any = userObj.purchasedItems;
    purchases.map((item: any) => {
      let ticketId: string = "";
      let purchasedItems: any = item.purchasedItems;
      purchasedItems.map((purchasedItem: any) => {
        purchasedTicketIds.push(purchasedItem.ticketId);
        if (purchasedItem.sessionId) {
          purchasedSessionIds.push({
            sessionId: purchasedItem.sessionId,
            status: item.paymentStatus,
          });
        }
        ticketId = purchasedItem.ticketId;
        let purchaseStatusObj: any = {
          ticketId: ticketId,
          sessionId: purchasedItem.sessionId,
          status: item.paymentStatus,
          purchasedOn:
            item.paymentStatus === "purchased" || "under-verification"
              ? item.createdAt
              : "",
        };
        purchasedTicketStatus.push(purchaseStatusObj);
      });

      // let purchaseStatusObj: any = {
      //   ticketId: ticketId,
      //   status: item.paymentStatus,
      //   purchasedOn:
      //     item.paymentStatus === "purchased" || "under-verification"
      //       ? item.createdAt
      //       : "",
      // };
      // purchasedTicketStatus.push(purchaseStatusObj);
    });

    /*-----------------------
    Extract & validate inputs
    -----------------------*/
    let { pageId, eventCode } = req.body;
    let { error } = validateGetTicketsByPageIdAndEventCodeInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(
        `/getticketsbypageidandeventcode - Invalid inputs to retrieve tickets`
      );
      return res.json(resp);
    }

    /*-------------
    Retrieve tickets
    --------------*/
    let retrievedTickets: any = await ticketModel_v2
      .find({
        eventCode: eventCode,
        pageId: pageId,
      })
      .exec();

    /*-------------
    Prepare payload
    --------------*/
    let formattedTicketArray: any = [];
    retrievedTickets.map((ticket: any) => {
      let obj: any;
      if (ticket.ticketType === "fullTicket") {
        let ticketPricingTiers: any = ticket.fullTicketDetails.tiers;
        let tierName: string = "";
        let tierEndDate: string = "";
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;

        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierName = tier.tierName;
            tierEndDate = tier.tierEndDate;
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        let isTicketInCart: boolean = false;
        let isTicketPurchased: boolean = false;
        let purchaseDate: string = "";
        let purchaseStatus: string = "";
        cartv2.map((cartItem: any) => {
          if (ticket.ticketId === cartItem.ticketId) {
            isTicketInCart = true;
          }
        });
        if (purchasedTicketIds.length > 0) {
          // purchasedTicketIds.map((purchasedTicketId: any) => {
          //   if (purchasedTicketId === ticket.ticketId) {
          //     isTicketPurchased = true;
          //   }
          // });
          purchasedTicketStatus.map((item: any) => {
            if (item.ticketId === ticket.ticketId) {
              isTicketPurchased = true;
              purchaseStatus = item.status;
              purchaseDate = item.purchasedOn;
            }
          });
        }

        if (
          purchaseStatus === "purchased" ||
          purchaseStatus === "under-verification"
        ) {
          ticketPricingTiers.map((tier: any) => {
            let isBetween: any = moment(purchaseDate).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );

            if (isBetween) {
              tierName = tier.tierName;
              tierEndDate = tier.tierEndDate;
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });
        }

        obj = {
          ticketId: ticket.ticketId,
          ticketType: ticket.ticketType,
          ticketTitle: ticket.fullTicketDetails.title,
          ticketTier: {
            name: tierName,
            endDate: tierEndDate,
            studentPrice: tierStudentPrice,
            professionalPrice: tierProfessionalPrice,
          },
          isTicketVisible: ticket.isTicketVisible,
          isTicketDisabled: ticket.isTicketDisabled,
          isTicketPrimary: ticket.isTicketPrimary,
          isTicketPrimaryDependent: ticket.isTicketPrimaryDependent,
          isTicketInCart: isTicketInCart,
          isTicketPurchased: isTicketPurchased,
          purchaseStatus: purchaseStatus,
          persona: persona,
        };
      } else if (ticket.ticketType === "basicTicket") {
        let ticketPricingTiers: any = ticket.basicTicketDetails.tiers;
        let tierName: string = "";
        let tierEndDate: string = "";
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;

        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierName = tier.tierName;
            tierEndDate = tier.tierEndDate;
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        let isTicketInCart: boolean = false;
        let isTicketPurchased: boolean = false;
        let purchaseDate: string = "";
        let purchaseStatus: string = "";
        cartv2.map((cartItem: any) => {
          if (ticket.ticketId === cartItem.ticketId) {
            isTicketInCart = true;
          }
        });

        if (purchasedTicketIds.length > 0) {
          // purchasedTicketIds.map((purchasedTicketId: any) => {
          //   if (purchasedTicketId === ticket.ticketId) {
          //     isTicketPurchased = true;
          //   }
          // });
          purchasedTicketStatus.map((item: any) => {
            if (item.ticketId === ticket.ticketId) {
              isTicketPurchased = true;
              purchaseStatus = item.status;
              purchaseDate = item.purchasedOn;
            }
          });
        }

        if (
          purchaseStatus === "purchased" ||
          purchaseStatus === "under-verification"
        ) {
          ticketPricingTiers.map((tier: any) => {
            let isBetween: any = moment(purchaseDate).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );

            if (isBetween) {
              tierName = tier.tierName;
              tierEndDate = tier.tierEndDate;
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });
        }

        obj = {
          ticketId: ticket.ticketId,
          ticketType: ticket.ticketType,
          ticketTitle: ticket.basicTicketDetails.title,
          ticketTier: {
            name: tierName,
            endDate: tierEndDate,
            studentPrice: tierStudentPrice,
            professionalPrice: tierProfessionalPrice,
          },
          isTicketVisible: ticket.isTicketVisible,
          isTicketDisabled: ticket.isTicketDisabled,
          isTicketPrimary: ticket.isTicketPrimary,
          isTicketPrimaryDependent: ticket.isTicketPrimaryDependent,
          isTicketInCart: isTicketInCart,
          isTicketPurchased: isTicketPurchased,
          purchaseStatus: purchaseStatus,
          persona: persona,
        };
      } else if (ticket.ticketType === "trackTicket") {
        let trackSessions: any = ticket.trackTicketDetails.sessions;
        let trackSessionsIdsInCart: any = [];

        cartv2.map((cartItem: any) => {
          if (cartItem.sessionId) {
            trackSessionsIdsInCart.push(cartItem.sessionId);
          }
        });

        let finalSessions: any = [];
        trackSessions.map((trackSession: any) => {
          let id: string = trackSession.id;
          let title: string = trackSession.title;
          let type: string = trackSession.type;
          let quantity: number = trackSession.quantity;
          let startsOn: string = trackSession.startsOn;
          let endsOn: string = trackSession.endsOn;
          let studentPrice: number = trackSession.studentPrice;
          let professionalPrice: number = trackSession.professionalPrice;
          let url: string = trackSession.url;
          let instructors: string = trackSession.instructors;
          let isVisible: boolean = trackSession.isVisible;
          let isDisabled: boolean = trackSession.isDisabled;
          let isSoldOut: boolean = trackSession.quantity <= 0 ? true : false;
          let isTicketInCart: boolean = false;
          let isTicketPurchased: boolean = false;
          let purchaseStatus: string = "";

          trackSessionsIdsInCart.map((trackSessionIdInCart: any) => {
            if (trackSessionIdInCart === trackSession.id) {
              isTicketInCart = true;
            } else {
              // isDisabled = true;
            }
          });

          purchasedSessionIds.map((purchasedSessionId: any) => {
            if (purchasedSessionId.sessionId === trackSession.id) {
              isTicketPurchased = true;
              purchaseStatus = purchasedSessionId.status;
            } else {
              // isDisabled = true;
            }
          });

          let sessionObj = {
            id: id,
            title: title,
            type: type,
            quantity: quantity,
            startsOn: startsOn,
            endsOn: endsOn,
            studentPrice: studentPrice,
            professionalPrice: professionalPrice,
            url: url,
            instructors: instructors,
            isVisible: isVisible,
            isDisabled: isDisabled,
            isSoldOut: isSoldOut,
            isTicketInCart: isTicketInCart,
            isTicketPurchased: isTicketPurchased,
            purchaseStatus: purchaseStatus,
          };

          finalSessions.push(sessionObj);
        });

        obj = {
          ticketId: ticket.ticketId,
          ticketType: ticket.ticketType,
          isTicketVisible: ticket.isTicketVisible,
          isTicketPrimary: ticket.isTicketPrimary,
          isTicketPrimaryDependent: ticket.isTicketPrimaryDependent,
          persona: persona,
          sessions: finalSessions,
        };
      }

      formattedTicketArray.push(obj);
    });

    let resp = {
      status: "Success",
      msg: "Tickets Fetched",
      payload: formattedTicketArray,
    };
    return res.status(200).json(resp);
  }
);
export default router;
