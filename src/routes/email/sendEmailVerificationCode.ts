import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel } from "../../models";
import { sendNewUserVerificationMail } from "../../helpers";

const router = Router();

router.post(
  "/sendemailverificationcode",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* Checks if user is logged in */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    let email: string = userObj.profile.email;
    let firstName: string = userObj.profile.name.first;

    /* --- generateEmailVerificationCode --- */
    let emailVerificationCode: number = Math.floor(1000 + Math.random() * 9000);

    /* --- updateEmailVerificationCode in userObj --- */
    let filter = {
      _userID: _userID,
    };
    let update = {
      "meta.email.isVerified": false,
      "meta.email.verificationCode": emailVerificationCode,
    };

    let newUser: any = await userModel.findOneAndUpdate(filter, update, {
      new: true,
    });

    let senderName: string = "HCIPAI";
    let templateId: any;

    if (senderName === "HCIPAI") {
      templateId = 28295351;
    }

    if (
      parseInt(newUser.meta.email.verificationCode) === emailVerificationCode
    ) {
      let isVerificationEmailSent: boolean = false;
      isVerificationEmailSent = await sendNewUserVerificationMail(
        firstName,
        email,
        emailVerificationCode,
        senderName,
        templateId
      );
      if (!isVerificationEmailSent) {
        let resp = {
          status: "Failed",
          msg: "Could not send verification mail",
        };
        return res.json(resp);
      } else {
        let resp = {
          status: "Success",
          msg: "Email verification code sent",
        };
        return res.json(resp);
      }
    } else {
      let resp = {
        status: "Failed",
        msg: "An error occured while saving verification code",
      };
      return res.json(resp);
    }
  }
);

export default router;
