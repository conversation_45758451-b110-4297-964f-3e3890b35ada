import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { membershipData } from "../../vars";

const router = Router();

router.post(
  "/get-membership-checkout-total",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
    Check if user is LoggedIn
    ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------------------------
    Extract membership details from req.body
    ------------------------------------- */
    let { membershipType, paymentGateway } = req.body;

    /* --------------------
    Grand Total Calculation
    -------------------- */
    let gatewayPerc: number = 0;
    let grandTotal: number = 0;
    let gatewayFee: number = 0;

    let title: string = "";
    let subTitle: string = "";
    let membershipPrice: number = 0;

    if (membershipType === "lifetimeMembership") {
      title = membershipData.lifetimeMembership.title;
      subTitle = membershipData.lifetimeMembership.subTitle;
      membershipPrice = membershipData.lifetimeMembership.price;
    } else if (membershipType === "annualMembership") {
      title = membershipData.annualMembership.title;
      subTitle = membershipData.annualMembership.subTitle;
      membershipPrice = membershipData.annualMembership.price;
    }

    if (paymentGateway === "razorpay") {
      gatewayPerc = 4;
      gatewayFee = (gatewayPerc / 100) * membershipPrice;
      grandTotal = membershipPrice + gatewayFee;
    } else if (paymentGateway === "bank") {
      gatewayPerc = 0;
      gatewayFee = (gatewayPerc / 100) * membershipPrice;
      grandTotal = membershipPrice + gatewayFee;
    } else if (paymentGateway === "instamojo") {
      gatewayPerc = 3;
      gatewayFee = (gatewayPerc / 100) * membershipPrice;
      grandTotal = membershipPrice + gatewayFee;
    } else {
      let resp = {
        status: "Failed",
        msg: "Invalid payment gateway",
      };
      return res.status(400).json(resp);
    }

    let payload = {
      ticketId: membershipType,
      title: title,
      subTitle: subTitle,
      currency: "₹",
      price: membershipPrice,
      gatewayPerc: gatewayPerc,
      cartTotal: membershipPrice,
      gatewayFee: Math.round(gatewayFee),
      grandTotal: Math.round(grandTotal),
    };

    let resp = {
      status: "Success",
      msg: "Membership checkout calculated",
      payload: payload,
    };
    return res.json(resp);
  }
);

export default router;
