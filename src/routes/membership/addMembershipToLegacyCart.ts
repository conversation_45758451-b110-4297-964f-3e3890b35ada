// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import { membershipData } from "../../vars";

const router = Router();

router.post(
  "/add-membership-to-legacy-cart",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
    Check if user is LoggedIn
    ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------------------------
    Extract membership details from req.body
    ------------------------------------- */
    let { membershipType, paymentGateway } = req.body;

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    /* --------------------
    Grand Total Calculation
    -------------------- */
    let gatewayPerc: number = 0;
    let grandTotal: number = 0;
    let gatewayFee: number = 0;

    let title: string = "";
    let subTitle: string = "";
    let membershipPrice: number = 0;

    if (membershipType === "lifetimeMembership") {
      title = membershipData.lifetimeMembership.title;
      subTitle = membershipData.lifetimeMembership.subTitle;
      membershipPrice = membershipData.lifetimeMembership.price;
    } else if (membershipType === "annualMembership") {
      title = membershipData.annualMembership.title;
      subTitle = membershipData.annualMembership.subTitle;
      membershipPrice = membershipData.annualMembership.price;
    }

    if (paymentGateway === "razorpay") {
      gatewayPerc = 4;
      gatewayFee = (gatewayPerc / 100) * membershipPrice;
      grandTotal = membershipPrice + gatewayFee;
    } else if (paymentGateway === "bank") {
      gatewayPerc = 0;
      gatewayFee = (gatewayPerc / 100) * membershipPrice;
      grandTotal = membershipPrice + gatewayFee;
    } else if (paymentGateway === "instamojo") {
      gatewayPerc = 3;
      gatewayFee = (gatewayPerc / 100) * membershipPrice;
      grandTotal = membershipPrice + gatewayFee;
    } else {
      let resp = {
        status: "Failed",
        msg: "Invalid payment gateway",
      };
      return res.status(400).json(resp);
    }

    /* --------------------------------------
    Clear legacy cart & insert membershipType
    -------------------------------------- */
    userModel.update(
      { _userID: _userID },
      { $set: { cart: [] } },
      async (err, affected) => {
        if (err) {
          let resp = {
            status: "Failed",
            msg: "Clearing cart failed!",
          };
          return res.status(400).json(resp);
        } else if (affected) {
          userModel.findOneAndUpdate(
            { _userID: _userID },
            { $push: { cart: { ticketID: membershipType } } },
            (error, success) => {
              if (error) {
                console.log(
                  "/add-membership-to-legacy-cart - Could not save membership to legacy cart"
                );
                let resp = {
                  status: "Failed",
                  msg: "Could not save membership to legacy cart",
                  payload: userObj,
                };
                return res.json(resp);
              } else {
                console.log(
                  "/add-membership-to-legacy-cart - Membership added to legacy cart"
                );

                let payload = {
                  ticketId: membershipType,
                  title: title,
                  subTitle: subTitle,
                  currency: "₹",
                  price: membershipPrice,
                  gatewayPerc: gatewayPerc,
                  cartTotal: membershipPrice,
                  gatewayFee: Math.round(gatewayFee),
                  grandTotal: Math.round(grandTotal),
                };

                let resp = {
                  status: "Success",
                  msg: "Ticket saved to cart",
                  payload: payload,
                };
                return res.json(resp);
              }
            }
          );
        }
      }
    );
  }
);

export default router;
