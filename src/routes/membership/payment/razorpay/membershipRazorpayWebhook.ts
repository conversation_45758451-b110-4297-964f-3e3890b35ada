// @ts-nocheck

import { Router } from "express";
import {
  couponModel,
  razorPaymentsModel,
  userModel,
  legacyMemberModel,
  memberModel,
  ticketModel_v2,
} from "../../../../models";
import crypto from "crypto";
import moment from "moment";
import { sendMembershipConfirmationMail } from "../../../../helpers";
import { v4 as uuidv4 } from "uuid";
import { membershipData, membershipCoupon } from "../../../../vars";
import * as voucherCodeGenerator from "@luxuryescapes/lib-voucher-code";

const router = Router();

router.post("/membership-razorpay-webhook-2", async (req, res) => {
  const rzpAccountID = "acc_BPcbAh3C2V0unD";
  if (req.body.account_id != rzpAccountID) {
    return res.status(200).json({ status: "ok" });
  }
  const secret = "hcipai";
  const shasum = crypto.createHmac("sha256", secret);
  shasum.update(JSON.stringify(req.body));
  const digest = shasum.digest("hex");
  if (digest != req.headers["x-razorpay-signature"]) {
    return res.status(200).json({ status: "ok" });
  }
  const paymentDetails = req.body.payload.payment.entity;
  const rzpPaymentID = paymentDetails.id;
  const orderID = paymentDetails.order_id;
  const isPaymentInternational = paymentDetails.international;
  const method = paymentDetails.method;
  const captured = paymentDetails.captured;
  const status = paymentDetails.status;

  const rzpPaymentObj: any = await razorPaymentsModel.findOne({ orderID });
  let eventCode: string = rzpPaymentObj.eventCode;
  if (!rzpPaymentObj) {
    return res.status(200).json({ status: "ok" });
  }
  if (eventCode != "membership") {
    return res.status(200).json({ status: "ok" });
  }
  if (rzpPaymentObj.captured === true && rzpPaymentObj.status === "captured") {
    return res.status(200).json({ status: "ok" });
  }
  const _userID = rzpPaymentObj._userID;
  const purchaseID = rzpPaymentObj.purchaseID;

  let filter = {
    orderID: orderID,
  };
  let updateRazorpayObj = {
    rzpPaymentID: rzpPaymentID,
    isPaymentInternational: isPaymentInternational,
    method: method,
    captured: captured,
    status: status,
  };
  await razorPaymentsModel.findOneAndUpdate(filter, updateRazorpayObj);

  if (status != "captured") {
    return res.status(200).json({ status: "ok" });
  }

  const userObj: any = await userModel.findOne({
    _userID,
  });

  let email: string = userObj.profile.email;
  let firstName: string = userObj.profile.name.first;

  /* --- blockDoubleEntryInDBstart --- */
  let purchaseArr = userObj.purchasedItems;
  let isPaymentRegisteredInDB: boolean = false;
  purchaseArr.forEach((purchase: any) => {
    if (purchase.transactionID === rzpPaymentID) {
      isPaymentRegisteredInDB = true;
    }
  });

  if (isPaymentRegisteredInDB) {
    return res.status(200).json({ status: "ok" });
  }

  /* --------------------------------
    Get membership price for processing
    -------------------------------- */
  let membershipType: string = userObj?.cart[0].ticketID;
  let cartTotal: number = 0;

  if (membershipType === "lifetimeMembership") {
    cartTotal = membershipData.lifetimeMembership.price;
  } else if (membershipType === "annualMembership") {
    cartTotal = membershipData.annualMembership.price;
  }

  let paymentGateway = "Razorpay";
  let gatewayPerc = 0;
  let grandTotal = 0;
  let gatewayFee = 0;
  if (paymentGateway === "Razorpay") {
    gatewayPerc = 4;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "BankTransfer") {
    gatewayPerc = 0;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "Instamojo") {
    gatewayPerc = 3;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else {
    console.log("Invalid payment gateway");
    return res.status(200).json({ status: "ok" });
  }

  // Generate coupon codes for each email
  let couponCodeArr: any = await voucherCodeGenerator.generate({
    length: 8,
    count: 1,
  });

  let couponCode = couponCodeArr[0];

  // Generate redeemerId
  let redeemerId = await uuidv4();

  // Generate redeemer obj
  let proposedRedeemerObj = {
    redeemerId: redeemerId,
    name: { first: "", last: "" },
    email: email,
    code: couponCode,
    isUsed: false,
    usedOn: "",
  };

  userModel.findOneAndUpdate(
    { _userID: _userID },
    {
      $push: {
        purchasedItems: {
          purchaseID: purchaseID,
          transactionID: rzpPaymentID,
          eventCode: eventCode,
          paymentGateway: paymentGateway,
          cartTotal: cartTotal,
          gatewayFee: gatewayFee,
          grandTotal: grandTotal,
          purchasedItems: userObj?.cart,
          paymentStatus: "purchased",
        },
      },
    },
    async (error, success) => {
      if (error) {
        console.log("Could not save membership to purchases");
        return res.status(200).json({ status: "ok" });
      }

      userModel.update(
        { _userID: _userID },
        {
          $set: { cart: [] },
        },
        async (err, affected) => {
          if (err) {
            console.log("Saving purchases failed");
            return res.status(200).json({ status: "ok" });
          }

          /* -------------------
            Update membership year
            ------------------- */
          let year: number = moment().year();
          year = year % 100;

          let hasYearChanged: boolean = false;
          let annualMemberCount: number;
          let lifetimeMemberCount: number;

          let allMember: any = await memberModel.findOne(
            {},
            async (err, members: any) => {
              if (err) {
                console.log("Retriving members failed");
                return res.status(200).json({ status: "ok" });
              }
              if (members.year != year) {
                let filter = {
                  _id: members._id,
                };
                let updateYear = {
                  year: year,
                };
                hasYearChanged = true;
                await memberModel.findOneAndUpdate(filter, updateYear);
              }
            }
          );

          if (hasYearChanged) {
            annualMemberCount = 0;
            lifetimeMemberCount = 0;
          } else {
            annualMemberCount = allMember?.annualMemberCount;
            lifetimeMemberCount = allMember?.lifetimeMemberCount;
          }

          /*--------------------
            Membership preparation
            ---------------------*/

          if (membershipType === "annualMembership") {
            let newMemberCount = annualMemberCount + 1;
            let newMemberCountSize = -3;
            let memberIDNumberWithPadding = `*********${newMemberCount}`;
            let memberIDNumberComponent =
              memberIDNumberWithPadding.slice(newMemberCountSize);
            let memberID = `A${year}${memberIDNumberComponent}`;
            let startDate = moment().toISOString();
            let endDate = moment(startDate).add(1, "year").toISOString();
            let userFilter = {
              _userID: _userID,
            };
            let updateObj = {
              "membership.isMember": true,
              "membership.id": memberID,
              "membership.type": "annual",
              "membership.startDate": startDate,
              "membership.endDate": endDate,
              "membership.paymentMethod": paymentGateway,
              "membership.amountPaid": cartTotal,
              "membership.txCode": rzpPaymentID,
              "membership.txTimestamp": startDate,
              "membership.remarks": "",
              "membership.isInCart": false,
            };
            await userModel.findOneAndUpdate(userFilter, updateObj);
            memberModel.findOneAndUpdate(
              { _id: allMember?._id },
              {
                $push: { annualMembers: { _userID: _userID } },
              },
              async (error, success) => {
                if (error) {
                  console.log("Adding userId to annual members failed");
                  return res.status(200).json({ status: "ok" });
                }
                let filter = {
                  _id: allMember?._id,
                };
                let updateAnnualMemberCount = {
                  annualMemberCount: newMemberCount,
                };
                await memberModel.findOneAndUpdate(
                  filter,
                  updateAnnualMemberCount
                );
              }
            );

            /* ---------------------------------
            Send confirmation mail
            --------------------------------- */
            await sendMembershipConfirmationMail(
              email,
              firstName,
              membershipType,
              memberID,
              startDate,
              endDate,
              rzpPaymentID,
              paymentGateway,
              grandTotal.toString(),
              purchaseID,
              startDate
            ).then((isConfirmationSent) => {
              if (isConfirmationSent)
                console.log("Bank confirmation mail sent!");
              else console.log("Failed to send bank confirmation mail!");
            });
          } else if (membershipType === "lifetimeMembership") {
            let newMemberCount = lifetimeMemberCount + 1;
            let newMemberCountSize = -3;
            let memberIDNumberWithPadding = `*********${newMemberCount}`;
            let memberIDNumberComponent =
              memberIDNumberWithPadding.slice(newMemberCountSize);

            let memberID = `L${year}${memberIDNumberComponent}`;
            let startDate = moment().toISOString();
            let endDate = "";
            let userFilter = {
              _userID: _userID,
            };
            let updateObj = {
              "membership.isMember": true,
              "membership.id": memberID,
              "membership.type": "lifetime",
              "membership.startDate": startDate,
              "membership.endDate": endDate,
              "membership.paymentMethod": paymentGateway,
              "membership.amountPaid": cartTotal,
              "membership.txCode": rzpPaymentID,
              "membership.txTimestamp": startDate,
              "membership.remarks": "",
              "membership.isInCart": false,
            };
            await userModel.findOneAndUpdate(userFilter, updateObj);
            memberModel.findOneAndUpdate(
              { _id: allMember?._id },
              {
                $push: {
                  lifetimeMembers: { _userID: _userID },
                },
              },
              async (error, success) => {
                if (error) {
                  console.log("Adding userId to lifetime members failed");
                  return res.status(200).json({ status: "ok" });
                }

                let filter = {
                  _id: allMember?._id,
                };
                let updateLifetimeMemberCount = {
                  lifetimeMemberCount: newMemberCount,
                };
                await memberModel.findOneAndUpdate(
                  filter,
                  updateLifetimeMemberCount
                );
              }
            );

            /* ---------------------------------
            Send confirmation mail
            --------------------------------- */
            await sendMembershipConfirmationMail(
              email,
              firstName,
              membershipType,
              memberID,
              startDate,
              endDate,
              rzpPaymentID,
              paymentGateway,
              grandTotal.toString(),
              purchaseID,
              startDate
            ).then((isConfirmationSent) => {
              if (isConfirmationSent)
                console.log("Bank confirmation mail sent!");
              else console.log("Failed to send bank confirmation mail!");
            });
          }

          /* -------------------------------
          Pull email from all HCIPAI coupons
          ------------------------------- */
          couponModel.updateMany(
            { name: membershipCoupon.name },
            { $pull: { redeemerList: { email: email } } },
            (error, success) => {
              if (error) {
                console.log("Email NOT REMOVED from HCIPAI coupons");
              } else {
                console.log("Email REMOVED from HCIPAI coupon");
              }
            }
          );

          /* -----------------------------
          Insert email into HCIPAI coupons
          ----------------------------- */
          couponModel.updateMany(
            { name: membershipCoupon.name },
            { $push: { redeemerList: proposedRedeemerObj } },
            (error, success) => {
              if (error) {
                console.log("Could not insert email into HCIPAI coupon");
              } else {
                console.log("Email inserted into HCIPAI coupon");
              }
            }
          );

          console.log("Purchases saved");
          return res.status(200).json({ status: "ok" });
        }
      );
    }
  );
});

export default router;
