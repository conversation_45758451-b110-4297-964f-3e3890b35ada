import { Router } from "express";
import { userModel, razorPaymentsModel } from "../../../../models";
import { isUserLogged, getUserID } from "../../../../middleware";
import { validateRazorpayPaymentDetails } from "../../../../validation";
import { membershipData } from "../../../../vars";
import { v4 as uuidv4 } from "uuid";
import Razorpay from "razorpay";

const router = Router();

router.post(
  "/membership-razorpay-prep",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*-----------------------
    Check if user is LoggedIn
    -----------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*--------------------------------
  Check if Razorpay Inputs are valid
  --------------------------------*/
    let { error } = validateRazorpayPaymentDetails(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.status(400).send(resp);
    }

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    let issuer = userObj.profile;
    let email: string = userObj.profile.email;
    let firstName: string = userObj.profile.name.first;

    if (userObj?.cart.length === 0) {
      let resp = {
        status: "Failed",
        msg: "Cart is empty",
      };
      return res.status(400).send(resp);
    }

    /* --------------------------------
    Get membership price for processing
    -------------------------------- */
    let membershipType: string = userObj?.cart[0].ticketID;
    let cartTotal: number = 0;

    if (membershipType === "lifetimeMembership") {
      cartTotal = membershipData.lifetimeMembership.price;
    } else if (membershipType === "annualMembership") {
      cartTotal = membershipData.annualMembership.price;
    }

    /* -------------------------------
  Calculate cart total
  ------------------------------- */
    const { paymentGateway } = req.body;
    let gatewayPerc = 0;
    let grandTotal = 0;
    let gatewayFee = 0;
    if (paymentGateway === "Razorpay") {
      gatewayPerc = 4;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "BankTransfer") {
      gatewayPerc = 0;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "Instamojo") {
      gatewayPerc = 3;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else {
      let resp = {
        status: "Failed",
        msg: "Invalid payment gateway",
      };
      return res.status(400).json(resp);
    }

    /* -----------------
  Razorpay code starts
  ----------------- */
    let purchaseID: string = await uuidv4();
    // prod
    const razorpay = new Razorpay({
      key_id: "***********************",
      key_secret: "dMGhnkJCxT4IhgqMhQ4TxCC0",
    });

    // test
    // const razorpay = new Razorpay({
    //   key_id: "rzp_test_L6Nh6GCncfTSDN",
    //   key_secret: "XxALfQRZPgGsbDn874HqPQWe",
    // });

    const payment_capture = 1;
    const currency = "INR";
    const options = {
      amount: grandTotal * 100,
      currency,
      receipt: purchaseID,
      payment_capture,
    };

    let orderID = "";
    let rzpCurrency = "";
    let amount = "";
    let rzpOrg = "HCI Professional Association of India";
    let rzpName = `${userObj?.profile.name.first} ${userObj?.profile.name.last}`;
    let rzpEmail = `${userObj?.profile.email}`;
    let rzpMobile = `${userObj?.profile.mobile.number}`;

    try {
      const response = await razorpay.orders.create(options);
      orderID = response.id;
      rzpCurrency = response.currency;
      amount = response.amount;
    } catch (error) {
      console.log(error);
    }
    const razorpayObj: any = await razorPaymentsModel
      .create({
        _userID: _userID,
        eventCode: "membership",
        purchaseID: purchaseID,
        rzpPaymentID: "",
        orderID: orderID,
        currency: rzpCurrency,
        amount: amount,
        isPaymentInternational: false,
        method: "",
        captured: false,
        status: "",
      })
      .catch((error) => {
        let resp = {
          status: "Failed",
          msg: "Failed to initate the payment",
          payload: error,
        };
        return res.status(200).json(resp);
      });
    if (razorpayObj) {
      let payload = {
        id: orderID,
        currency: rzpCurrency,
        amount: amount,
        org: rzpOrg,
        name: rzpName,
        email: rzpEmail,
        mobile: rzpMobile,
      };
      let resp = {
        status: "Success",
        msg: "Purchases Saved",
        payload: payload,
      };
      return res.json(resp);
    } else {
      let resp = {
        status: "Failed",
        msg: "Couldn't initiate payment",
      };
      return res.status(200).json(resp);
    }
  }
);

export default router;
