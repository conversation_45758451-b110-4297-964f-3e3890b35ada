// @ts-nocheck

import { Router } from "express";
import {
  alertModel,
  couponModel,
  userModel,
  memberModel,
} from "../../../../models";
import { isUserLogged, getUserID } from "../../../../middleware";
import { validateBankPaymentDetails } from "../../../../validation";
import { sendMembershipConfirmationMail } from "../../../../helpers";
import { membershipData, membershipCoupon } from "../../../../vars";
import * as voucherCodeGenerator from "@luxuryescapes/lib-voucher-code";

import { v4 as uuidv4 } from "uuid";
import moment from "moment";

const router = Router();

router.post(
  "/membership-bank-payment",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
    Check if user is LoggedIn
    ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ----------------------------
    Validate bank payemnt details
    ---------------------------- */
    let { error } = validateBankPaymentDetails(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.status(400).send(resp);
    }

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    let issuer = userObj.profile;
    let email: string = userObj.profile.email;
    let firstName: string = userObj.profile.name.first;

    if (userObj?.cart.length === 0) {
      let resp = {
        status: "Failed",
        msg: "Cart is empty",
      };
      return res.status(400).send(resp);
    }

    /* --------------------------------
    Get membership price for processing
    -------------------------------- */
    let membershipType: string = userObj?.cart[0].ticketID;
    let cartTotal: number = 0;

    if (membershipType === "lifetimeMembership") {
      cartTotal = membershipData.lifetimeMembership.price;
    } else if (membershipType === "annualMembership") {
      cartTotal = membershipData.annualMembership.price;
    }

    const { bankTxCode, paymentGateway } = req.body;

    let gatewayPerc = 0;
    let grandTotal = 0;
    let gatewayFee = 0;
    if (paymentGateway === "Razorpay") {
      gatewayPerc = 4;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "BankTransfer") {
      gatewayPerc = 0;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "Instamojo") {
      gatewayPerc = 3;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else {
      let resp = {
        status: "Failed",
        msg: "Invalid payment gateway",
      };
      return res.status(400).json(resp);
    }

    let purchaseID: string = await uuidv4();
    let alertID: string = await uuidv4();

    // Generate coupon codes for each email
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: 1,
    });

    let couponCode = couponCodeArr[0];

    // Generate redeemerId
    let redeemerId = await uuidv4();

    // Generate redeemer obj
    let proposedRedeemerObj = {
      redeemerId: redeemerId,
      name: { first: "", last: "" },
      email: email,
      code: couponCode,
      isUsed: false,
      usedOn: "",
    };

    userModel.findOneAndUpdate(
      { _userID: _userID },
      {
        $push: {
          purchasedItems: {
            purchaseID: purchaseID,
            transactionID: bankTxCode,
            eventCode: "membership",
            paymentGateway: paymentGateway,
            cartTotal: cartTotal,
            gatewayFee: gatewayFee,
            grandTotal: grandTotal,
            purchasedItems: userObj?.cart,
            paymentStatus: "under-verification",
          },
        },
      },
      async (error, success) => {
        if (error) {
          let resp = {
            status: "Failed",
            msg: "Could not save membership to purchases",
          };
          return res.status(400).json(resp);
        }

        /* ----------------------------------
        Create an alert for bank verification
        ---------------------------------- */
        let alertObj = {
          id: alertID,
          type: "Bank Transfer",
          eventCode: "membership",
          issuer: {
            name: {
              first: issuer.name.first,
              middle: issuer.name.middle,
              last: issuer.name.last,
            },
            email: issuer.email,
          },
          resolver: {
            name: {
              first: "",
              middle: "",
              last: "",
            },
            email: "",
          },
          details: {
            prop1: bankTxCode,
            prop2: grandTotal,
            prop3: purchaseID,
            prop4: "",
            prop5: "",
          },
          status: {
            isActive: true,
            remarks: "",
          },
          timestamp: {
            issuedOn: moment().toISOString(),
            resolvedOn: "",
          },
        };
        const alertInstance = await alertModel.create(alertObj);
        if (!alertInstance._id) {
          console.log("Creating BankTransfer alert failed!");
        }

        userModel.update(
          { _userID: _userID },
          {
            $set: { cart: [] },
          },
          async (err, affected) => {
            if (err) {
              let resp = {
                status: "Failed",
                msg: "Saving purchase failed!",
              };
              return res.status(400).json(resp);
            }

            /* -------------------
            Update membership year
            ------------------- */
            let year: number = moment().year();
            year = year % 100;

            let hasYearChanged: boolean = false;
            let annualMemberCount: number;
            let lifetimeMemberCount: number;

            let allMember: any = await memberModel.findOne(
              {},
              async (err, members: any) => {
                if (err) {
                  let resp = {
                    status: "Failed",
                    msg: "Retrieving members failed",
                  };
                  return res.status(400).json(resp);
                }
                if (members.year != year) {
                  let filter = {
                    _id: members._id,
                  };
                  let updateYear = {
                    year: year,
                  };
                  hasYearChanged = true;
                  await memberModel.findOneAndUpdate(filter, updateYear);
                }
              }
            );

            if (hasYearChanged) {
              annualMemberCount = 0;
              lifetimeMemberCount = 0;
            } else {
              annualMemberCount = allMember?.annualMemberCount;
              lifetimeMemberCount = allMember?.lifetimeMemberCount;
            }

            /*--------------------
            Membership preparation
            ---------------------*/

            if (membershipType === "annualMembership") {
              let newMemberCount = annualMemberCount + 1;
              let newMemberCountSize = -3;
              let memberIDNumberWithPadding = `*********${newMemberCount}`;
              let memberIDNumberComponent =
                memberIDNumberWithPadding.slice(newMemberCountSize);
              let memberID = `A${year}${memberIDNumberComponent}`;
              let startDate = moment().toISOString();
              let endDate = moment(startDate).add(1, "year").toISOString();
              let userFilter = {
                _userID: _userID,
              };
              let updateObj = {
                "membership.isMember": true,
                "membership.id": memberID,
                "membership.type": "annual",
                "membership.startDate": startDate,
                "membership.endDate": endDate,
                "membership.paymentMethod": paymentGateway,
                "membership.amountPaid": cartTotal,
                "membership.txCode": bankTxCode,
                "membership.txTimestamp": startDate,
                "membership.remarks": "",
                "membership.isInCart": false,
              };
              await userModel.findOneAndUpdate(userFilter, updateObj);
              memberModel.findOneAndUpdate(
                { _id: allMember?._id },
                {
                  $push: { annualMembers: { _userID: _userID } },
                },
                async (error, success) => {
                  if (error) {
                    let resp = {
                      status: "Failed",
                      msg: "adding userID to annualMember failed",
                    };
                    return res.status(400).json(resp);
                  }
                  let filter = {
                    _id: allMember?._id,
                  };
                  let updateAnnualMemberCount = {
                    annualMemberCount: newMemberCount,
                  };
                  await memberModel.findOneAndUpdate(
                    filter,
                    updateAnnualMemberCount
                  );
                }
              );
              /* ---------------------------------
              Send confirmation mail
              --------------------------------- */
              await sendMembershipConfirmationMail(
                email,
                firstName,
                membershipType,
                memberID,
                startDate,
                endDate,
                bankTxCode,
                paymentGateway,
                grandTotal.toString(),
                purchaseID,
                startDate
              ).then((isConfirmationSent) => {
                if (isConfirmationSent)
                  console.log("Bank confirmation mail sent!");
                else console.log("Failed to send bank confirmation mail!");
              });
            } else if (membershipType === "lifetimeMembership") {
              let newMemberCount = lifetimeMemberCount + 1;
              let newMemberCountSize = -3;
              let memberIDNumberWithPadding = `*********${newMemberCount}`;
              let memberIDNumberComponent =
                memberIDNumberWithPadding.slice(newMemberCountSize);

              let memberID = `L${year}${memberIDNumberComponent}`;
              let startDate = moment().toISOString();
              let endDate = "";
              let userFilter = {
                _userID: _userID,
              };
              let updateObj = {
                "membership.isMember": true,
                "membership.id": memberID,
                "membership.type": "lifetime",
                "membership.startDate": startDate,
                "membership.endDate": endDate,
                "membership.paymentMethod": paymentGateway,
                "membership.amountPaid": cartTotal,
                "membership.txCode": bankTxCode,
                "membership.txTimestamp": startDate,
                "membership.remarks": "",
                "membership.isInCart": false,
              };
              await userModel.findOneAndUpdate(userFilter, updateObj);
              memberModel.findOneAndUpdate(
                { _id: allMember?._id },
                {
                  $push: {
                    lifetimeMembers: { _userID: _userID },
                  },
                },
                async (error, success) => {
                  if (error) {
                    let resp = {
                      status: "Failed",
                      msg: "adding userID to lifetimeMembers failed",
                    };
                    return res.json(resp);
                  }

                  let filter = {
                    _id: allMember?._id,
                  };
                  let updateLifetimeMemberCount = {
                    lifetimeMemberCount: newMemberCount,
                  };
                  await memberModel.findOneAndUpdate(
                    filter,
                    updateLifetimeMemberCount
                  );
                }
              );
              /* ---------------------------------
              Send confirmation mail
              --------------------------------- */
              await sendMembershipConfirmationMail(
                email,
                firstName,
                membershipType,
                memberID,
                startDate,
                endDate,
                bankTxCode,
                paymentGateway,
                grandTotal.toString(),
                purchaseID,
                startDate
              ).then((isConfirmationSent) => {
                if (isConfirmationSent)
                  console.log("Bank confirmation mail sent!");
                else console.log("Failed to send bank confirmation mail!");
              });
            }

            /* -------------------------------
            Pull email from all HCIPAI coupons
            ------------------------------- */
            couponModel.updateMany(
              { name: membershipCoupon.name },
              { $pull: { redeemerList: { email: email } } },
              (error, success) => {
                if (error) {
                  console.log("Email NOT REMOVED from HCIPAI coupons");
                } else {
                  console.log("Email REMOVED from HCIPAI coupon");
                }
              }
            );

            /* -----------------------------
            Insert email into HCIPAI coupons
            ----------------------------- */
            couponModel.updateMany(
              { name: membershipCoupon.name },
              { $push: { redeemerList: proposedRedeemerObj } },
              (error, success) => {
                if (error) {
                  console.log("Could not insert email into HCIPAI coupon");
                } else {
                  console.log("Email inserted into HCIPAI coupon");
                }
              }
            );

            let resp = {
              status: "Success",
              msg: "Purchases Saved",
              payload: affected,
            };

            return res.json(resp);
          }
        );
      }
    );
  }
);

export default router;
