// @ts-nocheck

import { Router } from "express";
import {
  couponModel,
  userModel,
  ticketModel_v2,
  razorPaymentsModel,
} from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateRazorpayPaymentDetails_v2 } from "../../validation";
import { v4 as uuidv4 } from "uuid";
import Razorpay from "razorpay";
import moment from "moment";

const router = Router();

router.post(
  "/pay-with-razorpay-v2",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*-----------------------
    Check if user is LoggedIn
    -----------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*--------------------------------
    Check if Razorpay Inputs are valid
    --------------------------------*/
    let { error } = validateRazorpayPaymentDetails_v2(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.status(400).send(resp);
    }

    /*-----------------------------
    Get user details for processing
    -----------------------------*/
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    let persona = userObj.professional.occupation;

    if (userObj?.cartv2.length === 0) {
      let resp = {
        status: "Failed",
        msg: "Cart is empty",
      };
      return res.status(400).send(resp);
    }

    /* ----------------------------
    Extract inputs
    ---------------------------- */
    const { eventCode, paymentGateway } = req.body;
    let cartv2: any = userObj?.cartv2;
    let cartv2ForEventCode: any = [];

    cartv2.map((cartItem: any) => {
      if (cartItem.eventCode === eventCode) {
        cartv2ForEventCode.push(cartItem);
      }
    });

    let cartItemIDs: any = [];
    let trackSessionsIdsInCart: any = [];

    cartv2ForEventCode.forEach((item: any) => {
      cartItemIDs.push(item.ticketId);
      if (item.sessionId) {
        trackSessionsIdsInCart.push(item.sessionId);
      }
    });

    let cartItemDetails = await ticketModel_v2
      .find()
      .where("ticketId")
      .in(cartItemIDs)
      .exec();

    let cartItems: any = [];
    let cartTotal: number = 0;
    cartItemDetails.forEach((ticket: any) => {
      let obj = {};
      let price: number = 0;
      let tierName: string = "";
      let ticketTitle: string = "";
      let ticketSubtitle: string = "";

      if (ticket.ticketType === "fullTicket") {
        ticketTitle = ticket.fullTicketDetails.title;
        let ticketPricingTiers: any = ticket.fullTicketDetails.tiers;
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierName = tier.tierName;
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (persona === "student") {
          price = tierStudentPrice;
          ticketSubtitle = "For students";
        } else if (persona === "professional") {
          price = tierProfessionalPrice;
          ticketSubtitle = "For professionals";
        }
      } else if (ticket.ticketType === "basicTicket") {
        ticketTitle = ticket.basicTicketDetails.title;
        let ticketPricingTiers: any = ticket.basicTicketDetails.tiers;
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierName = tier.tierName;
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (persona === "student") {
          price = tierStudentPrice;
          ticketSubtitle = "-";
        } else if (persona === "professional") {
          price = tierProfessionalPrice;
          ticketSubtitle = "-";
        }
      } else if (ticket.ticketType === "trackTicket") {
        let trackSessions: any = ticket.trackTicketDetails.sessions;
        trackSessionsIdsInCart.map((trackSessionIdInCart: any) => {
          trackSessions.map((trackSession: any) => {
            if (trackSessionIdInCart === trackSession.id) {
              let sessionPrice: number =
                persona === "professional"
                  ? trackSession.professionalPrice
                  : trackSession.studentPrice;
              let obj = {
                ticketID: ticket.ticketId,
                sessionId: trackSessionIdInCart,
                title: trackSession.title,
                subTitle: trackSession.type,
                tier: "",
                currency: "₹",
                price: sessionPrice,
              };

              cartItems.push(obj);
              cartTotal = cartTotal + sessionPrice;
            }
          });
        });
      }

      if (ticket.ticketType != "trackTicket") {
        obj = {
          ticketID: ticket.ticketId,
          title: ticketTitle,
          subTitle: ticketSubtitle,
          tier: tierName,
          currency: "₹",
          price: price,
        };
        cartItems.push(obj);
        cartTotal = cartTotal + price;
      }
    });

    /* ------------------------
    Discount coupon calculation
    ------------------------ */
    let isCouponApplied: boolean = false;
    if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
    let couponName: string = "";
    let couponDeductionType: string = "";
    let subDeductionType: string = "";
    let couponDeductionValue: number = 0;
    let subDeductionValue: number = 0;
    let couponTicketTypeIds: any;
    let discountStatement: string = "";
    let percentageDeductionValue: number = 0;
    let deductedAmount: number = 0;
    let oldCartTotal: number = 0;
    let couponAccess: string = "";
    oldCartTotal = cartTotal;

    if (isCouponApplied) {
      let couponObj: any = await couponModel.findOne({
        id: userObj.coupon.id,
      });
      couponName = couponObj.name;
      couponDeductionType = couponObj.deduction.type;
      couponDeductionValue = couponObj.deduction.value;
      let couponTicketTypeIdsUnprocessed: any =
        couponObj.deduction.ticketTypes.items;
      let couponTicketTypeIds: any = [];
      couponTicketTypeIdsUnprocessed.map((ticket: any) => {
        couponTicketTypeIds.push(ticket.ticketId);
      });
      couponAccess = couponObj.meta.access;

      if (couponDeductionType === "fixed") {
        cartTotal = cartTotal - couponDeductionValue;
        deductedAmount = couponDeductionValue;
      } else if (couponDeductionType === "percentage") {
        percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
        cartTotal = cartTotal - percentageDeductionValue;
        deductedAmount = percentageDeductionValue;
      } else if (couponDeductionType === "ticketType") {
        subDeductionType = couponObj.deduction.ticketTypes.subDeductionType;
        subDeductionValue = couponObj.deduction.ticketTypes.subDeductionValue;
        let ticketDetailsArr: any = await ticketModel_v2
          .find()
          .where("ticketId")
          .in(couponTicketTypeIds)
          .exec();

        let finalProcessingArr: any = [];
        cartItemDetails.map((cartItemDetail: any) => {
          ticketDetailsArr.map((ticketDetail: any) => {
            if (cartItemDetail.ticketId === ticketDetail.ticketId) {
              finalProcessingArr.push(cartItemDetail);
            }
          });
        });

        let ticketDeductionValue: number = 0;
        finalProcessingArr.map((ticket: any) => {
          let price: number = 0;
          let ticketPricingTiers: any;
          if (ticket.ticketType === "fullTicket") {
            ticketPricingTiers = ticket.fullTicketDetails.tiers;
          } else if (ticket.ticketType === "basicTicket") {
            ticketPricingTiers = ticket.basicTicketDetails.tiers;
          }
          let tierStudentPrice: number = 0;
          let tierProfessionalPrice: number = 0;

          ticketPricingTiers.map((tier: any) => {
            let currentTime: string = moment().toISOString();
            let isBetween: any = moment(currentTime).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );
            if (isBetween) {
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });

          if (persona === "student") {
            price = tierStudentPrice;
          } else if (persona === "professional") {
            price = tierProfessionalPrice;
          }

          if (subDeductionType === "fixed") {
            ticketDeductionValue = price - subDeductionValue;
            deductedAmount = deductedAmount + subDeductionValue;
          } else if (subDeductionType === "percentage") {
            percentageDeductionValue = (subDeductionValue / 100) * price;
            ticketDeductionValue = price - percentageDeductionValue;
            deductedAmount = deductedAmount + percentageDeductionValue;
          }
        });

        cartTotal = cartTotal - deductedAmount;
        discountStatement = `${couponName}`;
      }
    }

    /*--------------------
    Main calculation starts
    ---------------------*/
    let gatewayPerc = 0;
    let grandTotal = 0;
    let gatewayFee = 0;
    if (paymentGateway === "Razorpay") {
      gatewayPerc = 4;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "BankTransfer") {
      gatewayPerc = 0;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else if (paymentGateway === "Instamojo") {
      gatewayPerc = 3;
      gatewayFee = (gatewayPerc / 100) * cartTotal;
      grandTotal = cartTotal + gatewayFee;
    } else {
      let resp = {
        status: "Failed",
        msg: "Invalid payment gateway",
      };
      return res.status(400).json(resp);
    }

    /* -----------------
    Razorpay code starts
    ----------------- */
    let purchaseID: string = await uuidv4();

    // Prod
    let keyId: string = "***********************";
    let keySecret: string = "dMGhnkJCxT4IhgqMhQ4TxCC0";

    // Test
    // let keyId: string = "rzp_test_L6Nh6GCncfTSDN";
    // let keySecret: string = "XxALfQRZPgGsbDn874HqPQWe";

    const razorpay = new Razorpay({
      key_id: keyId,
      key_secret: keySecret,
    });

    // grandTotal = parseFloat(grandTotal).toFixed(2);
    gatewayFee = Math.round(gatewayFee);
    grandTotal = Math.round(grandTotal);

    const payment_capture = 1;
    const currency = "INR";
    const options = {
      amount: grandTotal * 100,
      currency: currency,
      receipt: purchaseID,
      payment_capture,
    };

    let orderID = "";
    let rzpCurrency = "";
    let amount: any;
    let rzpOrg = "HCI Professional Association of India";
    let rzpName = `${userObj?.profile.name.first} ${userObj?.profile.name.last}`;
    let rzpEmail = `${userObj?.profile.email}`;
    let rzpMobile = `${userObj?.profile.mobile.number}`;

    try {
      const response = await razorpay.orders.create(options);
      orderID = response.id;
      rzpCurrency = response.currency;
      amount = response.amount;
    } catch (error) {
      console.log(error);
    }

    const razorpayObj: any = await razorPaymentsModel
      .create({
        _userID: _userID,
        eventCode: eventCode,
        purchaseID: purchaseID,
        rzpPaymentID: "",
        orderID: orderID,
        currency: rzpCurrency,
        amount: amount,
        isPaymentInternational: false,
        method: "",
        captured: false,
        status: "",
      })
      .catch((error) => {
        let resp = {
          status: "Failed",
          msg: "Failed to initate the payment",
          payload: error,
        };
        return res.status(200).json(resp);
      });

    if (razorpayObj) {
      let payload = {
        id: orderID,
        currency: rzpCurrency,
        amount: amount,
        org: rzpOrg,
        name: rzpName,
        email: rzpEmail,
        mobile: rzpMobile,
      };
      let resp = {
        status: "Success",
        msg: "Purchases Saved",
        payload: payload,
      };
      return res.json(resp);
    } else {
      let resp = {
        status: "Failed",
        msg: "Couldn't initiate payment",
      };
      return res.status(200).json(resp);
    }
  }
);

export default router;
