import { Router } from "express";
import {
  couponModel,
  userModel,
  ticketModel,
  razorPaymentsModel,
} from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateRazorpayPaymentDetails } from "../../validation";
import { v4 as uuidv4 } from "uuid";
import Razorpay from "razorpay";
import moment from "moment";
const router = Router();

router.post("/paywithrazorpay", isUserLogged, getUserID, async (req, res) => {
  /*-----------------------
  Check if user is LoggedIn
  -----------------------*/
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /*--------------------------------
  Check if Razorpay Inputs are valid
  --------------------------------*/
  let { error } = validateRazorpayPaymentDetails(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /*-----------------------------
  Get user details for processing
  -----------------------------*/
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  let persona = userObj.professional.occupation;

  if (userObj?.cart.length === 0) {
    let resp = {
      status: "Failed",
      msg: "Cart is empty",
    };
    return res.status(400).send(resp);
  }

  let cartItemIDs: any = [];
  userObj?.cart.forEach((item: any) => {
    cartItemIDs.push(item.ticketID);
  });
  let cartItemDetails = await ticketModel
    .find()
    .where("ticketID")
    .in(cartItemIDs)
    .exec();
  let cartItems: any = [];
  let cartTotal: number = 0;
  cartItemDetails.forEach((item) => {
    let price = 0;
    let tier = "";
    let currency = "₹";
    let priceArr = item.price;
    let subType = item.subType;
    priceArr.forEach((item: any) => {
      let startTx = item.startDate;
      let endTx = item.endDate;
      let startDate = new Date(parseInt(startTx) * 1000).toISOString();
      let endDate = new Date(parseInt(endTx) * 1000).toISOString();
      let nowDate = moment().toISOString();
      let isBetween = moment(nowDate).isBetween(startDate, endDate);
      if (isBetween) {
        if (subType === "course") {
          if (persona === item.name.toLowerCase()) {
            tier = item.name;
            price = item.price;
            cartTotal = cartTotal + item.price.inr;
          }
        } else {
          tier = item.name;
          price = item.price;
          cartTotal = cartTotal + item.price.inr;
        }
      }
    });
    let buff = {
      ticketID: item.ticketID,
      title: item.title,
      subTitle: item.subTitle,
      tier: tier,
      currency: currency,
      price: price,
    };
    cartItems.push(buff);
  });

  /* -------------------------------
  Discount coupon calculation starts
  ------------------------------- */
  let isCouponApplied: boolean = false;
  if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
  let couponName: string = "";
  let couponDeductionType: string = "";
  let couponDeductionValue: number = 0;
  let couponTicketSubTypes: any;
  let percentageDeductionValue: number = 0;
  let deductedAmount: number = 0;
  let oldCartTotal: number = 0;
  let discountStatement: string = "";
  oldCartTotal = cartTotal;

  if (isCouponApplied) {
    let couponObj: any = await couponModel.findOne({
      id: userObj.coupon.id,
    });
    couponName = couponObj.name;
    couponDeductionType = couponObj.deduction.type;
    couponDeductionValue = couponObj.deduction.value;
    couponTicketSubTypes = couponObj.deduction.ticketSubTypes;

    if (couponDeductionType === "fixed") {
      cartTotal = cartTotal - couponDeductionValue;
      deductedAmount = couponDeductionValue;
      discountStatement = `₹${couponDeductionValue} off on above cart total`;
    } else if (couponDeductionType === "percentage") {
      percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
      cartTotal = cartTotal - percentageDeductionValue;
      deductedAmount = percentageDeductionValue;
      discountStatement = `${couponDeductionValue}% of above cart total`;
    } else if (couponDeductionType === "ticketType") {
      let subTypesArr: any = [];
      couponTicketSubTypes.map((ticket: any) => {
        subTypesArr.push(ticket.subType);
      });
      let ticketDetailsArr = await ticketModel
        .find()
        .where("subType")
        .in(subTypesArr)
        .exec();
      let finalProcessingArr: any = [];
      cartItemDetails.map((cartItemDetail: any) => {
        ticketDetailsArr.map((ticketDetail: any) => {
          if (cartItemDetail.subType === ticketDetail.subType) {
            finalProcessingArr.push(cartItemDetail);
          }
        });
      });
      let ticketDeductionValue: number = 0;
      finalProcessingArr.map((ticket: any) => {
        let price = 0;
        let priceArr = ticket.price;
        let subType = ticket.subType;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let nowDate = moment().toISOString();
          let isBetween = moment(nowDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (subType === "course") {
              if (persona === item.name.toLowerCase()) {
                price = item.price.inr;
              }
            } else {
              price = item.price.inr;
            }
          }
          if (price > ticketDeductionValue) {
            ticketDeductionValue = price;
          }
        });
      });
      cartTotal = cartTotal - ticketDeductionValue;
      deductedAmount = ticketDeductionValue;
      discountStatement = `${couponName}`;
    }
  }

  /* -------------------------------
  Discount coupon calculation ends
  ------------------------------- */
  const { paymentGateway } = req.body;
  let gatewayPerc = 0;
  let grandTotal = 0;
  let gatewayFee = 0;
  if (paymentGateway === "Razorpay") {
    gatewayPerc = 4;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "BankTransfer") {
    gatewayPerc = 0;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "Instamojo") {
    gatewayPerc = 3;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else {
    let resp = {
      status: "Failed",
      msg: "Invalid payment gateway",
    };
    return res.status(400).json(resp);
  }

  /* -----------------
  Razorpay code starts
  ----------------- */
  let purchaseID: string = await uuidv4();
  const razorpay = new Razorpay({
    key_id: "***********************",
    key_secret: "dMGhnkJCxT4IhgqMhQ4TxCC0",
  });
  const payment_capture = 1;
  const currency = "INR";
  const options = {
    amount: grandTotal * 100,
    currency,
    receipt: purchaseID,
    payment_capture,
  };

  let orderID = "";
  let rzpCurrency = "";
  let amount = "";
  let rzpOrg = "HCI Professional Association of India";
  let rzpName = `${userObj?.profile.name.first} ${userObj?.profile.name.last}`;
  let rzpEmail = `${userObj?.profile.email}`;
  let rzpMobile = `${userObj?.profile.mobile.number}`;

  try {
    const response = await razorpay.orders.create(options);
    orderID = response.id;
    rzpCurrency = response.currency;
    amount = response.amount;
  } catch (error) {
    console.log(error);
  }
  const razorpayObj: any = await razorPaymentsModel
    .create({
      _userID: _userID,
      purchaseID: purchaseID,
      rzpPaymentID: "",
      orderID: orderID,
      currency: rzpCurrency,
      amount: amount,
      isPaymentInternational: false,
      method: "",
      captured: false,
      status: "",
    })
    .catch((error) => {
      let resp = {
        status: "Failed",
        msg: "Failed to initate the payment",
        payload: error,
      };
      return res.status(200).json(resp);
    });
  if (razorpayObj) {
    let payload = {
      id: orderID,
      currency: rzpCurrency,
      amount: amount,
      org: rzpOrg,
      name: rzpName,
      email: rzpEmail,
      mobile: rzpMobile,
    };
    let resp = {
      status: "Success",
      msg: "Purchases Saved",
      payload: payload,
    };
    return res.json(resp);
  } else {
    let resp = {
      status: "Failed",
      msg: "Couldn't initiate payment",
    };
    return res.status(200).json(resp);
  }
});

export default router;
