import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel, razorPaymentsModel } from "../../models";

const router = Router();

router.post("/razorpayconfirm", isUserLogged, getUserID, async (req, res) => {
  /*---------------------
  Checks if user is logged
  --------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -----------------------
    Checks if inputs are valid
    ----------------------- */
  // let { error } = validateRazorpayPaymentDetails(req.body);
  // if (error) {
  //   let resp = {
  //     status: "Failed",
  //     msg: error.message,
  //   };
  //   return res.status(400).json(resp);
  // }

  /*---------------
  Extract Variables
  ---------------*/
  let { paymentGateway, paymentID, orderID, signature } = req.body;
  const _userID = res.locals.userID;

  /*--------------------
  Checks if member exists
  --------------------*/
  const rzpPurchaseObj = await razorPaymentsModel.findOne({
    orderID: orderID,
  });

  if (!rzpPurchaseObj) {
    let resp = {
      status: "Failed",
      msg: "Purchase not found",
    };
    return res.status(400).json(resp);
  }

  const userObj: any = await userModel.findOne({ _userID });

  let isPaid: boolean = false;
  let isCartEmpty: boolean = false;
  let isPurchaseRecorded: boolean = false;

  if (rzpPurchaseObj.captured === true) {
    if (
      rzpPurchaseObj.status === "captured" ||
      rzpPurchaseObj.status === "authorized"
    ) {
      isPaid = true;
    }
  } else {
    console.log(
      `razorpay, captured: ${rzpPurchaseObj.captured}, status: ${rzpPurchaseObj.status} `
    );
  }

  if (userObj.cart.length === 0) isCartEmpty = true;

  let userPurchases = userObj?.purchasedItems;
  userPurchases?.forEach((item: any) => {
    let transactionID = item.transactionID;
    if (transactionID === paymentID) {
      isPurchaseRecorded = true;
    }
  });

  if (isPaid && isCartEmpty && isPurchaseRecorded) {
    let resp = {
      status: "Success",
      msg: "Razorpay payment success",
    };
    return res.json(resp);
  } else {
    let resp = {
      status: "Failed",
      msg: "Razorpay payment failed",
    };
    return res.json(resp);
  }
});

export default router;
