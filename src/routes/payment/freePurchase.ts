// @ts-nocheck

import { Router } from "express";
import {
  couponModel,
  userModel,
  ticketModel,
  memberModel,
  legacyMemberModel,
} from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { sendFreePurchaseConfirmationMail } from "../../helpers";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";

const router = Router();

router.post(
  "/freepurchaseconfirm",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
    Check if user is LoggedIn
    ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    let issuer = userObj.profile;
    let persona = userObj.professional.occupation;

    if (userObj?.cart.length === 0) {
      let resp = {
        status: "Failed",
        msg: "Cart is empty",
      };
      return res.status(400).send(resp);
    }

    let cartItemIDs: any = [];
    userObj?.cart.forEach((item: any) => {
      cartItemIDs.push(item.ticketID);
    });

    let cartItemDetails = await ticketModel
      .find()
      .where("ticketID")
      .in(cartItemIDs)
      .exec();

    let isMembershipPurchased: boolean = false;
    let membershipType: string = "";

    let cartItems: any = [];
    let cartTotal: number = 0;
    cartItemDetails.forEach(async (item) => {
      let price = 0;
      let tier = "";
      let currency = "₹";
      let priceArr = item.price;
      if (item.type === "membership" && isMembershipPurchased === false) {
        isMembershipPurchased = true;
        membershipType = item.subType;
      }
      let subType = item.subType;
      priceArr.forEach((item: any) => {
        let startTx = item.startDate;
        let endTx = item.endDate;
        let startDate = new Date(parseInt(startTx) * 1000).toISOString();
        let endDate = new Date(parseInt(endTx) * 1000).toISOString();
        let nowDate = moment().toISOString();
        let isBetween = moment(nowDate).isBetween(startDate, endDate);
        if (isBetween) {
          if (subType === "course") {
            if (persona === item.name.toLowerCase()) {
              tier = item.name;
              price = item.price;
              cartTotal = cartTotal + item.price.inr;
            }
          } else {
            tier = item.name;
            price = item.price;
            cartTotal = cartTotal + item.price.inr;
          }
        }
      });

      let buff = {
        ticketID: item.ticketID,
        title: item.title,
        subTitle: item.subTitle,
        tier: tier,
        currency: currency,
        price: price,
      };
      cartItems.push(buff);

      if (subType === "course" || subType === "workshop") {
        let filter = {
          ticketID: item.ticketID,
        };
        let updateQuantity = {
          $inc: { quantity: -1 },
        };
        await ticketModel
          .findOneAndUpdate(filter, updateQuantity)
          .exec((err, result) => {
            if (err) {
              console.log(err);
            } else {
              console.log(result);
            }
          });
      }
    });

    /* -------------------------------
    Discount coupon calculation starts
    ------------------------------- */
    let isCouponApplied: boolean = false;
    if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
    let couponName: string = "";
    let couponDeductionType: string = "";
    let couponDeductionValue: number = 0;
    let couponTicketSubTypes: any;
    let percentageDeductionValue: number = 0;
    let deductedAmount: number = 0;
    let oldCartTotal: number = 0;
    let discountStatement: string = "";
    let couponAccess: string = "";
    oldCartTotal = cartTotal;

    if (isCouponApplied) {
      let couponObj: any = await couponModel.findOne({
        id: userObj.coupon.id,
      });
      couponName = couponObj.name;
      couponDeductionType = couponObj.deduction.type;
      couponDeductionValue = couponObj.deduction.value;
      couponTicketSubTypes = couponObj.deduction.ticketSubTypes;
      couponAccess = couponObj.meta.access;

      if (couponDeductionType === "fixed") {
        cartTotal = cartTotal - couponDeductionValue;
        deductedAmount = couponDeductionValue;
        discountStatement = `₹${couponDeductionValue} off on above cart total`;
      } else if (couponDeductionType === "percentage") {
        percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
        cartTotal = cartTotal - percentageDeductionValue;
        deductedAmount = percentageDeductionValue;
        discountStatement = `${couponDeductionValue}% of above cart total`;
      } else if (couponDeductionType === "ticketType") {
        let subTypesArr: any = [];
        couponTicketSubTypes.map((ticket: any) => {
          subTypesArr.push(ticket.subType);
        });
        let ticketDetailsArr = await ticketModel
          .find()
          .where("subType")
          .in(subTypesArr)
          .exec();
        let finalProcessingArr: any = [];
        cartItemDetails.map((cartItemDetail: any) => {
          ticketDetailsArr.map((ticketDetail: any) => {
            if (cartItemDetail.subType === ticketDetail.subType) {
              finalProcessingArr.push(cartItemDetail);
            }
          });
        });
        let ticketDeductionValue: number = 0;
        finalProcessingArr.map((ticket: any) => {
          let price = 0;
          let priceArr = ticket.price;
          let subType = ticket.subType;
          priceArr.forEach((item: any) => {
            let startTx = item.startDate;
            let endTx = item.endDate;
            let startDate = new Date(parseInt(startTx) * 1000).toISOString();
            let endDate = new Date(parseInt(endTx) * 1000).toISOString();
            let nowDate = moment().toISOString();
            let isBetween = moment(nowDate).isBetween(startDate, endDate);
            if (isBetween) {
              if (subType === "course") {
                if (persona === item.name.toLowerCase()) {
                  price = item.price.inr;
                }
              } else {
                price = item.price.inr;
              }
            }
            if (price > ticketDeductionValue) {
              ticketDeductionValue = price;
            }
          });
        });
        cartTotal = cartTotal - ticketDeductionValue;
        deductedAmount = ticketDeductionValue;
        discountStatement = `${couponName}`;
      }
    }

    /* ----------------------------------------
    Return Failed if cart total is more than zero
    ------------------------------------------ */
    if (cartTotal > 0) {
      let resp = {
        status: "Failed",
        msg: "Cart total must be zero for this to work",
      };
      return res.status(400).json(resp);
    }
    /* -----------------------------
    Discount coupon calculation ends
    ----------------------------- */

    let grandTotal = 0;
    let gatewayFee = 0;

    let cartSummaryWithDiscount = [
      {
        field1: "Cart Total (before discount)",
        field2: "₹",
        field3: `${oldCartTotal}`,
      },
      {
        field1: `Discount (${discountStatement})`,
        field2: "₹",
        field3: `${deductedAmount}`,
      },
      {
        field1: "New Cart Total (after discount)",
        field2: "₹",
        field3: `${cartTotal}`,
      },
      {
        field1: "Gateway Fee",
        field2: "₹",
        field3: `${gatewayFee}`,
      },
      {
        field1: "Grand Total",
        field2: "₹",
        field3: `${grandTotal}`,
      },
    ];

    let cartSummaryWithoutDiscount = [
      {
        field1: "Cart Total",
        field2: "₹",
        field3: `${cartTotal}`,
      },
      {
        field1: "Gateway Fee",
        field2: "₹",
        field3: `${gatewayFee}`,
      },
      {
        field1: "Grand Total",
        field2: "₹",
        field3: `${grandTotal}`,
      },
    ];

    let cartSummary: any;

    if (isCouponApplied) {
      cartSummary = cartSummaryWithDiscount;
    } else {
      cartSummary = cartSummaryWithoutDiscount;
    }

    let purchaseID: string = await uuidv4();
    let paymentDetails = [
      {
        field1: "PaymentID",
        field2: "",
        field3: `${purchaseID}`,
      },
      {
        field1: "Payment Method",
        field2: "",
        field3: "Coupon",
      },
      {
        field1: "Coupon name",
        field2: "",
        field3: `${couponName}`,
      },
    ];

    let couponDetailsObj: any = {};

    if (isCouponApplied) {
      couponDetailsObj.id = userObj.coupon.id;
      couponDetailsObj.name = couponName;
      couponDetailsObj.deductionType = couponDeductionType;
      couponDetailsObj.deductionValue = couponDeductionValue;
      couponDetailsObj.cartAmountBeforeDeduction = oldCartTotal;
      couponDetailsObj.deductedAmount = deductedAmount;
      couponDetailsObj.cartAmountAfterDeduction = cartTotal;

      if (couponAccess === "open") {
        let redeemerId = await uuidv4();
        let redeemerObj = {
          redeemerId: redeemerId,
          name: {
            first: issuer.name.first,
            last: issuer.name.last,
          },
          email: issuer.email,
          isUsed: true,
          usedOn: moment().toISOString(),
        };
        couponModel.findOneAndUpdate(
          { id: userObj.coupon.id },
          { $push: { redeemerList: redeemerObj } },
          function (error, success) {
            if (error) {
              console.log("Could not save open coupon redeemer");
            } else {
              console.log("Saved open coupon redeemer");
            }
          }
        );
      } else if (couponAccess === "emaillist") {
        couponModel.findOneAndUpdate(
          { id: userObj.coupon.id, "redeemerList.email": issuer.email },
          {
            "redeemerList.$.name.first": issuer.name.first,
            "redeemerList.$.name.last": issuer.name.last,
            "redeemerList.$.isUsed": true,
            "redeemerList.$.usedOn": moment().toISOString(),
          },
          function (error, success) {
            if (error) {
              console.log("Could not save emaillist coupon redeemers");
            } else {
              console.log("Saved emaillist coupon redeemers");
            }
          }
        );
      }
    }

    userModel.findOneAndUpdate(
      { _userID: _userID },
      {
        $push: {
          purchasedItems: {
            purchaseID: purchaseID,
            transactionID: "",
            paymentGateway: "Coupon",
            isCouponApplied: isCouponApplied,
            coupon: couponDetailsObj,
            cartTotal: cartTotal,
            gatewayFee: gatewayFee,
            grandTotal: grandTotal,
            purchasedItems: userObj?.cart,
            paymentStatus: "purchased",
          },
        },
      },
      async function (error, success) {
        if (error) {
          let resp = {
            status: "Failed",
            msg: "Could not save ticket to cart",
          };
          return res.json(resp);
        } else {
          await sendFreePurchaseConfirmationMail(
            userObj?.profile.name.first,
            userObj?.profile.email,
            isCouponApplied,
            couponDetailsObj,
            cartItems,
            cartSummary,
            paymentDetails
          ).then((isConfirmationSent) => {
            if (isConfirmationSent) console.log("Bank confirmation mail sent!");
            else
              return res.status(400).json({
                status: "Failed",
                msg: "Unable to send verification email",
              });
          });

          userModel.update(
            { _userID: _userID },
            { $set: { "coupon.isApplied": false, "coupon.id": "", cart: [] } },
            async (err, affected) => {
              if (err) {
                let resp = {
                  status: "Failed",
                  msg: "Saving purchase failed!",
                };
                return res.status(400).json(resp);
              } else if (affected) {
                let resp = {
                  status: "Success",
                  msg: "Purchases Saved",
                  payload: affected,
                };
                let year: number = moment().year();
                year = year % 100;
                let allMember: any = await memberModel.findOne(
                  {},
                  async (err, members: any) => {
                    if (err) {
                      let resp = {
                        status: "Failed",
                        msg: "Retrieving members failed",
                      };
                      return res.status(400).json(resp);
                    }
                    if (members.year != year) {
                      let filter = {
                        _id: members._id,
                      };
                      let updateYear = {
                        year: year,
                      };
                      await memberModel.findOneAndUpdate(filter, updateYear);
                    }
                  }
                );
                let annualMemberCount: number = 0;
                annualMemberCount = allMember?.annualMemberCount;
                let lifetimeMemberCount: number = 0;
                lifetimeMemberCount = allMember?.lifetimeMemberCount;

                /*---------------------
                Membership is Purchased
                ---------------------*/
                if (isMembershipPurchased) {
                  if (membershipType === "annual-membership") {
                    let newMemberCount = annualMemberCount + 1;
                    let newMemberCountSize = -3;
                    let memberIDNumberWithPadding = `000000000${newMemberCount}`;
                    let memberIDNumberComponent =
                      memberIDNumberWithPadding.slice(newMemberCountSize);
                    let memberID = `A${year}${memberIDNumberComponent}`;
                    let startDate = moment().toISOString();
                    let endDate = moment(startDate)
                      .add(1, "year")
                      .toISOString();
                    let userFilter = {
                      _userID: _userID,
                    };
                    let updateObj = {
                      "membership.isMember": true,
                      "membership.id": memberID,
                      "membership.type": "annual",
                      "membership.startDate": startDate,
                      "membership.endDate": endDate,
                      "membership.paymentMethod": "Coupon",
                      "membership.amountPaid": -1,
                      "membership.txCode": "",
                      "membership.txTimestamp": startDate,
                      "membership.remarks": "",
                      "membership.isInCart": false,
                    };
                    await userModel.findOneAndUpdate(userFilter, updateObj);
                    memberModel.findOneAndUpdate(
                      { _id: allMember?._id },
                      { $push: { annualMembers: { _userID: _userID } } },
                      async (error, success) => {
                        if (error) {
                          let resp = {
                            status: "Failed",
                            msg: "adding userID to annualMember failed",
                          };
                          return res.json(resp);
                        } else if (success) {
                          let filter = {
                            _id: allMember?._id,
                          };
                          let updateAnnualMemberCount = {
                            annualMemberCount: newMemberCount,
                          };
                          await memberModel.findOneAndUpdate(
                            filter,
                            updateAnnualMemberCount
                          );
                        }
                      }
                    );
                  } else if (membershipType === "lifetime-membership") {
                    let newMemberCount = lifetimeMemberCount + 1;
                    let newMemberCountSize = -3;
                    let memberIDNumberWithPadding = `000000000${newMemberCount}`;
                    let memberIDNumberComponent =
                      memberIDNumberWithPadding.slice(newMemberCountSize);

                    let memberID = `L${year}${memberIDNumberComponent}`;
                    let startDate = moment().toISOString();
                    let endDate = "";
                    let userFilter = {
                      _userID: _userID,
                    };
                    let updateObj = {
                      "membership.isMember": true,
                      "membership.id": memberID,
                      "membership.type": "lifetime",
                      "membership.startDate": startDate,
                      "membership.endDate": endDate,
                      "membership.paymentMethod": "Coupon",
                      "membership.amountPaid": -1,
                      "membership.txCode": "",
                      "membership.txTimestamp": startDate,
                      "membership.remarks": "",
                      "membership.isInCart": false,
                    };
                    await userModel.findOneAndUpdate(userFilter, updateObj);
                    memberModel.findOneAndUpdate(
                      { _id: allMember?._id },
                      { $push: { lifetimeMembers: { _userID: _userID } } },
                      async (error, success) => {
                        if (error) {
                          let resp = {
                            status: "Failed",
                            msg: "adding userID to lifetimeMembers failed",
                          };
                          return res.json(resp);
                        } else if (success) {
                          let filter = {
                            _id: allMember?._id,
                          };
                          let updateLifetimeMemberCount = {
                            lifetimeMemberCount: newMemberCount,
                          };
                          await memberModel.findOneAndUpdate(
                            filter,
                            updateLifetimeMemberCount
                          );
                        }
                      }
                    );
                  }
                }

                /*----------------
                MemberID submitted
                ----------------*/
                if (userObj?.membership.submittedID.length > 0) {
                  let legacyMemberObj: any = await legacyMemberModel.findOne({
                    memberID: userObj?.membership.submittedID,
                  });
                  let membershipType: string = "";
                  let newMemberCount: number = 0;
                  let pushObj: any;
                  let updateMemberCountObj: any;
                  let amountPaid: number = 0;
                  if (legacyMemberObj?.type === "annual") {
                    membershipType = "annual";
                    newMemberCount = annualMemberCount + 1;
                    updateMemberCountObj = {
                      annualMemberCount: newMemberCount,
                    };
                  } else if (legacyMemberObj?.type === "lifetime") {
                    membershipType = "lifetime";
                    newMemberCount = lifetimeMemberCount + 1;
                    updateMemberCountObj = {
                      lifetimeMemberCount: newMemberCount,
                    };
                  }
                  if (legacyMemberObj?.fee) {
                    amountPaid = parseInt(legacyMemberObj?.fee);
                  } else {
                    amountPaid = -1;
                  }
                  let userFilter = {
                    _userID: _userID,
                  };
                  let updateObj = {
                    "membership.isMember": true,
                    "membership.id": legacyMemberObj?.memberID,
                    "membership.type": membershipType,
                    "membership.startDate": legacyMemberObj?.from,
                    "membership.endDate": legacyMemberObj?.to,
                    "membership.paymentMethod": legacyMemberObj?.paymentMethod,
                    "membership.amountPaid": amountPaid,
                    "membership.txCode": legacyMemberObj?.txCode,
                    "membership.txTimestamp": legacyMemberObj?.txTimestamp,
                    "membership.remarks": legacyMemberObj?.remarks,
                    "membership.isInCart": false,
                    "membership.submittedID": "",
                  };
                  await userModel.findOneAndUpdate(userFilter, updateObj);

                  if (membershipType === "annual") {
                    memberModel.findOneAndUpdate(
                      { _id: allMember?._id },
                      { $push: { annualMembers: { _userID: _userID } } },
                      async (error, success) => {
                        if (error) {
                          let resp = {
                            status: "Failed",
                            msg: "adding userID to lifetimeMembers failed",
                          };
                          return res.json(resp);
                        } else if (success) {
                          let filter = {
                            _id: allMember?._id,
                          };
                          let updateMemberCount = updateMemberCountObj;
                          await memberModel.findOneAndUpdate(
                            filter,
                            updateMemberCount
                          );
                        }
                      }
                    );
                  } else if (membershipType === "lifetime") {
                    memberModel.findOneAndUpdate(
                      { _id: allMember?._id },
                      { $push: { lifetimeMembers: { _userID: _userID } } },
                      async (error, success) => {
                        if (error) {
                          let resp = {
                            status: "Failed",
                            msg: "adding userID to lifetimeMembers failed",
                          };
                          return res.json(resp);
                        } else if (success) {
                          let filter = {
                            _id: allMember?._id,
                          };
                          let updateMemberCount = updateMemberCountObj;
                          await memberModel.findOneAndUpdate(
                            filter,
                            updateMemberCount
                          );
                        }
                      }
                    );
                  }
                }
                return res.json(resp);
              } else {
                let resp = {
                  status: "Failed",
                  msg: "Saving purchase failed!",
                };
                return res.status(400).json(resp);
              }
            }
          );
        }
      }
    );
  }
);

export default router;
