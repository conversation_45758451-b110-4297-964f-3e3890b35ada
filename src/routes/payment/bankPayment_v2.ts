// @ts-nocheck

import { Router } from "express";
import {
  alertModel,
  couponModel,
  userModel,
  ticketModel_v2,
  memberModel,
  legacyMemberModel,
} from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateBankPaymentDetails_v2 } from "../../validation";
import { sendBankConfirmationMail } from "../../helpers";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";
import * as voucherCodeGenerator from "@luxuryescapes/lib-voucher-code";

const router = Router();

router.post("/bank-payment-v2", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Validate bank payemnt details
  ---------------------------- */
  let { error } = validateBankPaymentDetails_v2(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  let issuer = userObj.profile;
  let persona = userObj.professional.occupation;

  if (userObj?.cartv2.length === 0) {
    let resp = {
      status: "Failed",
      msg: "Cart is empty",
    };
    return res.status(400).send(resp);
  }

  /* ----------------------------
  Extract inputs
  ---------------------------- */
  let { eventCode } = req.body;
  let cartv2: any = userObj?.cartv2;
  let cartv2ForEventCode: any = [];

  cartv2.map((cartItem: any) => {
    if (cartItem.eventCode === eventCode) {
      cartv2ForEventCode.push(cartItem);
    }
  });

  let cartItemIDs: any = [];
  let trackSessionsIdsInCart: any = [];

  cartv2ForEventCode.forEach((item: any) => {
    cartItemIDs.push(item.ticketId);
    if (item.sessionId) {
      trackSessionsIdsInCart.push(item.sessionId);
    }
  });

  let cartItemDetails = await ticketModel_v2
    .find()
    .where("ticketId")
    .in(cartItemIDs)
    .exec();

  let isMembershipPurchased: boolean = false;
  let membershipType: string = "";
  let cartItems: any = [];
  let cartTotal: number = 0;

  let isIndiaHCI24FullPurchased: boolean = false;
  let isVDWorkshopFreeTicketPurchased: boolean = false;

  cartItemDetails.forEach((ticket: any) => {
    let obj = {};
    let price: number = 0;
    let tierName: string = "";
    let ticketTitle: string = "";
    let ticketSubtitle: string = "";

    if (ticket.ticketType === "fullTicket") {
      ticketTitle = ticket.fullTicketDetails.title;

      if (ticketTitle === "India HCI 2024 Full Conference Ticket") {
        isIndiaHCI24FullPurchased = true;
      }
      if (ticketTitle === "Visual Discourse 2024 Full Conference Ticket") {
        isVDWorkshopFreeTicketPurchased = true;
      }

      let ticketPricingTiers: any = ticket.fullTicketDetails.tiers;
      let tierStudentPrice: number = 0;
      let tierProfessionalPrice: number = 0;
      ticketPricingTiers.map((tier: any) => {
        let currentTime: string = moment().toISOString();
        let isBetween: any = moment(currentTime).isBetween(
          tier.tierStartDate,
          tier.tierEndDate
        );

        if (isBetween) {
          tierName = tier.tierName;
          tierStudentPrice = tier.studentPrice;
          tierProfessionalPrice = tier.professionalPrice;
        }
      });

      if (persona === "student") {
        price = tierStudentPrice;
        ticketSubtitle = "For students";
      } else if (persona === "professional") {
        price = tierProfessionalPrice;
        ticketSubtitle = "For professionals";
      }
    } else if (ticket.ticketType === "basicTicket") {
      ticketTitle = ticket.basicTicketDetails.title;

      if (ticketTitle === "Visual Discourse 2024 One Day Pass (6th Nov)") {
        isVDWorkshopFreeTicketPurchased = true;
      }

      let ticketPricingTiers: any = ticket.basicTicketDetails.tiers;
      let tierStudentPrice: number = 0;
      let tierProfessionalPrice: number = 0;
      ticketPricingTiers.map((tier: any) => {
        let currentTime: string = moment().toISOString();
        let isBetween: any = moment(currentTime).isBetween(
          tier.tierStartDate,
          tier.tierEndDate
        );

        if (isBetween) {
          tierName = tier.tierName;
          tierStudentPrice = tier.studentPrice;
          tierProfessionalPrice = tier.professionalPrice;
        }
      });

      if (persona === "student") {
        price = tierStudentPrice;
        ticketSubtitle = "-";
      } else if (persona === "professional") {
        price = tierProfessionalPrice;
        ticketSubtitle = "-";
      }
    } else if (ticket.ticketType === "trackTicket") {
      let trackSessions: any = ticket.trackTicketDetails.sessions;
      trackSessionsIdsInCart.map((trackSessionIdInCart: any) => {
        trackSessions.map((trackSession: any) => {
          if (trackSessionIdInCart === trackSession.id) {
            let sessionPrice: number =
              persona === "professional"
                ? trackSession.professionalPrice
                : trackSession.studentPrice;
            let obj = {
              ticketID: ticket.ticketId,
              sessionId: trackSessionIdInCart,
              title: trackSession.title,
              subTitle: trackSession.type,
              tier: "",
              currency: "₹",
              price: sessionPrice,
            };

            cartItems.push(obj);
            cartTotal = cartTotal + sessionPrice;
          }
        });
      });
    }

    if (ticket.ticketType != "trackTicket") {
      obj = {
        ticketID: ticket.ticketId,
        title: ticketTitle,
        subTitle: ticketSubtitle,
        tier: tierName,
        currency: "₹",
        price: price,
      };
      cartItems.push(obj);
      cartTotal = cartTotal + price;
    }
  });

  /* ------------------------
  Discount coupon calculation
  ------------------------ */
  let isCouponApplied: boolean = false;
  if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
  let couponName: string = "";
  let couponDeductionType: string = "";
  let subDeductionType: string = "";
  let couponDeductionValue: number = 0;
  let subDeductionValue: number = 0;
  let couponTicketTypeIds: any;
  let discountStatement: string = "";
  let percentageDeductionValue: number = 0;
  let deductedAmount: number = 0;
  let oldCartTotal: number = 0;
  let couponAccess: string = "";
  oldCartTotal = cartTotal;

  if (isCouponApplied) {
    let couponObj: any = await couponModel.findOne({
      id: userObj.coupon.id,
    });
    couponName = couponObj.name;
    couponDeductionType = couponObj.deduction.type;
    couponDeductionValue = couponObj.deduction.value;
    let couponTicketTypeIdsUnprocessed: any =
      couponObj.deduction.ticketTypes.items;
    let couponTicketTypeIds: any = [];
    couponTicketTypeIdsUnprocessed.map((ticket: any) => {
      couponTicketTypeIds.push(ticket.ticketId);
    });
    couponAccess = couponObj.meta.access;

    if (couponDeductionType === "fixed") {
      cartTotal = cartTotal - couponDeductionValue;
      deductedAmount = couponDeductionValue;
    } else if (couponDeductionType === "percentage") {
      percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
      cartTotal = cartTotal - percentageDeductionValue;
      deductedAmount = percentageDeductionValue;
    } else if (couponDeductionType === "ticketType") {
      subDeductionType = couponObj.deduction.ticketTypes.subDeductionType;
      subDeductionValue = couponObj.deduction.ticketTypes.subDeductionValue;
      let ticketDetailsArr: any = await ticketModel_v2
        .find()
        .where("ticketId")
        .in(couponTicketTypeIds)
        .exec();

      let finalProcessingArr: any = [];
      cartItemDetails.map((cartItemDetail: any) => {
        ticketDetailsArr.map((ticketDetail: any) => {
          if (cartItemDetail.ticketId === ticketDetail.ticketId) {
            finalProcessingArr.push(cartItemDetail);
          }
        });
      });

      let ticketDeductionValue: number = 0;
      finalProcessingArr.map((ticket: any) => {
        let price: number = 0;
        let ticketPricingTiers: any;
        if (ticket.ticketType === "fullTicket") {
          ticketPricingTiers = ticket.fullTicketDetails.tiers;
        } else if (ticket.ticketType === "basicTicket") {
          ticketPricingTiers = ticket.basicTicketDetails.tiers;
        }
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );
          if (isBetween) {
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (persona === "student") {
          price = tierStudentPrice;
        } else if (persona === "professional") {
          price = tierProfessionalPrice;
        }

        if (subDeductionType === "fixed") {
          ticketDeductionValue = price - subDeductionValue;
          deductedAmount = deductedAmount + subDeductionValue;
        } else if (subDeductionType === "percentage") {
          percentageDeductionValue = (subDeductionValue / 100) * price;
          ticketDeductionValue = price - percentageDeductionValue;
          deductedAmount = deductedAmount + percentageDeductionValue;
        }
      });

      cartTotal = cartTotal - deductedAmount;
      discountStatement = `${couponName}`;
    }
  }

  /* --------------------
  Main calculation starts
  -------------------- */
  const { bankTxCode, paymentGateway } = req.body;
  let gatewayPerc = 0;
  let grandTotal = 0;
  let gatewayFee = 0;
  if (paymentGateway === "Razorpay") {
    gatewayPerc = 4;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "BankTransfer") {
    gatewayPerc = 0;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "Instamojo") {
    gatewayPerc = 3;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else {
    let resp = {
      status: "Failed",
      msg: "Invalid payment gateway",
    };
    return res.status(400).json(resp);
  }

  let cartSummaryWithDiscount = [
    {
      field1: "Cart Total (before discount)",
      field2: "₹",
      field3: `${oldCartTotal}`,
    },
    {
      field1: `Discount (${discountStatement})`,
      field2: "₹",
      field3: `${deductedAmount}`,
    },
    {
      field1: "New Cart Total (after discount)",
      field2: "₹",
      field3: `${cartTotal}`,
    },
    {
      field1: "Gateway Fee",
      field2: "₹",
      field3: `${gatewayFee}`,
    },
    {
      field1: "Grand Total",
      field2: "₹",
      field3: `${grandTotal}`,
    },
  ];

  let cartSummaryWithoutDiscount = [
    {
      field1: "Cart Total",
      field2: "₹",
      field3: `${cartTotal}`,
    },
    {
      field1: "Gateway Fee",
      field2: "₹",
      field3: `${gatewayFee}`,
    },
    {
      field1: "Grand Total",
      field2: "₹",
      field3: `${grandTotal}`,
    },
  ];

  let cartSummary: any;

  if (isCouponApplied) {
    cartSummary = cartSummaryWithDiscount;
  } else {
    cartSummary = cartSummaryWithoutDiscount;
  }

  let purchaseID: string = await uuidv4();
  let alertID: string = await uuidv4();
  let paymentDetails = [
    {
      field1: "PaymentID",
      field2: "",
      field3: `${purchaseID}`,
    },
    {
      field1: "Payment Method",
      field2: "",
      field3: "Bank Transfer",
    },
    {
      field1: "Bank Transaction Code",
      field2: "",
      field3: `${bankTxCode}`,
    },
  ];
  let couponDetailsObj: any = {};

  if (isCouponApplied) {
    couponDetailsObj.id = userObj.coupon.id;
    couponDetailsObj.name = couponName;
    couponDetailsObj.deductionType = couponDeductionType;
    couponDetailsObj.deductionValue = couponDeductionValue;
    couponDetailsObj.cartAmountBeforeDeduction = oldCartTotal;
    couponDetailsObj.deductedAmount = deductedAmount;
    couponDetailsObj.cartAmountAfterDeduction = cartTotal;

    if (couponAccess === "open") {
      let redeemerId = await uuidv4();
      let redeemerObj = {
        redeemerId: redeemerId,
        name: {
          first: issuer.name.first,
          last: issuer.name.last,
        },
        email: issuer.email,
        isUsed: true,
        usedOn: moment().toISOString(),
      };
      couponModel.findOneAndUpdate(
        { id: userObj.coupon.id },
        { $push: { redeemerList: redeemerObj } },
        (error, success) => {
          if (error) {
            console.log("Could not save open coupon redeemer");
          } else {
            console.log("Saved open coupon redeemer");
          }
        }
      );
    } else if (couponAccess === "emaillist") {
      couponModel.findOneAndUpdate(
        { id: userObj.coupon.id, "redeemerList.email": issuer.email },
        {
          "redeemerList.$.name.first": issuer.name.first,
          "redeemerList.$.name.last": issuer.name.last,
          "redeemerList.$.isUsed": true,
          "redeemerList.$.usedOn": moment().toISOString(),
        },
        (error, success) => {
          if (error) {
            console.log("Could not save emaillist coupon redeemers");
          } else {
            console.log("Saved emaillist coupon redeemers");
          }
        }
      );
    }
  }

  // START - Add coupon to VD
  if (isIndiaHCI24FullPurchased) {
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: 1,
    });
    let couponCode = couponCodeArr[0];
    let redeemerId = await uuidv4();
    let proposedRedeemerObj = {
      redeemerId: redeemerId,
      name: { first: "", last: "" },
      email: userObj?.profile.email,
      code: couponCode,
      isUsed: false,
      usedOn: "",
    };
    let couponName: string = "";
    if (persona === "student") {
      couponName = "IndiaHCI Student Attendee Discount";
    } else if (persona === "professional") {
      couponName = "IndiaHCI Professional Attendee Discount";
    }
    couponModel.updateMany(
      { name: couponName },
      { $push: { redeemerList: proposedRedeemerObj } },
      (error, success) => {
        if (error) {
          console.log("Could not insert email into VD coupon");
        } else {
          console.log("Email inserted into VD coupon");
        }
      }
    );
  }
  // END - Add coupon to VD

  // START - Add free workshop to VD
  if (isVDWorkshopFreeTicketPurchased) {
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: 1,
    });
    let couponCode = couponCodeArr[0];
    let redeemerId = await uuidv4();
    let proposedRedeemerObj = {
      redeemerId: redeemerId,
      name: { first: "", last: "" },
      email: userObj?.profile.email,
      code: couponCode,
      isUsed: false,
      usedOn: "",
    };
    let couponName: string = "Free Workshop";
    couponModel.updateMany(
      { name: couponName },
      { $push: { redeemerList: proposedRedeemerObj } },
      (error, success) => {
        if (error) {
          console.log("Could not insert email into VD coupon");
        } else {
          console.log("Email inserted into VD coupon");
        }
      }
    );
  }
  // END - Add free workshop to VD

  userModel.findOneAndUpdate(
    { _userID: _userID },
    {
      $push: {
        purchasedItems: {
          purchaseID: purchaseID,
          transactionID: bankTxCode,
          eventCode: eventCode,
          paymentGateway: paymentGateway,
          isCouponApplied: isCouponApplied,
          coupon: couponDetailsObj,
          cartTotal: cartTotal,
          gatewayFee: gatewayFee,
          grandTotal: grandTotal,
          purchasedItems: cartv2ForEventCode,
          paymentStatus: "under-verification",
        },
      },
    },
    async function (error, success) {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "Could not save ticket to cart",
        };
        return res.json(resp);
      } else {
        await sendBankConfirmationMail(
          eventCode,
          userObj?.profile.name.first,
          userObj?.profile.email,
          isCouponApplied,
          couponDetailsObj,
          cartItems,
          cartSummary,
          paymentDetails
        ).then((isConfirmationSent) => {
          if (isConfirmationSent) console.log("Bank confirmation mail sent!");
          else
            return res.status(400).json({
              status: "Failed",
              msg: "Unable to send verification email",
            });
        });

        let alertObj = {
          id: alertID,
          type: "Bank Transfer",
          eventCode: eventCode,
          issuer: {
            name: {
              first: issuer.name.first,
              middle: issuer.name.middle,
              last: issuer.name.last,
            },
            email: issuer.email,
          },
          resolver: {
            name: {
              first: "",
              middle: "",
              last: "",
            },
            email: "",
          },
          details: {
            prop1: bankTxCode,
            prop2: grandTotal,
            prop3: purchaseID,
            prop4: "",
            prop5: "",
          },
          status: {
            isActive: true,
            remarks: "",
          },
          timestamp: {
            issuedOn: moment().toISOString(),
            resolvedOn: "",
          },
        };
        const alertInstance = await alertModel.create(alertObj);
        if (!alertInstance._id) {
          console.log("Creating BankTransfer alert failed!");
        }

        // Decrement session quantity
        trackSessionsIdsInCart.map(async (trackSessionIdInCart: any) => {
          let filter = {
            eventCode: eventCode,
            ticketType: "trackTicket",
            "trackTicketDetails.sessions.id": trackSessionIdInCart,
          };
          let update = {
            $inc: { "trackTicketDetails.sessions.$.quantity": -1 },
          };
          await ticketModel_v2
            .findOneAndUpdate(filter, update)
            .exec((err, result) => {
              if (err) {
                console.log(err);
              } else {
                console.log(result);
              }
            });
        });

        userModel.update(
          { _userID: _userID },
          { $set: { "coupon.isApplied": false, "coupon.id": "", cartv2: [] } },
          async (err, affected) => {
            if (err) {
              let resp = {
                status: "Failed",
                msg: "Saving purchase failed!",
              };
              return res.status(400).json(resp);
            } else if (affected) {
              let resp = {
                status: "Success",
                msg: "Purchases Saved",
                payload: affected,
              };
              let year: number = moment().year();
              year = year % 100;
              let allMember: any = await memberModel.findOne(
                {},
                async (err, members: any) => {
                  if (err) {
                    let resp = {
                      status: "Failed",
                      msg: "Retrieving members failed",
                    };
                    return res.status(400).json(resp);
                  }
                  if (members.year != year) {
                    let filter = {
                      _id: members._id,
                    };
                    let updateYear = {
                      year: year,
                    };
                    await memberModel.findOneAndUpdate(filter, updateYear);
                  }
                }
              );
              let annualMemberCount: number = 0;
              annualMemberCount = allMember?.annualMemberCount;
              let lifetimeMemberCount: number = 0;
              lifetimeMemberCount = allMember?.lifetimeMemberCount;

              /*---------------------
              Membership is Purchased
              ---------------------*/
              if (isMembershipPurchased) {
                if (membershipType === "annual-membership") {
                  let newMemberCount = annualMemberCount + 1;
                  let newMemberCountSize = -3;
                  let memberIDNumberWithPadding = `000000000${newMemberCount}`;
                  let memberIDNumberComponent =
                    memberIDNumberWithPadding.slice(newMemberCountSize);
                  let memberID = `A${year}${memberIDNumberComponent}`;
                  let startDate = moment().toISOString();
                  let endDate = moment(startDate).add(1, "year").toISOString();
                  let userFilter = {
                    _userID: _userID,
                  };
                  let updateObj = {
                    "membership.isMember": true,
                    "membership.id": memberID,
                    "membership.type": "annual",
                    "membership.startDate": startDate,
                    "membership.endDate": endDate,
                    "membership.paymentMethod": paymentGateway,
                    "membership.amountPaid": -1,
                    "membership.txCode": bankTxCode,
                    "membership.txTimestamp": startDate,
                    "membership.remarks": "",
                    "membership.isInCart": false,
                  };
                  await userModel.findOneAndUpdate(userFilter, updateObj);
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { annualMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        let resp = {
                          status: "Failed",
                          msg: "adding userID to annualMember failed",
                        };
                        return res.json(resp);
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateAnnualMemberCount = {
                          annualMemberCount: newMemberCount,
                        };
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateAnnualMemberCount
                        );
                      }
                    }
                  );
                } else if (membershipType === "lifetime-membership") {
                  let newMemberCount = lifetimeMemberCount + 1;
                  let newMemberCountSize = -3;
                  let memberIDNumberWithPadding = `000000000${newMemberCount}`;
                  let memberIDNumberComponent =
                    memberIDNumberWithPadding.slice(newMemberCountSize);

                  let memberID = `L${year}${memberIDNumberComponent}`;
                  let startDate = moment().toISOString();
                  let endDate = "";
                  let userFilter = {
                    _userID: _userID,
                  };
                  let updateObj = {
                    "membership.isMember": true,
                    "membership.id": memberID,
                    "membership.type": "lifetime",
                    "membership.startDate": startDate,
                    "membership.endDate": endDate,
                    "membership.paymentMethod": paymentGateway,
                    "membership.amountPaid": -1,
                    "membership.txCode": bankTxCode,
                    "membership.txTimestamp": startDate,
                    "membership.remarks": "",
                    "membership.isInCart": false,
                  };
                  await userModel.findOneAndUpdate(userFilter, updateObj);
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { lifetimeMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        let resp = {
                          status: "Failed",
                          msg: "adding userID to lifetimeMembers failed",
                        };
                        return res.json(resp);
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateLifetimeMemberCount = {
                          lifetimeMemberCount: newMemberCount,
                        };
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateLifetimeMemberCount
                        );
                      }
                    }
                  );
                }
              }

              /*----------------
              MemberID submitted
              ----------------*/
              if (userObj?.membership.submittedID.length > 0) {
                let legacyMemberObj: any = await legacyMemberModel.findOne({
                  memberID: userObj?.membership.submittedID,
                });
                let membershipType: string = "";
                let newMemberCount: number = 0;
                let pushObj: any;
                let updateMemberCountObj: any;
                let amountPaid: number = 0;
                if (legacyMemberObj?.type === "annual") {
                  membershipType = "annual";
                  newMemberCount = annualMemberCount + 1;
                  updateMemberCountObj = {
                    annualMemberCount: newMemberCount,
                  };
                } else if (legacyMemberObj?.type === "lifetime") {
                  membershipType = "lifetime";
                  newMemberCount = lifetimeMemberCount + 1;
                  updateMemberCountObj = {
                    lifetimeMemberCount: newMemberCount,
                  };
                }
                if (legacyMemberObj?.fee) {
                  amountPaid = parseInt(legacyMemberObj?.fee);
                } else {
                  amountPaid = -1;
                }
                let userFilter = {
                  _userID: _userID,
                };
                let updateObj = {
                  "membership.isMember": true,
                  "membership.id": legacyMemberObj?.memberID,
                  "membership.type": membershipType,
                  "membership.startDate": legacyMemberObj?.from,
                  "membership.endDate": legacyMemberObj?.to,
                  "membership.paymentMethod": legacyMemberObj?.paymentMethod,
                  "membership.amountPaid": amountPaid,
                  "membership.txCode": legacyMemberObj?.txCode,
                  "membership.txTimestamp": legacyMemberObj?.txTimestamp,
                  "membership.remarks": legacyMemberObj?.remarks,
                  "membership.isInCart": false,
                  "membership.submittedID": "",
                };
                await userModel.findOneAndUpdate(userFilter, updateObj);

                if (membershipType === "annual") {
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { annualMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        let resp = {
                          status: "Failed",
                          msg: "adding userID to lifetimeMembers failed",
                        };
                        return res.json(resp);
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateMemberCount = updateMemberCountObj;
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateMemberCount
                        );
                      }
                    }
                  );
                } else if (membershipType === "lifetime") {
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { lifetimeMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        let resp = {
                          status: "Failed",
                          msg: "adding userID to lifetimeMembers failed",
                        };
                        return res.json(resp);
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateMemberCount = updateMemberCountObj;
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateMemberCount
                        );
                      }
                    }
                  );
                }
              }
              return res.json(resp);
            } else {
              let resp = {
                status: "Failed",
                msg: "Saving purchase failed!",
              };
              return res.status(400).json(resp);
            }
          }
        );
      }
    }
  );
});

export default router;
