import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, legacyMemberModel } from "../../models";
import moment from "moment";

const router = Router();

router.get("/legacymembers", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  const legacyMembers = await legacyMemberModel
    .find({}, "memberID email name type from to")
    .exec();

  if (!legacyMembers) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let payload: any = [];
  legacyMembers.forEach((member) => {
    let expireDate = moment(member.to);
    let nowDate = moment();
    let expiresIn = expireDate.diff(nowDate, "days");
    let obj = {
      name: member.name,
      email: member.email,
      memberID: member.memberID,
      memberType: member.type,
      expireDate: member.to,
      expiresIn: expiresIn,
    };
    payload.push(obj);
  });

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payloadLength: legacyMembers.length,
    payload: payload,
  };
  return res.json(resp);
});

export default router;
