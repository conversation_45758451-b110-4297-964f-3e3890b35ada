// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { isMember } from "../../validation";
import { legacyMemberModel, userModel, ticketModel } from "../../models";
import moment from "moment";

const router = Router();
router.post("/is-member", isUserLogged, getUserID, async (req, res) => {
  /* ---------------------
  Checks if user is logged
  --------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -----------------------
  Checks if inputs are valid
  ----------------------- */
  let { error } = isMember(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).json(resp);
  }

  /* -------------------------------
  Extract membershipID from req.body
  ------------------------------- */
  let { membershipID } = req.body;

  /* --------------------
  Checks if member exists
  -------------------- */
  const membershipObj = await legacyMemberModel.findOne({
    memberID: membershipID,
  });

  if (!membershipObj) {
    let resp = {
      status: "Failed",
      msg: "MemberID not found",
    };
    return res.json(resp);
  }

  if (membershipObj.type === "annual") {
    let expireDate = moment(membershipObj.to);
    let nowDate = moment();
    let expiresIn = expireDate.diff(nowDate, "days");
    if (expiresIn < 0) {
      let resp = {
        status: "Failed",
        msg: "Annual membership expired",
      };
      return res.json(resp);
    }
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({ _userID });

  let filter = {
    _userID: _userID,
  };
  let updateSubmittedID = {
    "membership.submittedID": membershipID,
  };
  await userModel.findOneAndUpdate(filter, updateSubmittedID);
  let updateIsInCart = {
    "membership.isInCart": true,
  };
  await userModel.findOneAndUpdate(filter, updateIsInCart);

  if (userObj.cart.length > 0) {
    userModel.update(
      { _userID: _userID },
      { $set: { cart: [] } },
      async (err, affected) => {
        if (err) {
          let resp = {
            status: "Failed",
            msg: "Clearing cart failed!",
          };
          return res.status(400).json(resp);
        } else if (affected) {
          const newFullConferenceTicket: any = await ticketModel.findOne({
            subType: "full-conference-hcipai-professional",
          });
          const newFullConferenceTicketID = newFullConferenceTicket.ticketID;
          userModel.findOneAndUpdate(
            { _userID: _userID },
            { $push: { cart: { ticketID: newFullConferenceTicketID } } },
            function (error, success) {
              if (error) {
                let resp = {
                  status: "Failed",
                  msg: "Updating cart failed!",
                  payload: userObj,
                };
                return res.json(resp);
              } else {
                let resp = {
                  status: "Success",
                  msg: "MemberID verified",
                };
                return res.json(resp);
              }
            }
          );
        }
      }
    );
  } else {
    let resp = {
      status: "Success",
      msg: "MemberID verified",
    };
    return res.json(resp);
  }
});

export default router;
