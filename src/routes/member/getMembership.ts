import { Router } from "express";
import { ticketModel, userModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";

const router = Router();

router.get("/getmembership", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({ _userID });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.status(400).json(resp);
  }

  let membershipSection = "";
  if (
    userObj.professional.occupation === "professional" &&
    userObj.membership.isMember === false &&
    userObj.membership.memberFromRegistration === true &&
    userObj.membership.submittedID.length === 0
  ) {
    membershipSection = "professional-hcipai-member";
  } else if (
    userObj.professional.occupation === "professional" &&
    userObj.membership.isMember === false &&
    userObj.membership.memberFromRegistration === false &&
    userObj.membership.submittedID.length === 0
  ) {
    membershipSection = "professional";
  }

  let payload = {
    membershipSection: membershipSection,
  };

  let resp = {
    status: "Success",
    msg: "Membership section retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
