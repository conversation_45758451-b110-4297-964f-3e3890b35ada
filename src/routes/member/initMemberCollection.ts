import { Router } from "express";
import { memberModel } from "../../models";

const router = Router();

router.post("/init-member-collection", async (req, res) => {
  /* --------------------------------------
    Extract registration inputs from req.body
    -------------------------------------- */
  let { year, annualMemberCount, lifetimeMemberCount } = req.body;

  /* --------------
    Create checkpoint
    -------------- */
  const init = await memberModel.create({
    year,
    annualMemberCount,
    lifetimeMemberCount,
  });

  if (!init._id) {
    let resp = {
      status: "Failed",
      msg: "Couldn't create legacy member",
    };
    return res.status(400).json(resp);
  } else {
    let resp = {
      status: "Success",
      msg: "Legacy created",
    };
    return res.status(400).json(resp);
  }
});

export default router;
