import { Router } from "express";
import { legacyMemberModel } from "../../models";

const router = Router();

router.post("/add-member-to-legacydb", async (req, res) => {
  /* --------------------------------------
  Extract registration inputs from req.body
  -------------------------------------- */
  let {
    memberID,
    name,
    email,
    mobile,
    orgInsti,
    type,
    from,
    to,
    paymentMethod,
    fee,
    txCode,
    txTimestamp,
    remarks,
  } = req.body;

  /* --------------
  Create checkpoint
  -------------- */
  const legacyMember = await legacyMemberModel.create({
    memberID,
    name,
    email,
    mobile,
    orgInsti,
    type,
    from,
    to,
    paymentMethod,
    fee,
    txCode,
    txTimestamp,
    remarks,
  });

  if (!legacyMember._id) {
    let resp = {
      status: "Failed",
      msg: "Couldn't create legacy member",
    };
    return res.status(400).json(resp);
  } else {
    let resp = {
      status: "Success",
      msg: "Legacy created",
    };
    return res.status(400).json(resp);
  }
});

export default router;
