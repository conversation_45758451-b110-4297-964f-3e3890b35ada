import { Router } from "express";
import { ticketModel, userModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";

const router = Router();

router.get(
  "/getmemberticketdetails",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
    Check if user is LoggedIn
    ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({ _userID });

    /* --------------------
    Query ticket and return
    -------------------- */
    const annualMembershipTicket: any = await ticketModel.findOne({
      subType: "annual-membership",
    });
    const lifetimeMembershipTicket: any = await ticketModel.findOne({
      subType: "lifetime-membership",
    });

    let payload: any = [];
    if (!annualMembershipTicket) {
      let resp = {
        status: "Failed",
        msg: "Couldn't fetch ticket data",
      };
      return res.status(400).json(resp);
    } else {
      let obj = {
        ticketID: annualMembershipTicket.ticketID,
        subType: annualMembershipTicket.subType,
        title: annualMembershipTicket.title,
        subTitle: annualMembershipTicket.subTitle,
        price: annualMembershipTicket.price[0].price.inr,
        ticketBtnStatus: "add-to-cart",
      };
      let userCart = userObj?.cart;
      userCart?.forEach((item: any) => {
        if (item.ticketID === obj.ticketID) {
          obj.ticketBtnStatus = "remove-from-cart";
        }
      });
      let userPurchases = userObj?.purchasedItems;
      userPurchases?.forEach((item: any) => {
        let purchaseList = item.purchasedItems;
        if (item.paymentStatus === "under-verification") {
          purchaseList.forEach((item: any) => {
            if (item.ticketID === annualMembershipTicket.ticketID) {
              payload.ticketBtnStatus = "under-verification";
            }
          });
        } else if (item.paymentStatus === "purchased") {
          purchaseList.forEach((item: any) => {
            if (item.ticketID === annualMembershipTicket.ticketID) {
              payload.ticketBtnStatus = "purchased";
            }
          });
        }
      });
      payload.push(obj);
    }

    if (!lifetimeMembershipTicket) {
      let resp = {
        status: "Failed",
        msg: "Couldn't fetch ticket data",
      };
      return res.status(400).json(resp);
    } else {
      let obj = {
        ticketID: lifetimeMembershipTicket.ticketID,
        subType: lifetimeMembershipTicket.subType,
        title: lifetimeMembershipTicket.title,
        subTitle: lifetimeMembershipTicket.subTitle,
        price: lifetimeMembershipTicket.price[0].price.inr,
        ticketBtnStatus: "add-to-cart",
      };
      let userCart = userObj?.cart;
      userCart?.forEach((item: any) => {
        if (item.ticketID === obj.ticketID) {
          obj.ticketBtnStatus = "remove-from-cart";
        } else {
          obj.ticketBtnStatus = "add-to-cart";
        }
      });
      let userPurchases = userObj?.purchasedItems;
      userPurchases?.forEach((item: any) => {
        let purchaseList = item.purchasedItems;
        if (item.paymentStatus === "under-verification") {
          purchaseList.forEach((item: any) => {
            if (item.ticketID === lifetimeMembershipTicket.ticketID) {
              payload.ticketBtnStatus = "under-verification";
            }
          });
        } else if (item.paymentStatus === "purchased") {
          purchaseList.forEach((item: any) => {
            if (item.ticketID === lifetimeMembershipTicket.ticketID) {
              payload.ticketBtnStatus = "purchased";
            }
          });
        }
      });
      payload.push(obj);
    }

    let resp = {
      status: "Success",
      msg: "Ticket data fetched",
      payload: payload,
    };
    return res.json(resp);
  }
);

export default router;
