import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import * as argon from "argon2";
import {
  isNameValid,
  isEmailValid,
  isPasswordValid,
  isMobileValid,
  isCountryValid,
  isProfileInfoStringValid,
  isProfileUpdateInputsValid,
  isInvoicePrefValid,
} from "../../validation";

const router = Router();

router.get("/profile", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  let payload = {
    name: userObj.profile.name,
    email: userObj.profile.email,
    mobile: userObj.profile.mobile,
    country: userObj.profile.country,
    professional: userObj.professional,
    billing: {
      isGSTInvoicePreferred:
        userObj.settings.billing.tax.india.gst.isGSTInvoicePreferred,
      businessName: userObj.settings.billing.tax.india.gst.businessName,
      taxID: userObj.settings.billing.tax.india.gst.taxID,
      taxJurisdiction: userObj.settings.billing.tax.india.gst.taxJurisdiction,
      billingAddressLine1:
        userObj.settings.billing.tax.india.gst.address.billing.line1,
      billingAddressLine2:
        userObj.settings.billing.tax.india.gst.address.billing.line2,
      billingAddressLine3:
        userObj.settings.billing.tax.india.gst.address.billing.line3,
    },
    membership: {
      isMember: userObj.membership.isMember,
      id: userObj.membership.id,
      type: userObj.membership.type,
      startDate: userObj.membership.startDate,
      endDate: userObj.membership.endDate,
    },
    isRegistrationManager: userObj.settings.isRegistrationManager,
    isAdmin: userObj.settings.isAdmin,
  };

  let resp = {
    status: "Success",
    msg: "Purchases retrieved",
    payload: payload,
  };
  return res.json(resp);
});

router.patch("/profile", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  let { item, value } = req.body;
  let { error } = isProfileUpdateInputsValid(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: "Invalid profile update inputs",
    };
    return res.json(resp);
  }

  let filter = {
    _userID: _userID,
  };

  if (item === "name") {
    let { error } = isNameValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid name values",
      };
      return res.json(resp);
    }
    let update = {
      "profile.name.first": value.firstName,
      "profile.name.last": value.lastName,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Name Changed",
    };
    return res.json(resp);
  } else if (item === "email") {
    let { error } = isEmailValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid email value",
      };
      return res.json(resp);
    }
    let update = {
      "profile.email": value.email,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Email Changed",
    };
    return res.json(resp);
  } else if (item === "password") {
    let { error } = isPasswordValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid password value",
      };
      return res.json(resp);
    }
    let psswd: string = await argon.hash(value.password, {
      type: argon.argon2id,
    });
    let update = {
      "profile.psswd": psswd,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Password Changed",
    };
    return res.json(resp);
  } else if (item === "phone") {
    let { error } = isMobileValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid mobile number values",
      };
      return res.json(resp);
    }
    let update = {
      "profile.mobile.isdCode": value.isdCode,
      "profile.mobile.country": value.mobileCountry,
      "profile.mobile.number": value.mobileNumber,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Mobile No. Changed",
    };
    return res.json(resp);
  } else if (item === "country") {
    let { error } = isCountryValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid country value",
      };
      return res.json(resp);
    }
    let update = {
      "profile.country": value.country,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Country Changed",
    };
    return res.json(resp);
  } else if (item === "orginsti") {
    let { error } = isProfileInfoStringValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid organisation/institution value",
      };
      return res.json(resp);
    }
    let update = {
      "professional.orgInsti": value.profileInfoString,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Organisation/Institution Changed",
    };
    return res.json(resp);
  } else if (item === "jobdegree") {
    let { error } = isProfileInfoStringValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid job/degree value",
      };
      return res.json(resp);
    }
    let update = {
      "professional.jobDegree": value.profileInfoString,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Job/Degree Changed",
    };
    return res.json(resp);
  } else if (item === "invoicePreferences") {
    let { error } = isInvoicePrefValid(value);
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Invalid invoice preferences",
      };
      return res.json(resp);
    }
    let update = {
      "settings.billing.tax.india.gst.isGSTInvoicePreferred":
        value.isGSTInvoicePreferred,
      "settings.billing.tax.india.gst.businessName": value.businessName,
      "settings.billing.tax.india.gst.taxID": value.taxID,
      "settings.billing.tax.india.gst.taxJurisdiction": value.taxJurisdiction,
      "settings.billing.tax.india.gst.address.billing.line1":
        value.billingAddressLine1,
      "settings.billing.tax.india.gst.address.billing.line2":
        value.billingAddressLine2,
      "settings.billing.tax.india.gst.address.billing.line3":
        value.billingAddressLine3,
    };
    await userModel.findOneAndUpdate(filter, update);
    let resp = {
      status: "Success",
      msg: "Invoice Preference Saved!",
    };
    return res.json(resp);
  } else {
    let resp = {
      status: "Failed",
      msg: "Invalid profile update inputs",
    };
    return res.json(resp);
  }
});

export default router;
