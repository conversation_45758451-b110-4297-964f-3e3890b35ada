import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import { isGetAllUserInputsValid } from "../../validation";
import moment from "moment";

const router = Router();

router.post("/user", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let {
    categoryFilter,
    membershipFilter,
    purchaseStatusFilter,
    pageNo,
    searchString,
    eventCode,
  } = req.body;
  let { error } = isGetAllUserInputsValid(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: "Invalid inputs to retrieve users",
    };
    return res.json(resp);
  }

  let users: any;
  if (!categoryFilter && !membershipFilter) {
    users = await userModel
      .find(
        {},
        "profile.name profile.email profile.country professional membership purchasedItems settings createdAt"
      )
      .sort({ createdAt: -1 })
      .exec();
  }

  if (categoryFilter && membershipFilter) {
    let isMember: boolean = false;
    if (membershipFilter === "member") {
      isMember = true;
    } else if (membershipFilter === "non-member") {
      isMember = false;
    }
    users = await userModel
      .find(
        {
          "professional.occupation": categoryFilter,
          "membership.isMember": isMember,
        },
        "profile.name profile.email profile.country professional membership purchasedItems settings createdAt"
      )
      .sort({ createdAt: -1 })
      .exec();
  } else if (categoryFilter) {
    users = await userModel
      .find(
        { "professional.occupation": categoryFilter },
        "profile.name profile.email profile.country professional membership purchasedItems settings createdAt"
      )
      .sort({ createdAt: -1 })
      .exec();
  } else if (membershipFilter) {
    let isMember: boolean = false;
    if (membershipFilter === "member") {
      isMember = true;
    } else if (membershipFilter === "non-member") {
      isMember = false;
    }
    users = await userModel
      .find(
        { "membership.isMember": isMember },
        "profile.name profile.email profile.country professional membership purchasedItems settings createdAt"
      )
      .sort({ createdAt: -1 })
      .exec();
  }

  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let payload: any = [];
  users.forEach((user: any) => {
    let nowDate = moment();
    let expiresIn: any = "";
    let hasPurchaseInEventCode: boolean = false;
    if (user.membership.type === "annual") {
      let expireDate = moment(user.membership.endDate);
      expiresIn = expireDate.diff(nowDate, "days");
    }
    let totalPurchasedItems = 0;
    user.purchasedItems.forEach((item: any) => {
      if (eventCode === item.eventCode) {
        hasPurchaseInEventCode = true;
        totalPurchasedItems = totalPurchasedItems + item.purchasedItems.length;
      }
    });
    let isBillingDetailsAvailable = true;
    if (
      user.settings.billing.tax.india.gst.isGSTInvoicePreferred === undefined &&
      user.settings.billing.tax.india.gst.taxJurisdiction === undefined
    ) {
      isBillingDetailsAvailable = false;
    }
    let obj = {
      firstName: user.profile.name.first,
      lastName: user.profile.name.last,
      email: user.profile.email,
      country: user.profile.country,
      occupation: user.professional.occupation,
      orgInsti: user.professional.orgInsti,
      jobDegree: user.professional.jobDegree,
      isMember: user.membership.isMember,
      memberID: user.membership.id,
      memberType: user.membership.type,
      startDate: user.membership.startDate,
      endDate: user.membership.endDate,
      expiresIn: expiresIn.toString(),
      totalPurchasedItems: totalPurchasedItems,
      isBillingDetailsAvailable: isBillingDetailsAvailable,
      createdAt: user.createdAt,
    };

    if (purchaseStatusFilter === "withoutPurchases") {
      if (!hasPurchaseInEventCode) {
        payload.push(obj);
      }
    } else if (purchaseStatusFilter === "withPurchases") {
      if (hasPurchaseInEventCode) {
        payload.push(obj);
      }
    } else {
      payload.push(obj);
    }
  });

  let professionalAccounts: number = 0;
  let studentAccounts: number = 0;
  let memberAccounts: number = 0;

  payload.map((user: any) => {
    if (user.isMember) {
      memberAccounts = memberAccounts + 1;
    } else {
      if (user.occupation === "student") {
        studentAccounts = studentAccounts + 1;
      } else if (user.occupation === "professional") {
        professionalAccounts = professionalAccounts + 1;
      }
    }
  });

  // Search Filtration
  let filteredPayload: any;
  if (searchString.length > 0) {
    filteredPayload = payload.filter((registrant) => {
      let fullName = registrant.firstName + " " + registrant.lastName;
      return (
        registrant.firstName
          .toLowerCase()
          .includes(searchString.toLowerCase()) ||
        registrant.lastName
          .toLowerCase()
          .includes(searchString.toLowerCase()) ||
        registrant.email.toLowerCase().includes(searchString.toLowerCase()) ||
        fullName.toLowerCase().includes(searchString.toLowerCase())
      );
    });
    payload = filteredPayload;
  }

  // Pagination
  let paginatedList: any;
  let range: number = 15;
  if (!pageNo) {
    pageNo = 1;
  }
  let lowestIndex: number = range * (pageNo - 1);
  let highestIndex: number = pageNo * range - 1;
  paginatedList = payload.slice(lowestIndex, highestIndex);

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    accounts: {
      total: payload.length,
      members: memberAccounts,
      professions: professionalAccounts,
      students: studentAccounts,
    },
    page: {
      total: Math.round(payload.length / range) + 1,
      lowestIndex: lowestIndex + 1,
      highestIndex: highestIndex + 1,
    },
    payload: paginatedList,
  };
  return res.json(resp);
});

export default router;
