import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models/user";
import moment from "moment";

const router = Router();
router.get("/home", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You must be logged in",
    };
    return res.status(400).json(resp);
  }
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne(
    { _userID },
    "meta.account.isSetupComplete meta.email.isVerified profile.name.first profile.name.last profile.email professional.occupation professional.orgInsti professional.jobDegree professional.idCard membership.isMember membership.id membership.type membership.endDate settings.currency.name settings.currency.symbol settings.isRegistrationManager settings.isAdmin settings.billing.tax.india.gst.isGSTInvoicePreferred settings.billing.tax.india.gst.taxJurisdiction"
  );

  let isEmailVerified: boolean = userObj.meta.email.isVerified;
  let isAccountSetupComplete: boolean = false;

  if (
    userObj.professional.orgInsti.length > 0 &&
    userObj.professional.jobDegree.length > 0
  ) {
    isAccountSetupComplete = true;
  }

  let isIdCardExists: boolean = false;
  let isIdCardExpired: boolean = false;
  let isIdCardVerified: boolean = false;
  if (userObj.professional.idCard.expiry) {
    isIdCardExists = true;
    isIdCardVerified = userObj.professional.idCard.isVerified;
    isIdCardExpired = moment().isAfter(userObj.professional.idCard.expiry);
  }

  let resp = {
    status: "Success",
    msg: "UserID retrieved",
    payload: userObj,
    isAccountSetupComplete: isAccountSetupComplete,
    isEmailVerified: isEmailVerified,
    isIdCardExists: isIdCardExists,
    isIdCardExpired: isIdCardExpired,
    isIdCardVerified: isIdCardVerified,
  };

  return res.json(resp);
});

export default router;
