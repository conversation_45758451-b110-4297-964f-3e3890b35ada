import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel } from "../../models";
import { isEmailValid } from "../../validation";
import moment from "moment";

const router = Router();

router.post("/singleuser", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const registrationManagerObj: any = await userModel.findOne({
    _userID,
  });

  if (!registrationManagerObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!registrationManagerObj.settings.isAdmin) {
  //   if (!registrationManagerObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let { error } = isEmailValid(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: "Invalid email input",
    };
    return res.json(resp);
  }

  let { email } = req.body;
  const userObj: any = await userModel.findOne(
    {
      "profile.email": email,
    },
    "profile.name profile.email profile.country profile.mobile.isdCode profile.mobile.number settings.billing.tax.india.gst professional membership purchasedItems createdAt"
  );

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let expireDate = moment(userObj.membership.endDate);
  let nowDate = moment();
  let expiresIn = expireDate.diff(nowDate, "days");
  let totalPurchasedItems = 0;
  userObj.purchasedItems.forEach((item: any) => {
    totalPurchasedItems = totalPurchasedItems + item.purchasedItems.length;
  });

  let gstStatus: string = "";
  let businessName: string = "";
  let taxID: string = "";
  let taxJurisdiction: string = "";
  let billingAddress: string = "";

  if (
    userObj.settings.billing.tax.india.gst.isGSTInvoicePreferred != undefined
  ) {
    if (userObj.settings.billing.tax.india.gst.isGSTInvoicePreferred === true) {
      gstStatus = "Registered";
    } else if (
      userObj.settings.billing.tax.india.gst.isGSTInvoicePreferred === false
    ) {
      gstStatus = "Not Registered";
    } else {
      gstStatus = "-";
    }
  } else {
    gstStatus = "-";
  }

  if (userObj.settings.billing.tax.india.gst.businessName != undefined) {
    if (userObj.settings.billing.tax.india.gst.businessName.length === 0) {
      businessName = "-";
    } else {
      businessName = userObj.settings.billing.tax.india.gst.businessName;
    }
  } else {
    businessName = "-";
  }

  if (userObj.settings.billing.tax.india.gst.taxID != undefined) {
    if (userObj.settings.billing.tax.india.gst.taxID.length === 0) {
      taxID = "-";
    } else {
      taxID = userObj.settings.billing.tax.india.gst.taxID;
    }
  } else {
    taxID = "-";
  }

  if (userObj.settings.billing.tax.india.gst.taxJurisdiction != undefined) {
    if (
      userObj.settings.billing.tax.india.gst.taxJurisdiction ===
      "inside-tax-jurisdiction-state"
    ) {
      taxJurisdiction = "Inside Maharashtra & Inside India";
    } else if (
      userObj.settings.billing.tax.india.gst.taxJurisdiction ===
      "outside-tax-jurisdiction-state"
    ) {
      taxJurisdiction = "Outside Maharashtra & Inside India";
    } else if (
      userObj.settings.billing.tax.india.gst.taxJurisdiction ===
      "outside-tax-jurisdiction-country"
    ) {
      taxJurisdiction = "Outside India";
    } else {
      taxJurisdiction = "-";
    }
  } else {
    taxJurisdiction = "-";
  }

  if (
    userObj.settings.billing.tax.india.gst.address.billing.line1 != undefined &&
    userObj.settings.billing.tax.india.gst.address.billing.line2 != undefined &&
    userObj.settings.billing.tax.india.gst.address.billing.line3 != undefined
  ) {
    if (
      userObj.settings.billing.tax.india.gst.address.billing.line1.length > 0
    ) {
      billingAddress =
        userObj.settings.billing.tax.india.gst.address.billing.line1;
    }

    if (
      userObj.settings.billing.tax.india.gst.address.billing.line2.length > 0
    ) {
      if (
        userObj.settings.billing.tax.india.gst.address.billing.line1.length ===
        0
      ) {
        billingAddress =
          userObj.settings.billing.tax.india.gst.address.billing.line2;
      } else {
        billingAddress =
          billingAddress +
          ", " +
          userObj.settings.billing.tax.india.gst.address.billing.line2;
      }
    }

    if (
      userObj.settings.billing.tax.india.gst.address.billing.line3.length > 0
    ) {
      if (
        userObj.settings.billing.tax.india.gst.address.billing.line1.length ===
          0 &&
        userObj.settings.billing.tax.india.gst.address.billing.line2.length ===
          0
      ) {
        billingAddress =
          userObj.settings.billing.tax.india.gst.address.billing.line3;
      } else {
        billingAddress =
          billingAddress +
          ", " +
          userObj.settings.billing.tax.india.gst.address.billing.line3;
      }
    }

    if (
      userObj.settings.billing.tax.india.gst.address.billing.line1.length ===
        0 &&
      userObj.settings.billing.tax.india.gst.address.billing.line2.length ===
        0 &&
      userObj.settings.billing.tax.india.gst.address.billing.line3.length === 0
    ) {
      billingAddress = "-";
    }
  } else {
    billingAddress = "-";
  }

  let payload = {
    firstName: userObj.profile.name.first,
    lastName: userObj.profile.name.last,
    email: userObj.profile.email,
    country: userObj.profile.country,
    isdCode: userObj.profile.mobile.isdCode,
    mobileNo: userObj.profile.mobile.number,
    occupation: userObj.professional.occupation,
    orgInsti: userObj.professional.orgInsti,
    jobDegree: userObj.professional.jobDegree,
    isMember: userObj.membership.isMember,
    memberID: userObj.membership.id,
    memberType: userObj.membership.type,
    startDate: userObj.membership.startDate,
    endDate: userObj.membership.endDate,
    expiresIn: expiresIn.toString(),
    totalPurchasedItems: totalPurchasedItems,
    gstStatus: gstStatus,
    businessName: businessName,
    taxID: taxID,
    taxJurisdiction: taxJurisdiction,
    billingAddress: billingAddress,
    createdAt: userObj.createdAt,
  };

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
