import { Router } from "express";
import { alertModel, razorPaymentsModel, userModel } from "../../models";
import crypto from "crypto";
import moment from "moment";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post("/rzppaymentfailed", async (req, res) => {
  const rzpAccountID = "acc_BPcbAh3C2V0unD";
  if (req.body.account_id != rzpAccountID) {
    return res.json({ status: "ok" });
  }

  const secret = "hcipai";
  const shasum = crypto.createHmac("sha256", secret);
  shasum.update(JSON.stringify(req.body));
  const digest = shasum.digest("hex");
  if (digest != req.headers["x-razorpay-signature"]) {
    return res.json({ status: "ok" });
  }

  const paymentDetails = req.body.payload.payment.entity;
  const rzpPaymentID = paymentDetails.id;
  const orderID = paymentDetails.order_id;

  const rzpPaymentObj: any = await razorPaymentsModel.findOne({ orderID });
  if (!rzpPaymentObj) {
    return res.json({ status: "ok" });
  }

  const _userID = rzpPaymentObj._userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  let issuer = userObj.profile;
  let alertID: string = await uuidv4();
  let amount = Number(rzpPaymentObj.amount) / 100;
  let alertObj = {
    id: alertID,
    type: "Razorpay Payment Failure",
    eventCode: rzpPaymentObj.eventCode,
    issuer: {
      name: {
        first: issuer.name.first,
        middle: issuer.name.middle,
        last: issuer.name.last,
      },
      email: issuer.email,
    },
    resolver: {
      name: {
        first: "",
        middle: "",
        last: "",
      },
      email: "",
    },
    details: {
      prop1: rzpPaymentID,
      prop2: amount,
      prop3: "",
      prop4: "",
      prop5: "",
    },
    status: {
      isActive: true,
      remarks: "",
    },
    timestamp: {
      issuedOn: moment().toISOString(),
      resolvedOn: "",
    },
  };
  const alertInstance = await alertModel.create(alertObj);
  if (!alertInstance._id) {
    console.log("Creating RzpFailed alert failed!");
  } else {
    console.log("RzpFailed alert created!");
  }
  return res.json({ status: "ok" });
});

export default router;
