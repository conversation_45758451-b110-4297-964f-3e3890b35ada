// @ts-nocheck

import { Router } from "express";
import {
  couponModel,
  razorPaymentsModel,
  userModel,
  ticketModel_v2,
} from "../../models";
import crypto from "crypto";
import moment from "moment";
import { sendRazorpayConfirmationMail } from "../../helpers";
import { v4 as uuidv4 } from "uuid";
import * as voucherCodeGenerator from "@luxuryescapes/lib-voucher-code";

const router = Router();

router.post("/razorpaymenthook-2", async (req, res) => {
  const rzpAccountID = "acc_BPcbAh3C2V0unD";
  if (req.body.account_id != rzpAccountID) {
    return res.status(200).json({ status: "ok" });
  }

  const secret = "hcipai";
  const shasum = crypto.createHmac("sha256", secret);
  shasum.update(JSON.stringify(req.body));
  const digest = shasum.digest("hex");

  if (digest != req.headers["x-razorpay-signature"]) {
    return res.status(200).json({ status: "ok" });
  }

  const paymentDetails = req.body.payload.payment.entity;
  const rzpPaymentID = paymentDetails.id;
  const orderID = paymentDetails.order_id;
  const isPaymentInternational = paymentDetails.international;
  const method = paymentDetails.method;
  const captured = paymentDetails.captured;
  const status = paymentDetails.status;

  const rzpPaymentObj: any = await razorPaymentsModel.findOne({ orderID });
  let eventCode: string = rzpPaymentObj.eventCode;
  if (!rzpPaymentObj) {
    return res.status(200).json({ status: "ok" });
  }

  if (eventCode === "membership") {
    return res.status(200).json({ status: "ok" });
  }

  if (rzpPaymentObj.captured === true && rzpPaymentObj.status === "captured") {
    return res.status(200).json({ status: "ok" });
  }

  const _userID = rzpPaymentObj._userID;
  const purchaseID = rzpPaymentObj.purchaseID;

  let filter = {
    orderID: orderID,
  };
  let updateRazorpayObj = {
    rzpPaymentID: rzpPaymentID,
    isPaymentInternational: isPaymentInternational,
    method: method,
    captured: captured,
    status: status,
  };
  await razorPaymentsModel.findOneAndUpdate(filter, updateRazorpayObj);

  if (status != "captured") {
    return res.status(200).json({ status: "ok" });
  }

  const userObj: any = await userModel.findOne({
    _userID,
  });
  const userObj: any = await userModel.findOne({
    _userID,
  });
  let persona = userObj.professional.occupation;
  let issuer = userObj.profile;

  /* --- blockDoubleEntryInDBstart --- */
  let purchaseArr = userObj.purchasedItems;
  let isPaymentRegisteredInDB: boolean = false;
  purchaseArr.forEach((purchase: any) => {
    if (purchase.transactionID === rzpPaymentID) {
      isPaymentRegisteredInDB = true;
    }
  });

  if (isPaymentRegisteredInDB) {
    return res.status(200).json({ status: "ok" });
  }

  /* --- blockDoubleEntryInDBend --- */

  if (userObj?.cartv2.length === 0) {
    return res.status(200).json({ status: "ok" });
  }

  /* --- Extract --- */
  let cartv2: any = userObj?.cartv2;
  let cartv2ForEventCode: any = [];

  cartv2.map((cartItem: any) => {
    if (cartItem.eventCode === eventCode) {
      cartv2ForEventCode.push(cartItem);
    }
  });

  let cartItemIDs: any = [];
  let trackSessionsIdsInCart: any = [];

  cartv2ForEventCode.forEach((item: any) => {
    cartItemIDs.push(item.ticketId);
    if (item.sessionId) {
      trackSessionsIdsInCart.push(item.sessionId);
    }
  });

  let cartItemDetails = await ticketModel_v2
    .find()
    .where("ticketId")
    .in(cartItemIDs)
    .exec();

  let cartItems: any = [];
  let cartTotal: number = 0;

  let isIndiaHCI24FullPurchased: boolean = false;
  let isVDWorkshopFreeTicketPurchased: boolean = false;

  cartItemDetails.forEach((ticket: any) => {
    let obj = {};
    let price: number = 0;
    let tierName: string = "";
    let ticketTitle: string = "";
    let ticketSubtitle: string = "";

    if (ticket.ticketType === "fullTicket") {
      ticketTitle = ticket.fullTicketDetails.title;

      if (ticketTitle === "India HCI 2024 Full Conference Ticket") {
        isIndiaHCI24FullPurchased = true;
      }
      if (ticketTitle === "Visual Discourse 2024 Full Conference Ticket") {
        isVDWorkshopFreeTicketPurchased = true;
      }

      let ticketPricingTiers: any = ticket.fullTicketDetails.tiers;
      let tierStudentPrice: number = 0;
      let tierProfessionalPrice: number = 0;
      ticketPricingTiers.map((tier: any) => {
        let currentTime: string = moment().toISOString();
        let isBetween: any = moment(currentTime).isBetween(
          tier.tierStartDate,
          tier.tierEndDate
        );

        if (isBetween) {
          tierName = tier.tierName;
          tierStudentPrice = tier.studentPrice;
          tierProfessionalPrice = tier.professionalPrice;
        }
      });

      if (persona === "student") {
        price = tierStudentPrice;
        ticketSubtitle = "For students";
      } else if (persona === "professional") {
        price = tierProfessionalPrice;
        ticketSubtitle = "For professionals";
      }
    } else if (ticket.ticketType === "basicTicket") {
      ticketTitle = ticket.basicTicketDetails.title;
      let ticketPricingTiers: any = ticket.basicTicketDetails.tiers;
      let tierStudentPrice: number = 0;
      let tierProfessionalPrice: number = 0;
      ticketPricingTiers.map((tier: any) => {
        let currentTime: string = moment().toISOString();
        let isBetween: any = moment(currentTime).isBetween(
          tier.tierStartDate,
          tier.tierEndDate
        );

        if (isBetween) {
          tierName = tier.tierName;
          tierStudentPrice = tier.studentPrice;
          tierProfessionalPrice = tier.professionalPrice;
        }
      });

      if (persona === "student") {
        price = tierStudentPrice;
        ticketSubtitle = "For students";
      } else if (persona === "professional") {
        price = tierProfessionalPrice;
        ticketSubtitle = "For professionals";
      }
    } else if (ticket.ticketType === "trackTicket") {
      let trackSessions: any = ticket.trackTicketDetails.sessions;
      trackSessionsIdsInCart.map((trackSessionIdInCart: any) => {
        trackSessions.map((trackSession: any) => {
          if (trackSessionIdInCart === trackSession.id) {
            let sessionPrice: number =
              persona === "professional"
                ? trackSession.professionalPrice
                : trackSession.studentPrice;
            let obj = {
              ticketID: ticket.ticketId,
              sessionId: trackSessionIdInCart,
              title: trackSession.title,
              subTitle: trackSession.type,
              tier: "",
              currency: "₹",
              price: sessionPrice,
            };
            cartItems.push(obj);
            cartTotal = cartTotal + sessionPrice;
          }
        });
      });
    }

    if (ticket.ticketType != "trackTicket") {
      obj = {
        ticketID: ticket.ticketId,
        title: ticketTitle,
        subTitle: ticketSubtitle,
        tier: tierName,
        currency: "₹",
        price: price,
      };
      cartItems.push(obj);
      cartTotal = cartTotal + price;
    }
  });

  /* -------------------------------
  Discount coupon calculation starts
  ------------------------------- */
  let isCouponApplied: boolean = false;
  if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
  let couponName: string = "";
  let couponDeductionType: string = "";
  let subDeductionType: string = "";
  let couponDeductionValue: number = 0;
  let subDeductionValue: number = 0;
  let couponTicketTypeIds: any;
  let discountStatement: string = "";
  let percentageDeductionValue: number = 0;
  let deductedAmount: number = 0;
  let oldCartTotal: number = 0;
  let couponAccess: string = "";
  oldCartTotal = cartTotal;

  if (isCouponApplied) {
    let couponObj: any = await couponModel.findOne({
      id: userObj.coupon.id,
    });
    couponName = couponObj.name;
    couponDeductionType = couponObj.deduction.type;
    couponDeductionValue = couponObj.deduction.value;
    let couponTicketTypeIdsUnprocessed: any =
      couponObj.deduction.ticketTypes.items;
    let couponTicketTypeIds: any = [];
    couponTicketTypeIdsUnprocessed.map((ticket: any) => {
      couponTicketTypeIds.push(ticket.ticketId);
    });
    couponAccess = couponObj.meta.access;

    if (couponDeductionType === "fixed") {
      cartTotal = cartTotal - couponDeductionValue;
      deductedAmount = couponDeductionValue;
    } else if (couponDeductionType === "percentage") {
      percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
      cartTotal = cartTotal - percentageDeductionValue;
      deductedAmount = percentageDeductionValue;
    } else if (couponDeductionType === "ticketType") {
      subDeductionType = couponObj.deduction.ticketTypes.subDeductionType;
      subDeductionValue = couponObj.deduction.ticketTypes.subDeductionValue;
      let ticketDetailsArr: any = await ticketModel_v2
        .find()
        .where("ticketId")
        .in(couponTicketTypeIds)
        .exec();

      let finalProcessingArr: any = [];
      cartItemDetails.map((cartItemDetail: any) => {
        ticketDetailsArr.map((ticketDetail: any) => {
          if (cartItemDetail.ticketId === ticketDetail.ticketId) {
            finalProcessingArr.push(cartItemDetail);
          }
        });
      });

      let ticketDeductionValue: number = 0;
      finalProcessingArr.map((ticket: any) => {
        let price: number = 0;
        let ticketPricingTiers: any;
        if (ticket.ticketType === "fullTicket") {
          ticketPricingTiers = ticket.fullTicketDetails.tiers;
        } else if (ticket.ticketType === "basicTicket") {
          ticketPricingTiers = ticket.basicTicketDetails.tiers;
        }
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;

        ticketPricingTiers.map((tier: any) => {
          let currentTime: string = moment().toISOString();
          let isBetween: any = moment(currentTime).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );
          if (isBetween) {
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (persona === "student") {
          price = tierStudentPrice;
        } else if (persona === "professional") {
          price = tierProfessionalPrice;
        }

        if (subDeductionType === "fixed") {
          ticketDeductionValue = price - subDeductionValue;
          deductedAmount = deductedAmount + subDeductionValue;
        } else if (subDeductionType === "percentage") {
          percentageDeductionValue = (subDeductionValue / 100) * price;
          ticketDeductionValue = price - percentageDeductionValue;
          deductedAmount = deductedAmount + percentageDeductionValue;
        }
      });

      cartTotal = cartTotal - deductedAmount;
      discountStatement = `${couponName}`;
    }
  }

  /* -----------------------------
  Discount coupon calculation ends
  ----------------------------- */
  let paymentGateway = "Razorpay";
  let gatewayPerc = 0;
  let grandTotal = 0;
  let gatewayFee = 0;
  if (paymentGateway === "Razorpay") {
    gatewayPerc = 4;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "BankTransfer") {
    gatewayPerc = 0;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "Instamojo") {
    gatewayPerc = 3;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else {
    return res.status(200).json({ status: "ok" });
  }

  gatewayFee = Math.round(gatewayFee);
  grandTotal = Math.round(grandTotal);

  let cartSummaryWithDiscount = [
    {
      field1: "Cart Total (before discount)",
      field2: "₹",
      field3: `${oldCartTotal}`,
    },
    {
      field1: `Discount (${discountStatement})`,
      field2: "₹",
      field3: `${deductedAmount}`,
    },
    {
      field1: "New Cart Total (after discount)",
      field2: "₹",
      field3: `${cartTotal}`,
    },
    {
      field1: "Gateway Fee",
      field2: "₹",
      field3: `${gatewayFee}`,
    },
    {
      field1: "Grand Total",
      field2: "₹",
      field3: `${grandTotal}`,
    },
  ];

  let cartSummaryWithoutDiscount = [
    {
      field1: "Cart Total",
      field2: "₹",
      field3: `${cartTotal}`,
    },
    {
      field1: "Gateway Fee",
      field2: "₹",
      field3: `${gatewayFee}`,
    },
    {
      field1: "Grand Total",
      field2: "₹",
      field3: `${grandTotal}`,
    },
  ];

  let cartSummary: any;

  if (isCouponApplied) {
    cartSummary = cartSummaryWithDiscount;
  } else {
    cartSummary = cartSummaryWithoutDiscount;
  }

  let paymentDetailsArr = [
    {
      field1: "PaymentID",
      field2: "",
      field3: `${rzpPaymentID}`,
    },
    {
      field1: "Payment Method",
      field2: "",
      field3: "Razorpay",
    },
  ];

  let couponDetailsObj: any = {};

  if (isCouponApplied) {
    couponDetailsObj.id = userObj.coupon.id;
    couponDetailsObj.name = couponName;
    couponDetailsObj.deductionType = couponDeductionType;
    couponDetailsObj.deductionValue = couponDeductionValue;
    couponDetailsObj.cartAmountBeforeDeduction = oldCartTotal;
    couponDetailsObj.deductedAmount = deductedAmount;
    couponDetailsObj.cartAmountAfterDeduction = cartTotal;

    if (couponAccess === "open") {
      let redeemerId = await uuidv4();
      let redeemerObj = {
        redeemerId: redeemerId,
        name: {
          first: issuer.name.first,
          last: issuer.name.last,
        },
        email: issuer.email,
        isUsed: true,
        usedOn: moment().toISOString(),
      };
      couponModel.findOneAndUpdate(
        { id: userObj.coupon.id },
        { $push: { redeemerList: redeemerObj } },
        (error, success) => {
          if (error) {
            console.log("Could not save open coupon redeemer");
          } else {
            console.log("Saved open coupon redeemer");
          }
        }
      );
    } else if (couponAccess === "emaillist") {
      couponModel.findOneAndUpdate(
        { id: userObj.coupon.id, "redeemerList.email": issuer.email },
        {
          "redeemerList.$.name.first": issuer.name.first,
          "redeemerList.$.name.last": issuer.name.last,
          "redeemerList.$.isUsed": true,
          "redeemerList.$.usedOn": moment().toISOString(),
        },
        (error, success) => {
          if (error) {
            console.log("Could not save emaillist coupon redeemers");
          } else {
            console.log("Saved emaillist coupon redeemers");
          }
        }
      );
    }
  }

  // START - Add coupon to VD
  if (isIndiaHCI24FullPurchased) {
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: 1,
    });
    let couponCode = couponCodeArr[0];
    let redeemerId = await uuidv4();
    let proposedRedeemerObj = {
      redeemerId: redeemerId,
      name: { first: "", last: "" },
      email: userObj?.profile.email,
      code: couponCode,
      isUsed: false,
      usedOn: "",
    };
    let couponName: string = "";
    if (persona === "student") {
      couponName = "IndiaHCI Student Attendee Discount";
    } else if (persona === "professional") {
      couponName = "IndiaHCI Professional Attendee Discount";
    }
    couponModel.updateMany(
      { name: couponName },
      { $push: { redeemerList: proposedRedeemerObj } },
      (error, success) => {
        if (error) {
          console.log("Could not insert email into VD coupon");
        } else {
          console.log("Email inserted into VD coupon");
        }
      }
    );
  }
  // END - Add coupon to VD

  // START - Add free workshop to VD
  if (isVDWorkshopFreeTicketPurchased) {
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: 1,
    });
    let couponCode = couponCodeArr[0];
    let redeemerId = await uuidv4();
    let proposedRedeemerObj = {
      redeemerId: redeemerId,
      name: { first: "", last: "" },
      email: userObj?.profile.email,
      code: couponCode,
      isUsed: false,
      usedOn: "",
    };
    let couponName: string = "Free Workshop";
    couponModel.updateMany(
      { name: couponName },
      { $push: { redeemerList: proposedRedeemerObj } },
      (error, success) => {
        if (error) {
          console.log("Could not insert email into VD coupon");
        } else {
          console.log("Email inserted into VD coupon");
        }
      }
    );
  }
  // END - Add free workshop to VD

  userModel.findOneAndUpdate(
    { _userID: _userID },
    {
      $push: {
        purchasedItems: {
          purchaseID: purchaseID,
          eventCode: eventCode,
          transactionID: rzpPaymentID,
          paymentGateway: paymentGateway,
          isCouponApplied: isCouponApplied,
          coupon: couponDetailsObj,
          cartTotal: cartTotal,
          gatewayFee: gatewayFee,
          grandTotal: grandTotal,
          purchasedItems: cartv2ForEventCode,
          paymentStatus: "purchased",
        },
      },
    },
    async (error, success) => {
      if (error) {
        console.log("Could not save ticket to cart");
        return res.status(200).json({ status: "ok" });
      } else {
        await sendRazorpayConfirmationMail(
          eventCode,
          userObj?.profile.name.first,
          userObj?.profile.email,
          isCouponApplied,
          couponDetailsObj,
          cartItems,
          cartSummary,
          paymentDetailsArr
        ).then((isConfirmationSent) => {
          if (isConfirmationSent)
            console.log("Razorpay confirmation mail sent!");
          else {
            console.log("Unable to send verification email");
            return res.status(200).json({ status: "ok" });
          }
        });

        // Decrement session quantity
        trackSessionsIdsInCart.map(async (trackSessionIdInCart: any) => {
          let filter = {
            eventCode: eventCode,
            ticketType: "trackTicket",
            "trackTicketDetails.sessions.id": trackSessionIdInCart,
          };
          let update = {
            $inc: { "trackTicketDetails.sessions.$.quantity": -1 },
          };
          await ticketModel_v2
            .findOneAndUpdate(filter, update)
            .exec((err, result) => {
              if (err) {
                console.log(err);
              } else {
                console.log(result);
              }
            });
        });

        userModel.update(
          { _userID: _userID },
          {
            $set: {
              "coupon.isApplied": false,
              "coupon.id": "",
              cartv2: [],
            },
          },
          async (err, affected) => {
            if (err) {
              return res.status(200).json({ status: "ok" });
            } else if (affected) {
              return res.status(200).json({ status: "ok" });
            } else {
              return res.status(200).json({ status: "ok" });
            }
          }
        );
      }
    }
  );
});

export default router;
