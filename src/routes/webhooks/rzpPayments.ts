// @ts-nocheck

import { Router } from "express";
import {
  couponModel,
  razorPaymentsModel,
  userModel,
  legacyMemberModel,
  memberModel,
  ticketModel,
} from "../../models";
import crypto from "crypto";
import moment from "moment";
import { sendRazorpayConfirmationMail } from "../../helpers";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post("/razorpaymenthook-2", async (req, res) => {
  const rzpAccountID = "acc_BPcbAh3C2V0unD";
  if (req.body.account_id != rzpAccountID) {
    return res.json({ status: "ok" });
  }
  const secret = "hcipai";
  const shasum = crypto.createHmac("sha256", secret);
  shasum.update(JSON.stringify(req.body));
  const digest = shasum.digest("hex");

  if (digest != req.headers["x-razorpay-signature"]) {
    return res.json({ status: "ok" });
  }

  const paymentDetails = req.body.payload.payment.entity;
  const rzpPaymentID = paymentDetails.id;
  const orderID = paymentDetails.order_id;
  const isPaymentInternational = paymentDetails.international;
  const method = paymentDetails.method;
  const captured = paymentDetails.captured;
  const status = paymentDetails.status;

  const rzpPaymentObj: any = await razorPaymentsModel.findOne({ orderID });
  if (!rzpPaymentObj) {
    return res.json({ status: "ok" });
  }

  if (rzpPaymentObj.captured === true && rzpPaymentObj.status === "captured") {
    return res.json({ status: "ok" });
  }

  const _userID = rzpPaymentObj._userID;
  const purchaseID = rzpPaymentObj.purchaseID;

  let filter = {
    orderID: orderID,
  };
  let updateRazorpayObj = {
    rzpPaymentID: rzpPaymentID,
    isPaymentInternational: isPaymentInternational,
    method: method,
    captured: captured,
    status: status,
  };
  await razorPaymentsModel.findOneAndUpdate(filter, updateRazorpayObj);

  if (status != "captured") {
    return res.json({ status: "ok" });
  }

  const userObj: any = await userModel.findOne({
    _userID,
  });
  let persona = userObj.professional.occupation;
  let issuer = userObj.profile;

  /* --- blockDoubleEntryInDBstart --- */
  let purchaseArr = userObj.purchasedItems;
  let isPaymentRegisteredInDB: boolean = false;
  purchaseArr.forEach((purchase: any) => {
    if (purchase.transactionID === rzpPaymentID) {
      isPaymentRegisteredInDB = true;
    }
  });

  if (isPaymentRegisteredInDB) {
    return res.json({ status: "ok" });
  }
  /* --- blockDoubleEntryInDBend --- */

  if (userObj?.cart.length === 0) {
    return res.json({ status: "ok" });
  }

  let cartItemIDs: any = [];
  userObj?.cart.forEach((item: any) => {
    cartItemIDs.push(item.ticketID);
  });

  let cartItemDetails = await ticketModel
    .find()
    .where("ticketID")
    .in(cartItemIDs)
    .exec();

  let isMembershipPurchased: boolean = false;
  let membershipType: string = "";

  let cartItems: any = [];
  let cartTotal: number = 0;
  cartItemDetails.forEach(async (item) => {
    let price = 0;
    let tier = "";
    let currency = "₹";
    let priceArr = item.price;
    if (item.type === "membership" && isMembershipPurchased === false) {
      isMembershipPurchased = true;
      membershipType = item.subType;
    }
    let subType = item.subType;
    priceArr.forEach((item: any) => {
      let startTx = item.startDate;
      let endTx = item.endDate;
      let startDate = new Date(parseInt(startTx) * 1000).toISOString();
      let endDate = new Date(parseInt(endTx) * 1000).toISOString();
      let nowDate = moment().toISOString();
      let isBetween = moment(nowDate).isBetween(startDate, endDate);
      if (isBetween) {
        if (subType === "course") {
          if (persona === item.name.toLowerCase()) {
            tier = item.name;
            price = item.price;
            cartTotal = cartTotal + item.price.inr;
          }
        } else {
          tier = item.name;
          price = item.price;
          cartTotal = cartTotal + item.price.inr;
        }
      }
    });

    let buff = {
      ticketID: item.ticketID,
      title: item.title,
      subTitle: item.subTitle,
      tier: tier,
      currency: currency,
      price: price,
    };
    cartItems.push(buff);

    if (subType === "course" || subType === "workshop") {
      let filter = {
        ticketID: item.ticketID,
      };
      let updateQuantity = {
        $inc: { quantity: -1 },
      };
      await ticketModel
        .findOneAndUpdate(filter, updateQuantity)
        .exec((err, result) => {
          if (err) {
            console.log(err);
          } else {
            console.log(result);
          }
        });
    }
  });

  /* -------------------------------
  Discount coupon calculation starts
  ------------------------------- */
  let isCouponApplied: boolean = false;
  if (userObj.coupon.isApplied) isCouponApplied = userObj.coupon.isApplied;
  let couponName: string = "";
  let couponDeductionType: string = "";
  let couponDeductionValue: number = 0;
  let couponTicketSubTypes: any;
  let percentageDeductionValue: number = 0;
  let deductedAmount: number = 0;
  let oldCartTotal: number = 0;
  let discountStatement: string = "";
  let couponAccess: string = "";
  oldCartTotal = cartTotal;

  if (isCouponApplied) {
    let couponObj: any = await couponModel.findOne({
      id: userObj.coupon.id,
    });
    couponName = couponObj.name;
    couponDeductionType = couponObj.deduction.type;
    couponDeductionValue = couponObj.deduction.value;
    couponTicketSubTypes = couponObj.deduction.ticketSubTypes;
    couponAccess = couponObj.meta.access;

    if (couponDeductionType === "fixed") {
      cartTotal = cartTotal - couponDeductionValue;
      deductedAmount = couponDeductionValue;
      discountStatement = `₹${couponDeductionValue} off on above cart total`;
    } else if (couponDeductionType === "percentage") {
      percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
      cartTotal = cartTotal - percentageDeductionValue;
      deductedAmount = percentageDeductionValue;
      discountStatement = `${couponDeductionValue}% of above cart total`;
    } else if (couponDeductionType === "ticketType") {
      let subTypesArr: any = [];
      couponTicketSubTypes.map((ticket: any) => {
        subTypesArr.push(ticket.subType);
      });
      let ticketDetailsArr = await ticketModel
        .find()
        .where("subType")
        .in(subTypesArr)
        .exec();
      let finalProcessingArr: any = [];
      cartItemDetails.map((cartItemDetail: any) => {
        ticketDetailsArr.map((ticketDetail: any) => {
          if (cartItemDetail.subType === ticketDetail.subType) {
            finalProcessingArr.push(cartItemDetail);
          }
        });
      });
      let ticketDeductionValue: number = 0;
      finalProcessingArr.map((ticket: any) => {
        let price = 0;
        let priceArr = ticket.price;
        let subType = ticket.subType;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let nowDate = moment().toISOString();
          let isBetween = moment(nowDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (subType === "course") {
              if (persona === item.name.toLowerCase()) {
                price = item.price.inr;
              }
            } else {
              price = item.price.inr;
            }
          }
          if (price > ticketDeductionValue) {
            ticketDeductionValue = price;
          }
        });
      });
      cartTotal = cartTotal - ticketDeductionValue;
      deductedAmount = ticketDeductionValue;
      discountStatement = `${couponName}`;
    }
  }

  /* -----------------------------
  Discount coupon calculation ends
  ----------------------------- */

  let paymentGateway = "Razorpay";
  let gatewayPerc = 0;
  let grandTotal = 0;
  let gatewayFee = 0;
  if (paymentGateway === "Razorpay") {
    gatewayPerc = 4;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "BankTransfer") {
    gatewayPerc = 0;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else if (paymentGateway === "Instamojo") {
    gatewayPerc = 3;
    gatewayFee = (gatewayPerc / 100) * cartTotal;
    grandTotal = cartTotal + gatewayFee;
  } else {
    return res.json({ status: "ok" });
  }

  let cartSummaryWithDiscount = [
    {
      field1: "Cart Total (before discount)",
      field2: "₹",
      field3: `${oldCartTotal}`,
    },
    {
      field1: `Discount (${discountStatement})`,
      field2: "₹",
      field3: `${deductedAmount}`,
    },
    {
      field1: "New Cart Total (after discount)",
      field2: "₹",
      field3: `${cartTotal}`,
    },
    {
      field1: "Gateway Fee",
      field2: "₹",
      field3: `${gatewayFee}`,
    },
    {
      field1: "Grand Total",
      field2: "₹",
      field3: `${grandTotal}`,
    },
  ];

  let cartSummaryWithoutDiscount = [
    {
      field1: "Cart Total",
      field2: "₹",
      field3: `${cartTotal}`,
    },
    {
      field1: "Gateway Fee",
      field2: "₹",
      field3: `${gatewayFee}`,
    },
    {
      field1: "Grand Total",
      field2: "₹",
      field3: `${grandTotal}`,
    },
  ];

  let cartSummary: any;

  if (isCouponApplied) {
    cartSummary = cartSummaryWithDiscount;
  } else {
    cartSummary = cartSummaryWithoutDiscount;
  }

  let paymentDetailsArr = [
    {
      field1: "PaymentID",
      field2: "",
      field3: `${rzpPaymentID}`,
    },
    {
      field1: "Payment Method",
      field2: "",
      field3: "Razorpay",
    },
  ];

  let couponDetailsObj: any = {};

  if (isCouponApplied) {
    couponDetailsObj.id = userObj.coupon.id;
    couponDetailsObj.name = couponName;
    couponDetailsObj.deductionType = couponDeductionType;
    couponDetailsObj.deductionValue = couponDeductionValue;
    couponDetailsObj.cartAmountBeforeDeduction = oldCartTotal;
    couponDetailsObj.deductedAmount = deductedAmount;
    couponDetailsObj.cartAmountAfterDeduction = cartTotal;

    if (couponAccess === "open") {
      let redeemerId = await uuidv4();
      let redeemerObj = {
        redeemerId: redeemerId,
        name: {
          first: issuer.name.first,
          last: issuer.name.last,
        },
        email: issuer.email,
        isUsed: true,
        usedOn: moment().toISOString(),
      };
      couponModel.findOneAndUpdate(
        { id: userObj.coupon.id },
        { $push: { redeemerList: redeemerObj } },
        function (error, success) {
          if (error) {
            console.log("Could not save open coupon redeemer");
          } else {
            console.log("Saved open coupon redeemer");
          }
        }
      );
    } else if (couponAccess === "emaillist") {
      couponModel.findOneAndUpdate(
        { id: userObj.coupon.id, "redeemerList.email": issuer.email },
        {
          "redeemerList.$.name.first": issuer.name.first,
          "redeemerList.$.name.last": issuer.name.last,
          "redeemerList.$.isUsed": true,
          "redeemerList.$.usedOn": moment().toISOString(),
        },
        function (error, success) {
          if (error) {
            console.log("Could not save emaillist coupon redeemers");
          } else {
            console.log("Saved emaillist coupon redeemers");
          }
        }
      );
    }
  }

  userModel.findOneAndUpdate(
    { _userID: _userID },
    {
      $push: {
        purchasedItems: {
          purchaseID: purchaseID,
          transactionID: rzpPaymentID,
          paymentGateway: paymentGateway,
          isCouponApplied: isCouponApplied,
          coupon: couponDetailsObj,
          cartTotal: cartTotal,
          gatewayFee: gatewayFee,
          grandTotal: grandTotal,
          purchasedItems: userObj?.cart,
          paymentStatus: "purchased",
        },
      },
    },
    async function (error, success) {
      if (error) {
        return res.json({ status: "ok" });
      } else {
        await sendRazorpayConfirmationMail(
          userObj?.profile.name.first,
          userObj?.profile.email,
          isCouponApplied,
          couponDetailsObj,
          cartItems,
          cartSummary,
          paymentDetailsArr
        ).then((isConfirmationSent) => {
          if (isConfirmationSent)
            console.log("Razorpay confirmation mail sent!");
          else
            return res.status(400).json({
              status: "Failed",
              msg: "Unable to send verification email",
            });
        });
        userModel.update(
          { _userID: _userID },
          { $set: { "coupon.isApplied": false, "coupon.id": "", cart: [] } },
          async (err, affected) => {
            if (err) {
              return res.json({ status: "ok" });
            } else if (affected) {
              let year: number = moment().year();
              year = year % 100;
              let allMember: any = await memberModel.findOne(
                {},
                async (err, members: any) => {
                  if (err) {
                    return res.json({ status: "ok" });
                  }
                  if (members.year != year) {
                    let filter = {
                      _id: members._id,
                    };
                    let updateYear = {
                      year: year,
                    };
                    await memberModel.findOneAndUpdate(filter, updateYear);
                  }
                }
              );
              let annualMemberCount: number = 0;
              annualMemberCount = allMember?.annualMemberCount;
              let lifetimeMemberCount: number = 0;
              lifetimeMemberCount = allMember?.lifetimeMemberCount;

              /*---------------------
              Membership is Purchased
              ---------------------*/
              if (isMembershipPurchased) {
                if (membershipType === "annual-membership") {
                  let newMemberCount = annualMemberCount + 1;
                  let newMemberCountSize = -3;
                  let memberIDNumberWithPadding = `000000000${newMemberCount}`;
                  let memberIDNumberComponent =
                    memberIDNumberWithPadding.slice(newMemberCountSize);
                  let memberID = `A${year}${memberIDNumberComponent}`;
                  let startDate = moment().toISOString();
                  let endDate = moment(startDate).add(1, "year").toISOString();
                  let userFilter = {
                    _userID: _userID,
                  };
                  let updateObj = {
                    "membership.isMember": true,
                    "membership.id": memberID,
                    "membership.type": "annual",
                    "membership.startDate": startDate,
                    "membership.endDate": endDate,
                    "membership.paymentMethod": paymentGateway,
                    "membership.amountPaid": -1,
                    "membership.txCode": rzpPaymentID,
                    "membership.txTimestamp": startDate,
                    "membership.remarks": "",
                    "membership.isInCart": false,
                  };
                  await userModel.findOneAndUpdate(userFilter, updateObj);
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { annualMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        return res.json({ status: "ok" });
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateAnnualMemberCount = {
                          annualMemberCount: newMemberCount,
                        };
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateAnnualMemberCount
                        );
                      }
                    }
                  );
                } else if (membershipType === "lifetime-membership") {
                  let newMemberCount = lifetimeMemberCount + 1;
                  let newMemberCountSize = -3;
                  let memberIDNumberWithPadding = `000000000${newMemberCount}`;
                  let memberIDNumberComponent =
                    memberIDNumberWithPadding.slice(newMemberCountSize);

                  let memberID = `L${year}${memberIDNumberComponent}`;
                  let startDate = moment().toISOString();
                  let endDate = "";
                  let userFilter = {
                    _userID: _userID,
                  };
                  let updateObj = {
                    "membership.isMember": true,
                    "membership.id": memberID,
                    "membership.type": "lifetime",
                    "membership.startDate": startDate,
                    "membership.endDate": endDate,
                    "membership.paymentMethod": paymentGateway,
                    "membership.amountPaid": -1,
                    "membership.txCode": rzpPaymentID,
                    "membership.txTimestamp": startDate,
                    "membership.remarks": "",
                    "membership.isInCart": false,
                  };
                  await userModel.findOneAndUpdate(userFilter, updateObj);
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { lifetimeMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        return res.json({ status: "ok" });
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateLifetimeMemberCount = {
                          lifetimeMemberCount: newMemberCount,
                        };
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateLifetimeMemberCount
                        );
                      }
                    }
                  );
                }
              }

              /*----------------
              MemberID submitted
              ----------------*/
              if (userObj?.membership.submittedID.length > 0) {
                let legacyMemberObj: any = await legacyMemberModel.findOne({
                  memberID: userObj?.membership.submittedID,
                });
                let membershipType: string = "";
                let newMemberCount: number = 0;
                let pushObj: any;
                let updateMemberCountObj: any;
                let amountPaid: number = 0;
                if (legacyMemberObj?.type === "annual") {
                  membershipType = "annual";
                  newMemberCount = annualMemberCount + 1;
                  updateMemberCountObj = {
                    annualMemberCount: newMemberCount,
                  };
                } else if (legacyMemberObj?.type === "lifetime") {
                  membershipType = "lifetime";
                  newMemberCount = lifetimeMemberCount + 1;
                  updateMemberCountObj = {
                    lifetimeMemberCount: newMemberCount,
                  };
                }
                if (legacyMemberObj?.fee) {
                  amountPaid = parseInt(legacyMemberObj?.fee);
                } else {
                  amountPaid = -1;
                }
                let userFilter = {
                  _userID: _userID,
                };
                let updateObj = {
                  "membership.isMember": true,
                  "membership.id": legacyMemberObj?.memberID,
                  "membership.type": membershipType,
                  "membership.startDate": legacyMemberObj?.from,
                  "membership.endDate": legacyMemberObj?.to,
                  "membership.paymentMethod": legacyMemberObj?.paymentMethod,
                  "membership.amountPaid": amountPaid,
                  "membership.txCode": legacyMemberObj?.txCode,
                  "membership.txTimestamp": legacyMemberObj?.txTimestamp,
                  "membership.remarks": legacyMemberObj?.remarks,
                  "membership.isInCart": false,
                  "membership.submittedID": "",
                };
                await userModel.findOneAndUpdate(userFilter, updateObj);

                if (membershipType === "annual") {
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { annualMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        return res.json({ status: "ok" });
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateMemberCount = updateMemberCountObj;
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateMemberCount
                        );
                      }
                    }
                  );
                } else if (membershipType === "lifetime") {
                  memberModel.findOneAndUpdate(
                    { _id: allMember?._id },
                    { $push: { lifetimeMembers: { _userID: _userID } } },
                    async (error, success) => {
                      if (error) {
                        return res.json({ status: "ok" });
                      } else if (success) {
                        let filter = {
                          _id: allMember?._id,
                        };
                        let updateMemberCount = updateMemberCountObj;
                        await memberModel.findOneAndUpdate(
                          filter,
                          updateMemberCount
                        );
                      }
                    }
                  );
                }
              }
              return res.json({ status: "ok" });
            } else {
              return res.json({ status: "ok" });
            }
          }
        );
      }
    }
  );
});

export default router;
