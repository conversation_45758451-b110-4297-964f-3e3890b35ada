import { Router } from "express";
import { loginHelper } from "../../../helpers";
import { userModel } from "../../../models";
import jwt from "jsonwebtoken";
const router = Router();

router.post("/getgoogleprofile", async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  let { googleJwt } = req.body;
  let decodedJwt: any = jwt.decode(googleJwt);
  let email: string = decodedJwt.email;
  let isEmailVerified: boolean = decodedJwt.email_verified;
  let dpUrl: string = decodedJwt.picture;
  let firstName: string = decodedJwt.given_name;
  let lastName: string = decodedJwt.family_name;

  if (!email || !isEmailVerified || !dpUrl || !firstName || !lastName) {
    let resp = {
      status: "Failed",
      msg: "We were not able to obtain profile details",
    };
    return res.json(resp);
  }

  let isUserExists: boolean = false;
  let isGoogleConnected: boolean = false;
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  if (userObj) {
    isUserExists = true;
    if (userObj.meta.oauth.isGoogleConnected) {
      isGoogleConnected = true;
      loginHelper(req, res, userObj!._userID);
    }
  }

  let resp = {
    status: "Success",
    msg: "Fetched google profile",
    payload: {
      firstName: firstName,
      lastName: lastName,
      email: email,
      isEmailVerified: isEmailVerified,
      dpUrl: dpUrl,
      isUserExists: isUserExists,
      isGoogleConnected: isGoogleConnected,
    },
  };
  res.json(resp);
});

export default router;
