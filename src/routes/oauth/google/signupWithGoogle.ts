import { Router } from "express";
import { loginHelper } from "../../../helpers";
import { userModel } from "../../../models";
import { validateOauthSignupInputs } from "../../../validation";
import { isUserLogged } from "../../../middleware";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post("/signup-with-google", isUserLogged, async (req, res) => {
  /* --- isUserLoggedIn --- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* --- isRegistrationInputsValid --- */
  let { error } = validateOauthSignupInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.json(resp);
  }

  /* --- extractRegistrationInputs --- */
  let {
    dpUrl,
    email,
    firstName,
    isdCode,
    lastName,
    mobileCountry,
    mobileNumber,
    occupation,
    password,
  } = req.body;

  /* --- check if user already exists --- */
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  if (userObj) {
    let resp = {
      status: "Failed",
      msg: "User alrady exists",
    };
    return res.json(resp);
  }

  /* --- generateEmailSignature --- */
  email = email.toLowerCase().trim();

  /* --- generateIDforUserAndCheckpoint --- */
  let _userID: string = await uuidv4();

  /* --- Create New User --- */
  const newUser: any = await userModel
    .create({
      _userID: _userID,
      meta: {
        account: { isSetupComplete: false },
        email: { isVerified: true, verificationCode: "" },
        oauth: {
          isLinkedInConnected: false,
          isGoogleConnected: true,
        },
      },
      profile: {
        name: { first: firstName, last: lastName },
        email: email,
        psswd: "",
        mobile: {
          isdCode: isdCode,
          country: mobileCountry,
          number: mobileNumber,
        },
        country: "",
        dpUrl: dpUrl,
      },
      professional: {
        occupation: occupation,
        orgInsti: "",
        jobDegree: "",
      },
      membership: {
        isMember: false,
        id: "",
        type: "",
        startDate: "",
        endDate: "",
        isInCart: false,
        memberFromRegistration: false,
        submittedID: "",
      },
      settings: {
        isRememberMe: true,
        isRegistrationManager: false,
        isAdmin: false,
        currency: { name: "inr", symbol: "₹" },
        billing: {
          tax: {
            india: {
              gst: {
                isGSTInvoicePreferred: false,
                businessName: "",
                taxID: "",
                taxJurisdiction: "",
                address: {
                  billing: {
                    line1: "",
                    line2: "",
                    line3: "",
                  },
                },
              },
            },
          },
        },
      },
      signature: "",
    })
    .catch((error) => {
      let resp = {
        status: "Failed",
        msg: "An error occured while creating the user",
      };
      console.log(error);
      res.status(200).json(resp);
    });

  if (!newUser._id) {
    let resp = {
      status: "Failed",
      msg: "Account creation failed!",
    };
    res.status(200).json(resp);
  }

  loginHelper(req, res, newUser!._userID);

  let resp = {
    status: "Success",
    msg: "Account created & user logged in",
  };
  res.json(resp);
});

export default router;
