import { Router } from "express";
import { loginHelper } from "../../../helpers";
import { userModel } from "../../../models";
import { APP_OPTIONS } from "../../../config";

import axios from "axios";
import qs from "qs";

const router = Router();

router.post("/getlinkedinprofile", async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  let { authCode } = req.body;
  let redirectUri: string = "https://account.indiahci.org/post-linkedin-oauth";

  if (APP_OPTIONS.env === "prod") {
    redirectUri = "https://account.indiahci.org/post-linkedin-oauth";
  } else if (APP_OPTIONS.env === "dev") {
    redirectUri = "http://localhost:3333/post-linkedin-oauth";
  }

  let isAccessTokenFetched: boolean = false;
  let accessToken: string = "";
  let data = qs.stringify({
    grant_type: "authorization_code",
    code: authCode,
    client_id: "772s5g83qpkuqn",
    client_secret: "x51LA8VE68XtmNnx",
    redirect_uri: redirectUri,
  });

  await axios({
    method: "POST",
    url: `https://www.linkedin.com/oauth/v2/accessToken`,
    data: data,
    responseType: "json",
    headers: {
      "content-type": "application/x-www-form-urlencoded",
    },
  })
    .then((response) => {
      accessToken = response.data.access_token;
      isAccessTokenFetched = true;
    })
    .catch((error) => {
      console.log(error);
    });

  if (!isAccessTokenFetched) {
    let resp = {
      status: "Failed",
      msg: "Error in fetching access token",
    };
    return res.json(resp);
  }

  let isPersonalDetailsFetched: boolean = false;
  let firstName: string = "";
  let lastName: string = "";
  let dpUrl: string = "";

  await axios({
    method: "GET",
    url: `https://api.linkedin.com/v2/me?projection=(firstName,lastName,profilePicture(displayImage~:playableStreams))`,
    responseType: "json",
    headers: { Authorization: `Bearer ${accessToken}` },
  })
    .then((response) => {
      firstName = response.data.firstName.localized.en_US;
      lastName = response.data.lastName.localized.en_US;
      // dpUrl =
      //   response.data.profilePicture["displayImage~"].elements[2].identifiers[0]
      //     .identifier;
      dpUrl = "-";
      isPersonalDetailsFetched = true;
    })
    .catch((error) => {
      console.log(error);
    });

  if (!isPersonalDetailsFetched) {
    let resp = {
      status: "Failed",
      msg: "Error in fetching profile data",
    };
    return res.json(resp);
  }

  let isEmailFetched: boolean = false;
  let email: string = "";

  await axios({
    method: "GET",
    url: `https://api.linkedin.com/v2/clientAwareMemberHandles?q=members&projection=(elements*(primary,type,handle~))`,
    responseType: "json",
    headers: { Authorization: `Bearer ${accessToken}` },
  })
    .then((response) => {
      let elementsArr = response.data.elements;
      let primaryElementObj: any;
      elementsArr.map((element: any) => {
        if (element.primary === true) {
          primaryElementObj = element;
        }
      });
      email = primaryElementObj["handle~"].emailAddress;
      isEmailFetched = true;
    })
    .catch((error) => {
      console.log(error);
    });

  if (!isEmailFetched) {
    let resp = {
      status: "Failed",
      msg: "Error in fetching Email",
    };
    return res.json(resp);
  }

  let isUserExists: boolean = false;
  let isLinkedInConnected: boolean = false;
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  if (userObj) {
    isUserExists = true;
    if (userObj.meta.oauth.isLinkedInConnected) {
      isLinkedInConnected = true;
      loginHelper(req, res, userObj!._userID);
    }
  }

  let resp = {
    status: "Success",
    msg: "Fetched profile info",
    payload: {
      firstName: firstName,
      lastName: lastName,
      email: email,
      dpUrl: dpUrl,
      isUserExists: isUserExists,
      isLinkedInConnected: isLinkedInConnected,
    },
  };
  res.json(resp);
});

export default router;
