// @ts-nocheck

import { Router } from "express";
import { login<PERSON>elper } from "../../../helpers";
import { userModel } from "../../../models";
import { validateLinkAccountWithOauth } from "../../../validation";
import { isUserLogged } from "../../../middleware";

const router = Router();

router.post("/link-account-with-linkedin", isUserLogged, async (req, res) => {
  /* --- isUserLoggedIn --- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* --- isRegistrationInputsValid --- */
  let { error } = validateLinkAccountWithOauth(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.json(resp);
  }

  /* --- extractRegistrationInputs --- */
  let { dpUrl, email } = req.body;

  /* --- check if user already exists --- */
  email = email.toLowerCase().trim();
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User doesn't exists",
    };
    return res.json(resp);
  }

  /* Updates account linking details */
  let filter = {
    _userID: userObj._userID,
  };
  let update = {
    "profile.dpUrl": dpUrl,
    "meta.oauth.isLinkedInConnected": true,
  };

  await userModel.findOneAndUpdate(filter, update, (error, success) => {
    if (error) {
      let resp = {
        status: "Failed",
        msg: "An error occured while updating the user",
      };
      return res.json(resp);
    }
    if (success) {
      loginHelper(req, res, userObj!._userID);

      let resp = {
        status: "Success",
        msg: "Account linked",
      };
      res.json(resp);
    }
  });
});

export default router;
