import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel, alertModel } from "../../models";
import moment from "moment";

const router = Router();

router.get("/trackoverview", isUserLogged, getUserID, async (req, res) => {
  //   if (!res.locals.isUserLogged) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "You are not logged in",
  //     };
  //     return res.status(400).json(resp);
  //   }

  //   const _userID = res.locals.userID;
  //   const userObj: any = await userModel.findOne({
  //     _userID,
  //   });

  //   if (!userObj) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "User not found",
  //     };
  //     return res.json(resp);
  //   }

  //   if (!userObj.settings.isAdmin) {
  //     if (!userObj.settings.isRegistrationManager) {
  //       let resp = {
  //         status: "Failed",
  //         msg: "Access Denied",
  //       };
  //       return res.json(resp);
  //     }
  //   }

  let users: any;
  users = await userModel
    .find({}, "professional membership purchasedItems")
    .exec();
  let tickets = await ticketModel
    .find({}, "ticketID type subType title subTitle price")
    .exec();

  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let totalRegistrantCount = 0;
  let studentCount = 0;
  let professionalCount = 0;
  let memberCount = 0;
  let nonMemberCount = 0;
  let registrantsWithPurchases = 0;
  let registrantsWithoutPurchases = 0;

  let totalPurchaseCount = 0;
  let fullConferenceCount = 0;
  let fullConferenceSales = 0;
  let fullConferenceNonMemberCount = 0;
  let fullConferenceNonMemberSales = 0;
  let fullConferenceStudentCount = 0;
  let fullConferenceStudentSales = 0;
  let fullConferenceMemberCount = 0;
  let fullConferenceMemberSales = 0;
  let membershipCount = 0;
  let membershipSales = 0;
  let lifetimeMembershipCount = 0;
  let lifetimeMembershipSales = 0;
  let annualMembershipCount = 0;
  let annualMembershipSales = 0;
  let totalSales = 0;
  let totalConfirmedSales = 0;
  let underVerificationSales = 0;
  let totalWorkshopCount = 0;
  let totalWorkshopSales = 0;
  let totalCourseCount = 0;
  let totalCourseSales = 0;
  let purchasedItemIDs: any = [];
  let unitWorkshopSale: any = [];
  let unitCourseSale: any = [];
  let razorpaySales = 0;
  let bankTransferSales = 0;
  let bankTransferCount = 0;
  let partialConferenceCount = 0;
  let partialConferenceSales = 0;
  let partialConferenceSingleDayCount = 0;
  let partialConferenceSingleDaySales = 0;
  let partialConferenceDoubleDayCount = 0;
  let partialConferenceDoubleDaySales = 0;
  let guestTicketCount = 0;
  let guestTicketSales = 0;
  let sponsor100Count = 0;
  let sponsor100Sales = 0;
  let sponsor50Count = 0;
  let sponsor50Sales = 0;
  let others50Sales = 0;
  let others50Count = 0;

  users.forEach((user: any) => {
    let totalPurchasedItems = 0;
    if (user.purchasedItems.length > 0) {
      user.purchasedItems.forEach((purchasedItemObj: any) => {
        totalPurchasedItems =
          totalPurchasedItems + purchasedItemObj.purchasedItems.length;
        totalPurchaseCount = totalPurchaseCount + totalPurchasedItems;
        purchasedItemObj.purchasedItems.forEach((purchaseTicketObj: any) => {
          purchasedItemIDs.push(purchaseTicketObj.ticketID);
        });
      });
      registrantsWithPurchases = registrantsWithPurchases + 1;
    } else {
      registrantsWithoutPurchases = registrantsWithoutPurchases + 1;
    }

    if (user.professional.occupation === "student") {
      studentCount = studentCount + 1;
    } else if (user.professional.occupation === "professional") {
      professionalCount = professionalCount + 1;
    }
    if (user.membership.isMember) {
      memberCount = memberCount + 1;
    } else {
      nonMemberCount = nonMemberCount + 1;
    }
    totalRegistrantCount = totalRegistrantCount + 1;
  });

  purchasedItemIDs.forEach((id: string) => {
    tickets.forEach((ticket: any) => {
      if (id === ticket.ticketID) {
        if (ticket.type === "full-conference") {
          if (ticket.subType === "full-conference-hcipai-professional") {
            fullConferenceMemberCount = fullConferenceMemberCount + 1;
          } else if (ticket.subType === "full-conference-professional") {
            fullConferenceNonMemberCount = fullConferenceNonMemberCount + 1;
          } else if (ticket.subType === "full-conference-student") {
            fullConferenceStudentCount = fullConferenceStudentCount + 1;
          }
          fullConferenceCount = fullConferenceCount + 1;
        } else if (ticket.type === "membership") {
          if (ticket.subType === "lifetime-membership") {
            lifetimeMembershipCount = lifetimeMembershipCount + 1;
          } else if (ticket.subType === "annual-membership") {
            annualMembershipCount = annualMembershipCount + 1;
          }
          membershipCount = membershipCount + 1;
        } else if (ticket.type === "track") {
          if (ticket.subType === "workshop") {
            totalWorkshopCount = totalWorkshopCount + 1;
          } else if (ticket.subType === "course") {
            totalCourseCount = totalCourseCount + 1;
          }
        } else if (ticket.type === "single-selector") {
          if (ticket.subTitle === "7th Nov 2020") {
            partialConferenceSingleDayCount =
              partialConferenceSingleDayCount + 1;
          } else if (ticket.subTitle === "7th & 8th Nov 2020") {
            partialConferenceDoubleDayCount =
              partialConferenceDoubleDayCount + 1;
          }
          partialConferenceCount = partialConferenceCount + 1;
        } else if (ticket.type === "full-conference-discounted") {
          guestTicketCount = guestTicketCount + 1;
        } else if (ticket.type === "full-conference-discounted-sponsor") {
          if (ticket.subType === "full-conference-discounted-sponsor-100") {
            sponsor100Count = sponsor100Count + 1;
          } else if (
            ticket.subType === "full-conference-discounted-sponsor-50"
          ) {
            sponsor50Count = sponsor50Count + 1;
          }
        } else if (ticket.type === "full-conference-discounted-student") {
          others50Count = others50Count + 1;
        }
      }
    });
  });

  let purchasesArrRaw: any = [];
  users.forEach((user: any) => {
    if (user.purchasedItems.length > 0) {
      let allPurchases = user.purchasedItems;
      allPurchases.forEach((unitPurchase: any) => {
        unitPurchase.purchasedItems.forEach((ticket: any) => {
          let obj = {
            ticket: ticket.ticketID,
            persona: user.professional.occupation,
            paymentStatus: unitPurchase.paymentStatus,
            paymentGateway: unitPurchase.paymentGateway,
            createdAt: unitPurchase.createdAt,
          };
          purchasesArrRaw.push(obj);
        });
      });
    }
  });

  tickets.forEach((ticket: any) => {
    let obj = {
      name: ticket.title,
      sold: 0,
      total: 0,
    };
    if (ticket.subType === "course") {
      unitCourseSale.push(obj);
    } else if (ticket.subType === "workshop") {
      unitWorkshopSale.push(obj);
    }
  });

  purchasesArrRaw.forEach((purchase: any) => {
    let ticketPrice = 0;
    tickets.forEach((ticket: any) => {
      if (purchase.ticket === ticket.ticketID) {
        let priceArr = ticket.price;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let purchaseDate = purchase.createdAt;
          let isBetween = moment(purchaseDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (ticket.subType === "course") {
              if (purchase.persona === item.name.toLowerCase()) {
                ticketPrice = parseInt(item.price.inr);
              }
            } else {
              ticketPrice = parseInt(item.price.inr);
            }
            if (purchase.paymentGateway != "BankTransfer") {
              if (ticket.subType === "course") {
                if (purchase.persona === item.name.toLowerCase()) {
                  ticketPrice = ticketPrice + 0.04 * ticketPrice;
                }
              } else {
                ticketPrice = ticketPrice + 0.04 * ticketPrice;
              }
            }
          }
        });

        if (ticket.type === "full-conference") {
          if (ticket.subType === "full-conference-hcipai-professional") {
            fullConferenceMemberSales = fullConferenceMemberSales + ticketPrice;
          } else if (ticket.subType === "full-conference-professional") {
            fullConferenceNonMemberSales =
              fullConferenceNonMemberSales + ticketPrice;
          } else if (ticket.subType === "full-conference-student") {
            fullConferenceStudentSales =
              fullConferenceStudentSales + ticketPrice;
          }
          fullConferenceSales = fullConferenceSales + ticketPrice;
        } else if (ticket.type === "membership") {
          if (ticket.subType === "lifetime-membership") {
            lifetimeMembershipSales = lifetimeMembershipSales + ticketPrice;
          } else if (ticket.subType === "annual-membership") {
            annualMembershipSales = annualMembershipSales + ticketPrice;
          }
          membershipSales = membershipSales + ticketPrice;
        } else if (ticket.type === "track") {
          if (ticket.subType === "course") {
            totalCourseSales = totalCourseSales + ticketPrice;
          } else if (ticket.subType === "workshop") {
            totalWorkshopSales = totalWorkshopSales + ticketPrice;
          }
        } else if (ticket.type === "single-selector") {
          if (ticket.subTitle === "7th Nov 2020") {
            partialConferenceSingleDaySales =
              partialConferenceSingleDaySales + ticketPrice;
          } else if (ticket.subTitle === "7th & 8th Nov 2020") {
            partialConferenceDoubleDaySales =
              partialConferenceDoubleDaySales + ticketPrice;
          }
          partialConferenceSales = partialConferenceSales + ticketPrice;
        } else if (ticket.type === "full-conference-discounted") {
          guestTicketSales = guestTicketSales + ticketPrice;
          fullConferenceSales = fullConferenceSales + ticketPrice;
        } else if (ticket.type === "full-conference-discounted-sponsor") {
          if (ticket.subType === "full-conference-discounted-sponsor-100") {
            sponsor100Sales = sponsor100Sales + ticketPrice;
          } else if (
            ticket.subType === "full-conference-discounted-sponsor-50"
          ) {
            sponsor50Sales = sponsor50Sales + ticketPrice;
          }
          fullConferenceSales = fullConferenceSales + ticketPrice;
        } else if (ticket.type === "full-conference-discounted-student") {
          others50Sales = others50Sales + ticketPrice;
          fullConferenceSales = fullConferenceSales + ticketPrice;
        }

        if (ticket.subType === "course") {
          unitCourseSale.forEach((course: any) => {
            if (ticket.title === course.name) {
              course.sold = course.sold + 1;
              course.total = course.total + ticketPrice;
            }
          });
        } else if (ticket.subType === "workshop") {
          unitWorkshopSale.forEach((workshop: any) => {
            if (ticket.title === workshop.name) {
              workshop.sold = workshop.sold + 1;
              workshop.total = workshop.total + ticketPrice;
            }
          });
        }

        if (purchase.paymentStatus === "purchased") {
          totalConfirmedSales = totalConfirmedSales + ticketPrice;
        } else if (purchase.paymentStatus === "under-verification") {
          underVerificationSales = underVerificationSales + ticketPrice;
        }

        if (purchase.paymentGateway === "BankTransfer") {
          bankTransferCount = bankTransferCount + 1;
          bankTransferSales = bankTransferSales + ticketPrice;
        } else if (purchase.paymentGateway === "Razorpay") {
          razorpaySales = razorpaySales + ticketPrice;
        }
        totalSales = totalSales + ticketPrice;
      }
    });
  });

  let alerts = await alertModel.find({ "status.isActive": true });

  let payload = {
    purchases: {
      workshops: {
        totalCount: totalWorkshopCount,
        totalSale: totalWorkshopSales,
        unitSale: unitWorkshopSale,
      },
      courses: {
        totalCount: totalCourseCount,
        totalSale: totalCourseSales,
        unitSale: unitCourseSale,
      },
    },
    notificationCount: alerts.length,
  };

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
