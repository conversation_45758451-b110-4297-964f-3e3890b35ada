import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { couponModel, userModel } from "../../models";
import { v4 as uuidv4 } from "uuid";
import { validateCouponInputs } from "../../validation";
import * as voucherCodeGenerator from "@luxuryescapes/lib-voucher-code";
import moment from "moment";

const router = Router();

router.post("/createcoupon", isUserLogged, getUserID, async (req, res) => {
  /* ------------
  Extract payload
  ------------ */
  let {
    couponName,
    couponDeductionType,
    couponFixedDeductionValue,
    couponPercentageDeductionValue,
    couponTicketsForDeduction,
    couponTicketDeductionLogic,
    couponTicketsDeductionValue,
    couponAccessType,
    eventCode,
    emailList,
  } = req.body;

  /* -------------
  Validate payload
  ------------- */
  let { error } = validateCouponInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(error);
    return res.send(resp);
  }

  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check is user rm/admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  /* --------------------
  Generate issuer details
  -------------------- */
  let issuerFirstName = userObj.profile.name.first;
  let issuerLastName = userObj.profile.name.last;
  let issuerEmail = userObj.profile.email;
  let issuedOn = moment().toISOString();

  let couponId = await uuidv4();
  let couponDeductionValue: number = 0;

  if (couponDeductionType === "fixed") {
    couponDeductionValue = couponFixedDeductionValue;
  } else if (couponDeductionType === "percentage") {
    couponDeductionValue = couponPercentageDeductionValue;
  }

  let subDeductionType: string = "";
  if (couponTicketDeductionLogic === "deductByPercentage") {
    subDeductionType = "percentage";
  } else if (couponTicketDeductionLogic === "deductByValue") {
    subDeductionType = "fixed";
  }

  if (couponAccessType === "open") {
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: 1,
    });
    let proposedCouponObj = {
      id: couponId,
      eventCode: eventCode,
      name: couponName,
      deduction: {
        type: couponDeductionType,
        value: couponDeductionValue,
        ticketTypes: {
          items: couponTicketsForDeduction,
          subDeductionType: subDeductionType,
          subDeductionValue: couponTicketsDeductionValue,
        },
      },
      redeemerList: [],
      meta: {
        access: couponAccessType,
        code: couponCodeArr[0],
        issuer: {
          name: { first: issuerFirstName, last: issuerLastName },
          email: issuerEmail,
        },
        issuedOn: issuedOn,
      },
    };
    const newCouponObj = await couponModel.create(proposedCouponObj);
    if (!newCouponObj._id) {
      let resp = {
        status: "Failed",
        msg: "Couldn't create coupon",
      };
      return res.status(400).json(resp);
    }
  } else if (couponAccessType === "emaillist") {
    /* -------------------------
    Initilize redeemer variables
    ------------------------- */
    let couponArrIndex = 0;
    let redeemerList: Array<object> = [];

    /* ---------------------------------
    Generate coupon codes for each email
    --------------------------------- */
    let couponCodeArr: any = await voucherCodeGenerator.generate({
      length: 8,
      count: emailList.length,
    });

    await emailList.map(async (email: string) => {
      /* ----------------
      Generate redeemerId
      ---------------- */
      let redeemerId = await uuidv4();

      /* ------------------
      Generate redeemer obj
      ------------------ */
      let proposedRedeemerObj = {
        redeemerId: redeemerId,
        name: { first: "", last: "" },
        email: email,
        code: couponCodeArr[couponArrIndex],
        isUsed: false,
        usedOn: "",
      };
      redeemerList.push(proposedRedeemerObj);
      couponArrIndex = couponArrIndex + 1;
    });

    let proposedCouponObj = {
      id: couponId,
      eventCode: eventCode,
      name: couponName,
      deduction: {
        type: couponDeductionType,
        value: couponDeductionValue,
        ticketTypes: {
          items: couponTicketsForDeduction,
          subDeductionType: subDeductionType,
          subDeductionValue: couponTicketsDeductionValue,
        },
      },
      redeemerList: redeemerList,
      meta: {
        eventCode: eventCode,
        access: couponAccessType,
        code: "",
        issuer: {
          name: { first: issuerFirstName, last: issuerLastName },
          email: issuerEmail,
        },
        issuedOn: issuedOn,
      },
    };

    const newCouponObj = await couponModel.create(proposedCouponObj);
    if (!newCouponObj._id) {
      let resp = {
        status: "Failed",
        msg: "Couldn't create coupon",
      };
      return res.status(400).json(resp);
    }
  }

  /* ------------------
  Send success response
  ------------------ */
  let resp = {
    status: "Success",
    msg: "Coupon created",
  };
  return res.status(200).json(resp);
});

export default router;
