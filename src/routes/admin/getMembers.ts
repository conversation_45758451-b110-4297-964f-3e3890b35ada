import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel, legacyMemberModel } from "../../models";
import moment from "moment";

const router = Router();

router.post("/getmembers", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let { memberType } = req.body;

  let users: any;
  users = await userModel.find({}, "profile membership").exec();
  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  const legacyMembers = await legacyMemberModel
    .find({}, "memberID email name type from to")
    .exec();
  if (!legacyMembers) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let allMemberArray: any = [];
  let annualMemberArray: any = [];
  let lifetimeMemberArray: any = [];

  users.forEach((user: any) => {
    if (user.membership.isMember === true) {
      let nowDate = moment();
      let expiresIn: any = "";
      let membershipEndDate: any = "";
      if (user.membership.type === "lifetime") {
        expiresIn = "-";
        membershipEndDate = "-";
      } else if (user.membership.type === "annual") {
        let expireDate = moment(user.membership.endDate);
        expiresIn = expireDate.diff(nowDate, "days");
        membershipEndDate = user.membership.endDate;
      }
      let obj = {
        name: `${user.profile.name.first} ${user.profile.name.last}`,
        email: user.profile.email,
        memberID: user.membership.id,
        membershipType: user.membership.type,
        membershipStartDate: user.membership.startDate,
        membershipEndDate: membershipEndDate,
        expiresIn: expiresIn,
        origin: "mainDB",
      };
      if (user.membership.type === "lifetime") {
        lifetimeMemberArray.push(obj);
      } else if (user.membership.type === "annual") {
        annualMemberArray.push(obj);
      }
      allMemberArray.push(obj);
    }
  });

  legacyMembers.forEach((legacyMember: any) => {
    if (legacyMember.type === "lifetime") {
      let obj = {
        name: legacyMember.name,
        email: legacyMember.email,
        memberID: legacyMember.memberID,
        membershipType: legacyMember.type,
        membershipStartDate: legacyMember.from,
        membershipEndDate: "-",
        expiresIn: "-",
        origin: "legacyDB",
      };
      lifetimeMemberArray.push(obj);
      allMemberArray.push(obj);
    }
  });

  allMemberArray.sort((a: any, b: any) => {
    let membershipStartDateA: any = new Date(a.membershipStartDate);
    let membershipStartDateB: any = new Date(b.membershipStartDate);
    return membershipStartDateB - membershipStartDateA;
  });

  annualMemberArray.sort((a: any, b: any) => {
    let membershipStartDateA: any = new Date(a.membershipStartDate);
    let membershipStartDateB: any = new Date(b.membershipStartDate);
    return membershipStartDateB - membershipStartDateA;
  });

  lifetimeMemberArray.sort((a: any, b: any) => {
    let membershipStartDateA: any = new Date(a.membershipStartDate);
    let membershipStartDateB: any = new Date(b.membershipStartDate);
    return membershipStartDateB - membershipStartDateA;
  });

  let uniqueValuesSet = new Set();
  let allMemberArrayFinal: any = allMemberArray.filter((obj: any) => {
    let isPresentInSet = uniqueValuesSet.has(obj.email);
    uniqueValuesSet.add(obj.email);
    return !isPresentInSet;
  });

  uniqueValuesSet = new Set();
  let annualMemberArrayFinal: any = annualMemberArray.filter((obj: any) => {
    let isPresentInSet = uniqueValuesSet.has(obj.email);
    uniqueValuesSet.add(obj.email);
    return !isPresentInSet;
  });

  uniqueValuesSet = new Set();
  let lifetimeMemberArrayFinal: any = lifetimeMemberArray.filter((obj: any) => {
    let isPresentInSet = uniqueValuesSet.has(obj.email);
    uniqueValuesSet.add(obj.email);
    return !isPresentInSet;
  });

  let payload: any;
  if (memberType === "annual") {
    payload = {
      memberCount: annualMemberArrayFinal.length,
      memberList: annualMemberArrayFinal,
      annualMemberCount: annualMemberArrayFinal.length,
      lifetimeMemberCount: lifetimeMemberArrayFinal.length,
    };
  } else if (memberType === "lifetime") {
    payload = {
      memberCount: lifetimeMemberArrayFinal.length,
      memberList: lifetimeMemberArrayFinal,
      annualMemberCount: annualMemberArrayFinal.length,
      lifetimeMemberCount: lifetimeMemberArrayFinal.length,
    };
  } else {
    payload = {
      memberCount: allMemberArrayFinal.length,
      memberList: allMemberArrayFinal,
      annualMemberCount: annualMemberArrayFinal.length,
      lifetimeMemberCount: lifetimeMemberArrayFinal.length,
    };
  }

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
