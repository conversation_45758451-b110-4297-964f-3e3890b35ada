import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel } from "../../models";
import moment from "moment";

const router = Router();

router.post("/purchases", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let userAndPurchases: any = await userModel
    .find({}, "profile purchasedItems professional settings")
    .exec();

  if (!userAndPurchases) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  let ticketsArr: any = await ticketModel
    .find({}, "ticketID type subType title subTitle price")
    .exec();
  let purchasesArrRaw: any = [];

  userAndPurchases.forEach((userPurchase: any) => {
    if (userPurchase.purchasedItems.length > 0) {
      let profile = userPurchase.profile;
      let persona = userPurchase.professional.occupation;

      let allPurchases = userPurchase.purchasedItems;
      let isBillingDetailsAvailable = true;
      if (
        userPurchase.settings.billing.tax.india.gst.isGSTInvoicePreferred ===
          undefined &&
        userPurchase.settings.billing.tax.india.gst.taxJurisdiction ===
          undefined
      ) {
        isBillingDetailsAvailable = false;
      }
      allPurchases.forEach((unitPurchase: any) => {
        unitPurchase.purchasedItems.forEach((ticket: any) => {
          let obj = {
            firstName: profile.name.first,
            lastName: profile.name.last,
            persona: persona,
            email: profile.email,
            ticket: ticket.ticketID,
            purchaseID: unitPurchase.purchaseID,
            transactionID: unitPurchase.transactionID,
            paymentGateway: unitPurchase.paymentGateway,
            paymentStatus: unitPurchase.paymentStatus,
            isBillingDetailsAvailable: isBillingDetailsAvailable,
            createdAt: unitPurchase.createdAt,
          };
          purchasesArrRaw.push(obj);
        });
      });
    }
  });

  purchasesArrRaw.sort((a: any, b: any) => {
    let AcreationDate: any = new Date(a.createdAt);
    let BcreationDate: any = new Date(b.createdAt);
    return BcreationDate - AcreationDate;
  });

  let { monthName } = req.body;
  let monthStartDate = moment().month(monthName).startOf("month").toISOString();
  let monthEndDate = moment().month(monthName).endOf("month").toISOString();
  let purchasesArrFinal: any = [];

  purchasesArrRaw.forEach((purchase: any) => {
    let ticketType = "";
    let ticketSubtype = "";
    let ticketTitle = "";
    let ticketSubTitle = "";
    let ticketPrice = 0;
    let ticketTier = "";
    let currency = "₹";
    ticketsArr.forEach((ticket: any) => {
      if (purchase.ticket === ticket.ticketID) {
        ticketType = ticket.type;
        ticketSubtype = ticket.subType;
        ticketTitle = ticket.title;
        ticketSubTitle = ticket.subTitle;
        let priceArr = ticket.price;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let purchaseDate = purchase.createdAt;
          let isBetween = moment(purchaseDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (ticketSubtype === "course") {
              if (purchase.persona === item.name.toLowerCase()) {
                ticketTier = item.name;
                ticketPrice = parseInt(item.price.inr);
              }
            } else {
              ticketTier = item.name;
              ticketPrice = parseInt(item.price.inr);
            }
            if (purchase.paymentGateway != "BankTransfer") {
              if (ticketSubtype === "course") {
                if (purchase.persona === item.name.toLowerCase()) {
                  ticketPrice = ticketPrice + 0.04 * ticketPrice;
                }
              } else {
                ticketPrice = ticketPrice + 0.04 * ticketPrice;
              }
            }
          }
        });
      }
    });

    let obj = {
      firstName: purchase.firstName,
      lastName: purchase.lastName,
      email: purchase.email,
      purchaseID: purchase.purchaseID,
      transactionID: purchase.transactionID,
      ticketType: ticketType,
      ticketSubtype: ticketSubtype,
      ticketTitle: ticketTitle,
      ticketSubTitle: ticketSubTitle,
      ticketPrice: `${currency}${ticketPrice}`,
      ticketTier: ticketTier,
      paymentGateway: purchase.paymentGateway,
      paymentStatus: purchase.paymentStatus,
      isBillingDetailsAvailable: purchase.isBillingDetailsAvailable,
      purchasedOn: purchase.createdAt,
    };

    if (monthName.length > 0) {
      let isPurchaseInBetween = moment(obj.purchasedOn).isBetween(
        monthStartDate,
        monthEndDate
      );
      if (isPurchaseInBetween) {
        purchasesArrFinal.push(obj);
      }
    } else {
      purchasesArrFinal.push(obj);
    }
  });

  let resp = {
    status: "Success",
    msg: "Purchases retrieved",
    payload: {
      total: purchasesArrFinal.length,
      purchases: purchasesArrFinal,
    },
  };
  return res.json(resp);
});

export default router;
