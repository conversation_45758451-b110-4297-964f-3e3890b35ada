import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { isGetRedeemerListInputValid } from "../../validation";
import { couponModel, userModel } from "../../models";

const router = Router();

router.post("/getredeemerlist", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check is user rm/admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  /* ------------
  Extract payload
  ------------ */
  let { couponId, redeemerUsage } = req.body;
  let { error } = isGetRedeemerListInputValid(req.body);

  if (error) {
    let resp = {
      status: "Failed",
      msg: "Invalid inputs to retrieve coupons",
    };
    return res.json(resp);
  }

  let redeemerList: any;
  let usedRedeemerList: Array<Object> = [];
  let unUsedRedeemerList: Array<Object> = [];
  let finalRedeemerList: any;

  let couponObj: any = await couponModel.findOne({ id: couponId });
  if (!couponObj) {
    let resp = {
      status: "Failed",
      msg: "Coupon not found",
    };
    return res.json(resp);
  }
  redeemerList = couponObj.redeemerList;

  redeemerList.map((redeemerObj: any) => {
    if (redeemerObj.isUsed) {
      usedRedeemerList.push(redeemerObj);
    } else {
      unUsedRedeemerList.push(redeemerObj);
    }
  });

  if (redeemerUsage === "used") {
    finalRedeemerList = usedRedeemerList;
  } else if (redeemerUsage === "notYetUsed") {
    finalRedeemerList = unUsedRedeemerList;
  } else {
    finalRedeemerList = redeemerList;
  }

  let resp = {
    status: "Success",
    msg: "Coupons retrieved",
    payload: {
      updatedRedeemerList: finalRedeemerList,
    },
  };
  return res.status(200).json(resp);
});

export default router;
