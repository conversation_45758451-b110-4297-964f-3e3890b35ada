import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
// import { isGetCouponsInputValid } from "../../validation";
import { couponModel, userModel } from "../../models";

const router = Router();

router.post("/getcoupons", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check is user rm/admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let { couponDeductionFilter, couponAccessFilter, eventCode } = req.body;
  // let { error } = isGetCouponsInputValid(req.body);
  // if (error) {
  //   let resp = {
  //     status: "Failed",
  //     msg: "Invalid inputs to retrieve coupons",
  //   };
  //   return res.json(resp);
  // }

  let coupons: any;
  if (!couponDeductionFilter && !couponAccessFilter) {
    coupons = await couponModel
      .find({ eventCode: eventCode })
      .sort({ "meta.issuedOn": -1 })
      .exec();
  } else if (couponDeductionFilter && couponAccessFilter) {
    coupons = await couponModel
      .find({
        eventCode: eventCode,
        "deduction.type": couponDeductionFilter,
        "meta.access": couponAccessFilter,
      })
      .sort({ "meta.issuedOn": -1 })
      .exec();
  } else if (couponDeductionFilter) {
    coupons = await couponModel
      .find({
        eventCode: eventCode,
        "deduction.type": couponDeductionFilter,
      })
      .sort({ "meta.issuedOn": -1 })
      .exec();
  } else if (couponAccessFilter) {
    coupons = await couponModel
      .find({
        eventCode: eventCode,
        "meta.access": couponAccessFilter,
      })
      .sort({ "meta.issuedOn": -1 })
      .exec();
  }

  let couponArr: Array<Object> = [];

  coupons.map((coupon: any) => {
    let issuedCount: number = 0;
    let usedCount: number = 0;
    let redeemerList: any = coupon.redeemerList;

    if (redeemerList.length > 0) issuedCount = redeemerList.length;

    redeemerList.map((redeemer: any) => {
      if (redeemer.isUsed === true) usedCount = usedCount + 1;
    });

    let newCouponObj = {
      id: coupon.id,
      name: coupon.name,
      deduction: coupon.deduction,
      meta: coupon.meta,
      redeemerList: coupon.redeemerList,
      issuedCount: issuedCount,
      usedCount: usedCount,
    };

    couponArr.push(newCouponObj);
  });

  let resp = {
    status: "Success",
    msg: "Coupons retrieved",
    payload: {
      totalCoupons: coupons.length,
      couponArr: couponArr,
    },
  };
  return res.status(200).json(resp);
});

export default router;
