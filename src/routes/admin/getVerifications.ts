import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";

const router = Router();

router.get("/getverifications", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let users: any;
  users = await userModel.find({}, "profile purchasedItems").exec();

  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  let verificationsRequiredArr: any = [];
  users.forEach((user: any) => {
    if (user.purchasedItems.length > 0) {
      user.purchasedItems.forEach((purchasedItemObj: any) => {
        if (
          purchasedItemObj.paymentGateway === "BankTransfer" &&
          purchasedItemObj.paymentStatus === "under-verification"
        ) {
          let obj = {
            firstName: user.profile.name.first,
            lastName: user.profile.name.last,
            email: user.profile.email,
            verificationType: "BankTransfer",
            orderID: purchasedItemObj.purchaseID,
            bankTxCode: purchasedItemObj.transactionID,
            transferredAmount: purchasedItemObj.grandTotal,
          };
          verificationsRequiredArr.push(obj);
        }
      });
    }
  });

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: verificationsRequiredArr,
  };
  return res.json(resp);
});

export default router;
