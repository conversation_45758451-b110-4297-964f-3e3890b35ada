import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/registrantswithoutpurchases",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let users: any;
    users = await userModel.find({}, "profile purchasedItems").exec();

    if (!users) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let registrantsWithoutPurchases: any = [];
    users.forEach((user: any) => {
      if (user.purchasedItems.length === 0) {
        let obj = {
          name: user.profile.name.first + " " + user.profile.name.last,
          email: user.profile.email,
        };
        registrantsWithoutPurchases.push(obj);
      }
    });

    const fields = [
      {
        label: "Name",
        value: "name",
      },
      {
        label: "Email",
        value: "email",
      },
    ];

    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(registrantsWithoutPurchases);
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=registrantsWithoutPurchases.csv`
    );
    return res.send(csv);
  }
);

export default router;
