// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { couponModel, userModel } from "../../models";
import { validateCouponCodeInputs } from "../../validation";

const router = Router();

router.post("/deletecouponcode", isUserLogged, getUserID, async (req, res) => {
  /* ------------
  Extract payload
  ------------ */
  let { couponId, emailForNewCouponCode } = req.body;

  /* -------------
  Validate payload
  ------------- */
  let { error } = validateCouponCodeInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(error);
    return res.send(resp);
  }

  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check is user rm/admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  /* -------------------------------
  Check if email is already enlisted
  ------------------------------- */
  let isEmailEnlisted: boolean = false;
  const couponObj: any = await couponModel.findOne({
    id: couponId,
  });

  if (!couponObj) {
    let resp = {
      status: "Failed",
      msg: "Coupon not found",
    };
    return res.json(resp);
  }
  const redeemerList: any = couponObj.redeemerList;
  redeemerList.map((redeemer: any) => {
    if (redeemer.email === emailForNewCouponCode) {
      isEmailEnlisted = true;
    }
  });

  if (!isEmailEnlisted) {
    let resp = {
      status: "Failed",
      msg: "Can't find the email",
    };
    return res.json(resp);
  }

  /* ------------
  Delete email id
  ------------ */
  couponModel.findOneAndUpdate(
    { id: couponId },
    { $pull: { redeemerList: { email: emailForNewCouponCode } } },
    (error, success) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "Could not delete coupon code",
        };
        return res.json(resp);
      } else {
        let resp = {
          status: "Success",
          msg: `Coupon code deleted for ${emailForNewCouponCode}`,
        };
        return res.status(200).json(resp);
      }
    }
  );
});

export default router;
