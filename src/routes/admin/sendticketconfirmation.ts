import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import {
  sendSponsorTicketConfirmation,
  sendGuestTicketConfirmation,
  sendDiscountedPrimaryTicketConfirmation,
} from "../../helpers";

const router = Router();

router.post(
  "/sendticketconfirmation",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let { email, type, ticketName, persona, tier, discountPerc } = req.body;
    const user: any = await userModel.findOne({
      "profile.email": email,
    });
    let firstName: string = user.profile.name.first;
    let companyName: string = user.professional.orgInsti;

    if (type === "guest") {
      await sendGuestTicketConfirmation(firstName, email).then(
        (isConfirmationSent) => {
          if (isConfirmationSent) {
            let resp = {
              status: "Success",
              msg: "Guest Ticket Confirmation Sent",
              payload: {
                firstName: firstName,
                email: email,
                companyName: companyName,
              },
            };
            return res.json(resp);
          } else
            return res.json({
              status: "Failed",
              msg: "Couldn't send Guest Ticket Confirmation",
            });
        }
      );
    } else if (type === "sponsor") {
      await sendSponsorTicketConfirmation(firstName, email, companyName).then(
        (isConfirmationSent) => {
          if (isConfirmationSent) {
            let resp = {
              status: "Success",
              msg: "Sponsor Ticket Confirmation Sent",
              payload: {
                firstName: firstName,
                email: email,
                companyName: companyName,
              },
            };
            return res.json(resp);
          } else
            return res.json({
              status: "Failed",
              msg: "Couldn't send Sponsor Ticket Confirmation",
            });
        }
      );
    } else if (type === "primary") {
      await sendDiscountedPrimaryTicketConfirmation(
        firstName,
        email,
        ticketName,
        persona,
        tier,
        discountPerc
      ).then((isConfirmationSent) => {
        if (isConfirmationSent) {
          let resp = {
            status: "Success",
            msg: "Discounted Primary Ticket Confirmation Sent",
            payload: {
              firstName: firstName,
              email: email,
              companyName: companyName,
            },
          };
          return res.json(resp);
        } else
          return res.json({
            status: "Failed",
            msg: "Couldn't send Discounted Primary Ticket Confirmation",
          });
      });
    }
  }
);

export default router;
