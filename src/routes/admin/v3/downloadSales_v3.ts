import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import {
  userModel,
  eventModel,
  ticketModel_v2,
  couponModel,
} from "../../../models";
import moment from "moment";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/download-sales-data-v3/:eventCode?/:year?/:month?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------------
    Check if user is logged in
    ----------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ----------------
    Find if user exists
    ---------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    /* -----------------
    Extract route params
    ----------------- */
    let eventCode: string = req.params.eventCode;
    let yearString: string = req.params.year;
    let monthString: string = req.params.month;

    /* ------------------------
    Extract eventdetails params
    ----------------- */
    let eventObj: any = await eventModel.findOne({ code: eventCode });
    let eventName: string = eventObj.name;

    /* -----------------
    Get all coupons
    ----------------- */
    let coupons: any = await couponModel.find({});

    /* -----------------
    Process route params
    ----------------- */
    let year: number = Number(yearString);
    let month: number = Number(monthString);
    let startDate: any = moment([year, month - 1]);
    let endDate: any = moment(startDate).endOf("month");
    let startDateISOString: string = startDate.toISOString();
    let endDateISOString: string = endDate.toISOString();

    let monthName: string = "";
    let months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    monthName = months[month - 1];

    /* ----------------------
    Get all users & purchases
    ---------------------- */
    let userAndPurchases: any;
    userAndPurchases = await userModel
      .find(
        { "purchasedItems.eventCode": eventCode },
        "profile professional purchasedItems settings"
      )
      .exec();

    if (!userAndPurchases) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    /* ------------------
    Get tickets for event
    ------------------ */
    const ticketsArr: any = await ticketModel_v2
      .find({ eventCode: eventCode })
      .exec();

    /* ---------------------
    Prepare purchaseArrayRaw
    --------------------- */
    let usersWhoPurchasedInEventCode: any = [];
    userAndPurchases.map((userAndPurchase: any) => {
      let profile: any = userAndPurchase.profile;
      let professional: any = userAndPurchase.professional;
      let purchasedItemsInEventCode: any = [];
      let settings: any = userAndPurchase.settings;
      let purchasedItems: any = userAndPurchase.purchasedItems;

      purchasedItems.map((purchasedItem: any) => {
        if (
          purchasedItem.eventCode === eventCode ||
          purchasedItem.eventCode === "membership"
        ) {
          purchasedItemsInEventCode.push(purchasedItem);
        }
      });

      let obj = {
        profile: profile,
        professional: professional,
        settings: settings,
        purchasedItems: purchasedItemsInEventCode,
      };

      usersWhoPurchasedInEventCode.push(obj);
    });

    let couponDeductionString: string = "";
    let couponDeductionFinalAmount: number = 0;

    let purchasesArrRaw: any = [];
    usersWhoPurchasedInEventCode.forEach((userWhoPurchasedInEventCode: any) => {
      let profile = userWhoPurchasedInEventCode.profile;
      let persona = userWhoPurchasedInEventCode.professional.occupation;
      let gstDetails =
        userWhoPurchasedInEventCode.settings.billing.tax.india.gst;

      let userPurchasesInEventCode = userWhoPurchasedInEventCode.purchasedItems;
      userPurchasesInEventCode.forEach((userPurchase: any) => {
        let isCouponApplied: boolean = userPurchase.isCouponApplied;
        let couponId: string = userPurchase.coupon.id;
        let couponDeductionType: string = userPurchase.coupon.deductionType;
        let couponDeductedAmount: number = userPurchase.coupon.deductedAmount;
        let purchasedTickets: any = userPurchase.purchasedItems;

        purchasedTickets.forEach((purchasedTicket: any) => {
          let ticketTitle: string = "";
          let ticketTier: any;
          let ticketPrice: number = 0;
          let ticketPriceWithGST: number = 0;
          let ticketPriceWithGSTandTxCost: number = 0;
          let finalDeductionFromCoupon: number = 0;

          ticketsArr.forEach((ticket: any) => {
            if (
              ticket.ticketType === "fullTicket" ||
              ticket.ticketType === "basicTicket"
            ) {
              if (purchasedTicket.ticketId === ticket.ticketId) {
                let ticketType: string = ticket.ticketType;
                let tierStudentPrice: number = 0;
                let tierProfessionalPrice: number = 0;

                //Extracting ticket info
                let ticketPricingTiers: any;
                if (ticketType === "fullTicket") {
                  ticketPricingTiers = ticket.fullTicketDetails.tiers;
                  ticketTitle = ticket.fullTicketDetails.title;
                } else if (ticketType === "basicTicket") {
                  ticketPricingTiers = ticket.basicTicketDetails.tiers;
                  ticketTitle = ticket.basicTicketDetails.title;
                }

                //Extracting ticket prices
                ticketPricingTiers.map((tier: any) => {
                  let isBetween: any = moment(userPurchase.createdAt).isBetween(
                    tier.tierStartDate,
                    tier.tierEndDate
                  );
                  if (isBetween) {
                    ticketTier = tier.tierName;
                    tierStudentPrice = tier.studentPrice;
                    tierProfessionalPrice = tier.professionalPrice;
                  }
                });

                //Getting student and professional prices
                if (persona === "student") {
                  ticketPrice = tierStudentPrice;
                } else if (persona === "professional") {
                  ticketPrice = tierProfessionalPrice;
                }

                //Extracting ticket price details
                ticketPriceWithGST = ticketPrice;

                // Coupon calculation
                if (isCouponApplied) {
                  let totalTicketBought: number = purchasedTickets.length;

                  let couponObj: any;
                  coupons.map((coupon: any) => {
                    if (coupon.id === couponId) {
                      couponObj = coupon;
                    }
                  });

                  // Determine final coupon deduction
                  if (couponDeductionType === "ticketType") {
                    finalDeductionFromCoupon = couponDeductedAmount;
                  } else {
                    if (totalTicketBought > 1) {
                      finalDeductionFromCoupon =
                        couponDeductedAmount / totalTicketBought;
                    } else {
                      finalDeductionFromCoupon = couponDeductedAmount;
                    }
                  }
                  if (couponDeductionType === "ticketType") {
                    let couponTargetTickets =
                      couponObj.deduction.ticketTypes.items;
                    let isDeductable: boolean = false;
                    couponTargetTickets.map((couponTargetTicket: any) => {
                      if (
                        couponTargetTicket.ticketId === purchasedTicket.ticketId
                      ) {
                        isDeductable = true;
                      }
                    });

                    if (isDeductable) {
                      ticketPriceWithGST =
                        ticketPriceWithGST - finalDeductionFromCoupon;
                    }
                  } else {
                    ticketPriceWithGST =
                      ticketPriceWithGST - finalDeductionFromCoupon;
                  }
                }

                ticketPrice = ticketPriceWithGST;
                if (userPurchase.paymentGateway != "BankTransfer") {
                  ticketPriceWithGSTandTxCost =
                    ticketPriceWithGST + 0.04 * ticketPriceWithGST;
                } else {
                  ticketPriceWithGSTandTxCost = ticketPriceWithGST;
                }
              }
            } else if (ticket.ticketType === "trackTicket") {
              if (purchasedTicket.ticketId === ticket.ticketId) {
                let ticketType: string = ticket.ticketType;
                let tierStudentPrice: number = 0;
                let tierProfessionalPrice: number = 0;

                let trackSessions: any = ticket.trackTicketDetails.sessions;
                trackSessions.map((trackSession: any) => {
                  if ((trackSession.id = purchasedTicket.sessionId)) {
                    ticketTitle = `${trackSession.type}: ${trackSession.title}`;
                    tierStudentPrice = trackSession.studentPrice;
                    tierProfessionalPrice = trackSession.professionalPrice;
                  }
                });

                //Getting student and professional prices
                if (persona === "student") {
                  ticketPrice = tierStudentPrice;
                } else if (persona === "professional") {
                  ticketPrice = tierProfessionalPrice;
                }

                //Extracting ticket price details
                ticketPriceWithGST = ticketPrice;

                // Coupon calculation
                if (isCouponApplied) {
                  let totalTicketBought: number = purchasedTickets.length;

                  let couponObj: any;
                  coupons.map((coupon: any) => {
                    if (coupon.id === couponId) {
                      couponObj = coupon;
                    }
                  });

                  // Determine final coupon deduction
                  if (couponDeductionType === "ticketType") {
                    finalDeductionFromCoupon = couponDeductedAmount;
                  } else {
                    if (totalTicketBought > 1) {
                      finalDeductionFromCoupon =
                        couponDeductedAmount / totalTicketBought;
                    } else {
                      finalDeductionFromCoupon = couponDeductedAmount;
                    }
                  }
                  if (couponDeductionType === "ticketType") {
                    let couponTargetTickets =
                      couponObj.deduction.ticketTypes.items;
                    let isDeductable: boolean = false;
                    couponTargetTickets.map((couponTargetTicket: any) => {
                      if (
                        couponTargetTicket.ticketId === purchasedTicket.ticketId
                      ) {
                        isDeductable = true;
                      }
                    });

                    if (isDeductable) {
                      ticketPriceWithGST =
                        ticketPriceWithGST - finalDeductionFromCoupon;
                    }
                  } else {
                    ticketPriceWithGST =
                      ticketPriceWithGST - finalDeductionFromCoupon;
                  }
                }

                ticketPrice = ticketPriceWithGST;
                if (userPurchase.paymentGateway != "BankTransfer") {
                  ticketPriceWithGSTandTxCost =
                    ticketPriceWithGST + 0.04 * ticketPriceWithGST;
                } else {
                  ticketPriceWithGSTandTxCost = ticketPriceWithGST;
                }
              }
            }
          });

          let obj = {
            firstName: profile.name.first,
            lastName: profile.name.last,
            persona: persona,
            email: profile.email,
            ticketId: purchasedTicket.ticketId,
            ticketTitle: ticketTitle,
            ticketTier: ticketTier,
            ticketPrice: ticketPrice,
            ticketPriceWithGST: ticketPriceWithGST,
            ticketPriceWithGSTandTxCost: ticketPriceWithGSTandTxCost,
            eventCode: userPurchase.eventCode,
            isCouponApplied: userPurchase.isCouponApplied,
            couponName: userPurchase.coupon.name,
            couponDeductionType: userPurchase.coupon.deductionType,
            couponDeductedAmount: userPurchase.coupon.deductedAmount,
            purchaseID: userPurchase.purchaseID,
            transactionID: userPurchase.transactionID,
            paymentGateway: userPurchase.paymentGateway,
            paymentStatus: userPurchase.paymentStatus,
            isGSTInvoicePreferred: gstDetails.isGSTInvoicePreferred,
            taxJurisdiction: gstDetails.taxJurisdiction,
            gstin: gstDetails.taxID,
            gstRegistrantName: gstDetails.businessName,
            createdAt: userPurchase.createdAt,
            orgInsti: userWhoPurchasedInEventCode.professional.orgInsti,
            jobDegree: userWhoPurchasedInEventCode.professional.jobDegree,
          };

          purchasesArrRaw.push(obj);
        });
      });
    });

    /* -------------------------------------------
    Prepare purchaseArray filtered by month & year
    ------------------------------------------- */
    let purchasesArrFiltered: any = [];
    purchasesArrRaw.forEach((purchase: any) => {
      let isBetween = moment(purchase.createdAt).isBetween(
        startDateISOString,
        endDateISOString
      );
      if (isBetween) {
        purchasesArrFiltered.push(purchase);
      }
    });

    /* -------------------------
    Prepare purchasesFinal Array
    ------------------------- */
    let purchasesArrFinal: any = [];
    let gstAmount: number = 0;
    let ticketBasePrice: number = 0;

    purchasesArrFiltered.forEach((purchase: any) => {
      // Initialisation for GST calculation
      let gstPerc = 18;
      let sgst = 0;
      let cgst = 0;
      let igst = 0;
      let transactionCost = 0;
      let ticketBasePriceRemainder: number = 0;
      let ticketBasePriceForTaxableValue = 0;
      let totalTaxableAmount: number = 0;
      gstAmount =
        purchase.ticketPriceWithGST -
        purchase.ticketPriceWithGST * (100 / (100 + gstPerc));
      gstAmount = parseFloat(gstAmount.toFixed(2));

      // Adjusts coupon deduction
      ticketBasePrice = purchase.ticketPriceWithGST - gstAmount;
      ticketBasePriceRemainder = ticketBasePrice - Math.round(ticketBasePrice);
      ticketBasePrice = Math.round(ticketBasePrice);
      ticketBasePriceForTaxableValue = ticketBasePrice;

      // GST calculation begins
      gstAmount =
        purchase.ticketPriceWithGSTandTxCost -
        purchase.ticketPriceWithGSTandTxCost * (100 / (100 + gstPerc));
      let ticketPriceWithoutGSTandTxCost =
        purchase.ticketPriceWithGSTandTxCost - gstAmount;

      transactionCost = ticketPriceWithoutGSTandTxCost - ticketBasePrice;
      transactionCost =
        Math.round((transactionCost + Number.EPSILON) * 100) / 100;

      let location = "";
      let addedTicketPrice: number = 0;
      let adjustmentValue: number = 0;

      // GST Breakdown
      if (purchase.taxJurisdiction === "inside-tax-jurisdiction-state") {
        location = "Within Maharashtra";
        sgst = gstAmount / 2;
        cgst = gstAmount / 2;
        sgst = parseFloat(sgst.toFixed(2));
        cgst = parseFloat(cgst.toFixed(2));
        addedTicketPrice =
          ticketBasePriceForTaxableValue + sgst + cgst + transactionCost;
      } else {
        if (purchase.taxJurisdiction === "outside-tax-jurisdiction-state") {
          location = "Outside Maharashtra but within India";
        } else if (
          purchase.taxJurisdiction === "outside-tax-jurisdiction-country"
        ) {
          location = "Outside India";
        }
        igst = gstAmount;
        igst = parseFloat(igst.toFixed(2));
        addedTicketPrice =
          ticketBasePriceForTaxableValue + igst + transactionCost;
      }

      if (purchase.email === "<EMAIL>") {
        console.log(`addedTicketPrice: ${addedTicketPrice}`);
      }

      // adjustmentValue = purchase.ticketPrice - addedTicketPrice;
      // adjustmentValue =
      //   Math.round((adjustmentValue + Number.EPSILON) * 100) / 100;

      transactionCost = transactionCost + adjustmentValue;
      totalTaxableAmount = ticketBasePriceForTaxableValue + transactionCost;

      let shortPurchaseDate = purchase.createdAt
        .toISOString()
        .replace(/T.*/, "")
        .split("-")
        .reverse()
        .join("-");

      let amountPaid: number =
        ticketBasePriceForTaxableValue + transactionCost + sgst + cgst + igst;
      amountPaid = parseFloat(amountPaid.toFixed(2));

      let obj = {
        ticketPrice: purchase.ticketPrice,
        isCouponApplied: purchase.isCouponApplied,
        couponName: purchase.couponName,
        couponDeductionString: couponDeductionString,
        couponDeductionFinalAmount: couponDeductionFinalAmount,
        ticketPriceWithGST:
          ticketBasePrice != 0 ? purchase.ticketPriceWithGST : 0,
        ticketPriceWithGSTandTxCost:
          ticketBasePrice != 0 ? purchase.ticketPriceWithGSTandTxCost : 0,
        ticketBasePriceRemainder:
          ticketBasePrice != 0 ? ticketBasePriceRemainder : 0,
        totalTaxableAmount: ticketBasePrice != 0 ? totalTaxableAmount : 0,
        sgst: ticketBasePrice != 0 ? sgst : 0,
        cgst: ticketBasePrice != 0 ? cgst : 0,
        igst: ticketBasePrice != 0 ? igst : 0,
        transactionCost: ticketBasePrice != 0 ? transactionCost : 0,
        paymentGateway: purchase.paymentGateway,
        email: purchase.email,
        name: `${purchase.firstName} ${purchase.lastName}`,
        location: location,
        amountPaid: amountPaid,
        ticketAmount: purchase.ticketPrice,
        ticketNarration: `${purchase.ticketTitle} (${purchase.ticketTier}) - ${purchase.persona}`,
        transactionID: purchase.transactionID,
        purchaseDate: shortPurchaseDate,
        purchaseDateLong: purchase.createdAt,
        ticketBasePrice: ticketBasePriceForTaxableValue,
        isGSTRequired: purchase.isGSTInvoicePreferred,
        GSTIN: purchase.gstin,
        gstRegistrantName: purchase.gstRegistrantName,
        orgInsti: purchase.orgInsti,
        jobDegree: purchase.jobDegree,
        eventName: eventName,
        eventCode: eventCode,
      };

      purchasesArrFinal.push(obj);
    });

    /* ------------------
    Sort purchaseArrFinal
    ------------------ */
    purchasesArrFinal.sort((a: any, b: any) => {
      let nameA = new Date(a.purchaseDateLong);
      let nameB = new Date(b.purchaseDateLong);
      if (nameA > nameB) return 1;
      if (nameA < nameB) return -1;
      return 0;
    });

    /* --------------
    Generate CSV file
    -------------- */
    const fields = [
      {
        label: "transactionDate",
        value: "purchaseDate",
      },
      {
        label: "invoiceNo",
        value: "",
      },
      {
        label: "fullName",
        value: "name",
      },
      {
        label: "email",
        value: "email",
      },
      {
        label: "location",
        value: "location",
      },
      {
        label: "state",
        value: "state",
      },
      {
        label: "isCouponApplied",
        value: "isCouponApplied",
      },
      {
        label: "couponName",
        value: "couponName",
      },
      {
        label: "couponDeduction",
        value: "couponDeductionString",
      },
      {
        label: "totalPaidAmount",
        value: "amountPaid",
      },
      {
        label: "sgst",
        value: "sgst",
      },
      {
        label: "cgst",
        value: "cgst",
      },
      {
        label: "igst",
        value: "igst",
      },
      {
        label: "transactionCost ",
        value: "transactionCost",
      },
      {
        label: "ticketBasePrice",
        value: "ticketBasePrice",
      },
      {
        label: "totalTaxableAmount",
        value: "totalTaxableAmount",
      },
      {
        label: "ticketNarration",
        value: "ticketNarration",
      },
      {
        label: "transactionId",
        value: "transactionID",
      },
      {
        label: "paymentMethod",
        value: "paymentGateway",
      },
      {
        label: "isGstInvoiceRequired",
        value: "isGSTRequired",
      },
      {
        label: "gstin",
        value: "GSTIN",
      },
      {
        label: "gstRegistrantName",
        value: "gstRegistrantName",
      },
      {
        label: "orgInsti",
        value: "orgInsti",
      },
      {
        label: "jobDegree",
        value: "jobDegree",
      },
      {
        label: "eventName",
        value: "eventName",
      },
      {
        label: "eventCode",
        value: "eventCode",
      },
    ];

    let filename: string = `Sales Data - ${monthName} ${year} - ${eventName}`;
    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(purchasesArrFinal);
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}.csv`
    );
    return res.send(csv);
  }
);

export default router;
