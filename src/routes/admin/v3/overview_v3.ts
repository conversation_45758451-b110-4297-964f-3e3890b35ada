import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { userModel, ticketModel_v2, alertModel } from "../../../models";

const router = Router();

interface LooseObject {
  [key: string]: any;
}

router.post("/overview-v3", isUserLogged, getUserID, async (req, res) => {
  /*-----------------------
  Check if user is LoggedIn
  -----------------------*/
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in. Kindly login at https://account.indiahci.org",
    };
    return res.json(resp);
  }

  /*-----------------------------
  Get user details for processing
  -----------------------------*/
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  /* ---------------------------------
  Extract ticket details from req.body
  --------------------------------- */
  let { eventCode, filterType } = req.body;

  /*---------------------
  Query ticket and return
  ---------------------*/
  const ticketsArr: any = await ticketModel_v2
    .find({ eventCode: eventCode })
    .exec();

  /*---------------------
  Query users
  ---------------------*/
  let users: any;
  users = await userModel
    .find({}, "profile professional membership purchasedItems")
    .exec();

  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  /*---------------------
  Accounts overview  
  ---------------------*/
  let totalAccounts: number = 0;
  let studentAccounts: number = 0;
  let professionalAccounts: number = 0;
  let memberAccounts: number = 0;

  users.map((user: any) => {
    let persona: string = user.professional.occupation;
    let isMember: boolean = user.membership.isMember;
    if (isMember) {
      memberAccounts = memberAccounts + 1;
    } else {
      if (persona === "student") {
        studentAccounts = studentAccounts + 1;
      } else if (persona === "professional") {
        professionalAccounts = professionalAccounts + 1;
      }
    }
    totalAccounts = totalAccounts + 1;
  });

  /*---------------------
  Sales overview  
  ---------------------*/
  let accountsWithTickets: number = 0;
  let accountsWithoutTickets: number = 0;
  let ticketRevenue: number = 0;

  let userTicketPurchasesForEventCode: any = [];
  users.map((user: any) => {
    let persona: string = user.professional.occupation;
    let isMember: boolean = user.membership.isMember;
    if (user.purchasedItems.length > 0) {
      user.purchasedItems.map((purchasedItem: any) => {
        if (purchasedItem.eventCode === eventCode) {
          ticketRevenue = ticketRevenue + purchasedItem.grandTotal;
          userTicketPurchasesForEventCode.push({
            email: user.profile.email,
            persona: persona,
            isMember: isMember,
            purchasedItem: purchasedItem,
          });
        }
      });
    }
  });

  let userTicketIdPurchasedForEventCode: any = [];
  userTicketPurchasesForEventCode.map((userPurchaseForEventCode: any) => {
    let purchasedItems = userPurchaseForEventCode.purchasedItem.purchasedItems;
    purchasedItems.map((purchasedItem: any) => {
      userTicketIdPurchasedForEventCode.push({
        email: userPurchaseForEventCode.email,
        ticketId: purchasedItem.ticketId,
        sessionId: purchasedItem.sessionId,
      });
    });
  });

  let primaryTicketBuyerEmails: any = [];
  userTicketIdPurchasedForEventCode.map(
    (ticketIdPurchasedForEventCode: any) => {
      ticketsArr.map((ticket: any) => {
        if (ticketIdPurchasedForEventCode.ticketId === ticket.ticketId) {
          if (ticket.isTicketPrimary) {
            primaryTicketBuyerEmails.push(ticketIdPurchasedForEventCode.email);
          }
        }
      });
    }
  );

  let uniqueValuesSet = new Set();
  let primaryTicketBuyerEmailsFinal = primaryTicketBuyerEmails.filter(
    (email: any) => {
      let isPresentInSet = uniqueValuesSet.has(email);
      uniqueValuesSet.add(email);
      return !isPresentInSet;
    }
  );

  accountsWithTickets = primaryTicketBuyerEmailsFinal.length;
  accountsWithoutTickets = totalAccounts - accountsWithTickets;

  // Extract tickets in eventCode
  let ticketsForEventCode: any = [];
  ticketsArr.map((ticket: any) => {
    if (ticket.eventCode === eventCode) {
      if (ticket.ticketType === "fullTicket") {
        ticketsForEventCode.push({
          ticketId: ticket.ticketId,
          ticketTitle: ticket.fullTicketDetails.title,
          ticketType: ticket.ticketType,
          sessionId: "",
        });
      } else if (ticket.ticketType === "basicTicket") {
        ticketsForEventCode.push({
          ticketId: ticket.ticketId,
          ticketTitle: ticket.basicTicketDetails.title,
          ticketType: ticket.ticketType,
          sessionId: "",
        });
      } else if (ticket.ticketType === "trackTicket") {
        ticket.trackTicketDetails.sessions.map((session: any) => {
          ticketsForEventCode.push({
            ticketId: ticket.ticketId,
            ticketTitle: session.title,
            ticketType: ticket.ticketType,
            sessionId: session.id,
          });
        });
      }
    }
  });

  let ticketSalesForEventCode: any = [];
  ticketsForEventCode.map((ticket: any) => {
    let saleCount: number = 0;
    let saleAmount: number = 0;
    userTicketIdPurchasedForEventCode.map((purchasedItem: any) => {
      if (ticket.ticketId === purchasedItem.ticketId) {
        if (ticket.ticketType === "trackTicket") {
          if (ticket.sessionId === purchasedItem.sessionId) {
            saleCount = saleCount + 1;
          }
        } else {
          saleCount = saleCount + 1;
        }
      }
    });
    let obj = { ...ticket, saleCount: saleCount };
    ticketSalesForEventCode.push(obj);
  });

  let formattedTicketSalesForEventCode: any = [];
  ticketSalesForEventCode.map((ticket: any) => {
    if (ticket.ticketType === "trackTicket") {
      let trackTicketId: string = ticket.ticketId;
      let trackSessionsWithDuplicates: any = [];
      ticketSalesForEventCode.map((session: any) => {
        if (trackTicketId === session.ticketId) {
          trackSessionsWithDuplicates.push(session);
        }
      });

      let uniqueValuesSet = new Set();
      let trackSessionsWithoutDuplicates = trackSessionsWithDuplicates.filter(
        (session: any) => {
          let isPresentInSet = uniqueValuesSet.has(session.sessionId);
          uniqueValuesSet.add(session.sessionId);
          return !isPresentInSet;
        }
      );
      let obj = {
        ticketId: trackTicketId,
        ticketType: "trackTicket",
        sessions: trackSessionsWithoutDuplicates,
      };
      formattedTicketSalesForEventCode.push(obj);
    } else {
      formattedTicketSalesForEventCode.push(ticket);
    }
  });

  uniqueValuesSet = new Set();
  let finalTicketSalesForEventCode = formattedTicketSalesForEventCode.filter(
    (ticket: any) => {
      let isPresentInSet = uniqueValuesSet.has(ticket.ticketId);
      uniqueValuesSet.add(ticket.ticketId);
      return !isPresentInSet;
    }
  );

  /*---------------------
  Alerts details
  ---------------------*/
  let alerts = await alertModel.find({
    "status.isActive": true,
  });

  let payload = {
    accounts: {
      total: totalAccounts,
      student: studentAccounts,
      professional: professionalAccounts,
      member: memberAccounts,
      withTicket: accountsWithTickets,
      withoutTicket: accountsWithoutTickets,
    },
    sales: {
      revenue: ticketRevenue,
      tickets: finalTicketSalesForEventCode,
    },
    notifications: {
      count: alerts.length,
    },
  };

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
