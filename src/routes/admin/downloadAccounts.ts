import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel } from "../../models";
import { Parser } from "json2csv";

const router = Router();

router.get("/download-accounts", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  const users: any = await userModel.find({}).sort({ createdAt: -1 }).exec();
  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  let accounts: any = [];
  users.forEach((user: any) => {
    let obj: any = {};
    if (userObj.settings.isAdmin) {
      obj = {
        name: `${user.profile.name.first} ${user.profile.name.last}`,
        email: user.profile.email,
        mobile: `+${user.profile.mobile.isdCode}-${user.profile.mobile.number}`,
        country: user.profile.country,
        occupation: user.professional.occupation,
        orgInsti: user.professional.orgInsti,
        jobDegree: user.professional.jobDegree,
        isMember: user.membership.isMember,
        membershipId: user.membership.id,
        memberType: user.membership.type,
        createdAt: new Date(user.createdAt),
      };
    } else {
      obj = {
        name: `${user.profile.name.first} ${user.profile.name.last}`,
        email: user.profile.email,
        isMember: user.membership.isMember,
        memberType: user.membership.type,
        createdAt: new Date(user.createdAt),
      };
    }
    accounts.push(obj);
  });

  let fields: any;
  if (userObj.settings.isAdmin) {
    fields = [
      {
        label: "Name",
        value: "name",
      },
      { label: "Email", value: "email" },
      { label: "Mobile", value: "mobile" },
      { label: "Country", value: "country" },
      { label: "Occupation", value: "occupation" },
      { label: "Organisation/Institution", value: "orgInsti" },
      { label: "Job/Degree", value: "jobDegree" },
      { label: "Is Member?", value: "isMember" },
      { label: "Membership ID", value: "membershipId" },
      { label: "Membership Type", value: "memberType" },
      { label: "Account creation", value: "createdAt" },
    ];
  } else {
    fields = [
      {
        label: "Name",
        value: "name",
      },
      { label: "Email", value: "email" },
      { label: "Is Member?", value: "isMember" },
      { label: "Membership Type", value: "memberType" },
      { label: "Account creation", value: "createdAt" },
    ];
  }

  let filename = "HCIPAI_Accounts";
  let csv: any;
  const json2csv = new Parser({ fields });
  csv = json2csv.parse(accounts);

  res.setHeader("Content-Type", "text/csv");
  res.setHeader("Content-Disposition", `attachment; filename=${filename}.csv`);
  return res.send(csv);
});

export default router;
