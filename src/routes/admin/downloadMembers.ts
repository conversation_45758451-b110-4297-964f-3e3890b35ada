import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, legacyMemberModel } from "../../models";
import moment from "moment";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/downloadmembers/:memberType?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let memberType = "";
    if (req.params.memberType != undefined) {
      memberType = req.params.memberType;
    }

    let users: any;
    users = await userModel.find({}, "profile membership").exec();
    if (!users) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    const legacyMembers = await legacyMemberModel
      .find({}, "memberID email name type from to")
      .exec();
    if (!legacyMembers) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve legacy",
      };
      return res.json(resp);
    }

    let allMemberArray: any = [];
    let annualMemberArray: any = [];
    let lifetimeMemberArray: any = [];

    users.forEach((user: any) => {
      if (user.membership.isMember === true) {
        let nowDate = moment();
        let expiresIn: any = "";
        let membershipEndDate: any = "";
        if (user.membership.type === "lifetime") {
          expiresIn = "-";
          membershipEndDate = "-";
        } else if (user.membership.type === "annual") {
          let expireDate = moment(user.membership.endDate);
          expiresIn = expireDate.diff(nowDate, "days");
          membershipEndDate = user.membership.endDate;
          membershipEndDate = moment(membershipEndDate).format(
            "dddd, MMMM Do YYYY, h:mm:ss a"
          );
        }
        let obj = {
          name: `${user.profile.name.first} ${user.profile.name.last}`,
          email: user.profile.email,
          memberID: user.membership.id,
          membershipType: user.membership.type,
          membershipStartDate: moment(user.membership.startDate).format(
            "dddd, MMMM Do YYYY, h:mm:ss a"
          ),
          membershipEndDate: membershipEndDate,
          expiresIn: expiresIn,
          origin: "mainDB",
        };
        if (user.membership.type === "lifetime") {
          lifetimeMemberArray.push(obj);
        } else if (user.membership.type === "annual") {
          annualMemberArray.push(obj);
        }
        allMemberArray.push(obj);
      }
    });

    legacyMembers.forEach((legacyMember: any) => {
      if (legacyMember.type === "lifetime") {
        let obj = {
          name: legacyMember.name,
          email: legacyMember.email,
          memberID: legacyMember.memberID,
          membershipType: legacyMember.type,
          membershipStartDate: moment(legacyMember.from).format(
            "dddd, MMMM Do YYYY, h:mm:ss a"
          ),
          membershipEndDate: "-",
          expiresIn: "-",
          origin: "legacyDB",
        };
        lifetimeMemberArray.push(obj);
        allMemberArray.push(obj);
      }
    });

    allMemberArray.sort((a: any, b: any) => {
      let membershipStartDateA: any = new Date(a.membershipStartDate);
      let membershipStartDateB: any = new Date(b.membershipStartDate);
      return membershipStartDateB - membershipStartDateA;
    });

    annualMemberArray.sort((a: any, b: any) => {
      let membershipStartDateA: any = new Date(a.membershipStartDate);
      let membershipStartDateB: any = new Date(b.membershipStartDate);
      return membershipStartDateB - membershipStartDateA;
    });

    lifetimeMemberArray.sort((a: any, b: any) => {
      let membershipStartDateA: any = new Date(a.membershipStartDate);
      let membershipStartDateB: any = new Date(b.membershipStartDate);
      return membershipStartDateB - membershipStartDateA;
    });

    let uniqueValuesSet = new Set();
    let allMemberArrayFinal: any = allMemberArray.filter((obj: any) => {
      let isPresentInSet = uniqueValuesSet.has(obj.email);
      uniqueValuesSet.add(obj.email);
      return !isPresentInSet;
    });

    uniqueValuesSet = new Set();
    let annualMemberArrayFinal: any = annualMemberArray.filter((obj: any) => {
      let isPresentInSet = uniqueValuesSet.has(obj.email);
      uniqueValuesSet.add(obj.email);
      return !isPresentInSet;
    });

    uniqueValuesSet = new Set();
    let lifetimeMemberArrayFinal: any = lifetimeMemberArray.filter(
      (obj: any) => {
        let isPresentInSet = uniqueValuesSet.has(obj.email);
        uniqueValuesSet.add(obj.email);
        return !isPresentInSet;
      }
    );

    const fields = [
      {
        label: "Name",
        value: "name",
      },
      {
        label: "Email",
        value: "email",
      },
      {
        label: "MemberID",
        value: "memberID",
      },
      {
        label: "Member Type",
        value: "membershipType",
      },
      {
        label: "Start Date",
        value: "membershipStartDate",
      },
      {
        label: "End Date",
        value: "membershipEndDate",
      },
      {
        label: "Expires In (days)",
        value: "expiresIn",
      },
    ];

    let filename = "";
    let csv: any;
    if (memberType === "annual") {
      filename = "HCIPAI-AnnualMembers";
      const json2csv = new Parser({ fields });
      csv = json2csv.parse(annualMemberArrayFinal);
    } else if (memberType === "lifetime") {
      filename = "HCIPAI-LifetimeMembers";
      const json2csv = new Parser({ fields });
      csv = json2csv.parse(lifetimeMemberArrayFinal);
    } else {
      filename = "HCIPAI-AllMembers";
      const json2csv = new Parser({ fields });
      csv = json2csv.parse(allMemberArrayFinal);
    }

    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}.csv`
    );
    return res.send(csv);
  }
);

export default router;
