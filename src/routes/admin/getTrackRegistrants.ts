import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel } from "../../models";

const router = Router();

router.post(
  "/gettrackregistrants",
  isUserLogged,
  getUserID,
  async (req, res) => {
    // if (!res.locals.isUserLogged) {
    //   let resp = {
    //     status: "Failed",
    //     msg: "You are not logged in",
    //   };
    //   return res.status(400).json(resp);
    // }

    // const _userID = res.locals.userID;
    // const userObj: any = await userModel.findOne({
    //   _userID,
    // });

    // if (!userObj) {
    //   let resp = {
    //     status: "Failed",
    //     msg: "User not found",
    //   };
    //   return res.json(resp);
    // }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let userAndPurchases: any = await userModel
      .find({}, "profile professional purchasedItems")
      .exec();

    if (!userAndPurchases) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let { trackTicketTitle } = req.body;

    let ticketsArr: any = await ticketModel
      .find({}, "ticketID type subType title subTitle price")
      .exec();

    let trackTicketID: any = "";
    ticketsArr.forEach((ticket: any) => {
      if (trackTicketTitle === ticket.title) {
        trackTicketID = ticket.ticketID;
      }
    });
    let trackRegistrants: any = [];

    userAndPurchases.forEach((userPurchase: any) => {
      if (userPurchase.purchasedItems.length > 0) {
        let profile = userPurchase.profile;
        let allPurchases = userPurchase.purchasedItems;
        allPurchases.forEach((unitPurchase: any) => {
          unitPurchase.purchasedItems.forEach((ticket: any) => {
            let obj = {
              name: profile.name.first + " " + profile.name.last,
              email: profile.email,
            };
            if (trackTicketID === ticket.ticketID) {
              trackRegistrants.push(obj);
            }
          });
        });
      }
    });

    let payload = {
      trackId: trackTicketID,
      registrants: trackRegistrants,
    };

    let resp = {
      status: "Success",
      msg: "Legacy members retrieved",
      payload: payload,
    };
    return res.json(resp);
  }
);

router.get("/gettrackregistrants/:trackId?", async (req, res) => {
  if (!req.params.trackId) {
    let resp = {
      status: "Failed",
      msg: "Track Not Mentioned",
    };
    return res.json(resp);
  }

  let trackId = req.params.trackId;

  const trackObj: any = await ticketModel.findOne({
    ticketID: trackId,
  });

  let userAndPurchases: any = await userModel
    .find({}, "profile professional purchasedItems")
    .exec();

  if (!userAndPurchases) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  let trackTicketTitle = trackObj.title;

  let trackRegistrants: any = [];
  let slNo: number = 1;
  userAndPurchases.forEach((userPurchase: any) => {
    if (userPurchase.purchasedItems.length > 0) {
      let profile = userPurchase.profile;
      let allPurchases = userPurchase.purchasedItems;
      allPurchases.forEach((unitPurchase: any) => {
        unitPurchase.purchasedItems.forEach((ticket: any) => {
          let obj = {
            slNo: slNo,
            name: profile.name.first + " " + profile.name.last,
            email: profile.email,
          };
          if (trackId === ticket.ticketID) {
            trackRegistrants.push(obj);
            slNo = slNo + 1;
          }
        });
      });
    }
  });

  let payload = {
    trackName: trackTicketTitle,
    registrants: trackRegistrants,
  };

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
