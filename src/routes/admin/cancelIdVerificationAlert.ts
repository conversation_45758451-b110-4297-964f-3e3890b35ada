// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { alertModel, userModel } from "../../models";
import { cancelIdVerificationMail } from "../../helpers";
import moment from "moment";

const router = Router();

router.post(
  "/cancel-id-verification-alert",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    let resolverFirstName = userObj.profile.name.first;
    let resolverLastName = userObj.profile.name.last;
    let resolverEmail = userObj.profile.email;
    let resolvedOn = moment().toISOString();

    let { alertId, reason } = req.body;
    const alert: any = await alertModel.findOne({
      id: alertId,
    });

    let issuerEmail = alert.issuer.email;
    let filter = {
      id: alertId,
    };
    let update = {
      "resolver.name.first": resolverFirstName,
      "resolver.name.middle": "",
      "resolver.name.last": resolverLastName,
      "resolver.email": resolverEmail,
      "status.isActive": false,
      "status.remarks": "Invalid ID",
      "timestamp.resolvedOn": resolvedOn,
    };
    await alertModel.findOneAndUpdate(filter, update);

    const issuer: any = await userModel.findOne({
      "profile.email": issuerEmail,
    });
    let firstName: string = issuer.profile.name.first;

    await userModel.updateOne(
      { "profile.email": issuerEmail },
      { $set: { "professional.idCard": {} } },
      async (err, result) => {
        console.log(result);
        if (err) {
          console.log(err);
          let resp = {
            status: "Failed",
            msg: "ID verification cancellation failed",
          };
          return res.json(resp);
        }

        let idType: string = "student";
        await cancelIdVerificationMail(
          issuerEmail,
          firstName,
          idType,
          reason
        ).then((isCancellationMailSent) => {
          if (isCancellationMailSent) {
            let resp = {
              status: "Success",
              msg: "Student ID verification cancelled",
            };
            return res.json(resp);
          } else
            return res.json({
              status: "Failed",
              msg: "Couldn't cancel student ID",
            });
        });
      }
    );
  }
);

export default router;
