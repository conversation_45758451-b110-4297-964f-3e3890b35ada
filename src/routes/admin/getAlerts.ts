import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, alertModel } from "../../models";

const router = Router();

router.post("/getalerts", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let { alertType, alertStatus, eventCode } = req.body;
  let isAlertActive: boolean = true;
  if (alertStatus === "active") {
    isAlertActive = true;
  } else if (alertStatus === "closed") {
    isAlertActive = false;
  }

  let ticketAlerts: any = [];
  if (!alertType && !alertStatus) {
    ticketAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      eventCode: eventCode,
    });
  } else if (alertType && alertStatus) {
    ticketAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      type: alertType,
      eventCode: eventCode,
    });
  } else if (alertType) {
    ticketAlerts = await alertModel.find({
      type: alertType,
      eventCode: eventCode,
    });
  } else if (alertStatus) {
    ticketAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      eventCode: eventCode,
    });
  }

  let membershipAlerts: any = [];
  if (!alertType && !alertStatus) {
    membershipAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      eventCode: "membership",
    });
  } else if (alertType && alertStatus) {
    membershipAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      type: alertType,
      eventCode: "membership",
    });
  } else if (alertType) {
    membershipAlerts = await alertModel.find({
      type: alertType,
      eventCode: "membership",
    });
  } else if (alertStatus) {
    membershipAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      eventCode: "membership",
    });
  }

  let idVerificationAlerts: any = [];
  if (!alertType && !alertStatus) {
    idVerificationAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      eventCode: "idVerification",
    });
  } else if (alertType && alertStatus) {
    idVerificationAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      type: alertType,
      eventCode: "idVerification",
    });
  } else if (alertType) {
    idVerificationAlerts = await alertModel.find({
      type: alertType,
      eventCode: "idVerification",
    });
  } else if (alertStatus) {
    idVerificationAlerts = await alertModel.find({
      "status.isActive": isAlertActive,
      eventCode: "idVerification",
    });
  }

  let finalAlerts: any = [];
  ticketAlerts.map((ticketAlert: any) => {
    finalAlerts.push(ticketAlert);
  });
  membershipAlerts.map((membershipAlert: any) => {
    finalAlerts.push(membershipAlert);
  });
  idVerificationAlerts.map((idVerificationAlert: any) => {
    finalAlerts.push(idVerificationAlert);
  });

  let resp = {
    status: "Success",
    msg: "Alerts retrieved",
    payload: {
      totalAlerts: finalAlerts.length,
      alertArr: finalAlerts,
    },
  };
  return res.json(resp);
});

export default router;
