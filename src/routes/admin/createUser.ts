import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import * as argon from "argon2";
import { v4 as uuidv4 } from "uuid";
import * as crypto from "crypto-js";

import moment from "moment";

const router = Router();

router.post("/createuser", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check is user rm/admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  /* --------
  Create user
  -------- */
  let {
    firstName,
    lastName,
    email,
    password,
    isdCode,
    mobileCountry,
    mobileNumber,
    occupation,
    orgInsti,
    jobDegree,
    country,
    currency,
    isHCIPAIMember,
    isGSTInvoicePreferred,
    businessName,
    taxID,
    taxJurisdiction,
    billingAddressLine1,
    billingAddressLine2,
    billingAddressLine3,
  } = req.body;

  let newUserID: string = await uuidv4();
  let psswd: string = await argon.hash(password, {
    type: argon.argon2id,
  });
  email = email.toLowerCase().trim();
  let signature = crypto
    .SHA3(email, {
      outputLength: 256,
    })
    .toString();

  let currencySymbol = "";
  if (currency === "eur") {
    currencySymbol = "€";
  } else if (currency === "inr") {
    currencySymbol = "₹";
  } else if (currency === "usd") {
    currencySymbol = "$";
  }
  let currencyObj = {
    name: currency,
    symbol: currencySymbol,
  };

  let profile = {
    name: {
      first: firstName,
      last: lastName,
    },
    email: email,
    psswd: psswd,
    mobile: {
      isdCode: isdCode,
      country: mobileCountry,
      number: mobileNumber,
    },
  };

  const newUser: any = await userModel
    .create({
      _userID: newUserID,
      profile: {
        name: profile.name,
        email: profile.email,
        psswd: profile.psswd,
        mobile: profile.mobile,
        country: country,
      },
      professional: {
        occupation: occupation,
        orgInsti: orgInsti,
        jobDegree: jobDegree,
      },
      membership: {
        isMember: false,
        id: "",
        type: "",
        startDate: "",
        endDate: "",
        isInCart: false,
        memberFromRegistration: isHCIPAIMember,
        submittedID: "",
      },
      settings: {
        isRememberMe: true,
        isRegistrationManager: false,
        isAdmin: false,
        currency: currencyObj,
        billing: {
          tax: {
            india: {
              gst: {
                isGSTInvoicePreferred: isGSTInvoicePreferred,
                businessName: businessName,
                taxID: taxID,
                taxJurisdiction: taxJurisdiction,
                address: {
                  billing: {
                    line1: billingAddressLine1,
                    line2: billingAddressLine2,
                    line3: billingAddressLine3,
                  },
                },
              },
            },
          },
        },
      },
      signature: signature,
    })
    .catch((error) => {
      let resp = {
        status: "Failed",
        msg: "An error occured while creating the user",
      };
      console.log(error);
      res.status(200).json(resp);
    });

  if (!newUser._id) {
    let resp = {
      status: "Failed",
      msg: "Account creation failed!",
    };
    res.status(200).json(resp);
  }

  let resp = {
    status: "Success",
    msg: "Account created",
  };
  res.json(resp);
});

export default router;
