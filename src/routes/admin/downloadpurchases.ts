import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel } from "../../models";
import moment from "moment";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/downloadpurchases/:month?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let userAndPurchases: any;
    userAndPurchases = await userModel
      .find({}, "profile professional purchasedItems settings")
      .exec();

    if (!userAndPurchases) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let ticketsArr: any = await ticketModel
      .find({}, "ticketID type subType title subTitle price")
      .exec();
    let purchasesArrRaw: any = [];
    userAndPurchases.forEach((userPurchase: any) => {
      if (userPurchase.purchasedItems.length > 0) {
        let profile = userPurchase.profile;
        let persona = userPurchase.professional.occupation;
        let gstDetails = userPurchase.settings.billing.tax.india.gst;
        let allPurchases = userPurchase.purchasedItems;
        allPurchases.forEach((unitPurchase: any) => {
          unitPurchase.purchasedItems.forEach((ticket: any) => {
            let obj = {
              firstName: profile.name.first,
              lastName: profile.name.last,
              persona: persona,
              email: profile.email,
              ticket: ticket.ticketID,

              isCouponApplied: unitPurchase.isCouponApplied,
              couponName: unitPurchase.coupon.name,
              couponDeductionType: unitPurchase.coupon.deductionType,
              couponDeductionValue: unitPurchase.coupon.deductionValue,

              purchaseID: unitPurchase.purchaseID,
              transactionID: unitPurchase.transactionID,
              paymentGateway: unitPurchase.paymentGateway,
              paymentStatus: unitPurchase.paymentStatus,
              isGSTInvoicePreferred: gstDetails.isGSTInvoicePreferred,
              taxJurisdiction: gstDetails.taxJurisdiction,
              gstin: gstDetails.taxID,
              gstRegistrantName: gstDetails.businessName,
              createdAt: unitPurchase.createdAt,
              orgInsti: userPurchase.professional.orgInsti,
              jobDegree: userPurchase.professional.jobDegree,
            };
            purchasesArrRaw.push(obj);
          });
        });
      }
    });

    let monthName = "";
    if (req.params.month != undefined) {
      monthName = req.params.month;
    }
    let monthStartDate = moment()
      .month(monthName)
      .startOf("month")
      .toISOString();
    let monthEndDate = moment().month(monthName).endOf("month").toISOString();

    let purchasesArrFinal: any = [];
    purchasesArrRaw.forEach((purchase: any) => {
      let ticketType = "";
      let ticketSubtype = "";
      let ticketTitle = "";
      let ticketSubTitle = "";
      let ticketPrice: number = 0;
      let ticketPriceWithGST: number = 0;
      let ticketPriceWithGSTandTxCost: number = 0;
      let couponDeductionString: string = "";
      let isWorkshopCourseDiscountApplied: boolean = false;

      let ticketBasePrice: number = 0;
      let gstAmount: number = 0;
      let ticketTier = "";
      let currency = "₹";

      ticketsArr.forEach((ticket: any) => {
        if (purchase.ticket === ticket.ticketID) {
          ticketType = ticket.type;
          ticketSubtype = ticket.subType;
          ticketTitle = ticket.title;
          ticketSubTitle = ticket.subTitle;
          let priceArr = ticket.price;
          priceArr.forEach((item: any) => {
            let startTx = item.startDate;
            let endTx = item.endDate;
            let startDate = new Date(parseInt(startTx) * 1000).toISOString();
            let endDate = new Date(parseInt(endTx) * 1000).toISOString();
            let purchaseDate = purchase.createdAt;
            let isBetween = moment(purchaseDate).isBetween(startDate, endDate);
            if (isBetween) {
              if (ticketSubtype === "course") {
                if (purchase.persona === item.name.toLowerCase()) {
                  ticketTier = item.name;
                  ticketPrice = item.price.inr;
                }
              } else {
                ticketTier = item.name;
                ticketPrice = item.price.inr;
              }
              ticketPriceWithGST = ticketPrice;
              if (purchase.paymentGateway != "BankTransfer") {
                if (ticketSubtype === "course") {
                  if (purchase.persona === item.name.toLowerCase()) {
                    ticketPrice = ticketPrice + 0.04 * ticketPrice;
                  }
                } else {
                  ticketPrice = ticketPrice + 0.04 * ticketPrice;
                }
              }
              ticketPriceWithGSTandTxCost = ticketPrice;
            }
          });
        }
      });

      let gstPerc = 18;
      let sgst = 0;
      let cgst = 0;
      let igst = 0;
      let transactionCost = 0;
      let ticketBasePriceRemainder: number = 0;
      let ticketBasePriceForTaxableValue = 0;
      let totalTaxableAmount: number = 0;
      gstAmount =
        ticketPriceWithGST - ticketPriceWithGST * (100 / (100 + gstPerc));
      gstAmount = parseFloat(gstAmount.toFixed(2));

      /* BaseCalculation */
      ticketBasePrice = ticketPriceWithGST - gstAmount;
      if (purchase.isCouponApplied) {
        if (purchase.couponDeductionType === "fixed") {
          ticketBasePrice = ticketBasePrice - purchase.couponDeductionValue;
          if (ticketBasePrice <= 0) {
            ticketBasePrice = 0;
          }
          couponDeductionString = `₹${purchase.couponDeductionValue}`;
        } else if (purchase.couponDeductionType === "percentage") {
          let percentageDeduction =
            (purchase.couponDeductionValue / 100) * ticketBasePrice;
          ticketBasePrice = ticketBasePrice - percentageDeduction;
          if (ticketBasePrice <= 0) {
            ticketBasePrice = 0;
          }
          couponDeductionString = `${purchase.couponDeductionValue}%`;
        } else if (purchase.couponDeductionType === "ticketType") {
          couponDeductionString = `Ticket type`;
        }
      }
      ticketBasePriceRemainder = ticketBasePrice - Math.round(ticketBasePrice);
      ticketBasePrice = Math.round(ticketBasePrice);
      ticketBasePriceForTaxableValue = ticketBasePrice;

      /* MainCalculation */
      gstAmount =
        ticketPriceWithGSTandTxCost -
        ticketPriceWithGSTandTxCost * (100 / (100 + gstPerc));
      let ticketPriceWithoutGSTandTxCost =
        ticketPriceWithGSTandTxCost - gstAmount;

      if (purchase.isCouponApplied) {
        if (purchase.couponDeductionType === "fixed") {
          ticketPriceWithoutGSTandTxCost =
            ticketPriceWithoutGSTandTxCost - purchase.couponDeductionValue;
          if (ticketPriceWithoutGSTandTxCost <= 0) {
            ticketPriceWithoutGSTandTxCost = 0;
          }
        } else if (purchase.couponDeductionType === "percentage") {
          let percentageDeduction =
            (purchase.couponDeductionValue / 100) *
            ticketPriceWithoutGSTandTxCost;
          ticketPriceWithoutGSTandTxCost =
            ticketPriceWithoutGSTandTxCost - percentageDeduction;
          if (ticketPriceWithoutGSTandTxCost <= 0) {
            ticketPriceWithoutGSTandTxCost = 0;
          }
        } else if (purchase.couponDeductionType === "ticketType") {
          couponDeductionString = `Ticket type`;
        }
      }

      transactionCost = ticketPriceWithoutGSTandTxCost - ticketBasePrice;
      transactionCost =
        Math.round((transactionCost + Number.EPSILON) * 100) / 100;

      let location = "";
      let addedTicketPrice: number = 0;
      let adjustmentValue: number = 0;
      if (purchase.isGSTInvoicePreferred === true) {
        if (purchase.taxJurisdiction === "inside-tax-jurisdiction-state") {
          location = "Within Maharashtra";
          sgst = gstAmount / 2;
          cgst = gstAmount / 2;
          sgst = parseFloat(sgst.toFixed(2));
          cgst = parseFloat(cgst.toFixed(2));
          addedTicketPrice =
            ticketBasePriceForTaxableValue + sgst + cgst + transactionCost;
        } else if (
          purchase.taxJurisdiction === "outside-tax-jurisdiction-state"
        ) {
          location = "Outside Maharashtra but within India";
          igst = gstAmount;
          igst = parseFloat(igst.toFixed(2));
          addedTicketPrice =
            ticketBasePriceForTaxableValue + igst + transactionCost;
        }
      } else if (purchase.isGSTInvoicePreferred === false) {
        if (purchase.taxJurisdiction === "inside-tax-jurisdiction-state") {
          location = "Within Maharashtra";
          sgst = gstAmount / 2;
          cgst = gstAmount / 2;
          sgst = parseFloat(sgst.toFixed(2));
          cgst = parseFloat(cgst.toFixed(2));
          addedTicketPrice =
            ticketBasePriceForTaxableValue + sgst + cgst + transactionCost;
        } else if (
          purchase.taxJurisdiction === "outside-tax-jurisdiction-state"
        ) {
          location = "Outside Maharashtra but within India";
          sgst = gstAmount / 2;
          cgst = gstAmount / 2;
          sgst = parseFloat(sgst.toFixed(2));
          cgst = parseFloat(cgst.toFixed(2));
          addedTicketPrice =
            ticketBasePriceForTaxableValue + sgst + cgst + transactionCost;
        } else if (
          purchase.taxJurisdiction === "outside-tax-jurisdiction-country"
        ) {
          location = "Outside India";
          igst = gstAmount;
          igst = parseFloat(igst.toFixed(2));
          addedTicketPrice =
            ticketBasePriceForTaxableValue + igst + transactionCost;
        }
      }

      adjustmentValue = ticketPrice - addedTicketPrice;
      if (purchase.isCouponApplied) {
        if (purchase.couponDeductionType === "fixed") {
          adjustmentValue = adjustmentValue - purchase.couponDeductionValue;
          if (adjustmentValue <= 0) {
            adjustmentValue = 0;
          }
        } else if (purchase.couponDeductionType === "percentage") {
          let percentageDeduction =
            (purchase.couponDeductionValue / 100) * adjustmentValue;
          adjustmentValue = adjustmentValue - percentageDeduction;
          if (adjustmentValue <= 0) {
            adjustmentValue = 0;
          }
        } else if (purchase.couponDeductionType === "ticketType") {
          couponDeductionString = `Ticket type`;
        }
      }
      adjustmentValue =
        Math.round((adjustmentValue + Number.EPSILON) * 100) / 100;
      transactionCost = transactionCost + adjustmentValue;
      totalTaxableAmount = ticketBasePriceForTaxableValue + transactionCost;

      let shortPurchaseDate = purchase.createdAt
        .toISOString()
        .replace(/T.*/, "")
        .split("-")
        .reverse()
        .join("-");

      if (ticketBasePrice === 0) {
        ticketPriceWithGST = 0;
        ticketPriceWithGSTandTxCost = 0;
        ticketBasePriceRemainder = 0;
        totalTaxableAmount = 0;
        sgst = 0;
        cgst = 0;
        igst = 0;
        transactionCost = 0;
      }

      let obj = {
        ticketPrice: ticketPrice,
        isCouponApplied: purchase.isCouponApplied,
        couponName: purchase.couponName,
        couponDeductionString: couponDeductionString,
        ticketPriceWithGST: ticketPriceWithGST,
        ticketPriceWithGSTandTxCost: ticketPriceWithGSTandTxCost,
        ticketBasePriceRemainder: ticketBasePriceRemainder,
        totalTaxableAmount: totalTaxableAmount,
        sgst: sgst,
        cgst: cgst,
        igst: igst,
        transactionCost: transactionCost,
        email: purchase.email,
        name: `${purchase.firstName} ${purchase.lastName}`,
        location: location,
        amountPaid: `${
          ticketBasePriceForTaxableValue + transactionCost + sgst + cgst + igst
        }`,
        ticketAmount: ticketPrice,
        ticketNarration: `${ticketTitle} - ${ticketSubTitle} (${ticketTier})`,
        transactionID: purchase.transactionID,
        purchaseDate: shortPurchaseDate,
        ticketBasePrice: ticketBasePriceForTaxableValue,
        isGSTRequired: purchase.isGSTInvoicePreferred,
        GSTIN: purchase.gstin,
        gstRegistrantName: purchase.gstRegistrantName,
        orgInsti: purchase.orgInsti,
        jobDegree: purchase.jobDegree,
      };
      if (monthName.length > 0) {
        let isPurchaseInBetween = moment(purchase.createdAt).isBetween(
          monthStartDate,
          monthEndDate
        );
        if (isPurchaseInBetween) {
          purchasesArrFinal.push(obj);
        }
      } else {
        purchasesArrFinal.push(obj);
      }

      // if (
      //   purchase.couponName ===
      //     "50% discount on Full Conference Ticket (Professional)" ||
      //   purchase.couponName ===
      //     "50% discount on Full Conference Ticket (Professional) - For Cubyts" ||
      //   purchase.couponName ===
      //     "50% discount on Full Conference Ticket (Professional) - For ServiceNow" ||
      //   purchase.couponName ===
      //     "50% discount on Full Conference Ticket (Professional) - For Copods"
      // ) {
      //   console.log("");
      //   console.log(`amountPaid: ${obj.amountPaid}`);
      //   console.log(`ticketBasePrice: ${obj.ticketBasePrice}`);
      //   console.log(`cgst: ${obj.cgst}`);
      //   console.log(`sgst: ${obj.sgst}`);
      //   console.log(`igst: ${obj.igst}`);
      //   console.log(`transaction: ${obj.transactionCost}`);
      //   console.log("");
      // }
    });

    const fields = [
      {
        label: "Email",
        value: "email",
      },
      {
        label: "Name",
        value: "name",
      },
      {
        label: "Location",
        value: "location",
      },
      {
        label: "Is coupon applied?",
        value: "isCouponApplied",
      },
      {
        label: "Coupon Name",
        value: "couponName",
      },
      {
        label: "Coupon Deduction",
        value: "couponDeductionString",
      },
      {
        label: "Total Paid Amount",
        value: "amountPaid",
      },
      {
        label: "SGST",
        value: "sgst",
      },
      {
        label: "CGST",
        value: "cgst",
      },
      {
        label: "IGST",
        value: "igst",
      },
      {
        label: "Transaction Cost",
        value: "transactionCost",
      },
      {
        label: "Ticket Base Price",
        value: "ticketBasePrice",
      },
      {
        label: "Total Taxable Amount",
        value: "totalTaxableAmount",
      },
      {
        label: "Ticket Narration",
        value: "ticketNarration",
      },
      {
        label: "Transaction ID",
        value: "transactionID",
      },
      {
        label: "Transaction Date",
        value: "purchaseDate",
      },
      {
        label: "Is GST Invoice Preferred?",
        value: "isGSTRequired",
      },
      {
        label: "GSTIN",
        value: "GSTIN",
      },
      {
        label: "GST Registrant Name",
        value: "gstRegistrantName",
      },
      {
        label: "OrgInsti",
        value: "orgInsti",
      },
      {
        label: "JobDegree",
        value: "jobDegree",
      },
    ];

    let filename = "";
    if (monthName.length > 0) {
      filename = `IndiaHCI2024-Sales-${monthName}`;
    } else {
      filename = `IndiaHCI2024-Sales-All`;
    }
    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(purchasesArrFinal);
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}.csv`
    );
    return res.send(csv);
  }
);

export default router;
