import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel, couponModel } from "../../models";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/downloadcouponemails/:couponId/:filterOption?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let userAndPurchases: any = await userModel
      .find({}, "profile professional purchasedItems")
      .exec();

    if (!userAndPurchases) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let couponIdRaw = req.params.couponId;
    let filterOptionRaw = req.params.filterOption;
    let couponId = decodeURIComponent(couponIdRaw);
    let filterOption = decodeURIComponent(filterOptionRaw);

    let coupon: any = await couponModel.findOne({ id: couponId });
    let couponName: string = coupon.name;
    let redeemerList: any = coupon.redeemerList;
    let usedRedeemerList: Array<Object> = [];
    let unUsedRedeemerList: Array<Object> = [];
    let finalRedeemerListRaw: any;
    let finalRedeemerListProcessed: Array<Object> = [];

    redeemerList.map((redeemerObj: any) => {
      if (redeemerObj.isUsed) {
        usedRedeemerList.push(redeemerObj);
      } else {
        unUsedRedeemerList.push(redeemerObj);
      }
    });

    if (filterOption === "used") {
      finalRedeemerListRaw = usedRedeemerList;
      couponName = couponName + " (Used)";
    } else if (filterOption === "notYetUsed") {
      finalRedeemerListRaw = unUsedRedeemerList;
      couponName = couponName + " (Not yet used)";
    } else {
      finalRedeemerListRaw = redeemerList;
    }

    finalRedeemerListRaw.map((redeemer: any) => {
      let name: string = "";
      let usedOn: string = "";

      if (redeemer.name.first.length > 0 && redeemer.name.last.length > 0) {
        name = redeemer.name.first + " " + redeemer.name.last;
      } else if (redeemer.name.first.length > 0) {
        name = redeemer.name.first;
      } else if (redeemer.name.last.length > 0) {
        name = redeemer.name.last;
      } else {
        name = "-";
      }

      if (redeemer.usedOn.length > 0) {
        usedOn = new Date(redeemer.usedOn).toString().substring(4, 15);
      } else {
        usedOn = "-";
      }

      let obj = {
        name: name,
        email: redeemer.email,
        couponCode: redeemer.code,
        usedOn: usedOn,
      };
      finalRedeemerListProcessed.push(obj);
    });

    const fields = [
      {
        label: "Name",
        value: "name",
      },
      {
        label: "Email",
        value: "email",
      },
      {
        label: "Coupon Code",
        value: "couponCode",
      },
      {
        label: "Used On",
        value: "usedOn",
      },
    ];

    let filename: string = couponName;
    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(finalRedeemerListProcessed);
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}.csv`
    );
    return res.send(csv);
  }
);

export default router;
