// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import { validateBankTxInputs } from "../../validation";
import { sendBankTransferVerificationMail } from "../../helpers";
import moment from "moment";

const router = Router();

router.post(
  "/banktxverification",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let { email, bankTxCode, orderID } = req.body;
    let { error } = validateBankTxInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.send(resp);
    }

    const user: any = await userModel.findOne({
      "profile.email": email,
    });

    let firstName: string = user.profile.name.first;
    let purchasedItems = user.purchasedItems;
    let transferredAmount: string = "";
    let paymentDate: string = "";
    let isBankTxValid: boolean = false;

    purchasedItems.forEach((item: any) => {
      if (bankTxCode === item.transactionID && orderID === item.purchaseID) {
        isBankTxValid = true;
        transferredAmount = `₹${item.grandTotal}`;
        paymentDate = item.createdAt;
      }
    });

    paymentDate = moment(paymentDate).toString();

    if (!isBankTxValid) {
      let resp = {
        status: "Failed",
        msg: "Not a valid Bank Transaction",
      };
      return res.json(resp);
    }

    await userModel.updateOne(
      { "profile.email": email, "purchasedItems.purchaseID": orderID },
      { $set: { "purchasedItems.$.paymentStatus": "purchased" } },
      async (err, result) => {
        if (err) {
          console.log(err);
          let resp = {
            status: "Failed",
            msg: "Updation failed",
          };
          return res.json(resp);
        }
        await sendBankTransferVerificationMail(
          firstName,
          email,
          orderID,
          transferredAmount,
          bankTxCode,
          paymentDate
        ).then((isConfirmationSent) => {
          if (isConfirmationSent) {
            console.log(
              `[BANK TX VERIFIED] - orderID: ${orderID}, bankTxCode: ${bankTxCode}`
            );
            let resp = {
              status: "Success",
              msg: "Bank Transaction Verified",
            };
            return res.json(resp);
          } else
            return res.json({
              status: "Failed",
              msg: "Couldn't verify bank transaction",
            });
        });
      }
    );
  }
);

export default router;
