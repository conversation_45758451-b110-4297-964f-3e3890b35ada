import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { couponModel, userModel } from "../../models";

const router = Router();

router.post("/getcouponbyid", isUserLogged, getUserID, async (req, res) => {
  /* ------------
  Extract payload
  ------------ */
  let { couponId } = req.body;

  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check is user rm/admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }
  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let coupon: any = await couponModel.findOne({ id: couponId });
  let issuedCount: number = 0;
  let usedCount: number = 0;
  let redeemerList: any = coupon.redeemerList;

  if (redeemerList.length > 0) issuedCount = redeemerList.length;
  redeemerList.map((redeemer: any) => {
    if (redeemer.isUsed === true) usedCount = usedCount + 1;
  });

  let formattedCouponObj = {
    id: coupon.id,
    name: coupon.name,
    deduction: coupon.deduction,
    meta: coupon.meta,
    redeemerList: coupon.redeemerList,
    issuedCount: issuedCount,
    usedCount: usedCount,
  };

  let resp = {
    status: "Success",
    msg: "Coupon retrieved",
    payload: formattedCouponObj,
  };
  return res.status(200).json(resp);
});

export default router;
