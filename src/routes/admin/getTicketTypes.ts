import { Router } from "express";
import { userModel, ticketModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";

const router = Router();

router.get("/get-ticket-types", isUserLogged, getUserID, async (req, res) => {
  /*-----------------------
  Check if user is LoggedIn
  -----------------------*/
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({ _userID });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  /* --------------------
  Query ticket and return
  -------------------- */
  const ticketsArr: any = await ticketModel.find({});
  let firstPassTicketArr: any = [];
  let secondPassTicketArr: any = [];
  let finalTicketArr: any = [];

  ticketsArr.map((ticket: any) => {
    let ticketObj = {
      type: ticket.type,
      subType: ticket.subType,
      title: ticket.title,
    };
    firstPassTicketArr.push(ticketObj);
  });

  firstPassTicketArr.map((firstPassObj: any) => {
    let isFound: boolean = false;
    secondPassTicketArr.map((secondPassObj: any) => {
      if (firstPassObj.subType === secondPassObj.subType) {
        isFound = true;
      }
    });
    if (!isFound) secondPassTicketArr.push(firstPassObj);
  });

  secondPassTicketArr.map((secondPassObj: any) => {
    let title: string = secondPassObj.title;
    let subType: string = secondPassObj.subType;

    if (subType === "full-conference-professional") {
      title = "Full Conference (Professional)";
    } else if (subType === "full-conference-student") {
      title = "Full Conference (Student)";
    } else if (subType === "full-conference-hcipai-professional") {
      title = "Full Conference (HCIPAI Member)";
    } else if (subType === "workshop") {
      title = "Workshop";
    } else if (subType === "course") {
      title = "Course";
    }

    let obj = {
      title: title,
      subType: subType,
    };

    finalTicketArr.push(obj);
  });

  let resp = {
    status: "Success",
    msg: "Ticket data fetched",
    payload: finalTicketArr,
  };

  return res.json(resp);
});

export default router;
