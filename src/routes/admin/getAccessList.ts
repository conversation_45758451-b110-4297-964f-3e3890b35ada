import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel } from "../../models";
import moment from "moment";

const router = Router();

router.get("/getaccesslist", isUserLogged, getUserID, async (req, res) => {
  let userAndPurchases: any = await userModel
    .find({}, "profile purchasedItems professional settings")
    .exec();

  if (!userAndPurchases) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  let ticketsArr: any = await ticketModel
    .find({}, "ticketID type subType title subTitle price")
    .exec();
  let purchasesArrRaw: any = [];

  userAndPurchases.forEach((userPurchase: any) => {
    if (userPurchase.purchasedItems.length > 0) {
      let profile = userPurchase.profile;
      let persona = userPurchase.professional.occupation;

      let allPurchases = userPurchase.purchasedItems;
      let isBillingDetailsAvailable = true;
      if (
        userPurchase.settings.billing.tax.india.gst.isGSTInvoicePreferred ===
          undefined &&
        userPurchase.settings.billing.tax.india.gst.taxJurisdiction ===
          undefined
      ) {
        isBillingDetailsAvailable = false;
      }
      allPurchases.forEach((unitPurchase: any) => {
        unitPurchase.purchasedItems.forEach((ticket: any) => {
          let obj = {
            firstName: profile.name.first,
            lastName: profile.name.last,
            mobile: profile.mobile.isdCode + "-" + profile.mobile.number,
            persona: persona,
            email: profile.email,
            ticket: ticket.ticketID,
            purchaseID: unitPurchase.purchaseID,
            transactionID: unitPurchase.transactionID,
            paymentGateway: unitPurchase.paymentGateway,
            paymentStatus: unitPurchase.paymentStatus,
            isBillingDetailsAvailable: isBillingDetailsAvailable,
            createdAt: unitPurchase.createdAt,
          };
          purchasesArrRaw.push(obj);
        });
      });
    }
  });

  let monthName = "";
  let monthStartDate = moment().month(monthName).startOf("month").toISOString();
  let monthEndDate = moment().month(monthName).endOf("month").toISOString();
  let purchasesArrFinal: any = [];

  purchasesArrRaw.forEach((purchase: any) => {
    let ticketType = "";
    let ticketSubtype = "";
    let ticketTitle = "";
    let ticketSubTitle = "";
    let ticketPrice = 0;
    let ticketTier = "";
    let currency = "₹";
    ticketsArr.forEach((ticket: any) => {
      if (purchase.ticket === ticket.ticketID) {
        ticketType = ticket.type;
        ticketSubtype = ticket.subType;
        ticketTitle = ticket.title;
        ticketSubTitle = ticket.subTitle;
        let priceArr = ticket.price;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let purchaseDate = purchase.createdAt;
          let isBetween = moment(purchaseDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (ticketSubtype === "course") {
              if (purchase.persona === item.name.toLowerCase()) {
                ticketTier = item.name;
                ticketPrice = parseInt(item.price.inr);
              }
            } else {
              ticketTier = item.name;
              ticketPrice = parseInt(item.price.inr);
            }
            if (purchase.paymentGateway != "BankTransfer") {
              if (ticketSubtype === "course") {
                if (purchase.persona === item.name.toLowerCase()) {
                  ticketPrice = ticketPrice + 0.04 * ticketPrice;
                }
              } else {
                ticketPrice = ticketPrice + 0.04 * ticketPrice;
              }
            }
          }
        });
      }
    });

    let obj = {
      firstName: purchase.firstName,
      lastName: purchase.lastName,
      email: purchase.email,
      mobile: purchase.mobile,
      purchaseID: purchase.purchaseID,
      transactionID: purchase.transactionID,
      ticketType: ticketType,
      ticketSubtype: ticketSubtype,
      ticketTitle: ticketTitle,
      ticketSubTitle: ticketSubTitle,
      ticketPrice: `${currency}${ticketPrice}`,
      ticketTier: ticketTier,
      paymentGateway: purchase.paymentGateway,
      paymentStatus: purchase.paymentStatus,
      isBillingDetailsAvailable: purchase.isBillingDetailsAvailable,
      purchasedOn: purchase.createdAt,
    };

    if (monthName.length > 0) {
      let isPurchaseInBetween = moment(obj.purchasedOn).isBetween(
        monthStartDate,
        monthEndDate
      );
      if (isPurchaseInBetween) {
        purchasesArrFinal.push(obj);
      }
    } else {
      purchasesArrFinal.push(obj);
    }
  });

  // purchasesArrFinal.sort((a: any, b: any) => {
  //   let nameA = a.firstName.toLowerCase();
  //   let nameB = b.firstName.toLowerCase();
  //   if (nameA < nameB) return -1;
  //   if (nameA > nameB) return 1;
  //   return 0;
  // });

  purchasesArrFinal.sort((a: any, b: any) => {
    // let AcreationDate: any = new Date(a.createdAt);
    // let BcreationDate: any = new Date(b.createdAt);
    // return AcreationDate - BcreationDate;
    let nameA = new Date(a.purchasedOn);
    let nameB = new Date(b.purchasedOn);
    if (nameA > nameB) return -1;
    if (nameA < nameB) return 1;
    return 0;
  });

  let accessList: any = [];
  let slno = 0;
  purchasesArrFinal.forEach((purchase: any) => {
    if (
      purchase.ticketType === "full-conference" ||
      purchase.ticketType === "single-selector" ||
      purchase.ticketType === "full-conference-discounted" ||
      purchase.ticketType === "full-conference-discounted-sponsor" ||
      purchase.ticketType === "full-conference-discounted-student"
    ) {
      slno = slno + 1;
      let obj = {
        slNo: slno,
        firstName: purchase.firstName,
        lastName: purchase.lastName,
        email: purchase.email,
        mobile: purchase.mobile,
        purchasedOn: purchase.purchasedOn,
      };
      accessList.push(obj);
    }
  });

  // let accessList: any;
  // let bufferList: any = [];
  // let slno = 0;
  // purchasesArrFinal.forEach((purchase: any) => {
  //   if (
  //     purchase.ticketType === "full-conference" ||
  //     purchase.ticketType === "single-selector" ||
  //     purchase.ticketType === "full-conference-discounted" ||
  //     purchase.ticketType === "full-conference-discounted-sponsor" ||
  //     purchase.ticketType === "full-conference-discounted-student" ||
  //     purchase.ticketType === "track"
  //   ) {
  //     slno = slno + 1;
  //     let obj = {
  //       slNo: slno,
  //       firstName: purchase.firstName,
  //       lastName: purchase.lastName,
  //       email: purchase.email,
  //       purchasedOn: purchase.purchasedOn,
  //     };
  //     bufferList.push(obj);
  //   }
  // });

  // let uniqueValuesSet = new Set();
  // accessList = bufferList.filter((obj: any) => {
  //   let isPresentInSet = uniqueValuesSet.has(obj.email);
  //   uniqueValuesSet.add(obj.email);
  //   return !isPresentInSet;
  // });

  let resp = {
    status: "Success",
    msg: "Purchases retrieved",
    payload: {
      total: accessList.length,
      registrants: accessList,
    },
  };
  return res.json(resp);
});

export default router;
