import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel } from "../../models";
import moment from "moment";

const router = Router();

router.post("/purchases-summary", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let userAndPurchases: any = await userModel
    .find({}, "profile purchasedItems professional settings")
    .exec();

  if (!userAndPurchases) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve users",
    };
    return res.json(resp);
  }

  let purchasesArrRaw: any = [];

  let totalPurchases: number = 0;
  userAndPurchases.forEach((userPurchase: any) => {
    if (userPurchase.purchasedItems.length > 0) {
      userPurchase.purchasedItems.forEach(async (item: any) => {
        totalPurchases = totalPurchases + 1;
      });
    }
  });

  let { monthName } = req.body;
  let monthStartDate = moment().month(monthName).startOf("month").toISOString();
  let monthEndDate = moment().month(monthName).endOf("month").toISOString();
  let purchasesArrFinal: any = [];

  userAndPurchases.forEach(async (userPurchase: any) => {
    let persona = userObj.professional.occupation;
    if (userPurchase.purchasedItems.length > 0) {
      userPurchase.purchasedItems.forEach(async (item: any) => {
        let itemObj: any = {
          name: "",
          email: "",
          currency: "",
          purchaseDate: "",
          paymentStatus: "",
          isCouponApplied: false,
          couponName: "",
          couponDeductionType: "",
          couponDeductionValue: 0,
          cartAmountBeforeDeduction: 0,
          deductedAmount: 0,
          cartAmountAfterDeduction: 0,
          cartTotal: 0,
          gatewayFee: 0,
          grandTotal: 0,
          transactionId: "",
          purchaseId: "",
          paymentMethod: "",
          purchasedItems: [],
        };

        itemObj.name = `${userPurchase.profile.name.first} ${userPurchase.profile.name.last}`;
        itemObj.email = userPurchase.profile.email;
        itemObj.currency = "₹";
        itemObj.purchaseDate = item.createdAt;
        itemObj.paymentStatus = item.paymentStatus;

        itemObj.isCouponApplied = item.isCouponApplied;
        itemObj.couponName = item.coupon.name;
        itemObj.couponDeductionType = item.coupon.deductionType;
        itemObj.couponDeductionValue = item.coupon.deductionValue;
        itemObj.cartAmountBeforeDeduction =
          item.coupon.cartAmountBeforeDeduction;
        itemObj.deductedAmount = item.coupon.deductedAmount;
        itemObj.cartAmountAfterDeduction = item.coupon.cartAmountAfterDeduction;

        itemObj.cartTotal = item.cartTotal;
        itemObj.gatewayFee = item.gatewayFee;
        itemObj.grandTotal = item.grandTotal;
        itemObj.transactionId = item.transactionID;
        itemObj.purchaseId = item.purchaseID;
        itemObj.paymentMethod = item.paymentGateway;

        let purchasedItemIDs: any = [];
        item.purchasedItems.forEach((item: any) => {
          purchasedItemIDs.push(item.ticketID);
        });

        const purchasedTicketDetailsArr = await ticketModel
          .find()
          .where("ticketID")
          .in(purchasedItemIDs)
          .exec();

        let purchasedTicketsArr: Array<Object> = [];
        purchasedTicketDetailsArr.forEach((ticketDetails) => {
          let ticketPrice = 0;
          let ticketPriceArr = ticketDetails.price;
          let ticketTier = "";
          let subType = ticketDetails.subType;

          ticketPriceArr.forEach((item: any) => {
            let startTx = item.startDate;
            let endTx = item.endDate;
            let startDate = new Date(parseInt(startTx) * 1000).toISOString();
            let endDate = new Date(parseInt(endTx) * 1000).toISOString();
            let purchaseDate = itemObj.purchaseDate;
            let isBetween = moment(purchaseDate).isBetween(startDate, endDate);
            if (isBetween) {
              if (subType === "course") {
                if (persona === item.name.toLowerCase()) {
                  ticketTier = item.name;
                  ticketPrice = item.price.inr;
                }
              } else {
                ticketPrice = item.price.inr;
                ticketTier = item.name;
              }
            }
          });
          let purchasedTicketObj = {
            title: ticketDetails.title,
            subTitle: ticketDetails.subTitle,
            tier: ticketTier,
            price: ticketPrice,
          };
          purchasedTicketsArr.push(purchasedTicketObj);
        });
        itemObj.purchasedItems = purchasedTicketsArr;
        purchasesArrRaw.push(itemObj);

        if (totalPurchases === purchasesArrRaw.length) {
          purchasesArrRaw.sort((a: any, b: any) => {
            let ApurchaseDate: any = new Date(a.purchaseDate);
            let BpurchaseDate: any = new Date(b.purchaseDate);
            return BpurchaseDate - ApurchaseDate;
          });

          purchasesArrRaw.map((purchasedItem: any) => {
            if (monthName.length > 0) {
              let isPurchaseInBetween = moment(
                purchasedItem.purchaseDate
              ).isBetween(monthStartDate, monthEndDate);
              if (isPurchaseInBetween) {
                purchasesArrFinal.push(purchasedItem);
              }
            } else {
              purchasesArrFinal.push(purchasedItem);
            }
          });

          let resp = {
            status: "Success",
            msg: "Purchases retrieved",
            payload: purchasesArrFinal,
          };
          return res.json(resp);
        }
      });
    }
  });
});

export default router;
