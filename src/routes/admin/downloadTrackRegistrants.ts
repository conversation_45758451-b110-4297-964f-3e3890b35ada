import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel } from "../../models";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/downloadtrackregistrants/:trackTicketTitleRaw?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let userAndPurchases: any = await userModel
      .find({}, "profile professional purchasedItems")
      .exec();

    if (!userAndPurchases) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let trackTicketTitleRaw = req.params.trackTicketTitleRaw;
    let trackTicketTitle = decodeURIComponent(trackTicketTitleRaw);

    let ticketsArr: any = await ticketModel
      .find({}, "ticketID type subType title subTitle price")
      .exec();

    let trackTicketID: any = "";
    ticketsArr.forEach((ticket: any) => {
      if (trackTicketTitle === ticket.title) {
        trackTicketID = ticket.ticketID;
      }
    });
    let trackRegistrants: any = [];
    userAndPurchases.forEach((userPurchase: any) => {
      if (userPurchase.purchasedItems.length > 0) {
        let profile = userPurchase.profile;
        let allPurchases = userPurchase.purchasedItems;
        allPurchases.forEach((unitPurchase: any) => {
          unitPurchase.purchasedItems.forEach((ticket: any) => {
            let obj = {
              name: profile.name.first + " " + profile.name.last,
              email: profile.email,
            };
            if (trackTicketID === ticket.ticketID) {
              trackRegistrants.push(obj);
            }
          });
        });
      }
    });

    const fields = [
      {
        label: "Name",
        value: "name",
      },
      {
        label: "Email",
        value: "email",
      },
    ];

    let filename = trackTicketTitle;
    const json2csv = new Parser({ fields });
    const csv = json2csv.parse(trackRegistrants);
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}.csv`
    );
    return res.send(csv);
  }
);

export default router;
