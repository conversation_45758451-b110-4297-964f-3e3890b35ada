import { Router } from "express";
import { userModel, ticketModel_v2 } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";

const router = Router();

router.post(
  "/get-ticket-types-v2",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /*-----------------------
    Check if user is LoggedIn
    -----------------------*/
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /*-----------------------------
    Get user details for processing
    -----------------------------*/
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({ _userID });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    /* ---------------------------------
    Extract ticket details from req.body
    --------------------------------- */
    let { eventCode } = req.body;

    /*---------------------
    Query ticket and return
    ---------------------*/
    const ticketsArr: any = await ticketModel_v2
      .find({ eventCode: eventCode })
      .exec();

    let finalTicketArr: any = [];
    ticketsArr.map((ticket: any) => {
      let title: string = "";
      if (ticket.ticketType === "fullTicket") {
        title = ticket.fullTicketDetails.title;
      } else if (ticket.ticketType === "basicTicket") {
        title = ticket.basicTicketDetails.title;
      } else if (ticket.ticketType === "partialTicket") {
        title = "Partial ticket";
      } else if (ticket.ticketType === "trackTicket") {
        title = "Track ticket";
      }
      let obj = {
        id: ticket.ticketId,
        title: title,
        type: ticket.ticketType,
      };
      finalTicketArr.push(obj);
    });

    let resp = {
      status: "Success",
      msg: "Ticket data fetched",
      payload: finalTicketArr,
    };

    return res.json(resp);
  }
);

export default router;
