import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { userModel } from "../../../models";
import { Parser } from "json2csv";

const router = Router();

router.get(
  "/download-accounts-v2/:eventCode?/:persona?/:membership?/:purchaseStatus?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -----------------
    Extract route params
    ----------------- */
    let eventCode: string = req.params.eventCode;
    let persona: string = req.params.persona;
    let membership: string = req.params.membership;
    let purchaseStatus: string = req.params.purchaseStatus;
    let filename = "Accounts";

    let users: any;
    if (persona === "-" && membership === "-") {
      users = await userModel
        .find(
          {},
          "profile.name profile.email profile.mobile profile.country professional membership purchasedItems settings createdAt"
        )
        .sort({ createdAt: -1 })
        .exec();
    }

    if (persona != "-" && membership != "-") {
      let isMember: boolean = false;
      if (membership === "member") {
        isMember = true;
      }
      users = await userModel
        .find(
          {
            "professional.occupation": persona,
            "membership.isMember": isMember,
          },
          "profile.name profile.email profile.mobile profile.country professional membership purchasedItems settings createdAt"
        )
        .sort({ createdAt: -1 })
        .exec();
      filename = `${filename}-${persona}-${membership}`;
    } else if (persona != "-") {
      users = await userModel
        .find(
          { "professional.occupation": persona },
          "profile.name profile.email profile.mobile profile.country professional membership purchasedItems settings createdAt"
        )
        .sort({ createdAt: -1 })
        .exec();
      filename = `${filename}-${persona}`;
    } else if (membership != "-") {
      let isMember: boolean = false;
      if (membership === "member") {
        isMember = true;
      } else if (membership === "non-member") {
        isMember = false;
      }
      users = await userModel
        .find(
          { "membership.isMember": isMember },
          "profile.name profile.email profile.mobile profile.country professional membership purchasedItems settings createdAt"
        )
        .sort({ createdAt: -1 })
        .exec();
      filename = `${filename}-${membership}`;
    }

    if (!users) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let payload: any = [];
    users.forEach((user: any) => {
      let hasPurchaseInEventCode: boolean = false;
      let totalPurchasedItems = 0;
      user.purchasedItems.forEach((item: any) => {
        if (eventCode === item.eventCode) {
          hasPurchaseInEventCode = true;
          totalPurchasedItems =
            totalPurchasedItems + item.purchasedItems.length;
        }
      });

      let mobileNo: string = "";
      if (user.profile.mobile.isdCode.length > 0) {
        mobileNo = `+${user.profile.mobile.isdCode}`;
      }
      if (user.profile.mobile.number.length > 0) {
        mobileNo = `${mobileNo}-${user.profile.mobile.number}`;
      }

      let obj = {
        name: `${user.profile.name.first} ${user.profile.name.last}`,
        email: user.profile.email,
        mobile: mobileNo,
        purchasedItems: user.purchasedItems,
        country: user.profile.country,
        occupation: user.professional.occupation,
        orgInsti: user.professional.orgInsti,
        jobDegree: user.professional.jobDegree,
        isMember: user.membership.isMember,
        membershipId: user.membership.id,
        memberType: user.membership.type,
        createdAt: new Date(user.createdAt),
      };

      if (purchaseStatus != "-") {
        if (purchaseStatus === "withoutPurchases") {
          if (!hasPurchaseInEventCode) {
            payload.push(obj);
          }
        } else if (purchaseStatus === "withPurchases") {
          if (hasPurchaseInEventCode) {
            payload.push(obj);
          }
        }
      } else {
        payload.push(obj);
      }
    });

    if (purchaseStatus != "-") {
      filename = `${filename}-${purchaseStatus}`;
    }

    let fields = [
      {
        label: "Name",
        value: "name",
      },
      { label: "Email", value: "email" },
      { label: "Country", value: "country" },
      { label: "Occupation", value: "occupation" },
      { label: "Organisation/Institution", value: "orgInsti" },
      { label: "Job/Degree", value: "jobDegree" },
      { label: "Is Member?", value: "isMember" },
      { label: "Membership ID", value: "membershipId" },
      { label: "Membership Type", value: "memberType" },
      { label: "Account creation", value: "createdAt" },
    ];

    let csv: any;
    const json2csv = new Parser({ fields });
    csv = json2csv.parse(payload);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}.csv`
    );
    return res.send(csv);
  }
);

export default router;
