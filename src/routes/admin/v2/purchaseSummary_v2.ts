import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { userModel, ticketModel_v2, eventModel } from "../../../models";
import moment from "moment";

const router = Router();

router.post(
  "/purchases-summary-v2",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------------
    Check if user is logged in
    ----------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ----------------
    Find if user exists
    ---------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    // if (!userObj.settings.isAdmin) {
    //   if (!userObj.settings.isRegistrationManager) {
    //     let resp = {
    //       status: "Failed",
    //       msg: "Access Denied",
    //     };
    //     return res.json(resp);
    //   }
    // }

    let userAndPurchases: any = await userModel
      .find({}, "profile purchasedItems professional settings")
      .exec();

    if (!userAndPurchases) {
      let resp = {
        status: "Failed",
        msg: "Could not retrieve users",
      };
      return res.json(resp);
    }

    let purchasesArrRaw: any = [];
    let { monthName, eventCode } = req.body;

    // Get Event Details
    let eventObj: any = await eventModel.findOne({ code: eventCode });
    let registrationStartDate: any = eventObj.schedule.registration.startsOn;
    let registrationEndDate: any = eventObj.schedule.registration.endsOn;

    let totalPurchases: number = 0;
    userAndPurchases.forEach((userPurchase: any) => {
      if (userPurchase.purchasedItems.length > 0) {
        userPurchase.purchasedItems.forEach(async (item: any) => {
          if (item.eventCode) {
            if (
              item.eventCode === eventCode ||
              item.eventCode === "membership"
            ) {
              totalPurchases = totalPurchases + 1;
            }
          }
        });
      }
    });

    if (totalPurchases === 0) {
      let resp = {
        status: "Success",
        msg: "No Purchases",
        payload: [],
      };
      return res.json(resp);
    }

    let monthStartDate = moment()
      .month(monthName)
      .startOf("month")
      .toISOString();
    let monthEndDate = moment().month(monthName).endOf("month").toISOString();
    let purchasesArrFinal: any = [];
    let purchasesArrFiltered: any = [];

    userAndPurchases.forEach(async (userPurchase: any) => {
      let persona = userPurchase.professional.occupation;
      if (userPurchase.purchasedItems.length > 0) {
        userPurchase.purchasedItems.forEach(async (item: any) => {
          let itemObj: any = {
            eventCode: "",
            name: "",
            email: "",
            persona: "",
            currency: "",
            purchaseDate: "",
            paymentStatus: "",
            isCouponApplied: false,
            couponName: "",
            couponDeductionType: "",
            couponDeductionValue: 0,
            cartAmountBeforeDeduction: 0,
            deductedAmount: 0,
            cartAmountAfterDeduction: 0,
            cartTotal: 0,
            gatewayFee: 0,
            grandTotal: 0,
            transactionId: "",
            purchaseId: "",
            paymentMethod: "",
            purchasedItems: [],
            isStudentVerified: false,
          };

          itemObj.eventCode = item.eventCode;
          itemObj.name = `${userPurchase.profile.name.first} ${userPurchase.profile.name.last}`;
          itemObj.email = userPurchase.profile.email;
          itemObj.persona = persona;
          itemObj.currency = "₹";
          itemObj.purchaseDate = item.createdAt;
          itemObj.paymentStatus = item.paymentStatus;
          itemObj.isCouponApplied = item.isCouponApplied;
          itemObj.couponName = item.coupon.name;
          itemObj.couponDeductionType = item.coupon.deductionType;
          itemObj.couponDeductionValue = item.coupon.deductionValue;
          itemObj.cartAmountBeforeDeduction =
            item.coupon.cartAmountBeforeDeduction;
          itemObj.deductedAmount = item.coupon.deductedAmount;
          itemObj.cartAmountAfterDeduction =
            item.coupon.cartAmountAfterDeduction;
          itemObj.cartTotal = item.cartTotal;
          itemObj.gatewayFee = item.gatewayFee;
          itemObj.grandTotal = item.grandTotal;
          itemObj.transactionId = item.transactionID;
          itemObj.purchaseId = item.purchaseID;
          itemObj.paymentMethod = item.paymentGateway;
          itemObj.isStudentVerified =
            userPurchase.professional.idCard.isVerified;

          let purchasedItemIDs: any = [];
          let purchasedSessionIds: any = [];
          item.purchasedItems.forEach((item: any) => {
            if (item.eventCode) {
              if (item.eventCode === eventCode) {
                purchasedItemIDs.push(item.ticketId);
                if (item.sessionId) {
                  purchasedSessionIds.push(item.sessionId);
                }
              }
            }
          });

          let purchasedTicketDetailsArr: any = [];
          if (purchasedItemIDs.length > 0) {
            purchasedTicketDetailsArr = await ticketModel_v2
              .find()
              .where("ticketId")
              .in(purchasedItemIDs)
              .exec();
          }

          let purchasedTicketsArr: Array<Object> = [];
          purchasedTicketDetailsArr.map((ticket: any) => {
            let ticketPrice = 0;
            let ticketTier = "";
            let ticketTitle = "";
            let tierStudentPrice: number = 0;
            let tierProfessionalPrice: number = 0;
            let ticketPricingTiers: any;
            let purchasedTicketObj: any = {
              title: "",
              tier: "",
              price: "",
            };

            if (ticket.ticketType === "fullTicket") {
              ticketPricingTiers = ticket.fullTicketDetails.tiers;
              ticketTitle = ticket.fullTicketDetails.title;
              ticketPricingTiers.map((tier: any) => {
                let purchaseDate = item.createdAt;
                let isBetween: any = moment(purchaseDate).isBetween(
                  tier.tierStartDate,
                  tier.tierEndDate
                );
                if (isBetween) {
                  ticketTier = tier.tierName;
                  tierStudentPrice = tier.studentPrice;
                  tierProfessionalPrice = tier.professionalPrice;
                }
              });

              if (persona === "student") {
                ticketPrice = tierStudentPrice;
              } else if (persona === "professional") {
                ticketPrice = tierProfessionalPrice;
              }

              purchasedTicketObj.title = ticketTitle;
              purchasedTicketObj.tier = `${persona.toUpperCase()} - ${ticketTier}`;
              purchasedTicketObj.price = ticketPrice;
            } else if (ticket.ticketType === "basicTicket") {
              ticketPricingTiers = ticket.basicTicketDetails.tiers;
              ticketTitle = ticket.basicTicketDetails.title;
              ticketPricingTiers.map((tier: any) => {
                let purchaseDate = item.createdAt;
                let isBetween: any = moment(purchaseDate).isBetween(
                  tier.tierStartDate,
                  tier.tierEndDate
                );
                if (isBetween) {
                  ticketTier = tier.tierName;
                  tierStudentPrice = tier.studentPrice;
                  tierProfessionalPrice = tier.professionalPrice;
                }
              });

              if (persona === "student") {
                ticketPrice = tierStudentPrice;
              } else if (persona === "professional") {
                ticketPrice = tierProfessionalPrice;
              }

              purchasedTicketObj.title = ticketTitle;
              purchasedTicketObj.tier = `${persona.toUpperCase()} - ${ticketTier}`;
              purchasedTicketObj.price = ticketPrice;
            } else if (ticket.ticketType === "trackTicket") {
              let trackSessions: any = ticket.trackTicketDetails.sessions;
              purchasedSessionIds.map((purchasedSessionId: any) => {
                trackSessions.map((trackSession: any) => {
                  if (purchasedSessionId === trackSession.id) {
                    purchasedTicketsArr.push({
                      title: trackSession.title,
                      tier: trackSession.type,
                      price:
                        persona === "professional"
                          ? trackSession.professionalPrice
                          : trackSession.studentPrice,
                    });
                  }
                });
              });
            }

            if (
              ticket.ticketType === "fullTicket" ||
              ticket.ticketType === "basicTicket"
            ) {
              purchasedTicketsArr.push(purchasedTicketObj);
            }
          });

          if (item.eventCode === "membership") {
            let membershipType: string = item.purchasedItems[0].ticketID;
            let membershipName: string = "";

            if (membershipType === "lifetimeMembership") {
              membershipName = "LIFETIME";
            } else if (membershipType === "annualMembership") {
              membershipName = "ANNUAL";
            }

            let purchasedTicketObj: any = {
              title: "HCIPAI Membership",
              tier: membershipName,
              price: item.cartTotal,
            };
            purchasedTicketsArr.push(purchasedTicketObj);
          }

          itemObj.purchasedItems = purchasedTicketsArr;

          if (
            itemObj.eventCode === eventCode ||
            itemObj.eventCode === "membership"
          ) {
            purchasesArrRaw.push(itemObj);
          }

          if (totalPurchases === purchasesArrRaw.length) {
            purchasesArrRaw.sort((a: any, b: any) => {
              let ApurchaseDate: any = new Date(a.purchaseDate);
              let BpurchaseDate: any = new Date(b.purchaseDate);
              return BpurchaseDate - ApurchaseDate;
            });

            purchasesArrRaw.map((purchasedItem: any) => {
              if (monthName.length > 0) {
                let isPurchaseInBetween = moment(
                  purchasedItem.purchaseDate
                ).isBetween(monthStartDate, monthEndDate);
                if (isPurchaseInBetween) {
                  purchasesArrFinal.push(purchasedItem);
                }
              } else {
                purchasesArrFinal.push(purchasedItem);
              }
            });

            purchasesArrFinal.map((finalItem: any) => {
              let isSaleInVetween: any = moment(
                finalItem.purchaseDate
              ).isBetween(registrationStartDate, registrationEndDate);
              if (isSaleInVetween) {
                purchasesArrFiltered.push(finalItem);
              }
            });

            let resp = {
              status: "Success",
              msg: "Purchases retrieved",
              payload: purchasesArrFiltered,
            };
            return res.json(resp);
          }
        });
      }
    });
  }
);

export default router;
