import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { userModel, ticketModel_v2, alertModel } from "../../../models";
import moment from "moment";

const router = Router();

router.post("/overview-v2", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let { eventCode, filterType, startDate, endDate } = req.body;

  let users: any;
  let tickets: any;

  users = await userModel
    .find({}, "profile professional membership purchasedItems")
    .exec();
  tickets = await ticketModel_v2
    .find(
      { eventCode: eventCode },
      "ticketId eventCode ticketType fullTicketDetails basicTicketDetails"
    )
    .exec();

  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let totalRegistrantCount = 0;
  let studentCount = 0;
  let professionalCount = 0;
  let memberCount = 0;
  let nonMemberCount = 0;
  let registrantsWithPurchases = 0;
  let registrantsWithoutPurchases = 0;

  let totalPurchaseCount = 0;
  let fullConferenceCount = 0;
  let fullConferenceSales = 0;
  let fullConferenceNonMemberCount = 0;
  let fullConferenceNonMemberSales = 0;
  let fullConferenceStudentCount = 0;
  let fullConferenceStudentSales = 0;
  let fullConferenceMemberCount = 0;
  let fullConferenceMemberSales = 0;
  let membershipCount = 0;
  let membershipSales = 0;
  let lifetimeMembershipCount = 0;
  let lifetimeMembershipSales = 0;
  let annualMembershipCount = 0;
  let annualMembershipSales = 0;
  let totalSales = 0;
  let verifiedSales = 0;
  let verifiedSalesCount = 0;
  let unVerifiedSales = 0;
  let unVerifiedSalesCount = 0;
  let totalWorkshopCount = 0;
  let totalWorkshopSales = 0;
  let totalCourseCount = 0;
  let totalCourseSales = 0;
  let purchasedItemIDs: any = [];
  let unitWorkshopSale: any = [];
  let unitCourseSale: any = [];
  let razorpaySales = 0;
  let razorpayCount = 0;
  let bankTransferSales = 0;
  let bankTransferCount = 0;
  let partialConferenceCount = 0;
  let partialConferenceSales = 0;
  let partialConferenceSingleDayCount = 0;
  let partialConferenceSingleDaySales = 0;
  let partialConferenceDoubleDayCount = 0;
  let partialConferenceDoubleDaySales = 0;
  let guestTicketCount = 0;
  let guestTicketSales = 0;
  let sponsor100Count = 0;
  let sponsor100Sales = 0;
  let sponsor50Count = 0;
  let sponsor50Sales = 0;
  let others50Sales = 0;
  let others50Count = 0;

  // Unicorn Track
  let unicornStartup_Count = 0;
  let unicornStartup_Sales = 0;
  // Day pass
  let dayPass09th_Count = 0;
  let dayPass09th_Sales = 0;
  let dayPass10th_Count = 0;
  let dayPass10th_Sales = 0;
  let dayPass11th_Count = 0;
  let dayPass11th_Sales = 0;
  // Digital Pass
  let digitalPass_Count = 0;
  let digitalPass_Sales = 0;
  // Digital Day Pass
  let digitalPass09th_Count = 0;
  let digitalPass09th_Sales = 0;
  let digitalPass10th_Count = 0;
  let digitalPass10th_Sales = 0;
  let digitalPass11th_Count = 0;
  let digitalPass11th_Sales = 0;

  users.forEach((user: any) => {
    let totalPurchasedItems = 0;
    let persona: string = user.professional.occupation;
    let isMember: boolean = user.membership.isMember;
    let isTicketPurchased: boolean = false;

    if (user.purchasedItems.length > 0) {
      if (filterType === "eventCode") {
        user.purchasedItems.forEach((purchasedItemObj: any) => {
          if (eventCode === purchasedItemObj.eventCode) {
            totalPurchasedItems =
              totalPurchasedItems + purchasedItemObj.purchasedItems.length;
            totalPurchaseCount = totalPurchaseCount + totalPurchasedItems;
            purchasedItemObj.purchasedItems.forEach(
              (purchaseTicketObj: any) => {
                let ticketId: string = "";
                if (eventCode === "membership") {
                  ticketId = purchaseTicketObj.ticketID;
                } else {
                  ticketId = purchaseTicketObj.ticketId;
                }
                let obj = {
                  persona: persona,
                  isMember: isMember,
                  ticketId: ticketId,
                };
                purchasedItemIDs.push(obj);
              }
            );

            purchasedItemIDs.forEach((item: any) => {
              tickets.forEach((ticket: any) => {
                if (item.ticketId === ticket.ticketId) {
                  if (
                    ticket.ticketType === "fullTicket" ||
                    ticket.ticketType === "partialTicket" ||
                    ticket.ticketType === "basicTicket"
                  ) {
                    isTicketPurchased = true;
                  }
                }
              });
            });
          }
        });
      }
    }

    if (isTicketPurchased) {
      registrantsWithPurchases = registrantsWithPurchases + 1;
    } else {
      registrantsWithoutPurchases = registrantsWithoutPurchases + 1;
    }

    if (persona === "student") {
      studentCount = studentCount + 1;
    } else if (persona === "professional") {
      professionalCount = professionalCount + 1;
    }

    if (isMember) {
      memberCount = memberCount + 1;
    } else {
      nonMemberCount = nonMemberCount + 1;
    }
    totalRegistrantCount = totalRegistrantCount + 1;
  });

  purchasedItemIDs.forEach((item: any) => {
    tickets.forEach((ticket: any) => {
      if (item.ticketId === ticket.ticketId) {
        if (ticket.ticketType === "fullTicket") {
          if (item.isMember) {
            fullConferenceMemberCount = fullConferenceMemberCount + 1;
          } else {
            if (item.persona === "student") {
              fullConferenceStudentCount = fullConferenceStudentCount + 1;
            } else if (item.persona === "professional") {
              fullConferenceNonMemberCount = fullConferenceNonMemberCount + 1;
            }
          }
          fullConferenceCount = fullConferenceCount + 1;
        }
      }
    });
  });

  let purchasesArrRaw: any = [];
  users.forEach((user: any) => {
    if (user.purchasedItems.length > 0) {
      let allPurchases = user.purchasedItems;
      allPurchases.forEach((unitPurchase: any) => {
        unitPurchase.purchasedItems.forEach((ticket: any) => {
          let ticketId: string = "";
          let membershipPrice: number = 0;
          if (
            ticket.ticketID === "lifetimeMembership" ||
            ticket.ticketID === "annualMembership"
          ) {
            ticketId = ticket.ticketID;
            membershipPrice = unitPurchase.grandTotal;
          } else {
            let eventCode: string = ticket["eventCode"];
            if (eventCode) {
              ticketId = ticket.ticketId;
            } else {
              ticketId = ticket.ticketID;
            }
          }

          let isMember: boolean = user.membership.isMember;
          let obj = {
            email: user.profile.email,
            ticket: ticketId,
            persona: user.professional.occupation,
            paymentStatus: unitPurchase.paymentStatus,
            paymentGateway: unitPurchase.paymentGateway,
            membershipPrice: membershipPrice,
            createdAt: unitPurchase.createdAt,
            isMember: isMember,
            isCouponApplied: unitPurchase.isCouponApplied,
            deductionType: unitPurchase.coupon.deductionType,
            deductedAmount: unitPurchase.coupon.deductedAmount,
          };
          purchasesArrRaw.push(obj);
        });
      });
    }
  });

  purchasesArrRaw.map((purchase: any) => {
    let ticketPrice = 0;
    tickets.map((ticket: any) => {
      if (purchase.ticket === ticket.ticketId) {
        let ticketPricingTiers: any;
        if (ticket.ticketType === "fullTicket") {
          ticketPricingTiers = ticket.fullTicketDetails.tiers;
        } else if (ticket.ticketType === "basicTicket") {
          ticketPricingTiers = ticket.basicTicketDetails.tiers;
        }
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        ticketPricingTiers.map((tier: any) => {
          let purchaseDate = purchase.createdAt;
          let isBetween: any = moment(purchaseDate).isBetween(
            tier.tierStartDate,
            tier.tierEndDate
          );

          if (isBetween) {
            tierStudentPrice = tier.studentPrice;
            tierProfessionalPrice = tier.professionalPrice;
          }
        });

        if (purchase.persona === "student") {
          ticketPrice = tierStudentPrice;
        } else if (purchase.persona === "professional") {
          ticketPrice = tierProfessionalPrice;
        }

        /* ----------------
        Coupon calculations
        -----------------*/
        if (purchase.isCouponApplied) {
          if (
            purchase.deductionType === "ticketType" ||
            purchase.deductionType === "percentage" ||
            purchase.deductionType === "fixed"
          ) {
            ticketPrice = ticketPrice - purchase.deductedAmount;
          }
        }

        if (ticket.ticketType === "fullTicket") {
          if (purchase.isMember) {
            fullConferenceMemberSales = fullConferenceMemberSales + ticketPrice;
          } else {
            if (purchase.persona === "student") {
              fullConferenceStudentSales =
                fullConferenceStudentSales + ticketPrice;
            } else if (purchase.persona === "professional") {
              fullConferenceNonMemberSales =
                fullConferenceNonMemberSales + ticketPrice;
            }
          }
          fullConferenceSales = fullConferenceSales + ticketPrice;
        }

        if (ticket.ticketType === "basicTicket") {
          ticketPricingTiers = ticket.basicTicketDetails.tiers;
          let ticketTitle = ticket.basicTicketDetails.title;
          ticketPricingTiers.map((tier: any) => {
            let purchaseDate = purchase.createdAt;
            let isBetween: any = moment(purchaseDate).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );
            if (isBetween) {
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });

          if (purchase.persona === "student") {
            ticketPrice = tierStudentPrice;
          } else if (purchase.persona === "professional") {
            ticketPrice = tierProfessionalPrice;
          }

          if (purchase.isCouponApplied) {
            if (
              purchase.deductionType === "ticketType" ||
              purchase.deductionType === "percentage" ||
              purchase.deductionType === "fixed"
            ) {
              ticketPrice = ticketPrice - purchase.deductedAmount;
            }
          }

          if (ticketTitle === "Flying Unicorn startup track") {
            unicornStartup_Count = unicornStartup_Count + 1;
            unicornStartup_Sales = unicornStartup_Sales + ticketPrice;
          } else if (
            ticketTitle === "India HCI 2022 Day Pass (09th Nov 2022)"
          ) {
            dayPass09th_Count = dayPass09th_Count + 1;
            dayPass09th_Sales = dayPass09th_Sales + ticketPrice;
          } else if (
            ticketTitle === "India HCI 2022 Day Pass (10th Nov 2022)"
          ) {
            dayPass10th_Count = dayPass10th_Count + 1;
            dayPass10th_Sales = dayPass10th_Sales + ticketPrice;
          } else if (
            ticketTitle === "India HCI 2022 Day Pass (11th Nov 2022)"
          ) {
            dayPass11th_Count = dayPass11th_Count + 1;
            dayPass11th_Sales = dayPass11th_Sales + ticketPrice;
          } else if (ticketTitle === "Digital Pass") {
            digitalPass_Count = digitalPass_Count + 1;
            digitalPass_Sales = digitalPass_Sales + ticketPrice;
          } else if (ticketTitle === "Digital Pass (09th Nov 2022)") {
            digitalPass09th_Count = digitalPass09th_Count + 1;
            digitalPass09th_Sales = digitalPass09th_Sales + ticketPrice;
          } else if (ticketTitle === "Digital Pass (10th Nov 2022)") {
            digitalPass10th_Count = digitalPass10th_Count + 1;
            digitalPass10th_Sales = digitalPass10th_Sales + ticketPrice;
          } else if (ticketTitle === "Digital Pass (11th Nov 2022)") {
            digitalPass11th_Count = digitalPass11th_Count + 1;
            digitalPass11th_Sales = digitalPass11th_Sales + ticketPrice;
          }
        }

        if (purchase.paymentStatus === "purchased") {
          verifiedSales = verifiedSales + ticketPrice;
          verifiedSalesCount = verifiedSalesCount + 1;
        } else if (purchase.paymentStatus === "under-verification") {
          unVerifiedSales = unVerifiedSales + ticketPrice;
          unVerifiedSalesCount = unVerifiedSalesCount + 1;
        }

        if (purchase.paymentGateway === "BankTransfer") {
          bankTransferCount = bankTransferCount + 1;
          bankTransferSales = bankTransferSales + ticketPrice;
        } else if (purchase.paymentGateway === "Razorpay") {
          razorpayCount = razorpayCount + 1;
          razorpaySales = razorpaySales + ticketPrice;
        }
      }
    });

    /*---------------------
    Membership calculations
    ---------------------*/
    if (purchase.ticket === "lifetimeMembership") {
      lifetimeMembershipSales =
        lifetimeMembershipSales + purchase.membershipPrice;
      lifetimeMembershipCount = lifetimeMembershipCount + 1;
      membershipSales =
        membershipSales + lifetimeMembershipSales + annualMembershipSales;
      membershipCount =
        membershipCount + lifetimeMembershipCount + annualMembershipCount;
    } else if (purchase.ticket === "annualMembership") {
      annualMembershipSales = annualMembershipSales + purchase.membershipPrice;
      annualMembershipCount = annualMembershipCount + 1;
      membershipSales =
        membershipSales + lifetimeMembershipSales + annualMembershipSales;
      membershipCount =
        membershipCount + lifetimeMembershipCount + annualMembershipCount;
    }

    if (
      purchase.ticket === "lifetimeMembership" ||
      purchase.ticket === "annualMembership"
    ) {
      if (purchase.paymentStatus === "purchased") {
        verifiedSales = verifiedSales + purchase.membershipPrice;
        verifiedSalesCount = verifiedSalesCount + 1;
      } else if (purchase.paymentStatus === "under-verification") {
        unVerifiedSales = unVerifiedSales + purchase.membershipPrice;
        unVerifiedSalesCount = unVerifiedSalesCount + 1;
      }

      if (purchase.paymentGateway === "BankTransfer") {
        bankTransferCount = bankTransferCount + 1;
        bankTransferSales = bankTransferSales + purchase.membershipPrice;
      } else if (purchase.paymentGateway === "Razorpay") {
        razorpayCount = razorpayCount + 1;
        razorpaySales = razorpaySales + purchase.membershipPrice;
      }
    }

    totalSales = totalSales + ticketPrice;
  });

  let alerts = await alertModel.find({
    "status.isActive": true,
  });

  let nonFullTicketTotal_Count: number =
    unicornStartup_Count +
    dayPass09th_Count +
    dayPass10th_Count +
    dayPass11th_Count +
    digitalPass_Count +
    digitalPass09th_Count +
    digitalPass10th_Count +
    digitalPass11th_Count;

  let nonFullTicketTotal_Sales: number =
    unicornStartup_Sales +
    dayPass09th_Sales +
    dayPass10th_Sales +
    dayPass11th_Sales +
    digitalPass_Sales +
    digitalPass09th_Sales +
    digitalPass10th_Sales +
    digitalPass11th_Sales;

  let payload = {
    registrants: {
      total: totalRegistrantCount,
      student: studentCount,
      professional: professionalCount - memberCount,
      member: memberCount,
      nonMember: nonMemberCount,
      withPurchases: registrantsWithPurchases,
      withoutPurchases: registrantsWithoutPurchases,
    },
    purchases: {
      allPurchases: {
        count: totalPurchaseCount,
        sales: {
          total: totalSales,
          confirmed: verifiedSales,
          confirmedCount: verifiedSalesCount,
          unconfirmed: unVerifiedSales,
          unconfirmedCount: unVerifiedSalesCount,
          razorpaySales: razorpaySales,
          razorpayCount: razorpayCount,
          bankTransferSales: bankTransferSales,
          bankTransferCount: bankTransferCount,
        },
      },
      fullConference: {
        count: {
          total: fullConferenceCount,
          member: fullConferenceMemberCount,
          nonMember: fullConferenceNonMemberCount,
          student: fullConferenceStudentCount,
          guest: guestTicketCount,
          sponsor100: sponsor100Count,
          sponsor50: sponsor50Count,
          others50: others50Count,
        },
        sales: {
          total: fullConferenceSales,
          member: fullConferenceMemberSales,
          nonMember: fullConferenceNonMemberSales,
          student: fullConferenceStudentSales,
          guest: guestTicketSales,
          sponsor100: sponsor100Sales,
          sponsor50: sponsor50Sales,
          others50: others50Sales,
        },
      },
      partialConference: {
        count: {
          total: partialConferenceCount,
          singleDay: partialConferenceSingleDayCount,
          doubleDay: partialConferenceDoubleDayCount,
        },
        sales: {
          total: partialConferenceSales,
          singleDay: partialConferenceSingleDaySales,
          doubleDay: partialConferenceDoubleDaySales,
        },
      },
      membership: {
        count: {
          total: membershipCount,
          annual: annualMembershipCount,
          lifetime: lifetimeMembershipCount,
        },
        sales: {
          total: membershipSales,
          annual: annualMembershipSales,
          lifetime: lifetimeMembershipSales,
        },
      },
      workshops: {
        totalCount: totalWorkshopCount,
        totalSale: totalWorkshopSales,
        unitSale: unitWorkshopSale,
      },
      courses: {
        totalCount: totalCourseCount,
        totalSale: totalCourseSales,
        unitSale: unitCourseSale,
      },
      indiaHci2022: {
        count: {
          unicornStartup_Count: unicornStartup_Count,
          dayPass09th_Count: dayPass09th_Count,
          dayPass10th_Count: dayPass10th_Count,
          dayPass11th_Count: dayPass11th_Count,
          digitalPass_Count: digitalPass_Count,
          digitalPass09th_Count: digitalPass09th_Count,
          digitalPass10th_Count: digitalPass10th_Count,
          digitalPass11th_Count: digitalPass11th_Count,
          nonFullTicketTotal_Count: nonFullTicketTotal_Count,
        },
        sales: {
          unicornStartup_Sales: unicornStartup_Sales,
          dayPass09th_Sales: dayPass09th_Sales,
          dayPass10th_Sales: dayPass10th_Sales,
          dayPass11th_Sales: dayPass11th_Sales,
          digitalPass_Sales: digitalPass_Sales,
          digitalPass09th_Sales: digitalPass09th_Sales,
          digitalPass10th_Sales: digitalPass10th_Sales,
          digitalPass11th_Sales: digitalPass11th_Sales,
          nonFullTicketTotal_Sales: nonFullTicketTotal_Sales,
        },
      },
    },
    notificationCount: alerts.length,
  };

  let resp = {
    status: "Success",
    msg: "Legacy members retrieved",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
