// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { alertModel, userModel } from "../../models";
import {
  sendBankTransferVerificationMail,
  sendIdVerificationMail,
} from "../../helpers";
import moment from "moment";

const router = Router();

router.post("/resolvealerts", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   if (!userObj.settings.isRegistrationManager) {
  //     let resp = {
  //       status: "Failed",
  //       msg: "Access Denied",
  //     };
  //     return res.json(resp);
  //   }
  // }

  let resolverFirstName = userObj.profile.name.first;
  let resolverLastName = userObj.profile.name.last;
  let resolverEmail = userObj.profile.email;
  let resolvedOn = moment().toISOString();

  let { alertId } = req.body;
  const alert: any = await alertModel.findOne({
    id: alertId,
  });

  let eventCode: string = alert.eventCode;

  if (alert.type === "Student ID Verification") {
    let issuerEmail = alert.issuer.email;
    let filter = {
      id: alertId,
    };
    let update = {
      "resolver.name.first": resolverFirstName,
      "resolver.name.middle": "",
      "resolver.name.last": resolverLastName,
      "resolver.email": resolverEmail,
      "status.isActive": false,
      "timestamp.resolvedOn": resolvedOn,
    };
    await alertModel.findOneAndUpdate(filter, update);
    const issuer: any = await userModel.findOne({
      "profile.email": issuerEmail,
    });

    let firstName: string = issuer.profile.name.first;

    await userModel.updateOne(
      { "profile.email": issuerEmail },
      { $set: { "professional.idCard.isVerified": true } },
      async (err, result) => {
        if (err) {
          console.log(err);
          let resp = {
            status: "Failed",
            msg: "ID verification update failed",
          };
          return res.json(resp);
        }

        let idType: string = "student";
        await sendIdVerificationMail(issuerEmail, firstName, idType).then(
          (isVerificationMailSent) => {
            if (isVerificationMailSent) {
              let resp = {
                status: "Success",
                msg: "Student ID verified",
              };
              return res.json(resp);
            } else
              return res.json({
                status: "Failed",
                msg: "Couldn't verify student ID",
              });
          }
        );
      }
    );
  } else if (alert.type === "Bank Transfer") {
    let issuerEmail = alert.issuer.email;
    let bankTxCode = alert.details.prop1;
    let purchaseId = alert.details.prop3;

    const issuer: any = await userModel.findOne({
      "profile.email": issuerEmail,
    });

    let filter = {
      id: alertId,
    };
    let update = {
      "resolver.name.first": resolverFirstName,
      "resolver.name.middle": "",
      "resolver.name.last": resolverLastName,
      "resolver.email": resolverEmail,
      "status.isActive": false,
      "timestamp.resolvedOn": resolvedOn,
    };
    await alertModel.findOneAndUpdate(filter, update);

    let firstName: string = issuer.profile.name.first;
    let purchasedItems = issuer.purchasedItems;
    let transferredAmount: string = "";
    let paymentDate: string = "";
    let isBankTxValid: boolean = false;

    purchasedItems.forEach((item: any) => {
      if (bankTxCode === item.transactionID && purchaseId === item.purchaseID) {
        isBankTxValid = true;
        transferredAmount = `₹${item.grandTotal}`;
        paymentDate = item.createdAt;
      }
    });
    paymentDate = moment(paymentDate).toString();

    if (!isBankTxValid) {
      let resp = {
        status: "Failed",
        msg: "Not a valid Bank Transaction",
      };
      return res.json(resp);
    }

    await userModel.updateOne(
      { "profile.email": issuerEmail, "purchasedItems.purchaseID": purchaseId },
      { $set: { "purchasedItems.$.paymentStatus": "purchased" } },
      async (err, result) => {
        if (err) {
          console.log(err);
          let resp = {
            status: "Failed",
            msg: "Updation failed",
          };
          return res.json(resp);
        }
        await sendBankTransferVerificationMail(
          eventCode,
          firstName,
          issuerEmail,
          purchaseId,
          transferredAmount,
          bankTxCode,
          paymentDate
        ).then((isConfirmationSent) => {
          if (isConfirmationSent) {
            console.log(
              `[BANK TX VERIFIED] - orderID: ${purchaseId}, bankTxCode: ${bankTxCode}`
            );
            let resp = {
              status: "Success",
              msg: "Bank Transaction Verified",
            };
            return res.json(resp);
          } else
            return res.json({
              status: "Failed",
              msg: "Couldn't verify bank transaction",
            });
        });
      }
    );
  } else {
    return res.json({
      status: "Success",
      msg: "Alert Closed",
    });
  }
});

export default router;
