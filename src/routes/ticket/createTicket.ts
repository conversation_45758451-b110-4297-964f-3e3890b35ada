import { Router } from "express";
import { ticketModel } from "../../models";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post("/createticket", async (req, res) => {
  /* --------------------------------------
  Extract ticket inputs from req.body
  -------------------------------------- */
  let {
    type,
    subType,
    eventCode,
    title,
    subTitle,
    desc,
    props,
    quantity,
    price,
  } = req.body;

  let ticketID = await uuidv4();
  let isDisabled = false;
  let isVisible = true;

  /* ---------------
  Create entry in DB
  --------------- */
  const createdTicket = await ticketModel.create({
    ticketID,
    type,
    subType,
    eventCode,
    title,
    subTitle,
    desc,
    props,
    quantity,
    price,
    isDisabled,
    isVisible,
  });

  /* ---------------
  Send response back
  --------------- */
  if (!createdTicket._id) {
    let resp = {
      status: "Failed",
      msg: "Couldn't create ticket",
    };
    return res.status(400).json(resp);
  }

  let resp = {
    status: "Success",
    msg: "Ticket created",
  };
  return res.status(400).json(resp);
});

export default router;
