import { Router } from "express";
import { userModel, ticketModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import trackData from "../../helpers/trackData";
import moment from "moment";

const router = Router();

router.get("/track", isUserLogged, getUserID, async (req, res) => {
  /* --- isUserLoggedIn --- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }
  /* --- getUserDetails --- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({ _userID });

  /* --- findTicketAndReturn --- */
  const trackEvents: any = await ticketModel.find({
    type: "track",
  });
  /* --- isTicketRetrievalSuccessful --- */
  if (trackEvents.length === 0) {
    let resp = {
      status: "Failed",
      msg: "Couldn't fetch ticket data",
    };
    return res.json(resp);
  }

  let payload: any = {
    isTrackEnabled: false,
    trackEvents: trackEvents.length,
    day1: [],
    day2: [],
  };

  let persona = userObj.professional.occupation;

  let userCart = userObj?.cart;
  let cartItemIDs: any = [];
  /* --- getAllCartIDs --- */
  userCart.forEach((cartItem: any) => {
    cartItemIDs.push(cartItem.ticketID);
  });

  const cartTicketDetailsArr = await ticketModel
    .find()
    .where("ticketID")
    .in(cartItemIDs)
    .exec();
  cartTicketDetailsArr.forEach((ticket: any) => {
    if (
      ticket.type === "full-conference" ||
      ticket.type === "single-selector"
    ) {
      payload.isTrackEnabled = true;
    }
  });

  let userPurchases = userObj?.purchasedItems;
  let purchasedItemIDs: any = [];
  /* --- getAllUserPurchaseIDs --- */
  userPurchases.forEach((purchases: any) => {
    purchases.purchasedItems.forEach((item: any) => {
      purchasedItemIDs.push(item.ticketID);
    });
  });

  const purchasedTicketDetailsArr = await ticketModel
    .find()
    .where("ticketID")
    .in(purchasedItemIDs)
    .exec();
  purchasedTicketDetailsArr.forEach((ticket: any) => {
    if (
      ticket.type === "full-conference" ||
      ticket.type === "single-selector"
    ) {
      payload.isTrackEnabled = true;
    }
  });

  trackEvents.forEach((trackEvent: any) => {
    let props = JSON.parse(trackEvent.props);
    let tier = "";
    let priceArr = trackEvent.price;
    let price = "";
    let ticketBtnStatus = "add-to-cart";
    let isPurchased = false;
    let isTicketDisabled = false;

    if (trackEvent.subType === "workshop") {
      price = priceArr[0].price.inr;
    } else if (trackEvent.subType === "course") {
      priceArr.forEach((priceObj: any) => {
        if (persona === "professional") {
          tier = "Professional";
        } else if (persona === "student") {
          tier = "Student";
        }
        if (priceObj.name === tier) {
          price = priceObj.price.inr;
        }
      });
    }

    /* --- isTicketInCart --- */
    userCart?.forEach((item: any) => {
      if (item.ticketID === trackEvent.ticketID) {
        ticketBtnStatus = "remove-from-cart";
      }
    });

    /* --- isTicketPurchased --- */
    userPurchases?.forEach((item: any) => {
      let purchaseList = item.purchasedItems;
      purchaseList.forEach((item: any) => {
        if (item.ticketID === trackEvent.ticketID) {
          isPurchased = true;
        }
      });
      if (isPurchased) {
        if (item.paymentStatus === "under-verification") {
          ticketBtnStatus = "under-verification";
        } else if (item.paymentStatus === "purchased") {
          ticketBtnStatus = "purchased";
        }
      }
    });

    // let year = "2021";
    // let month = "November";
    // let day = props.day.substring(0, 2);
    // let startDateString = new Date(
    //   `${day} ${month} ${year} ${props.start} UTC`
    // );
    // let endDateString = new Date(`${day} ${month} ${year} ${props.end} GMT`);

    let year = "2024";
    let month = "11";
    let day = props.day.substring(0, 2);
    let startDateString = moment(
      `${year}-${month}-${day} ${props.start}`
    ).toISOString();
    let endDateString = moment(
      `${year}-${month}-${day} ${props.end}`
    ).toISOString();

    let obj: any = {
      ticketID: trackEvent.ticketID,
      type: trackEvent.type,
      subType: trackEvent.subType,
      title: trackEvent.title,
      subTitle: trackEvent.subTitle,
      desc: trackEvent.desc,
      day: props.day,
      start: props.start,
      startTx: startDateString,
      end: props.end,
      endTx: endDateString,
      people: props.people,
      url: props.url,
      tier: tier,
      price: price,
      quantity: trackEvent.quantity,
      isTicketDisabled: isTicketDisabled,
      ticketBtnStatus: ticketBtnStatus,
      isDisabled: trackEvent.isDisabled,
      isVisible: trackEvent.isVisible,
    };

    if (obj.day === "18th Nov") {
      payload.day1.push(obj);
    } else if (obj.day === "19th Nov") {
      payload.day2.push(obj);
    }
  });

  payload.day1.sort((a: any, b: any) => {
    let startDateA: any = new Date(a.startTx);
    let startDateB: any = new Date(b.startTx);
    return startDateA - startDateB;
  });

  payload.day2.sort((a: any, b: any) => {
    let startDateA: any = new Date(a.startTx);
    let startDateB: any = new Date(b.startTx);
    return startDateA - startDateB;
  });

  let disableTrackEventArrFinal: any = [];
  let enableArrayFinal: any = [];

  payload.day1.forEach((day1event: any) => {
    if (
      day1event.ticketBtnStatus === "purchased" ||
      day1event.ticketBtnStatus === "remove-from-cart" ||
      day1event.ticketBtnStatus === "under-verification"
    ) {
      let disableEventNames: any = [];
      trackData.forEach((item: any) => {
        if (day1event.title === item.eventName) {
          disableEventNames = item.parallelEvents;
        }
      });
      disableEventNames.forEach((item: any) => {
        disableTrackEventArrFinal.push(item);
      });
    }

    if (day1event.ticketBtnStatus === "add-to-cart") {
      let enableEventNames: any = [];
      trackData.forEach((item: any) => {
        if (day1event.title === item.eventName) {
          enableEventNames = item.parallelEvents;
        }
      });
      enableEventNames.forEach((item: any) => {
        let isItemInArray: boolean = false;
        enableArrayFinal.forEach((finalItem: any) => {
          if (finalItem === item) {
            isItemInArray = true;
          }
        });
        if (isItemInArray === false) {
          enableArrayFinal.push(item);
        }
      });
    }
  });

  payload.day2.forEach((day2event: any) => {
    if (
      day2event.ticketBtnStatus === "purchased" ||
      day2event.ticketBtnStatus === "remove-from-cart" ||
      day2event.ticketBtnStatus === "under-verification"
    ) {
      let disableEventNames: any = [];
      trackData.forEach((item: any) => {
        if (day2event.title === item.eventName) {
          disableEventNames = item.parallelEvents;
        }
      });
      disableEventNames.forEach((item: any) => {
        disableTrackEventArrFinal.push(item);
      });
    }

    if (day2event.ticketBtnStatus === "add-to-cart") {
      let enableEventNames: any = [];
      trackData.forEach((item: any) => {
        if (day2event.title === item.eventName) {
          enableEventNames = item.parallelEvents;
        }
      });
      enableEventNames.forEach((item: any) => {
        let isItemInArray: boolean = false;
        enableArrayFinal.forEach((finalItem: any) => {
          if (finalItem === item) {
            isItemInArray = true;
          }
        });
        if (isItemInArray === false) {
          enableArrayFinal.push(item);
        }
      });
    }
  });

  enableArrayFinal.forEach((item: any) => {
    payload.day1.forEach((day1event: any) => {
      if (day1event.title === item) {
        day1event.isTicketDisabled = false;
      }
    });
    payload.day2.forEach((day2event: any) => {
      if (day2event.title === item) {
        day2event.isTicketDisabled = false;
      }
    });
  });

  disableTrackEventArrFinal.forEach((item: any) => {
    payload.day1.forEach((day1event: any) => {
      if (day1event.title === item) {
        day1event.isTicketDisabled = true;
      }
    });
    payload.day2.forEach((day2event: any) => {
      if (day2event.title === item) {
        day2event.isTicketDisabled = true;
      }
    });
  });

  let resp = {
    status: "Success",
    msg: "Ticket data fetched",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
