import { Router } from "express";
import { userModel, ticketModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateGetTicketDetails } from "../../validation";
import moment from "moment";

const router = Router();

router.get(
  "/ticket/:type?/:subType?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* --- isUserLoggedIn --- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* --- getUserDetails --- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({ _userID });

    /* --- extractReqParamsIntoVars --- */
    let type = req.params.type;
    let subType = req.params.subType;

    /* --- isRequestDataValid */
    // let { error } = validateGetTicketDetails(req.body);
    // if (error) {
    //   let resp = {
    //     status: "Failed",
    //     msg: error.message,
    //   };
    //   return res.status(400).send(resp);
    // }
    /* --- findTicketAndReturn --- */
    const tickets: any = await ticketModel.find({
      subType: subType,
    });

    /* --- isTicketRetrievalSuccessful --- */
    if (tickets.length === 0) {
      let resp = {
        status: "Failed",
        msg: "Couldn't fetch ticket data",
      };
      return res.json(resp);
    }

    /* --- getOneTicket --- */
    let ticketObj = tickets[0];
    let priceArr = ticketObj.price;

    /* --- setSelectorOptions --- */
    let selectorOpts: any = [];
    tickets.forEach((ticket: any) => {
      let obj = {
        value: ticket.ticketID,
        label: ticket.subTitle,
      };
      selectorOpts.push(obj);
    });

    /* --- initPayload --- */
    let payload = {
      selectorOpts: [],
      tier: "",
      price: "",
      availableTill: "",
      purchaseDate: "",
      cartStatus: {
        isInCart: false,
        label: "",
        value: "",
      },
      purchaseStatus: {
        isPurchased: false,
        label: "",
        value: "",
      },
      ticketBtnStatus: "add-to-cart",
    };
    payload.selectorOpts = selectorOpts;

    /* --- isTicketInCart --- */
    let userCart = userObj?.cart;
    userCart?.forEach((item: any) => {
      selectorOpts.forEach((opt: any) => {
        if (item.ticketID === opt.value) {
          payload.cartStatus.isInCart = true;
          payload.cartStatus.label = opt.label;
          payload.cartStatus.value = opt.value;
          payload.ticketBtnStatus = "remove-from-cart";
        }
      });
    });

    /* --- isTicketPurchased --- */
    let isPurchased = false;
    let userPurchases = userObj?.purchasedItems;

    userPurchases?.forEach((item: any) => {
      let purchaseList = item.purchasedItems;
      purchaseList.forEach((item: any) => {
        selectorOpts.forEach((opt: any) => {
          if (item.ticketID === opt.value) {
            isPurchased = true;
            payload.purchaseStatus.isPurchased = true;
            payload.purchaseStatus.label = opt.label;
            payload.purchaseStatus.value = opt.value;
          }
        });
      });
      if (isPurchased) {
        if (item.paymentStatus === "under-verification") {
          payload.ticketBtnStatus = "under-verification";
        } else if (item.paymentStatus === "purchased") {
          payload.ticketBtnStatus = "purchased";
        }
      }
      payload.purchaseDate = item.createdAt;
    });

    priceArr.map((price: any) => {
      let startTx = price.startDate;
      let endTx = price.endDate;
      let startDate = new Date(parseInt(startTx) * 1000).toISOString();
      let endDate = new Date(parseInt(endTx) * 1000).toISOString();
      if (isPurchased) {
        let isBetween = moment(payload.purchaseDate).isBetween(
          startDate,
          endDate
        );
        if (isBetween) {
          payload.tier = price.name;
          payload.price = price.price.inr;
          payload.availableTill = "";
        }
      } else {
        let nowDate = moment().toISOString();
        let isBetween = moment(nowDate).isBetween(startDate, endDate);
        if (isBetween) {
          payload.tier = price.name;
          payload.price = price.price.inr;
          payload.availableTill = moment(endDate).format("DD MMMM YYYY");
        }
      }
    });
    let resp = {
      status: "Success",
      msg: "Ticket data fetched",
      payload: payload,
    };
    return res.json(resp);
  }
);

export default router;
