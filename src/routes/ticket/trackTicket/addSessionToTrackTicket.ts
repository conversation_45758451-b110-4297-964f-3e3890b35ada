// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { ticketModel_v2, userModel } from "../../../models";

import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post(
  "/add-session-to-track",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------
    Check is user logged
    ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
    Check if user is Admin
    ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* ------------
    Extract payload
    ------------ */
    let {
      ticketId,
      title,
      type,
      quantity,
      startsOn,
      endsOn,
      studentPrice,
      professionalPrice,
      url,
      instructors,
    } = req.body;

    /* ------------------------
    Preparation to save session
    ------------------------ */
    let sessionId: string = await uuidv4();

    /*--------------------------
    Preparation of ticket object
    --------------------------*/
    let proposedSessionObj = {
      id: sessionId,
      title: title,
      type: type,
      quantity: quantity,
      startsOn: startsOn,
      endsOn: endsOn,
      studentPrice: studentPrice,
      professionalPrice: professionalPrice,
      url: url,
      instructors: instructors,
      isVisible: true,
      isDisabled: false,
      isSoldOut: false,
    };

    /* --------------------
    Insert into TrackTicket
    -------------------- */
    ticketModel_v2.findOneAndUpdate(
      { ticketId: ticketId },
      { $push: { "trackTicketDetails.sessions": proposedSessionObj } },
      (error: any, success: any) => {
        if (error) {
          let resp = {
            status: "Failed",
            msg: "Could not add session to track",
            payload: userObj,
          };
          return res.json(resp);
        }
      }
    );

    /* ----------
    Return success
    ---------- */
    let resp = {
      status: "Success",
      msg: "Session added to track",
    };
    return res.status(200).json(resp);
  }
);

export default router;
