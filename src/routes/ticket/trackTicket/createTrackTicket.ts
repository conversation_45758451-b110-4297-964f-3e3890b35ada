import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { ticketModel_v2, userModel } from "../../../models";

import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post(
  "/create-track-ticket",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------
    Check is user logged
    ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
    Check if user is Admin
    ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* ------------
    Extract payload
    ------------ */
    let { eventCode, pageId, ticketType } = req.body;

    /* -----------------------
    Preparation to save ticket
    ----------------------- */
    let ticketId: string = await uuidv4();
    let isTicketVisible: boolean = true;
    let isTicketPrimary: boolean = false;
    let isTicketPrimaryDependent: boolean = true;

    /*--------------------------
    Preparation of ticket object
    --------------------------*/
    let proposedTicketObj = {
      ticketId: ticketId,
      eventCode: eventCode,
      pageId: pageId,
      trackTicketDetails: {
        sessions: [],
      },
      ticketType: ticketType,
      isTicketVisible: isTicketVisible,
      isTicketPrimary: isTicketPrimary,
      isTicketPrimaryDependent: isTicketPrimaryDependent,
    };

    /* ---------------
    Create TrackTicket
    --------------- */
    let newTicketObj = await ticketModel_v2.create(proposedTicketObj);
    if (!newTicketObj._id) {
      let resp = {
        status: "Failed",
        msg: "Failed: Could not create track ticket",
      };
      console.log(
        "/create-track-ticket - Failed: Could not create track ticket"
      );
      return res.status(400).json(resp);
    }

    /* ----------
    Return success
    ---------- */
    let resp = {
      status: "Success",
      msg: "Track Ticket Created",
    };
    return res.status(200).json(resp);
  }
);

export default router;
