import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { ticketModel_v2, userModel } from "../../../models";

const router = Router();

router.post(
  "/get-session-details",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------
    Check is user logged
    ----------------- */
    // if (!res.locals.isUserLogged) {
    //   let resp = {
    //     status: "Failed",
    //     msg: "You are not logged in",
    //   };
    //   return res.status(400).json(resp);
    // }

    /* ------------
    Extract payload
    ------------ */
    let { ticketId, sessionId } = req.body;

    /* ------------
    Get ticket
    ------------ */
    const ticketObj: any = await ticketModel_v2.findOne({
      ticketId: ticketId,
    });

    let ticketSessions: any = ticketObj.trackTicketDetails.sessions;

    let sessionOfInterest: any;
    ticketSessions.map((session: any) => {
      if (session.id === sessionId) {
        sessionOfInterest = session;
      }
    });

    /* -------
    Get users
    ------- */
    let users: any;
    users = await userModel.find({}, "profile purchasedItems").exec();

    let userAndPurchaseItems: any = [];
    users.map((user: any) => {
      if (user.purchasedItems.length > 0) {
        user.purchasedItems.map((purchasedItem: any) => {
          let obj = {
            profile: user.profile,
            purchasedItems: purchasedItem.purchasedItems,
          };
          userAndPurchaseItems.push(obj);
        });
      }
    });

    // console.log(userAndPurchaseItems);
    let sessionParticipants: any = [];
    userAndPurchaseItems.map((userPurchase: any) => {
      userPurchase.purchasedItems.map((item) => {
        if (item.ticketId === ticketId && item.sessionId === sessionId) {
          let obj = {
            name: `${userPurchase.profile.name.first} ${userPurchase.profile.name.last}`,
            email: `${userPurchase.profile.email}`,
          };
          sessionParticipants.push(obj);
        }
      });
    });

    /* ----------------------
    Session details retrieved
    ---------------------- */

    let payload = {
      sessionTitle: sessionOfInterest.title,
      sessionParticipants: sessionParticipants,
    };

    let resp = {
      status: "Success",
      msg: "Session details retrieved",
      payload: payload,
    };
    return res.status(200).json(resp);
  }
);

export default router;
