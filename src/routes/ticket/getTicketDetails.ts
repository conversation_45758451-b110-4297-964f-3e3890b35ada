import { Router } from "express";
import { userModel, ticketModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateGetTicketDetails } from "../../validation";
import moment from "moment";

const router = Router();

router.post("/getticketdetails", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ----------------------------
Check if login Inputs are valid
---------------------------- */
  let { error } = validateGetTicketDetails(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* ----------------------------
  Get user details for processing
  ---------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({ _userID });

  /* ---------------------------------
  Extract ticket details from req.body
  --------------------------------- */
  let { type } = req.body;

  /* ------------------
  Full Conference check
  ------------------ */
  let subType = "";
  if (type === "full-conference") {
    if (userObj?.professional.occupation === "student") {
      subType = "full-conference-student";
    } else if (userObj?.professional.occupation === "professional") {
      if (userObj.membership.isInCart === false) {
        subType = "full-conference-professional";
      } else if (userObj.membership.isInCart === true) {
        subType = "full-conference-hcipai-professional";
      }
      if (userObj.membership.isMember === true) {
        subType = "full-conference-hcipai-professional";
      }
    }
  }

  // if (type === "one-day") {
  //   if (userObj?.professional.occupation === "student") {
  //     subType = "full-conference-student";
  //   } else if (userObj?.professional.occupation === "professional") {
  //     if (userObj.membership.isInCart === false) {
  //       subType = "full-conference-professional";
  //     } else if (userObj.membership.isInCart === true) {
  //       subType = "full-conference-hcipai-professional";
  //     }
  //     if (userObj.membership.isMember === true) {
  //       subType = "full-conference-hcipai-professional";
  //     }
  //   }
  // }

  /* --------------------
  Query ticket and return
  -------------------- */
  const ticketObj: any = await ticketModel.findOne({
    subType: subType,
  });

  if (!ticketObj) {
    let resp = {
      status: "Failed",
      msg: "Couldn't fetch ticket data",
    };
    return res.json(resp);
  } else {
    let priceArr = ticketObj.price;
    let payload = {
      ticketID: ticketObj.ticketID,
      tier: "",
      subTitle: ticketObj.subTitle,
      desc: ticketObj.desc,
      price: "",
      availableTill: "",
      purchaseDate: "",
      ticketBtnStatus: "add-to-cart",
    };

    let userCart = userObj?.cart;
    userCart?.forEach((item: any) => {
      if (item.ticketID === ticketObj.ticketID) {
        payload.ticketBtnStatus = "remove-from-cart";
      }
    });

    let isPurchased = false;
    let userPurchases = userObj?.purchasedItems;

    userPurchases?.forEach((item: any) => {
      let purchaseList = item.purchasedItems;
      if (item.paymentStatus === "under-verification") {
        purchaseList.forEach((item: any) => {
          if (item.ticketID === ticketObj.ticketID) {
            payload.ticketBtnStatus = "under-verification";
            isPurchased = true;
          }
        });
      } else if (item.paymentStatus === "purchased") {
        purchaseList.forEach((item: any) => {
          if (item.ticketID === ticketObj.ticketID) {
            payload.ticketBtnStatus = "purchased";
            isPurchased = true;
          }
        });
      }
      payload.purchaseDate = item.createdAt;
    });

    priceArr.map((price: any) => {
      let startTx = price.startDate;
      let endTx = price.endDate;
      let startDate = new Date(parseInt(startTx) * 1000).toISOString();
      let endDate = new Date(parseInt(endTx) * 1000).toISOString();
      if (isPurchased) {
        let isBetween = moment(payload.purchaseDate).isBetween(
          startDate,
          endDate
        );
        if (isBetween) {
          payload.tier = price.name;
          payload.price = price.price.inr;
          payload.availableTill = "";
        }
      } else {
        let nowDate = moment().toISOString();
        let isBetween = moment(nowDate).isBetween(startDate, endDate);
        if (isBetween) {
          payload.tier = price.name;
          payload.price = price.price.inr;
          payload.availableTill = moment(endDate).format("DD MMMM YYYY");
        }
      }
    });

    let resp = {
      status: "Success",
      msg: "Ticket data fetched",
      payload: payload,
    };
    return res.json(resp);
  }
});

export default router;
