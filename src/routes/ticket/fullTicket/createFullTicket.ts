import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { ticketModel_v2, userModel } from "../../../models";
import { validateCreateTicketInputs } from "../../../validation";

import moment from "moment";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post(
  "/create-full-ticket",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------
    Check is user logged
    ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
    Check if user is Admin
    ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* ------------
    Extract payload
    ------------ */
    let { eventCode, pageId, ticketTitle, ticketType, tiers } = req.body;
    tiers = JSON.parse(tiers);

    /* -------------
    Validate payload
    ------------- */
    let inputsForValidation: any = {
      eventCode: eventCode,
      pageId: pageId,
      ticketTitle: ticketTitle,
      ticketType: ticketType,
      tiers: tiers,
    };

    let { error } = validateCreateTicketInputs(inputsForValidation);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/create-full-ticket - ${error}`);
      return res.send(resp);
    }

    /* -----------------------
    Preparation to save ticket
    ----------------------- */
    let ticketId: string = await uuidv4();
    let isTicketVisible: boolean = true;
    let isTicketPrimary: boolean = true;
    let isTicketPrimaryDependent: boolean = false;
    let formattedTiers: any = [];

    let promises = tiers.map(async (tier: any) => {
      let tierId: string = await uuidv4();
      let tierName: string = tier.name;
      let tierStartDate: string = moment(tier.start)
        .utc()
        .startOf("day")
        .toISOString();
      let tierEndDate: string = moment(tier.end)
        .utc()
        .endOf("day")
        .toISOString();
      let studentPrice: number = tier.price.student;
      let professionalPrice: number = tier.price.professional;

      let obj = {
        tierId: tierId,
        tierName: tierName,
        tierStartDate: tierStartDate,
        tierEndDate: tierEndDate,
        studentPrice: studentPrice,
        professionalPrice: professionalPrice,
      };

      return obj;
    });

    formattedTiers = await Promise.all(promises);
    formattedTiers.sort((a: any, b: any) => {
      let tierStartDateA: any = new Date(a.tierStartDate);
      let tierStartDateB: any = new Date(b.tierStartDate);
      return tierStartDateA - tierStartDateB;
    });

    /*--------------------------
    Preparation of ticket object
    --------------------------*/
    let proposedTicketObj = {
      ticketId: ticketId,
      eventCode: eventCode,
      pageId: pageId,
      fullTicketDetails: {
        title: ticketTitle,
        tiers: formattedTiers,
      },
      ticketType: ticketType,
      isTicketVisible: isTicketVisible,
      isTicketPrimary: isTicketPrimary,
      isTicketPrimaryDependent: isTicketPrimaryDependent,
    };

    /* --------------
    Create FullTicket
    -------------- */
    let newTicketObj = await ticketModel_v2.create(proposedTicketObj);
    if (!newTicketObj._id) {
      let resp = {
        status: "Failed",
        msg: "Failed: Could not create full ticket",
      };
      console.log("/create-full-ticket - Failed: Could not create full ticket");
      return res.status(400).json(resp);
    }

    /* ----------
    Return success
    ---------- */
    let resp = {
      status: "Success",
      msg: "Full Ticket Created",
    };
    return res.status(200).json(resp);
  }
);

export default router;
