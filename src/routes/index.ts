/*----------------
Routes for Account
----------------*/
export { default as accountsetup } from "./account/accountsetup";
export { default as accountsetupv2 } from "./account/accountsetup-v2";
export { default as login } from "./auth/login";
export { default as logout } from "./auth/logout";
export { default as signup } from "./auth/signup";
export { default as register } from "./account/register";
export { default as verifyemail } from "./account/verifyemail";
export { default as sendpasswordresetcode } from "./account/sendpasswordresetcode";
export { default as verifypasswordresetcode } from "./account/verifypasswordresetcode";
export { default as resendpasswordresetcode } from "./account/resendpasswordresetcode";
export { default as resetpassword } from "./account/resetPassword";
export { default as confirmnewpassword } from "./account/confirmnewpassword";
export { default as getPurchasesV2 } from "./account/getPurchasesByUser_v2";
export { default as accountsetupv2student } from "./account/accountsetup-v2-student";
export { default as uploadidcard } from "./account/uploadIdCard";
export { default as convertAccountToPro } from "./account/convertAccountToPro";
export { default as getAllAccounts } from "./account/getAllAccounts";

/*--------------
Routes for Admin
--------------*/
export { default as overview } from "./admin/overview";
export { default as trackoverview } from "./admin/trackOverview";
export { default as getverifications } from "./admin/getVerifications";
export { default as banktxverification } from "./admin/bankTxVerification";
export { default as purchases } from "./admin/purchases";
export { default as purchasesSummary } from "./admin/purchasesSummary";
export { default as downloadpurchases } from "./admin/downloadpurchases";
export { default as downloadmembers } from "./admin/downloadMembers";
export { default as gettrackregistrants } from "./admin/getTrackRegistrants";
export { default as downloadtrackregistrants } from "./admin/downloadTrackRegistrants";
// export { default as registrantswithoutpurchases } from "./admin/registrantsWithoutPurchases";
export { default as sendticketconfirmation } from "./admin/sendticketconfirmation";
export { default as getaccesslist } from "./admin/getAccessList";
export { default as getaccesslist19nov } from "./admin/getAccessList19Nov";

export { default as getmembers } from "./admin/getMembers";
export { default as createcoupon } from "./admin/createCoupon";
export { default as getcoupons } from "./admin/getCoupons";
export { default as getcouponbyid } from "./admin/getCouponById";
export { default as getredeemerlist } from "./admin/getRedeemerList";
export { default as createcouponcodeforemail } from "./admin/createCouponCodeForEmail";
export { default as deletecouponcode } from "./admin/deleteCouponCode";
export { default as downloadcouponemails } from "./admin/downloadCouponEmails";
export { default as gettickettypes } from "./admin/getTicketTypes";
export { default as createuser } from "./admin/createUser";

export { default as createFullTicket } from "./ticket/fullTicket/createFullTicket";
export { default as createBasicTicket } from "./ticket/basicTicket/createBasicTicket";
export { default as createTrackTicket } from "./ticket/trackTicket/createTrackTicket";
export { default as addSessionToTrackTicket } from "./ticket/trackTicket/addSessionToTrackTicket";

export { default as downloadSales } from "./admin/v2/downloadSales_v2";
export { default as downloadSalesV3 } from "./admin/v3/downloadSales_v3";
export { default as downloadAccounts } from "./admin/downloadAccounts";
export { default as downloadAccount_V2 } from "./admin/v2/downloadAccountV2";
export { default as attendeeListV3 } from "./admin/v3/attendee_list_v3";
export { default as downloadSalesV4 } from "./admin/v4/downloadSales_v4";
export { default as downloadSalesV5 } from "./admin/v5/downloadSales_v5";

/*--------------
Routes for Alerts
--------------*/
export { default as getalerts } from "./admin/getAlerts";
export { default as resolvealerts } from "./admin/resolveAlerts";
export { default as cancelIdVerificationAlert } from "./admin/cancelIdVerificationAlert";

/*-------------
Routes for Cart
-------------*/
export { default as cart } from "./cart/cart";
export { default as getcarttotal } from "./cart/getCartTotal";

/*----------------
Routes for Coupons
----------------*/
export { default as checkandapplycoupon } from "./coupon/checkAndApplyCoupon";
export { default as checkAndApplyCoupon_v2 } from "./coupon/checkAndApplyCoupon_v2";
export { default as clearcoupon } from "./coupon/clearCoupon";
export { default as getavailablecoupons } from "./coupon/getAvailableCoupons";

/*-------------------
Routes for Membership
-------------------*/
export { default as ismember } from "./member/isMember";
export { default as getmemberticketdetails } from "./member/getMemberTicketDetails";
export { default as getmembership } from "./member/getMembership";
export { default as initMemberCollection } from "./member/initMemberCollection";

/*--------------
Routes for Oauth
--------------*/
export { default as getlinkedinprofile } from "./oauth/linkedin/getLinkedinProfile";
export { default as signupwithlinkedin } from "./oauth/linkedin/signupWithLinkedIn";
export { default as getgoogleprofile } from "./oauth/google/getGoogleProfile";
export { default as linkaccountwithlinkedin } from "./oauth/linkedin/linkAccountWithLinkedIn";
export { default as linkaccountwithgoogle } from "./oauth/google/linkAccountWithGoogle";
export { default as signupwithgoogle } from "./oauth/google/signupWithGoogle";

/*-----------------
Routes for Payments
-----------------*/
export { default as bankpayment } from "./payment/bankPayment";
export { default as bankPayment_v2 } from "./payment/bankPayment_v2";
export { default as paywithrazorpay } from "./payment/payWithRazorpay";
export { default as payWithRazorPay_v2 } from "./payment/payWithRazorPay_v2";
export { default as razorpayconfirm } from "./payment/razorpayConfirm";
export { default as razorpayConfirm_v2 } from "./payment/razorpayConfirm_v2";
export { default as freepurchaseconfirm } from "./payment/freePurchase";
export { default as freePurchaseConfirm_v2 } from "./payment/freePurchase_v2";

/*----------------
Routes for Tickets
----------------*/
export { default as ticket } from "./ticket/ticket";
export { default as createticket } from "./ticket/createTicket";
export { default as getticketdetails } from "./ticket/getTicketDetails";
export { default as track } from "./ticket/track";
export { default as getSessionDetails } from "./ticket/trackTicket/getSessionDetails";

/*--------------
Routes for Users
--------------*/
export { default as home } from "./user/home";
export { default as getpurchases } from "./user/getPurchases";
export { default as profile } from "./user/profile";
export { default as user } from "./user/user";
export { default as singleuser } from "./user/singleUser";

/*-----------------
Routes for Webhooks
-----------------*/
// export { default as razorpaymenthook } from "./webhooks/rzpPayments";
export { default as razorpaymenthook } from "./webhooks/rzpPayments_v2";
export { default as rzppaymentfailed } from "./webhooks/rzpPaymentFailed";

/*-----------------------
Routes for Legacy Members
-----------------------*/
export { default as addmembertolegacydb } from "./member/addMemberToLegacyDB";
export { default as legacymembers } from "./member/legacyMembers";

/*----------------
Routes for Testing
----------------*/
export { default as generictest } from "./test/genericTest";

/*---------------
Routes for Events
---------------*/
export { default as createevent } from "./event/createEvent";
export { default as getevents } from "./event/getEvents";
export { default as geteventauth } from "./event/getEventAuth";
export { default as geteventbycode } from "./event/getEventByCode";
export { default as updateeventpublishing } from "./event/updateEventPublishing";

export { default as editeventassets } from "./event/edit/editEventAssets";
export { default as editeventbasics } from "./event/edit/editEventBasics";
export { default as editeventinvoicing } from "./event/edit/editEventInvoicing";
export { default as editeventmanagers } from "./event/edit/editEventManagers";
export { default as editeventschedule } from "./event/edit/editEventSchedule";

/*-------------
Routes for Mail
-------------*/
export { default as sendemailverificationcode } from "./email/sendEmailVerificationCode";

/*---------------
Routes for RegSys
---------------*/
export { default as getregsysbyeventcode } from "./regSys/getRegSysByEventCode";
export { default as getticketsbypageidandeventcode } from "./regSys/tickets/getTicketsByPageIdAndEventCode";
export { default as addpagetoeventcode } from "./regSys/pages/addPageToEventCode";
export { default as getpagesbyeventcode } from "./regSys/pages/getPagesByEventCode";
export { default as deletepagebypageid } from "./regSys/pages/deletePageByPageId";
export { default as updatepagebyid } from "./regSys/pages/updatePageById";

/*------------------
NEW: Routes for Cart
------------------*/
export { default as addToCart } from "./cart/cart_v2/addToCart";
export { default as removeFromCart } from "./cart/cart_v2/removeFromCart";
export { default as getCart } from "./cart/cart_v2/getCart";
export { default as getCheckoutTotal } from "./cart/cart_v2/getCheckoutTotal";

/*--------------------------
NEW: Routes for Ticket types
--------------------------*/
export { default as getTicketTypes_v2 } from "./admin/getTicketTypes_v2";

/*--------------------------
NEW: Routes for Membership
--------------------------*/
export { default as addMembershipToLegacyCart } from "./membership/addMembershipToLegacyCart";
export { default as getMembershipCheckoutTotal } from "./membership/getMembershipCheckoutTotal";
export { default as membershipBankPayment } from "./membership/payment/bank/membershipBankPayment";
export { default as membershipRazorpayPrep } from "./membership/payment/razorpay/membershipRazorpayPrep";
export { default as membershipRazorpayPayment } from "./membership/payment/razorpay/membershipRazorpayPayment";
export { default as membershipRazorpayWebhook } from "./membership/payment/razorpay/membershipRazorpayWebhook";

/*--------------------------
NEW: Routes for Dashboard
--------------------------*/
export { default as overviewv2 } from "./admin/v2/overview_v2";
export { default as purchaseSummary_v2 } from "./admin/v2/purchaseSummary_v2";
export { default as overviewv3 } from "./admin/v3/overview_v3";

/*----------------------
NEW: Routes for Invoices
----------------------*/
export { default as downloadDataForInvoice } from "./invoice/downloadDataForInvoice";
export { default as sendAllInvoices } from "./invoice/sendAllInvoices";
export { default as uploadInvoiceGroup } from "./invoice/uploadInvoiceGroup";
export { default as downloadInvoice } from "./invoice/downloadInvoice";
export { default as downloadInvoiceCustomEvent } from "./invoice/downloadInvoiceCustomEvent";
export { default as getInvoiceGroups } from "./invoice/getInvoiceGroups";

/*---------------------------
NEW: Routes for Attendee List
---------------------------*/
export { default as attendeelist } from "./admin/v2/attendeelist_v2";
