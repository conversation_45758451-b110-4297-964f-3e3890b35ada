import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { eventModel, userModel } from "../../models";
import { validateUpdateEventPublishingInputs } from "../../validation";

const router = Router();

router.post(
  "/updateeventpublishing",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------
  Check is user logged
  ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
  Check if user is Admin
  ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* ------------
  Extract payload
  ------------ */
    let { eventCode, newEventPublishStatus } = req.body;

    /* -------------
  Validate payload
  ------------- */
    let { error } = validateUpdateEventPublishingInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/updateeventpublishing - ${error}`);
      return res.send(resp);
    }

    /* --------
  Preparation
  -------- */
    let isEventPublished: boolean = false;
    if (newEventPublishStatus === "publish") {
      isEventPublished = true;
    } else if (newEventPublishStatus === "unpublish") {
      isEventPublished = false;
    }

    /* ----------------------
  Update event publish status
  ------------------------ */
    let filter = {
      code: eventCode,
    };
    let updateEventPublishStatus = {
      isPublished: isEventPublished,
    };
    await eventModel.findOneAndUpdate(filter, updateEventPublishStatus);

    /* ------------------
  Send success response
  ------------------ */
    let resp = {
      status: "Success",
      msg: "Event publish updated",
    };
    return res.status(200).json(resp);
  }
);

export default router;
