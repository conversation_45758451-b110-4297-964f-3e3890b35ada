// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { eventModel, userModel } from "../../../models";
import { validateEditEventBasicsInputs } from "../../../validation";

const router = Router();

router.post("/editeventbasics", isUserLogged, getUserID, async (req, res) => {
  console.log("");
  console.log("/editeventbasics - Hit");
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }
  console.log("/editeventbasics - User is logged in");
  /* -------------------
  Check if user is Admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }
  console.log("/editeventbasics - User exists");

  if (!userObj.settings.isAdmin) {
    let resp = {
      status: "Failed",
      msg: "Access Denied - You are not an admin",
    };
    return res.json(resp);
  }
  console.log("/editeventbasics - User is an admin");

  /* -------------
  Validate payload
  ------------- */
  let { error } = validateEditEventBasicsInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(`/editeventbasics - ${error}`);
    return res.send(resp);
  }
  console.log("/editeventbasics - Payload validated");

  /* ------------
  Extract payload
  ------------ */
  let {
    eventCode,
    eventName,
    eventTagline,
    eventVenueLabel,
    eventVenueUrl,
    eventWebsite,
  } = req.body;

  /* ----------------
  Update event basics
  ---------------- */
  let filter = {
    code: eventCode,
  };
  let updateObj = {
    name: eventName,
    tagline: eventTagline,
    "venue.label": eventVenueLabel,
    "venue.url": eventVenueUrl,
    website: eventWebsite,
  };

  await eventModel.findOneAndUpdate(filter, updateObj, (error, success) => {
    if (success) {
      console.log(`/editeventbasics - Basic even updated`);
      let resp = {
        status: "Success",
        msg: "Event basics updated",
      };
      return res.json(resp);
    }
    if (error) {
      console.log(`/editeventbasics - Error in updating basic events`);
      let resp = {
        status: "Failed",
        msg: "An error occured while updating event basics",
      };
      return res.json(resp);
    }
  });
});

export default router;
