// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { eventModel, userModel } from "../../../models";
import { multer } from "../../../helpers";
import { validateEditEventAssetsInputs } from "../../../validation";
import { v2 } from "cloudinary";
import { createReadStream } from "streamifier";
import { CLOUDINARY_OPTIONS } from "../../../config";

const router = Router();

router.post(
  "/editeventassets",
  isUserLogged,
  getUserID,
  multer.any(),
  async (req, res) => {
    /* -----------------
  Check is user logged
  ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
  Check if user is Admin
  ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* -------------
  Validate payload
  ------------- */
    let { error } = validateEditEventAssetsInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/editeventassets - ${error}`);
      return res.send(resp);
    }

    /* ------------
  Extract payload
  ------------ */
    let { eventCode } = req.body;

    /* --------------------
  Save images on Cloudinary
  -------------------- */
    const files: any = req.files;

    v2.config({
      cloud_name: CLOUDINARY_OPTIONS.cloudName,
      api_key: CLOUDINARY_OPTIONS.apiKey,
      api_secret: CLOUDINARY_OPTIONS.apiSecret,
    });

    let logoUrl: string = "";
    let bannerUrl: string = "";
    let posterUrl: string = "";
    for (const file of files) {
      let result: any = await fromBufferToCloudinary(file, eventCode);
      if (file.fieldname === "eventLogo") {
        logoUrl = result.secure_url;
      } else if (file.fieldname === "eventBanner") {
        bannerUrl = result.secure_url;
      } else if (file.fieldname === "eventPoster") {
        posterUrl = result.secure_url;
      }
    }

    /* ----------------
  Update event assets
  ---------------- */
    let filter = {
      code: eventCode,
    };

    let updateObj: any;
    if (logoUrl && bannerUrl && posterUrl) {
      updateObj = {
        "branding.logoUrl": logoUrl,
        "branding.bannerUrl": bannerUrl,
        "branding.posterUrl": posterUrl,
      };
    } else if (!logoUrl && bannerUrl && posterUrl) {
      updateObj = {
        "branding.bannerUrl": bannerUrl,
        "branding.posterUrl": posterUrl,
      };
    } else if (logoUrl && !bannerUrl && posterUrl) {
      updateObj = {
        "branding.logoUrl": logoUrl,
        "branding.posterUrl": posterUrl,
      };
    } else if (logoUrl && bannerUrl && !posterUrl) {
      updateObj = {
        "branding.logoUrl": logoUrl,
        "branding.bannerUrl": bannerUrl,
      };
    } else if (!logoUrl && !bannerUrl && posterUrl) {
      updateObj = {
        "branding.posterUrl": posterUrl,
      };
    } else if (logoUrl && !bannerUrl && !posterUrl) {
      updateObj = {
        "branding.logoUrl": logoUrl,
      };
    } else if (!logoUrl && bannerUrl && !posterUrl) {
      updateObj = {
        "branding.bannerUrl": bannerUrl,
      };
    }

    await eventModel.findOneAndUpdate(filter, updateObj, (error, success) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "An error occured while updating event assets",
        };
        return res.json(resp);
      }
      if (success) {
        let resp = {
          status: "Success",
          msg: "Event assets updated",
        };
        return res.json(resp);
      }
    });
  }
);

const fromBufferToCloudinary = (file: any, eventCode: string) => {
  return new Promise((resolve, reject) => {
    let cld_upload_stream = v2.uploader.upload_stream(
      {
        folder: `events/${eventCode}`,
      },
      (error: any, result: any) => {
        if (result) {
          resolve(result);
        } else {
          reject(error);
        }
      }
    );
    createReadStream(file.buffer).pipe(cld_upload_stream);
  });
};

export default router;
