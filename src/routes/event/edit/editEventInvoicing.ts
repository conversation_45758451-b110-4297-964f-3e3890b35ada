// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { eventModel, userModel } from "../../../models";
import { validateEditEventInvoicingInputs } from "../../../validation";

const router = Router();

router.post(
  "/editeventinvoicing",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* -----------------
    Check is user logged
    ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
    Check if user is Admin
    ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }
    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* -------------
    Validate payload
    ------------- */
    let { error } = validateEditEventInvoicingInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/editeventinvoicing - ${error}`);
      return res.send(resp);
    }

    /* ------------
    Extract payload
    ------------ */
    let { eventCode, invoicePrefix } = req.body;

    /* -----------------
    Update event invoicing
    ------------------- */
    let filter = {
      code: eventCode,
    };
    let updateObj = {
      "invoice.prefix": invoicePrefix,
    };
    await eventModel.findOneAndUpdate(filter, updateObj, (error, success) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "An error occured while updating event invoicing",
        };
        return res.json(resp);
      }
      if (success) {
        let resp = {
          status: "Success",
          msg: "Event invoicing updated",
        };
        return res.json(resp);
      }
    });
  }
);

export default router;
