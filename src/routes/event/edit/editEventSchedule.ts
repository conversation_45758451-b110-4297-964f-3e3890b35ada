// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { eventModel, userModel } from "../../../models";
import { validateEditEventScheduleInputs } from "../../../validation";
import moment from "moment";

const router = Router();

router.post("/editeventschedule", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check if user is Admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }
  if (!userObj.settings.isAdmin) {
    let resp = {
      status: "Failed",
      msg: "Access Denied - You are not an admin",
    };
    return res.json(resp);
  }

  /* -------------
  Validate payload
  ------------- */
  let { error } = validateEditEventScheduleInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(`/editeventschedule - ${error}`);
    return res.send(resp);
  }

  /* ------------
  Extract payload
  ------------ */
  let {
    eventCode,
    eventStartDate,
    eventStartTime,
    eventEndDate,
    eventEndTime,
    registrationStartDate,
    registrationStartTime,
    registrationEndDate,
    registrationEndTime,
  } = req.body;

  /* -----------
  Pre-processing
  ----------- */
  let eventStartISOtime = moment(
    eventStartDate + " " + eventStartTime,
    "YYYY/MM/DD HH:mm"
  ).toISOString();
  let eventEndISOtime = moment(
    eventEndDate + " " + eventEndTime,
    "YYYY/MM/DD HH:mm"
  ).toISOString();
  let registrationStartISOtime = moment(
    registrationStartDate + " " + registrationStartTime,
    "YYYY/MM/DD HH:mm"
  ).toISOString();
  let registrationEndISOtime = moment(
    registrationEndDate + " " + registrationEndTime,
    "YYYY/MM/DD HH:mm"
  ).toISOString();

  /* ------------------
  Update event schedule
  ------------------ */
  let filter = {
    code: eventCode,
  };
  let updateObj = {
    "schedule.event.startsOn": eventStartISOtime,
    "schedule.event.endsOn": eventEndISOtime,
    "schedule.registration.startsOn": registrationStartISOtime,
    "schedule.registration.endsOn": registrationEndISOtime,
  };

  await eventModel.findOneAndUpdate(filter, updateObj, (error, success) => {
    if (error) {
      let resp = {
        status: "Failed",
        msg: "An error occured while updating event schedule",
      };
      return res.json(resp);
    }
    if (success) {
      let resp = {
        status: "Success",
        msg: "Event schedule updated",
      };
      return res.json(resp);
    }
  });
});

export default router;
