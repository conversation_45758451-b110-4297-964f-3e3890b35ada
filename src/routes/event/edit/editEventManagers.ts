// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../../middleware";
import { eventModel, userModel } from "../../../models";
import { validateEditEventManagersInputs } from "../../../validation";

const router = Router();

router.post("/editeventmanagers", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check if user is Admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }
  if (!userObj.settings.isAdmin) {
    let resp = {
      status: "Failed",
      msg: "Access Denied - You are not an admin",
    };
    return res.json(resp);
  }

  /* -------------
  Validate payload
  ------------- */
  let { error } = validateEditEventManagersInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(`/editeventmanagers - ${error}`);
    return res.send(resp);
  }

  /* ------------
  Extract payload
  ------------ */
  let { eventCode, managers } = req.body;

  /* ------------------
  Update event managers
  ------------------ */
  let filter = {
    code: eventCode,
  };
  let updateObj = {
    managers: managers,
  };
  await eventModel.findOneAndUpdate(filter, updateObj, (error, success) => {
    if (error) {
      let resp = {
        status: "Failed",
        msg: "An error occured while updating event managers",
      };
      return res.json(resp);
    }
    if (success) {
      let resp = {
        status: "Success",
        msg: "Event managers updated",
      };
      return res.json(resp);
    }
  });
});

export default router;
