import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { eventModel, userModel } from "../../models";
import { validateGetEventsInputs } from "../../validation";
import moment from "moment";

const router = Router();

router.post("/getevents", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check if user is Admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  let userEmail: string = userObj.profile.email;
  let isUserAdmin: boolean = userObj.settings.isAdmin;

  /* -------------
  Validate inputs
  ------------- */
  let { eventType, publishingFilter } = req.body;
  let { error } = validateGetEventsInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: "Invalid inputs to retrieve events",
    };
    console.log(`/getevents - Invalid inputs to retrieve events`);
    return res.json(resp);
  }

  /* -------------
  Segregate events
  ------------- */
  let eventsArr: any = await eventModel
    .find(
      {},
      "id code displayPos name tagline venue website branding schedule managers timestamp isPublished"
    )
    .exec();

  let formattedEventsArr: any = [];
  eventsArr.map(async (event: any) => {
    let isUserManager: boolean = false;
    let eventManagers: Array<Object> = event.managers;
    eventManagers.map((manager: any) => {
      if (manager.email === userEmail) isUserManager = true;
    });

    let isEventUpcoming: boolean = false;
    let isRegistrationOpen: boolean = false;
    let isEventArchived: boolean = false;

    let nowIsBefore: boolean = moment().isBefore(
      event.schedule.registration.startsOn
    );
    let nowIsAfter: boolean = moment().isAfter(
      event.schedule.registration.endsOn
    );
    let nowIsBetween = moment().isBetween(
      event.schedule.registration.startsOn,
      event.schedule.registration.endsOn
    );

    if (nowIsBefore || nowIsBetween) {
      isEventUpcoming = true;
      if (nowIsBetween) {
        isRegistrationOpen = true;
      } else {
        isRegistrationOpen = false;
      }
    }

    if (nowIsAfter) {
      isEventArchived = true;
    }

    let isEventActive: boolean = moment().isBetween(
      event.schedule.event.startsOn,
      event.schedule.event.endsOn
    );

    let formattedEventObj: any = {
      id: event.id,
      code: event.code,
      name: event.name,
      displayPos: event.displayPos,
      tagline: event.tagline,
      venueLabel: event.venue.label,
      venueUrl: event.venue.url,
      website: event.website,
      logoUrl: event.branding.logoUrl,
      bannerUrl: event.branding.bannerUrl,
      posterUrl: event.branding.posterUrl,
      startsOn: event.schedule.event.startsOn,
      endsOn: event.schedule.event.endsOn,
      registrationStartsOn: event.schedule.registration.startsOn,
      registrationEndsOn: event.schedule.registration.endsOn,
      isPublished: event.isPublished,
      isUpcoming: isEventUpcoming,
      isArchived: isEventArchived,
      isRegistrationOpen: isRegistrationOpen,
      isActive: isEventActive,
      isUserManager: isUserManager,
    };
    formattedEventsArr.push(formattedEventObj);
  });

  let eventsBasedOnType: any = [];
  if (eventType === "upcoming") {
    formattedEventsArr.map((formattedEvent: any) => {
      if (formattedEvent.isUpcoming) {
        eventsBasedOnType.push(formattedEvent);
      }
    });
  } else if (eventType === "archived") {
    formattedEventsArr.map((formattedEvent: any) => {
      if (formattedEvent.isArchived) {
        eventsBasedOnType.push(formattedEvent);
      }
    });
  } else {
    eventsBasedOnType = formattedEventsArr;
  }

  let eventsBasedOnPublishing: any = [];
  if (publishingFilter === "published") {
    eventsBasedOnType.map((event: any) => {
      if (event.isPublished) {
        eventsBasedOnPublishing.push(event);
      }
    });
  } else if (publishingFilter === "unpublished") {
    eventsBasedOnType.map((event: any) => {
      if (!event.isPublished) {
        eventsBasedOnPublishing.push(event);
      }
    });
  } else {
    eventsBasedOnPublishing = eventsBasedOnType;
  }

  let finalEventsArr: any = [];

  if (isUserAdmin) {
    finalEventsArr = eventsBasedOnPublishing;
  } else {
    eventsBasedOnPublishing.map((event: any) => {
      if (event.isPublished) {
        finalEventsArr.push(event);
      }
    });
  }
  if (eventType === "upcoming") {
    finalEventsArr.sort((a: any, b: any) => {
      let ADisplayPos: number = a.displayPos;
      let BDisplayPos: number = b.displayPos;
      return ADisplayPos - BDisplayPos;
    });
  } else if (eventType === "archived") {
    finalEventsArr.sort((a: any, b: any) => {
      let AstartDate: any = new Date(a.startsOn);
      let BstartDate: any = new Date(b.startsOn);
      return BstartDate - AstartDate;
    });
  }

  /* ------------------
  Send success response
  ------------------ */
  let resp = {
    status: "Success",
    msg: "Events fetched",
    payload: finalEventsArr,
  };
  return res.status(200).json(resp);
});

export default router;
