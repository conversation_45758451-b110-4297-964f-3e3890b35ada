import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { eventModel, regSysModel, userModel } from "../../models";
import { multer } from "../../helpers";
import { v2 } from "cloudinary";
import { createReadStream } from "streamifier";
import { v4 as uuidv4 } from "uuid";
import { validateCreateEventInputs } from "../../validation";
import moment from "moment";
import { CLOUDINARY_OPTIONS } from "../../config";

const router = Router();

router.post(
  "/createevent",
  isUserLogged,
  getUserID,
  multer.any(),
  async (req, res) => {
    /* -----------------
    Check is user logged
    ----------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------------------
    Check if user is Admin
    ------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    if (!userObj.settings.isAdmin) {
      let resp = {
        status: "Failed",
        msg: "Access Denied - You are not an admin",
      };
      return res.json(resp);
    }

    /* ------------
    Extract payload
    ------------ */
    let {
      eventName,
      eventTagline,
      eventVenueLabel,
      eventVenueUrl,
      eventWebsite,
      invoicePrefix,
      eventStartDate,
      eventStartTime,
      eventEndDate,
      eventEndTime,
      registrationStartDate,
      registrationStartTime,
      registrationEndDate,
      registrationEndTime,
      managers,
      isPublished,
    } = req.body;

    managers = JSON.parse(managers);
    isPublished = JSON.parse(isPublished);

    let inputsForValidation: any = {
      eventName: eventName,
      eventTagline: eventTagline,
      eventVenueLabel: eventVenueLabel,
      eventVenueUrl: eventVenueUrl,
      eventWebsite: eventWebsite,
      invoicePrefix: invoicePrefix,
      eventStartDate: eventStartDate,
      eventStartTime: eventStartTime,
      eventEndDate: eventEndDate,
      eventEndTime: eventEndTime,
      registrationStartDate: registrationStartDate,
      registrationStartTime: registrationStartTime,
      registrationEndDate: registrationEndDate,
      registrationEndTime: registrationEndTime,
      managers: managers,
      isPublished: isPublished,
    };

    /* -------------
    Validate payload
    ------------- */
    let { error } = validateCreateEventInputs(inputsForValidation);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      console.log(`/createevent - ${error}`);
      return res.send(resp);
    }

    /* --------
    Preparation
    -------- */
    let eventId: string = await uuidv4();
    let buffName: string = eventName.toLowerCase();
    buffName = buffName.replace(/\s+/g, "-");
    let eventCode: string = `${buffName}-${eventId}`;

    let eventStartISOtime = moment(
      eventStartDate + " " + eventStartTime,
      "YYYY/MM/DD HH:mm"
    ).toISOString();
    let eventEndISOtime = moment(
      eventEndDate + " " + eventEndTime,
      "YYYY/MM/DD HH:mm"
    ).toISOString();
    let registrationStartISOtime = moment(
      registrationStartDate + " " + registrationStartTime,
      "YYYY/MM/DD HH:mm"
    ).toISOString();
    let registrationEndISOtime = moment(
      registrationEndDate + " " + registrationEndTime,
      "YYYY/MM/DD HH:mm"
    ).toISOString();

    let scheduleObj = {
      event: {
        startsOn: eventStartISOtime,
        endsOn: eventEndISOtime,
      },
      registration: {
        startsOn: registrationStartISOtime,
        endsOn: registrationEndISOtime,
      },
    };

    let venueObj = {
      label: eventVenueLabel,
      url: eventVenueUrl,
    };

    let timestampObj = {
      createdOn: moment().toISOString(),
      updatedOn: moment().toISOString(),
    };

    /* ----------------------
    Save images on Cloudinary
    ---------------------- */
    const files: any = req.files;

    v2.config({
      cloud_name: CLOUDINARY_OPTIONS.cloudName,
      api_key: CLOUDINARY_OPTIONS.apiKey,
      api_secret: CLOUDINARY_OPTIONS.apiSecret,
    });

    let logoUrl: string = "";
    let bannerUrl: string = "";
    let posterUrl: string = "";
    for (const file of files) {
      let result: any = await fromBufferToCloudinary(file, eventCode);
      if (file.fieldname === "eventLogo") {
        logoUrl = result.secure_url;
      } else if (file.fieldname === "eventBanner") {
        bannerUrl = result.secure_url;
      } else if (file.fieldname === "eventPoster") {
        posterUrl = result.secure_url;
      }
    }

    /* ------------------------
    Generate proposed event obj
    ------------------------ */
    let brandingObj = {
      logoUrl: logoUrl,
      bannerUrl: bannerUrl,
      posterUrl: posterUrl,
    };

    let invoiceObj = {
      prefix: invoicePrefix,
      count: 0,
    };

    let proposedEventObj: any = {
      id: eventId,
      code: eventCode,
      name: eventName,
      tagline: eventTagline,
      venue: venueObj,
      website: eventWebsite,
      invoice: invoiceObj,
      branding: brandingObj,
      schedule: scheduleObj,
      managers: managers,
      timestamp: timestampObj,
      isPublished: isPublished,
    };

    /* ---------
    Create Event
    --------- */
    let newEventObj = await eventModel.create(proposedEventObj);
    if (!newEventObj._id) {
      let resp = {
        status: "Failed",
        msg: "Could not save event data",
      };
      console.log("/createevent - Failed: Could not save event data");
      return res.status(400).json(resp);
    }

    /* -------------------------
    Generate proposed RegSys Obj
    ------------------------- */
    let regSysId: string = await uuidv4();
    let pageId: string = await uuidv4();
    let ticketsInPage: any = [];
    let pageObj: any = {
      meta: {
        schedule: {
          isScheduled: false,
          enableOn: "",
          disableOn: "",
        },
        isDisabled: false,
        isPublished: true,
      },
      id: pageId,
      label: "Tickets",
      icon: "ticket-outline",
      ticketsInPage: ticketsInPage,
    };
    let pages: any = [];
    pages[0] = pageObj;
    let proposedRegSys_Obj: any = {
      id: regSysId,
      eventCode: eventCode,
      pages: pages,
    };

    /* ----------
    Create RegSys 
    ---------- */
    let newRegSysObj = await regSysModel.create(proposedRegSys_Obj);
    if (!newRegSysObj._id) {
      let resp = {
        status: "Failed",
        msg: "Could not create RegSys",
      };
      console.log("/createevent - Failed: Could not create RegSys");
      return res.status(400).json(resp);
    }

    /* ------------------
    Send success response
    ------------------ */
    console.log("/createevent - Success: Event Created");

    let resp = {
      status: "Success",
      msg: "Event created",
    };
    return res.status(200).json(resp);
  }
);

const fromBufferToCloudinary = (file: any, eventCode: string) => {
  return new Promise((resolve, reject) => {
    let cld_upload_stream = v2.uploader.upload_stream(
      {
        folder: `events/${eventCode}`,
      },
      (error: any, result: any) => {
        if (result) {
          resolve(result);
        } else {
          reject(error);
        }
      }
    );
    createReadStream(file.buffer).pipe(cld_upload_stream);
  });
};

export default router;
