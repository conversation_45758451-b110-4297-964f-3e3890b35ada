import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { eventModel } from "../../models";
import { validateGetEventByCodeInputs } from "../../validation";
import moment from "moment";

const router = Router();

router.post("/geteventauth", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is logged in
  ---------------------- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* ----------------------
  Extract & validate inputs
  ---------------------- */
  let { eventCode } = req.body;
  let { error } = validateGetEventByCodeInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(`/geteventbycode - Invalid inputs to retrieve event`);
    return res.json(resp);
  }

  /* -----------
  Retrieve event
  ----------- */
  let eventObj: any = await eventModel
    .findOne(
      { code: eventCode },
      "id code name tagline venue website branding schedule timestamp isPublished"
    )
    .exec();

  let isEventUpcoming: boolean = false;
  let isRegistrationOpen: boolean = false;
  let isEventArchived: boolean = false;

  let nowIsBefore: boolean = moment().isBefore(
    eventObj.schedule.registration.startsOn
  );
  let nowIsAfter: boolean = moment().isAfter(
    eventObj.schedule.registration.endsOn
  );
  let nowIsBetween = moment().isBetween(
    eventObj.schedule.registration.startsOn,
    eventObj.schedule.registration.endsOn
  );

  if (nowIsBefore || nowIsBetween) {
    isEventUpcoming = true;
    if (nowIsBetween) {
      isRegistrationOpen = true;
    } else {
      isRegistrationOpen = false;
    }
  }

  if (nowIsAfter) {
    isEventArchived = true;
  }

  let isEventActive: boolean = moment().isBetween(
    eventObj.schedule.event.startsOn,
    eventObj.schedule.event.endsOn
  );

  let isAuthEnabled: boolean = false;
  if (eventObj.isPublished && isRegistrationOpen) {
    isAuthEnabled = true;
  }

  let formattedEventObj: any = {
    id: eventObj.id,
    code: eventObj.code,
    name: eventObj.name,
    tagline: eventObj.tagline,
    venueLabel: eventObj.venue.label,
    venueUrl: eventObj.venue.url,
    website: eventObj.website,
    logoUrl: eventObj.branding.logoUrl,
    bannerUrl: eventObj.branding.bannerUrl,
    posterUrl: eventObj.branding.posterUrl,
    startsOn: eventObj.schedule.event.startsOn,
    endsOn: eventObj.schedule.event.endsOn,
    registrationStartsOn: eventObj.schedule.registration.startsOn,
    registrationEndsOn: eventObj.schedule.registration.endsOn,
    isPublished: eventObj.isPublished,
    isUpcoming: isEventUpcoming,
    isArchived: isEventArchived,
    isRegistrationOpen: isRegistrationOpen,
    isActive: isEventActive,
    isAuthEnabled: isAuthEnabled,
  };

  /* ---------------------------
  Sends invalid checkpoint error
  --------------------------- */
  if (!eventObj) {
    let resp = {
      status: "Failed",
      msg: "Failed: Event not found",
    };
    return res.json(resp);
  }

  /* ------------------
  Send success response
  ------------------ */
  let resp = {
    status: "Success",
    msg: "Event fetched",
    payload: formattedEventObj,
  };
  return res.status(200).json(resp);
});

export default router;
