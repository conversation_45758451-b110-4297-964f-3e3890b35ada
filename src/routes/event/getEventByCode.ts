import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { eventModel, userModel } from "../../models";
import { validateGetEventByCodeInputs } from "../../validation";

const router = Router();

router.post("/geteventbycode", isUserLogged, getUserID, async (req, res) => {
  /* -----------------
  Check is user logged
  ----------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Check if user is Admin
  ------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  // if (!userObj.settings.isAdmin) {
  //   let resp = {
  //     status: "Failed",
  //     msg: "Access Denied - You are not an admin",
  //   };
  //   return res.json(resp);
  // }

  /* ----------------------
  Extract & validate inputs
  ---------------------- */
  let { eventCode } = req.body;
  let { error } = validateGetEventByCodeInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    console.log(`/geteventbycode - Invalid inputs to retrieve event`);
    return res.json(resp);
  }

  /* -----------
  Retrieve event
  ----------- */
  let eventObj: any = await eventModel
    .findOne(
      { code: eventCode },
      "code name tagline venue website invoice branding schedule managers"
    )
    .exec();

  /* --------------------------
  Sends "event not found" error
  -------------------------- */
  if (!eventObj) {
    let resp = {
      status: "Failed",
      msg: "Failed: Event not found",
    };
    return res.json(resp);
  }

  let formattedEventObj: any = {
    code: eventObj.code,
    name: eventObj.name,
    tagline: eventObj.tagline,
    venueLabel: eventObj.venue.label,
    venueUrl: eventObj.venue.url,
    website: eventObj.website,
    invoicePrefix: eventObj.invoice.prefix,
    managers: eventObj.managers,
    logoUrl: eventObj.branding.logoUrl,
    bannerUrl: eventObj.branding.bannerUrl,
    posterUrl: eventObj.branding.posterUrl,
    startsOn: eventObj.schedule.event.startsOn,
    endsOn: eventObj.schedule.event.endsOn,
    registrationStartsOn: eventObj.schedule.registration.startsOn,
    registrationEndsOn: eventObj.schedule.registration.endsOn,
  };

  /* ------------------
  Send success response
  ------------------ */
  let resp = {
    status: "Success",
    msg: "Event details fetched",
    payload: formattedEventObj,
  };
  return res.status(200).json(resp);
});

export default router;
