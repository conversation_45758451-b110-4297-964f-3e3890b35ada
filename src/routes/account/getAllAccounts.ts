import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel } from "../../models";

const router = Router();

router.post("/get-all-accounts", isUserLogged, getUserID, async (req, res) => {
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });

  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found",
    };
    return res.json(resp);
  }

  let users: any;
  users = await userModel
    .find(
      {},
      "profile.name profile.email profile.country professional membership purchasedItems settings createdAt"
    )
    .sort({ createdAt: -1 })
    .exec();

  if (!users) {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve legacy",
    };
    return res.json(resp);
  }

  let payload: any = [];
  users.forEach((user: any) => {
    let obj = {
      firstName: user.profile.name.first,
      lastName: user.profile.name.last,
      email: user.profile.email,
      country: user.profile.country,
      occupation: user.professional.occupation,
      orgInsti: user.professional.orgInsti,
      jobDegree: user.professional.jobDegree,
      isMember: user.membership.isMember,
      memberID: user.membership.id,
      memberType: user.membership.type,
      startDate: user.membership.startDate,
      endDate: user.membership.endDate,
      createdAt: user.createdAt,
    };
    payload.push(obj);
  });

  let resp = {
    status: "Success",
    payload: payload,
  };
  return res.json(resp);
});

export default router;
