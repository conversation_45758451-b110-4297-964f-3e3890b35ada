import { Router } from "express";
import { isUserLogged } from "../../middleware";
import { userModel } from "../../models";
import { validatePasswordResetCode } from "../../validation";

const router = Router();
router.post("/verifypasswordresetcode", isUserLogged, async (req, res) => {
  /* Checks if user is logged */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }
  /* Validates email verification inputs */
  // let { error } = validatePasswordResetCode(req.body);
  // if (error) {
  //   let resp = {
  //     status: "Failed",
  //     msg: error.message,
  //   };
  //   return res.send(resp);
  // }

  /* Validates email verification inputs */
  // let { email, resetSessionID, passwordResetPublicCode } = req.body;
  // let { email, resetSessionID, passwordResetPublicCode } = req.body;
  // let passwordResetCode = resetSessionID + "-" + passwordResetPublicCode;
  let { email, passwordResetCode } = req.body;

  /* Validates email verification inputs */
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  /* Checks if User & if passwordResetCode is valid */
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "Reset Code could not be verified. Please try again!",
    };
    return res.json(resp);
  }
  if (userObj.settings.passwordResetCode != passwordResetCode) {
    let resp = {
      status: "Failed",
      msg: "Password Reset Code does not match",
    };
    return res.json(resp);
  }

  /* Updates code in the database */
  let resp = {
    status: "Success",
    msg: "Password Reset Code Sent",
  };
  return res.json(resp);
});

export default router;
