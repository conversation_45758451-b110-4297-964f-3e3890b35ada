// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, alertModel } from "../../models";
import { multer } from "../../helpers";
import { v2 } from "cloudinary";
import { createReadStream } from "streamifier";
import moment from "moment";
import { v4 as uuidv4 } from "uuid";
import { CLOUDINARY_OPTIONS } from "../../config";

const router = Router();

router.post(
  "/upload-id-card",
  isUserLogged,
  getUserID,
  multer.any(),
  async (req, res) => {
    /* ---------------------
    Checks if user is logged
    --------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* --------------------------
    Extracts inputs from req.body
    -------------------------- */
    let { idCardExpiry } = req.body;

    /* --------------------
    Prepares ID card expiry
    -------------------- */
    let idCardExpiryISOString = moment(
      idCardExpiry,
      "YYYY/MM/DD"
    ).toISOString();

    /* ------------
    Invalid ID card
    ------------ */
    let isIdCardExpired: boolean = false;
    isIdCardExpired = moment().isAfter(idCardExpiryISOString);
    if (isIdCardExpired) {
      let resp = {
        status: "Failed",
        msg: "ID card has expired",
      };
      return res.status(200).json(resp);
    }

    /* -------
    Read User
    ------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    /* -------
    Inits Vars
    ------- */
    let idCardFrontUrl: string = "";
    let idCardBackUrl: string = "";
    let studentName: string = "";

    if (userObj.profile.name.last) {
      studentName = `${userObj.profile.name.first}-${userObj.profile.name.last}`;
    } else {
      studentName = userObj.profile.name.first;
    }

    let folderName = `${studentName}-${_userID}`;

    /* ----------
    Extract files
    ---------- */
    const files: any = req.files;

    /* ------------
    Prepare Uploads
    ------------ */
    v2.config({
      cloud_name: CLOUDINARY_OPTIONS.cloudName,
      api_key: CLOUDINARY_OPTIONS.apiKey,
      api_secret: CLOUDINARY_OPTIONS.apiSecret,
    });

    let result: any = await fromBufferToCloudinary(
      files[0],
      folderName,
      "front"
    );
    idCardFrontUrl = result.secure_url;
    if (files[1]) {
      result = await fromBufferToCloudinary(files[1], folderName, "back");
      idCardBackUrl = result.secure_url;
    }

    /* ---------------
    Writes to database
    --------------- */
    let filter = {
      _userID: _userID,
    };
    let update = {
      "professional.idCard.type": userObj.professional.occupation,
      "professional.idCard.url.front": idCardFrontUrl,
      "professional.idCard.url.back": idCardBackUrl,
      "professional.idCard.expiry": idCardExpiryISOString,
      "professional.idCard.isVerified": false,
    };

    await userModel.findOneAndUpdate(filter, update, async (error, success) => {
      if (error) {
        console.log(error);
        let resp = {
          status: "Failed",
          msg: "An error occured while uploading ID card",
        };
        return res.json(resp);
      }

      if (success) {
        // Add alert
        let alertID: string = await uuidv4();
        let alertObj = {
          id: alertID,
          type: `${
            userObj.professional.occupation === "student"
              ? "Student"
              : "Professional"
          } ID Verification`,
          eventCode: "idVerification",
          issuer: {
            name: {
              first: userObj.profile.name.first,
              middle: userObj.profile.name.middle,
              last: userObj.profile.name.last,
            },
            email: userObj.profile.email,
          },
          resolver: {
            name: {
              first: "",
              middle: "",
              last: "",
            },
            email: "",
          },
          details: {
            prop1: idCardFrontUrl,
            prop2: idCardBackUrl,
            prop3: idCardExpiryISOString,
            prop4: userObj.professional.orgInsti,
            prop5: userObj.professional.jobDegree,
          },
          status: {
            isActive: true,
            remarks: "",
          },
          timestamp: {
            issuedOn: moment().toISOString(),
            resolvedOn: "",
          },
        };
        const alertInstance = await alertModel.create(alertObj);
        if (!alertInstance._id) {
          console.log("Creating ID verification alert failed!");
        }
        let resp = {
          status: "Success",
          msg: "ID card uploaded",
        };
        return res.json(resp);
      }
    });
  }
);

const fromBufferToCloudinary = (
  file: any,
  folderName: string,
  side: string
) => {
  return new Promise((resolve, reject) => {
    let cld_upload_stream = v2.uploader.upload_stream(
      {
        folder: `IDs/student/${folderName}/${side}`,
      },
      (error: any, result: any) => {
        if (result) {
          resolve(result);
        } else {
          reject(error);
        }
      }
    );
    createReadStream(file.buffer).pipe(cld_upload_stream);
  });
};

export default router;
