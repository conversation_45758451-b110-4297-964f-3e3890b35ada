// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel } from "../../models";

const router = Router();

router.post(
  "/convert-account-to-pro",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ---------------------
    Checks if user is logged
    --------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* -------
    Read User
    ------- */
    const _userID = res.locals.userID;

    /* ---------------
    Writes to database
    --------------- */
    let filter = {
      _userID: _userID,
    };
    let update = {
      "professional.occupation": "professional",
    };

    await userModel.findOneAndUpdate(filter, update, (error, success) => {
      if (error) {
        console.log(error);
        let resp = {
          status: "Failed",
          msg: "An error occured while converting account to Professional",
        };
        return res.json(resp);
      }
      if (success) {
        console.log("success converted account to professional");
        let resp = {
          status: "Success",
          msg: "Account converted to Professional",
        };
        return res.json(resp);
      }
    });
  }
);

export default router;
