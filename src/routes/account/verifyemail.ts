import { Router } from "express";
import { isUserLogged, getUser<PERSON> } from "../../middleware";
import { userModel } from "../../models";
import { validateEmailVerificationInputs } from "../../validation";

const router = Router();

router.post("/verifyemail", isUserLogged, getUserID, async (req, res) => {
  /* Checks if user is logged ----------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.json(resp);
  }

  /* Validates email verification inputs ------------ */
  let { error } = validateEmailVerificationInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.send(resp);
  }

  /* Extracts verificationCode from payload --------- */
  let { emailVerificationCode } = req.body;

  /* Get data from dB ------------------------------- */
  const _userID = res.locals.userID;
  const userObj: any = await userModel.findOne({
    _userID,
  });
  let isEmailVerified: boolean = userObj.meta.email.isVerified;
  let savedEmailVerificationCode: number = parseInt(
    userObj.meta.email.verificationCode
  );

  /* Check if email is already verified ------------- */
  if (isEmailVerified) {
    let resp = {
      status: "Failed",
      msg: "Your email is already verified",
    };
    return res.status(200).json(resp);
  }

  /* Check verificationCode ------------------------- */
  let isVerificationCodeSimilar: boolean = false;
  if (emailVerificationCode === savedEmailVerificationCode) {
    isVerificationCodeSimilar = true;
  }

  if (!isVerificationCodeSimilar) {
    let resp = {
      status: "Failed",
      msg: "Verification code is wrong",
    };
    return res.status(200).json(resp);
  }

  /* Update email verification status -------------- */
  let filter = {
    _userID: _userID,
  };
  let update = {
    "meta.email.isVerified": true,
    "meta.email.verificationCode": "",
  };

  let updateUserObj: any = await userModel.findOneAndUpdate(filter, update, {
    new: true,
  });

  if (updateUserObj.meta.email.isVerified) {
    let resp = {
      status: "Success",
      msg: "Your email is verified",
    };
    return res.json(resp);
  } else {
    let resp = {
      status: "Failed",
      msg: "COuld not verify your email",
    };
    return res.json(resp);
  }
});

export default router;
