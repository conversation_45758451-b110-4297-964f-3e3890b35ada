import { Router } from "express";
import { isUserLogged } from "../../middleware";
import { userModel } from "../../models";
// import { validateResendPasswordResetCode } from "../../validation";
import { v4 as uuidv4 } from "uuid";
import { sendPasswordResetCode } from "../../helpers";

const router = Router();
router.post("/resendpasswordresetcode", isUserLogged, async (req, res) => {
  /* Checks if user is logged */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* Validates email verification inputs */
  // let { error } = validateResendPasswordResetCode(req.body);
  // if (error) {
  //   let resp = {
  //     status: "Failed",
  //     msg: error.message,
  //   };
  //   return res.send(resp);
  // }

  /* Retrieves email & finds User */
  // let { email, resetSessionID } = req.body;
  let { email } = req.body;
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  /* Checks if User exists */
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "We could not find a user with this email. ",
    };
    return res.json(resp);
  }

  /* Checks if resetSessionID is valid */
  // let oldPasswordResetCode = userObj.settings.passwordResetCode;
  // let oldResetSessionID = oldPasswordResetCode.substr(
  //   0,
  //   oldPasswordResetCode.length - 5
  // );

  // if (oldResetSessionID != resetSessionID) {
  //   let resp = {
  //     status: "Failed",
  //     msg: "ResetSessionIDs don't match",
  //   };
  //   return res.json(resp);
  // }

  /* Retireves userID from userObj */
  let userID = userObj._userID;

  /* Generates reset code */
  // let newResetSessionID: string = await uuidv4();
  // let newPublicResetCode: number = Math.floor(1000 + Math.random() * 9000);
  // let newPasswordResetCode: string =
  //   newResetSessionID + "-" + newPublicResetCode;
  let newPasswordResetCode: any = Math.floor(1000 + Math.random() * 9000);

  /* Updates code in the database */
  let filter = {
    _userID: userID,
  };
  let update = {
    "settings.passwordResetCode": newPasswordResetCode,
  };
  await userModel.findOneAndUpdate(filter, update);

  /* Get user's firstName */
  let firstName = userObj.profile.name.first;

  /* Send password reset code */
  sendPasswordResetCode(firstName, email, newPasswordResetCode).then(
    (isPasswordResetCodeSent) => {
      if (isPasswordResetCodeSent)
        return res.json({
          status: "Success",
          msg: "Password Reset Code Sent",
        });
      else
        return res.json({
          status: "Failed",
          msg: "Unable to send password reset code",
        });
    }
  );
});

export default router;
