import { Router } from "express";
import { isUserLogged } from "../../middleware";
import { userModel } from "../../models";
import { validatePasswordConfirmation } from "../../validation";
import { sendPasswordChangeConfirmation } from "../../helpers";

const router = Router();
router.post("/confirmnewpassword", isUserLogged, async (req, res) => {
  /* Checks if user is logged */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.status(400).json(resp);
  }

  /* Validates email verification inputs */
  let { error } = validatePasswordConfirmation(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* Validates email verification inputs */
  let { email, resetSessionID, passwordResetPublicCode } = req.body;
  let passwordResetCode = resetSessionID + "-" + passwordResetPublicCode;

  /* Validates email verification inputs */
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  /* Checks if User & if passwordResetCode is valid */
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "Reset Code could not be verified. Please try again!",
    };
    return res.status(400).json(resp);
  }
  if (userObj.settings.passwordResetCode != passwordResetCode) {
    let resp = {
      status: "Failed",
      msg: "Password Reset Code does not match",
    };
    return res.status(400).json(resp);
  }

  /* Get userID from userObj */
  let userID = userObj._userID;

  /* Updates code in the database */
  let filter = {
    _userID: userID,
  };
  let update = {
    "settings.passwordResetCode": "",
  };
  await userModel.findOneAndUpdate(filter, update);

  /* Get user's firstName */
  let firstName = userObj.profile.name.first;

  /* Send password change confirmation */
  sendPasswordChangeConfirmation(firstName, email).then(
    (isPasswordResetCodeSent) => {
      if (isPasswordResetCodeSent)
        return res.json({
          status: "Success",
          msg: "Password Change Confirmed",
        });
      else
        return res.status(400).json({
          status: "Failed",
          msg: "Unable to send password reset code",
        });
    }
  );
});

export default router;
