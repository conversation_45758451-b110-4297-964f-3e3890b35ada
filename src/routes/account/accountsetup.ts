import { Router } from "express";
import { loginHelper } from "../../helpers";
import { isUserLogged } from "../../middleware";
import { checkpointModel, userModel, alertModel } from "../../models";
import { validateAccountSetupInputs } from "../../validation";

const router = Router();
router.post("/accountsetup", isUserLogged, async (req, res) => {
  /* ---------------------
  Checks if user is logged
  --------------------- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.status(400).json(resp);
  }

  /* ---------------------------
  Validates account setup inputs
  --------------------------- */
  let { error } = validateAccountSetupInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* --------------------------
  Extracts inputs fron req.body
  -------------------------- */
  let {
    occupation,
    orgInsti,
    jobDegree,
    country,
    currency,
    isHCIPAIMember,
    checkpointID,
    isGSTInvoicePreferred,
    businessName,
    taxID,
    taxJurisdiction,
    billingAddressLine1,
    billingAddressLine2,
    billingAddressLine3,
  } = req.body;

  /* -----------------
  Retrieves Checkpoint
  ----------------- */
  let checkpointObj: any = await checkpointModel.findOne({
    "checkpoint.id": checkpointID,
  });

  /* ---------------------------
  Sends invalid checkpoint error
  --------------------------- */
  if (!checkpointObj) {
    let resp = {
      status: "Failed",
      msg: "Invalid checkpoint",
    };
    res.json(resp);
  }

  /* ------------------------
  Checks if email is verified
  ------------------------ */
  if (!checkpointObj.checkpoint.isEmailVerified) {
    let resp = {
      status: "Failed",
      msg: "Email is not verified",
    };
    res.json(resp);
  }

  /* ----------------------------
  Checks if account already setup
  ---------------------------- */
  if (checkpointObj.checkpoint.isAccountSetup) {
    let resp = {
      status: "Failed",
      msg: "Account already setup",
    };
    res.json(resp);
  }

  /* ---------------
  Writes to database
  --------------- */
  let currencySymbol = "";

  if (currency === "eur") {
    currencySymbol = "€";
  } else if (currency === "inr") {
    currencySymbol = "₹";
  } else if (currency === "usd") {
    currencySymbol = "$";
  }

  let currencyObj = {
    name: currency,
    symbol: currencySymbol,
  };

  const newUser: any = await userModel
    .create({
      _userID: checkpointObj!._userID,
      profile: {
        name: checkpointObj.profile.name,
        email: checkpointObj.profile.email,
        psswd: checkpointObj.profile.psswd,
        mobile: checkpointObj.profile.mobile,
        country: country,
      },
      professional: {
        occupation: occupation,
        orgInsti: orgInsti,
        jobDegree: jobDegree,
      },
      membership: {
        isMember: false,
        id: "",
        type: "",
        startDate: "",
        endDate: "",
        isInCart: false,
        memberFromRegistration: isHCIPAIMember,
        submittedID: "",
      },
      settings: {
        isRememberMe: true,
        isRegistrationManager: false,
        isAdmin: false,
        currency: currencyObj,
        billing: {
          tax: {
            india: {
              gst: {
                isGSTInvoicePreferred: isGSTInvoicePreferred,
                businessName: businessName,
                taxID: taxID,
                taxJurisdiction: taxJurisdiction,
                address: {
                  billing: {
                    line1: billingAddressLine1,
                    line2: billingAddressLine2,
                    line3: billingAddressLine3,
                  },
                },
              },
            },
          },
        },
      },
      signature: checkpointObj.signature,
    })
    .catch((error) => {
      let resp = {
        status: "Failed",
        msg: "An error occured during account setup",
      };
      console.log(error);
      res.status(200).json(resp);
    });

  if (!newUser._id) {
    let resp = {
      status: "Failed",
      msg: "Account creation failed!",
    };
    res.status(200).json(resp);
  }

  /* --------------------------------
  Updates isAccountSetup flag to true
  -------------------------------- */
  let filter = {
    "checkpoint.id": checkpointID,
  };
  let updateAccountSetupFlag = {
    "checkpoint.isAccountSetup": true,
  };
  await checkpointModel.findOneAndUpdate(filter, updateAccountSetupFlag);

  /* deletesAlertObj */
  alertModel
    .deleteMany({ "issuer.email": checkpointObj.profile.email })
    .then(() => {
      console.log("AlertObj successfully deleted");
    })
    .catch((error) => {
      console.log(`Error: ${error}`);
    });

  /* -------------------------------------
  Sends successful accountCreation message
  ------------------------------------- */
  loginHelper(req, res, newUser!._userID);
  let resp = {
    status: "Success",
    msg: "Account created & user logged in",
  };
  res.json(resp);
});

export default router;
