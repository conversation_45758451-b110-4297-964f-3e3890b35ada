import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, ticketModel_v2 } from "../../models";
import moment from "moment";

const router = Router();

router.get(
  "/purchases-summary-v2",
  isUserLogged,
  getUserID,
  async (req, res) => {
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    if (!userObj) {
      let resp = {
        status: "Failed",
        msg: "User not found",
      };
      return res.json(resp);
    }

    let persona = userObj.professional.occupation;
    if (userObj?.purchasedItems.length === 0) {
      let resp = {
        status: "Success",
        msg: "You haven't made any purchases",
      };
      return res.send(resp);
    }

    let payload: any = [];
    await userObj?.purchasedItems.forEach(async (item: any) => {
      let itemObj: any = {
        eventCode: "",
        currency: "",
        purchaseDate: "",
        paymentStatus: "",
        isCouponApplied: false,
        couponName: "",
        couponDeductionType: "",
        couponDeductionValue: 0,
        cartAmountBeforeDeduction: 0,
        deductedAmount: 0,
        cartAmountAfterDeduction: 0,
        cartTotal: 0,
        gatewayFee: 0,
        grandTotal: 0,
        transactionId: "",
        purchaseId: "",
        paymentMethod: "",
        purchasedItems: [],
      };

      itemObj.eventCode = item.eventCode;
      itemObj.currency = "₹";
      itemObj.purchaseDate = item.createdAt;
      itemObj.paymentStatus = item.paymentStatus;
      itemObj.isCouponApplied = item.isCouponApplied;
      itemObj.couponName = item.coupon.name;
      itemObj.couponDeductionType = item.coupon.deductionType;
      itemObj.couponDeductionValue = item.coupon.deductionValue;
      itemObj.cartAmountBeforeDeduction = item.coupon.cartAmountBeforeDeduction;
      itemObj.deductedAmount = item.coupon.deductedAmount;
      itemObj.cartAmountAfterDeduction = item.coupon.cartAmountAfterDeduction;
      itemObj.cartTotal = item.cartTotal;
      itemObj.gatewayFee = item.gatewayFee;
      itemObj.grandTotal = item.grandTotal;
      itemObj.transactionId = item.transactionID;
      itemObj.purchaseId = item.purchaseID;
      itemObj.paymentMethod = item.paymentGateway;

      let purchasedItemIDs: any = [];
      item.purchasedItems.forEach((item: any) => {
        purchasedItemIDs.push(item.ticketId);
      });

      let purchasedSessionIds: any = [];
      item.purchasedItems.forEach((item: any) => {
        purchasedItemIDs.push(item.ticketId);
        if (item.sessionId) {
          purchasedSessionIds.push(item.sessionId);
        }
      });

      const purchasedTicketDetailsArr = await ticketModel_v2
        .find()
        .where("ticketId")
        .in(purchasedItemIDs)
        .exec();

      let purchasedTicketsArr: Array<Object> = [];
      purchasedTicketDetailsArr.forEach((ticket: any) => {
        let ticketPrice = 0;
        let ticketTier = "";
        let ticketTitle = "";
        let tierStudentPrice: number = 0;
        let tierProfessionalPrice: number = 0;
        let ticketPricingTiers: any;
        let purchasedTicketObj: any = {
          title: "",
          tier: "",
          price: "",
        };

        if (ticket.ticketType === "fullTicket") {
          ticketPricingTiers = ticket.fullTicketDetails.tiers;
          ticketTitle = ticket.fullTicketDetails.title;
          ticketPricingTiers.map((tier: any) => {
            let purchaseDate = item.createdAt;
            let isBetween: any = moment(purchaseDate).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );
            if (isBetween) {
              ticketTier = tier.tierName;
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });
          if (persona === "student") {
            ticketPrice = tierStudentPrice;
          } else if (persona === "professional") {
            ticketPrice = tierProfessionalPrice;
          }
          purchasedTicketObj.title = ticketTitle;
          purchasedTicketObj.tier = `${persona.toUpperCase()} - ${ticketTier}`;
          purchasedTicketObj.price = ticketPrice;
        } else if (ticket.ticketType === "basicTicket") {
          ticketPricingTiers = ticket.basicTicketDetails.tiers;
          ticketTitle = ticket.basicTicketDetails.title;
          ticketPricingTiers.map((tier: any) => {
            let purchaseDate = item.createdAt;
            let isBetween: any = moment(purchaseDate).isBetween(
              tier.tierStartDate,
              tier.tierEndDate
            );
            if (isBetween) {
              ticketTier = tier.tierName;
              tierStudentPrice = tier.studentPrice;
              tierProfessionalPrice = tier.professionalPrice;
            }
          });
          if (persona === "student") {
            ticketPrice = tierStudentPrice;
          } else if (persona === "professional") {
            ticketPrice = tierProfessionalPrice;
          }
          purchasedTicketObj.title = ticketTitle;
          purchasedTicketObj.tier = `${persona.toUpperCase()} - ${ticketTier}`;
          purchasedTicketObj.price = ticketPrice;
        } else if (ticket.ticketType === "trackTicket") {
          let trackSessions: any = ticket.trackTicketDetails.sessions;
          purchasedSessionIds.map((purchasedSessionId: any) => {
            trackSessions.map((trackSession: any) => {
              if (purchasedSessionId === trackSession.id) {
                purchasedTicketsArr.push({
                  title: trackSession.title,
                  tier: trackSession.type,
                  price:
                    persona === "professional"
                      ? trackSession.professionalPrice
                      : trackSession.studentPrice,
                });
              }
            });
          });
        }

        if (
          ticket.ticketType === "fullTicket" ||
          ticket.ticketType === "basicTicket"
        ) {
          purchasedTicketsArr.push(purchasedTicketObj);
        }
      });

      if (item.eventCode === "membership") {
        let membershipType: string = item.purchasedItems[0].ticketID;
        let membershipName: string = "";

        if (membershipType === "lifetimeMembership") {
          membershipName = "LIFETIME";
        } else if (membershipType === "annualMembership") {
          membershipName = "ANNUAL";
        }

        let purchasedTicketObj: any = {
          title: "HCIPAI Membership",
          tier: membershipName,
          price: item.cartTotal,
        };
        purchasedTicketsArr.push(purchasedTicketObj);
      }

      itemObj.purchasedItems = purchasedTicketsArr;
      payload.push(itemObj);

      if (payload.length === userObj?.purchasedItems.length) {
        let resp = {
          status: "Success",
          msg: "Purchases retrieved",
          payload: payload,
        };
        return res.json(resp);
      }
    });
  }
);

export default router;
