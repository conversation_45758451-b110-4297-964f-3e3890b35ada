import { Router } from "express";
import { alertModel, checkpointModel } from "../../models";
import { validateRegistrationInputs } from "../../validation";
import { isUserLogged } from "../../middleware";
import * as argon from "argon2";
import { v4 as uuidv4 } from "uuid";
import * as crypto from "crypto-js";
import moment from "moment";

const router = Router();

router.post("/register", isUserLogged, async (req, res) => {
  /* --- isUserLoggedIn --- */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* --- isRegistrationInputsValid --- */
  let { error } = validateRegistrationInputs(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.json(resp);
  }

  /* --- extractRegistrationInputs --- */
  let {
    firstName,
    lastName,
    email,
    password,
    occupation,
    isdCode,
    mobileCountry,
    mobileNumber,
  } = req.body;

  /* --- generateEmailSignature --- */
  email = email.toLowerCase().trim();
  let signature = crypto
    .SHA3(email, {
      outputLength: 256,
    })
    .toString();

  /* --- generateIDforUserAndCheckpoint --- */
  let _userID: string = await uuidv4();
  let checkpointID: string = await uuidv4();
  let alertID: string = await uuidv4();

  /* --- hasThePassword --- */
  let psswd: string = await argon.hash(password, {
    type: argon.argon2id,
  });

  /* --- generateEmailVerificationCode --- */
  let publicEmailVerificationCode: number = Math.floor(
    1000 + Math.random() * 9000
  );
  let emailVerificationCode: string =
    checkpointID + "-" + publicEmailVerificationCode;

  /* --- createProfileObject --- */
  let profile = {
    name: {
      first: firstName,
      last: lastName,
    },
    email: email,
    psswd: psswd,
    mobile: {
      isdCode: isdCode,
      country: mobileCountry,
      number: mobileNumber,
    },
  };

  const existingCheckpoint: any = await checkpointModel.findOne({ signature });

  if (existingCheckpoint) {
    if (existingCheckpoint.checkpoint.isAccountSetup) {
      let resp = {
        status: "Failed",
        msg: "A user is already registered with this email",
      };
      return res.json(resp);
    } else if (existingCheckpoint.checkpoint.isEmailVerified) {
      /* --- updateCheckpoint&resetIsEmailVerified --- */
      let filter = {
        signature: signature,
      };
      let update = {
        _userID: _userID,
        "profile.name.first": firstName,
        "profile.name.last": lastName,
        "profile.psswd": psswd,
        "profile.mobile.isdCode": isdCode,
        "profile.mobile.country": mobileCountry,
        "profile.mobile.number": mobileNumber,
        "checkpoint.id": checkpointID,
        "checkpoint.isEmailVerified": false,
        "checkpoint.emailVerificationCode": emailVerificationCode,
      };
      await checkpointModel.findOneAndUpdate(filter, update);
    } else {
      /* --- updateCheckpoint --- */
      let filter = {
        signature: signature,
      };
      let update = {
        _userID: _userID,
        "profile.name.first": firstName,
        "profile.name.last": lastName,
        "profile.psswd": psswd,
        "profile.mobile.isdCode": isdCode,
        "profile.mobile.country": mobileCountry,
        "profile.mobile.number": mobileNumber,
        "checkpoint.id": checkpointID,
        "checkpoint.emailVerificationCode": emailVerificationCode,
      };
      await checkpointModel.findOneAndUpdate(filter, update);
    }
  } else {
    /* --- createCheckpoint --- */
    let checkpoint = {
      id: checkpointID,
      isEmailVerified: false,
      isAccountSetup: false,
      emailVerificationCode: emailVerificationCode,
    };
    const userInstance = await checkpointModel.create({
      _userID,
      profile,
      checkpoint,
      signature,
    });
    if (!userInstance._id) {
      let resp = {
        status: "Failed",
        msg: "Couldn't register",
      };
      return res.json(resp);
    }
    let alertObj = {
      id: alertID,
      type: "Partial Registration",
      issuer: {
        name: {
          first: profile.name.first,
          middle: "",
          last: profile.name.last,
        },
        email: profile.email,
      },
      resolver: {
        name: {
          first: "",
          middle: "",
          last: "",
        },
        email: "",
      },
      details: {
        prop1: "",
        prop2: "",
        prop3: "",
        prop4: "",
        prop5: "",
      },
      status: {
        isActive: true,
        remarks: "",
      },
      timestamp: {
        issuedOn: moment().toISOString(),
        resolvedOn: "",
      },
    };
    const alertInstance = await alertModel.create(alertObj);
    if (!alertInstance._id) console.log("Creating Checkpoint alertObj failed");
    else console.log("Checkpoint alertObj Created");
  }
});

export default router;
