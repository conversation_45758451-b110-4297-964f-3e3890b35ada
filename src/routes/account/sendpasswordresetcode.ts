import { Router } from "express";
import { isUserLogged } from "../../middleware";
import { userModel } from "../../models";
import { validateEmail } from "../../validation";
import { v4 as uuidv4 } from "uuid";
import { sendPasswordResetCode } from "../../helpers";

const router = Router();
router.post("/sendpasswordresetcode", isUserLogged, async (req, res) => {
  /* Checks if user is logged */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.json(resp);
  }

  /* Validates email verification inputs */
  let { error } = validateEmail(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.send(resp);
  }

  /* Retrieves email & finds User */
  let { email } = req.body;
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  /* Checks if User exists */
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "We could not find a user with this email",
    };
    return res.json(resp);
  }

  /* Retireves userID from userObj */
  let userID = userObj._userID;

  /* Generates reset code */
  // let resetSessionID: string = await uuidv4();
  // let publicResetCode: number = Math.floor(1000 + Math.random() * 9000);
  // let passwordResetCode: string = resetSessionID + "-" + publicResetCode;
  let passwordResetCode: any = Math.floor(1000 + Math.random() * 9000);

  /* Updates code in the database */
  let filter = {
    _userID: userID,
  };
  let update = {
    "settings.passwordResetCode": passwordResetCode,
  };
  await userModel.findOneAndUpdate(filter, update);

  /* Get user's firstName */
  let firstName = userObj.profile.name.first;

  /* Send password reset code */
  sendPasswordResetCode(firstName, email, passwordResetCode).then(
    (isPasswordResetCodeSent) => {
      if (isPasswordResetCodeSent)
        return res.json({
          status: "Success",
          msg: "Password Reset Code Sent",
        });
      else
        return res.json({
          status: "Failed",
          msg: "Unable to send password reset code",
        });
    }
  );
});

export default router;
