// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel } from "../../models";
import { validateAccountSetupInputs_v2 } from "../../validation";

const router = Router();

router.post("/accountsetupv2", isUserLogged, getUserID, async (req, res) => {
  /* ---------------------
  Checks if user is logged
  --------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ---------------------------
  Validates account setup inputs
  --------------------------- */
  let { error } = validateAccountSetupInputs_v2(req.body);
  if (error) {
    let resp = {
      status: "Failed",
      msg: error.message,
    };
    return res.status(400).send(resp);
  }

  /* --------------------------
  Extracts inputs from req.body
  -------------------------- */
  let {
    orgInsti,
    jobDegree,
    country,
    mobileIsdCode,
    mobileCountry,
    mobileNumber,
    isGSTInvoicePreferred,
    businessName,
    taxID,
    taxJurisdiction,
    billingAddressLine1,
    billingAddressLine2,
    billingAddressLine3,
  } = req.body;

  /* ---------------
  Writes to database
  --------------- */

  const _userID = res.locals.userID;

  let currencyName: string = "inr";
  let currencySymbol: string = "₹";

  let filter = {
    _userID: _userID,
  };
  let update = {
    "meta.account.isSetupComplete": true,
    "professional.orgInsti": orgInsti,
    "professional.jobDegree": jobDegree,
    "profile.country": country,
    "mobile.isdCode": mobileIsdCode,
    "mobile.country": mobileCountry,
    "mobile.number": mobileNumber,
    "settings.currency.name": currencyName,
    "settings.currency.symbol": currencySymbol,
    "settings.billing.tax.india.gst.isGSTInvoicePreferred":
      isGSTInvoicePreferred,
    "settings.billing.tax.india.gst.businessName": businessName,
    "settings.billing.tax.india.gst.taxID": taxID,
    "settings.billing.tax.india.gst.taxJurisdiction": taxJurisdiction,
    "settings.billing.tax.india.gst.address.billing.line1": billingAddressLine1,
    "settings.billing.tax.india.gst.address.billing.line2": billingAddressLine2,
    "settings.billing.tax.india.gst.address.billing.line3": billingAddressLine3,
  };

  //   console.log(`userID: ${res.locals.userID}`);
  //   let result = await userModel.findOneAndUpdate(filter, update);
  //   console.log(result);

  await userModel.findOneAndUpdate(filter, update, (error, success) => {
    if (error) {
      console.log(error);
      let resp = {
        status: "Failed",
        msg: "An error occured while account setup",
      };
      return res.json(resp);
    }
    if (success) {
      console.log("success");
      let resp = {
        status: "Success",
        msg: "Account setup complete",
      };
      return res.json(resp);
    }
  });
});

export default router;
