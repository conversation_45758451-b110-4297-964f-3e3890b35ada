import { Router } from "express";
import { isUserLogged } from "../../middleware";
import { userModel } from "../../models";
import { validateNewPassword } from "../../validation";
import * as argon from "argon2";

const router = Router();
router.post("/resetpassword", isUserLogged, async (req, res) => {
  /* Checks if user is logged */
  if (res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are already logged in",
    };
    return res.status(400).json(resp);
  }

  /* Validates email verification inputs */
  // let { error } = validateNewPassword(req.body);
  // if (error) {
  //   let resp = {
  //     status: "Failed",
  //     msg: error.message,
  //   };
  //   return res.status(400).send(resp);
  // }

  /* Validates email verification inputs */
  // let {
  //   email,
  //   resetSessionID,
  //   passwordResetPublicCode,
  //   newPassword,
  //   confirmNewPassword,
  // } = req.body;
  // let passwordResetCode = resetSessionID + "-" + passwordResetPublicCode;
  let { email, passwordResetCode, newPassword } = req.body;

  /* Validates email verification inputs */
  const userObj: any = await userModel.findOne({
    "profile.email": email,
  });

  /* Checks if User & if passwordResetCode is valid */
  if (!userObj) {
    let resp = {
      status: "Failed",
      msg: "User not found. Please try again!",
    };
    return res.status(400).json(resp);
  }

  if (userObj.settings.passwordResetCode != passwordResetCode) {
    let resp = {
      status: "Failed",
      msg: "Password Reset Code does not match",
    };
    return res.status(400).json(resp);
  }

  /* Generate new password */
  let psswd: string = await argon.hash(newPassword, {
    type: argon.argon2id,
  });

  /* Get userID from userObj */
  let userID = userObj._userID;

  /* Updates code in the database */
  let filter = {
    _userID: userID,
  };
  let update = {
    "profile.psswd": psswd,
  };
  await userModel.findOneAndUpdate(filter, update);

  /* Updates code in the database */
  let resp = {
    status: "Success",
    msg: "Password Changed",
  };
  return res.json(resp);
});

export default router;
