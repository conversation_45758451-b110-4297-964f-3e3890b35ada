// @ts-nocheck

import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, alertModel } from "../../models";
import { multer } from "../../helpers";
import { v2 } from "cloudinary";
import { createReadStream } from "streamifier";
import { validateAccountSetupInputs_v2 } from "../../validation";
import moment from "moment";
import { v4 as uuidv4 } from "uuid";
import { CLOUDINARY_OPTIONS } from "../../config";

const router = Router();

router.post(
  "/accountsetupv2-student",
  isUserLogged,
  getUserID,
  multer.any(),
  async (req, res) => {
    /* ---------------------
    Checks if user is logged
    --------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ---------------------------
    Validates account setup inputs
    --------------------------- */
    let { error } = validateAccountSetupInputs_v2(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.status(400).send(resp);
    }

    /* --------------------------
    Extracts inputs from req.body
    -------------------------- */
    let {
      orgInsti,
      jobDegree,
      country,
      idCardExpiry,
      mobileIsdCode,
      mobileCountry,
      mobileNumber,
      isGSTInvoicePreferred,
      businessName,
      taxID,
      taxJurisdiction,
      billingAddressLine1,
      billingAddressLine2,
      billingAddressLine3,
    } = req.body;

    /* --------------------
    Prepares ID card expiry
    -------------------- */
    let idCardExpiryISOString = moment(
      idCardExpiry,
      "YYYY/MM/DD"
    ).toISOString();

    /* ------------
    Invalid ID card
    ------------ */
    let isIdCardExpired: boolean = false;
    isIdCardExpired = moment().isAfter(idCardExpiryISOString);
    if (isIdCardExpired) {
      let resp = {
        status: "Failed",
        msg: "ID card has expired",
      };
      return res.status(200).json(resp);
    }

    /* -------
    Read User
    ------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });

    /* -------
    Inits Vars
    ------- */
    let idCardFrontUrl: string = "";
    let idCardBackUrl: string = "";
    let currencyName: string = "inr";
    let currencySymbol: string = "₹";
    let studentName: string = "";

    if (userObj.profile.name.last) {
      studentName = `${userObj.profile.name.first}-${userObj.profile.name.last}`;
    } else {
      studentName = userObj.profile.name.first;
    }

    let folderName = `${studentName}-${_userID}`;

    /* ----------
    Extract files
    ---------- */
    const files: any = req.files;

    /* ------------
    Prepare Uploads
    ------------ */
    v2.config({
      cloud_name: CLOUDINARY_OPTIONS.cloudName,
      api_key: CLOUDINARY_OPTIONS.apiKey,
      api_secret: CLOUDINARY_OPTIONS.apiSecret,
    });

    let result: any = await fromBufferToCloudinary(
      files[0],
      folderName,
      "front"
    );
    idCardFrontUrl = result.secure_url;
    if (files[1]) {
      result = await fromBufferToCloudinary(files[1], folderName, "back");
      idCardBackUrl = result.secure_url;
    }

    /* ---------------
    Writes to database
    --------------- */
    let filter = {
      _userID: _userID,
    };
    let update = {
      "meta.account.isSetupComplete": true,
      "professional.orgInsti": orgInsti,
      "professional.jobDegree": jobDegree,
      "professional.idCard.type": "student",
      "professional.idCard.url.front": idCardFrontUrl,
      "professional.idCard.url.back": idCardBackUrl,
      "professional.idCard.expiry": idCardExpiryISOString,
      "professional.idCard.isVerified": false,
      "profile.country": country,
      "mobile.isdCode": mobileIsdCode,
      "mobile.country": mobileCountry,
      "mobile.number": mobileNumber,
      "settings.currency.name": currencyName,
      "settings.currency.symbol": currencySymbol,
      "settings.billing.tax.india.gst.isGSTInvoicePreferred":
        isGSTInvoicePreferred,
      "settings.billing.tax.india.gst.businessName": businessName,
      "settings.billing.tax.india.gst.taxID": taxID,
      "settings.billing.tax.india.gst.taxJurisdiction": taxJurisdiction,
      "settings.billing.tax.india.gst.address.billing.line1":
        billingAddressLine1,
      "settings.billing.tax.india.gst.address.billing.line2":
        billingAddressLine2,
      "settings.billing.tax.india.gst.address.billing.line3":
        billingAddressLine3,
    };

    await userModel.findOneAndUpdate(filter, update, async (error, success) => {
      if (error) {
        console.log(error);
        let resp = {
          status: "Failed",
          msg: "An error occured while account setup",
        };
        return res.json(resp);
      }

      if (success) {
        // Add alert
        let alertID: string = await uuidv4();
        let alertObj = {
          id: alertID,
          type: "Student ID Verification",
          eventCode: "idVerification",
          issuer: {
            name: {
              first: userObj.profile.name.first,
              middle: userObj.profile.name.middle,
              last: userObj.profile.name.last,
            },
            email: userObj.profile.email,
          },
          resolver: {
            name: {
              first: "",
              middle: "",
              last: "",
            },
            email: "",
          },
          details: {
            prop1: idCardFrontUrl,
            prop2: idCardBackUrl,
            prop3: idCardExpiryISOString,
            prop4: orgInsti,
            prop5: jobDegree,
          },
          status: {
            isActive: true,
            remarks: "",
          },
          timestamp: {
            issuedOn: moment().toISOString(),
            resolvedOn: "",
          },
        };
        const alertInstance = await alertModel.create(alertObj);
        if (!alertInstance._id) {
          console.log("Creating ID verification alert failed!");
        }
        let resp = {
          status: "Success",
          msg: "Account setup complete",
        };
        return res.json(resp);
      }
    });
  }
);

const fromBufferToCloudinary = (
  file: any,
  folderName: string,
  side: string
) => {
  return new Promise((resolve, reject) => {
    let cld_upload_stream = v2.uploader.upload_stream(
      {
        folder: `IDs/student/${folderName}/${side}`,
      },
      (error: any, result: any) => {
        if (result) {
          resolve(result);
        } else {
          reject(error);
        }
      }
    );
    createReadStream(file.buffer).pipe(cld_upload_stream);
  });
};

export default router;
