import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import csv from "csvtojson";
import upload from "../../helpers/upload";
import { invoiceGroupModel } from "../../models";
import moment from "moment";
import { v4 as uuidv4 } from "uuid";

const router = Router();

router.post(
  "/upload-invoice-group-data",
  isUserLogged,
  getUserID,
  upload.single("invoiceGroupData"),
  async (req, res) => {
    let csvFilePath: any = req?.file?.path;
    let invoiceObjArr: any;

    await csv()
      .fromFile(csvFilePath)
      .then((localisationCsvRow) => {
        invoiceObjArr = localisationCsvRow;
      });

    let { invoiceGroupName } = req.body;

    /* --------------
    Get event details
    -------------- */
    // let eventsObj: any = await eventModel
    //   .find({}, "code branding name invoice")
    //   .exec();

    /* --------
    Preparation
    -------- */
    let sellerCin: string = "U72900PN2013NPL147301";
    let sellerGstin: string = "27AADCH2139P2Z8";
    let proposedInvoiceArray: any = [];
    let invoiceDate: string = moment(new Date()).format("DD/MM/YYYY");
    invoiceDate = invoiceDate.replace(/\//g, "-");

    await invoiceObjArr.map(async (invoiceObj: any) => {
      let invoiceId: string = await uuidv4();
      let branding: any;
      let signatory: any;

      // if (invoiceObj.eventCode === "membership") {
      //   branding = {
      //     primaryLogoUrl:
      //       "https://res.cloudinary.com/layerpark/image/upload/v1668714370/hcipai/main/hcipai-logo_nihn9o.png",
      //     secondaryLogoUrl: "",
      //   };
      //   signatory = {
      //     primary: {
      //       name: "Shashank Deshpande",
      //       designation: "Director",
      //       signatureUrl:
      //         "https://res.cloudinary.com/layerpark/image/upload/v1670697762/hcipai/main/signature/shashank-deshpande-signature_sxlhyb.png",
      //     },
      //     secondary: {
      //       name: "",
      //       designation: "",
      //       signatureUrl: "",
      //     },
      //   };
      // } else {
      //   eventsObj.map((event: any) => {
      //     if (event.code === invoiceObj.eventCode) {
      //       branding = {
      //         primaryLogoUrl:
      //           "https://res.cloudinary.com/layerpark/image/upload/v1668714370/hcipai/main/hcipai-logo_nihn9o.png",
      //         secondaryLogoUrl: event.branding.logoUrl_Rectangle,
      //       };
      //       signatory = event.invoice.signatory;
      //     }
      //   });
      // }

      if (
        invoiceObj.eventCode ===
        "visual-discourse-2024-b5dbe345-8dd9-4ec3-bdb9-4de7388f7dee"
      ) {
        branding = {
          primaryLogoUrl:
            "https://res.cloudinary.com/layerpark/image/upload/v1731391336/hcipai/vd2024/logo_vwiqls.png",
          secondaryLogoUrl: "",
        };

        signatory = {
          primary: {
            name: "Dr. Prasad Bokil",
            designation: "Conference Chair, Visual Discourse 2024",
            signatureUrl:
              "https://res.cloudinary.com/layerpark/image/upload/v1731391337/hcipai/vd2024/sign_fsxeg0.png",
          },
          secondary: {
            name: "",
            designation: "",
            signatureUrl: "",
          },
        };
      } else {
        branding = {
          primaryLogoUrl:
            "https://res.cloudinary.com/layerpark/image/upload/v1668714370/hcipai/main/hcipai-logo_nihn9o.png",
          secondaryLogoUrl: "",
        };

        signatory = {
          primary: {
            name: "Shashank Deshpande",
            designation: "Director, HCIPAI",
            signatureUrl:
              "https://res.cloudinary.com/layerpark/image/upload/v1670697762/hcipai/main/signature/shashank-deshpande-signature_sxlhyb.png",
          },
          secondary: {
            name: "",
            designation: "",
            signatureUrl: "",
          },
        };
      }

      let proposedInvoiceObj: any = {
        id: invoiceId,
        eventCode: invoiceObj.eventCode,
        eventName:
          invoiceObj.eventCode === "membership"
            ? "HCIPAI Membership"
            : invoiceObj.eventName,
        sellerCin: sellerCin,
        sellerGstin: sellerGstin,
        invoiceNo: invoiceObj.invoiceNo,
        invoiceDate: invoiceObj.invoiceDate,
        isGstInvoiceRequested:
          invoiceObj.isGstInvoiceRequired === "FALSE" ? false : true,
        buyerName: invoiceObj.fullName,
        buyerEmail: invoiceObj.email,
        buyerGstin: invoiceObj.gstin,
        buyerCompany: invoiceObj.gstRegistrantName,
        buyerLocation: invoiceObj.location,
        isCouponApplied: invoiceObj.isCouponApplied === "TRUE" ? true : false,
        ticketNarration: invoiceObj.ticketNarration,
        couponName: invoiceObj.couponName,
        couponDeduction: invoiceObj.couponDeduction,
        ticketBasePrice: invoiceObj.ticketBasePrice,
        processingCharges: invoiceObj.transactionCost,
        totalTaxableValue: invoiceObj.totalTaxableAmount,
        cgst: invoiceObj.cgst,
        sgst: invoiceObj.sgst,
        igst: invoiceObj.igst,
        totalPaidAmount: invoiceObj.totalPaidAmount,
        paymentMethod: invoiceObj.paymentMethod,
        branding: branding,
        signatory: signatory,
      };

      proposedInvoiceArray.push(proposedInvoiceObj);
    });

    let proposedInvoiceGroupId: string = await uuidv4();
    let proposedInvoiceGroup: any = {
      id: proposedInvoiceGroupId,
      name: invoiceGroupName,
      invoiceCount: proposedInvoiceArray.length,
      bulkSendCount: 0,
      invoices: proposedInvoiceArray,
      createdOn: moment().toISOString(),
    };

    let newInvoiceGroup: any = await invoiceGroupModel
      .create(proposedInvoiceGroup)
      .catch((error) => {
        let resp = {
          status: "Failed",
          msg: "An error occured during invoice group creation",
        };
        res.status(200).json(resp);
      });

    if (!newInvoiceGroup._id) {
      let resp = {
        status: "Failed",
        msg: "Invoice group creation failed!",
      };
      res.status(200).json(resp);
    }

    let resp = {
      status: "Success",
      msg: "Invoice group created",
    };

    res.json(resp);
  }
);

export default router;
