import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { invoiceGroupModel, eventModel } from "../../models";
import { sendInvoiceHelper } from "../../helpers";
import { APP_OPTIONS } from "../../config";

const router = Router();

router.post("/send-all-invoices", isUserLogged, getUserID, async (req, res) => {
  let { invoiceGroupId } = req.body;
  let invoiceGroupObj: any = await invoiceGroupModel.findOne({
    id: invoiceGroupId,
  });

  // let eventCode: string = invoiceGroupObj.eventCode;
  // let eventObj: any = await eventModel.findOne({ code: eventCode });
  // let eventName: string = eventObj.name;

  let invoices: any = invoiceGroupObj.invoices;
  let recipientEmails: any = [];

  invoices.map((invoice: any) => {
    recipientEmails.push(invoice.buyerEmail);
  });

  let recipientEmails_noDuplicates: any = recipientEmails.filter(
    (elem, index, self) => {
      return index === self.indexOf(elem);
    }
  );

  let templateModels: any = [];
  recipientEmails_noDuplicates.map((receiverEmail: string) => {
    let invoiceLinkDetails: any = [];
    let receiverName: string = "";
    let eventNames: any = [];

    invoices.map((invoice: any) => {
      if (receiverEmail === invoice.buyerEmail) {
        let downloadLink: string = "";
        if (APP_OPTIONS.env === "prod") {
          if (invoice.eventCode === "membership") {
            let urlEncodedInvoiceNo: string = encodeURIComponent(
              invoice.invoiceNo
            );
            downloadLink = `https://account.indiahci.org/download-invoice/${urlEncodedInvoiceNo}`;
          } else {
            downloadLink = `https://account.indiahci.org/download-invoice/${invoice.invoiceNo}`;
          }
        } else if (APP_OPTIONS.env === "dev") {
          if (invoice.eventCode === "membership") {
            let urlEncodedInvoiceNo: string = encodeURIComponent(
              invoice.invoiceNo
            );
            downloadLink = `http://localhost:1916/download-invoice/${urlEncodedInvoiceNo}`;
          } else {
            downloadLink = `http://localhost:1916/download-invoice/${invoice.invoiceNo}`;
          }
        }
        receiverName = invoice.buyerName;
        let invoiceLinkObj = {
          ticketNarration: invoice.ticketNarration,
          downloadLink: downloadLink,
        };
        invoiceLinkDetails.push(invoiceLinkObj);
        eventNames.push(invoice.eventName);
      }
    });

    let eventNames_noDuplicates: any = eventNames.filter(
      (elem, index, self) => {
        return index === self.indexOf(elem);
      }
    );

    let eventNameString: string = "";
    if (eventNames_noDuplicates.length > 1) {
      eventNameString = eventNames_noDuplicates.join(", ");
    } else {
      eventNameString = eventNames_noDuplicates[0];
    }

    let obj = {
      receiverName: receiverName,
      receiverEmail: receiverEmail,
      eventNameString: eventNameString,
      ownerLogo:
        "https://res.cloudinary.com/layerpark/image/upload/v1668714370/hcipai/main/hcipai-logo_nihn9o.png",
      eventLogo:
        "https://res.cloudinary.com/layerpark/image/upload/v1665587385/hcipai/indiahci2022/India%20HCI%202022%20logo.png",
      invoiceLinks: invoiceLinkDetails,
    };
    templateModels.push(obj);
  });

  let emailPayload: any = [];
  templateModels.map((templateModel: any) => {
    let obj = {
      From: "HCI Professionals Association <NAME_EMAIL>",
      To: templateModel.receiverEmail,
      TemplateId: 29823596,
      TemplateModel: templateModel,
    };
    emailPayload.push(obj);
  });

  await sendInvoiceHelper(emailPayload).then(async (isBulkInvoiceSent) => {
    if (!isBulkInvoiceSent) {
      let resp = {
        status: "Failed",
        msg: `Could not send bulk invoices`,
      };
      return res.status(400).json(resp);
    }

    let filter = {
      id: invoiceGroupId,
    };

    let update = {
      $inc: { bulkSendCount: 1 },
    };

    await invoiceGroupModel
      .findOneAndUpdate(filter, update)
      .exec((err, result) => {
        if (err) {
          let resp = {
            status: "Failed",
            msg: `Could not send bulk invoices: ${err}`,
          };
          return res.json(resp);
        } else {
          let resp = {
            status: "Success",
            msg: `Bulk invoices sent`,
          };
          return res.json(resp);
        }
      });
  });
});

export default router;
