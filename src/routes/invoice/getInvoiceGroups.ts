import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { userModel, invoiceGroupModel } from "../../models";
import moment from "moment";

const router = Router();

router.get("/get-invoice-groups", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* -------------------
  Get all invoice groups
  ------------------- */
  const invoiceGroups: any = await invoiceGroupModel.find(
    {},
    "id name invoiceCount bulkSendCount invoices createdOn"
  );

  invoiceGroups.sort((a: any, b: any) => {
    let nameA = new Date(a.createdOn);
    let nameB = new Date(b.createdOn);
    if (nameA > nameB) return -1;
    if (nameA < nameB) return 1;
    return 0;
  });

  let invoiceYears: any = [];
  invoiceGroups.map((invoiceGroup) => {
    let createdOn = invoiceGroup.createdOn;
    let momentDate = moment(createdOn);
    let year = momentDate.year();
    invoiceYears.push(year);
  });

  let invoiceYears_noDuplicates: any = invoiceYears.filter(
    (elem, index, self) => {
      return index === self.indexOf(elem);
    }
  );

  let invoiceYears_preFinal: any = [];
  invoiceYears_preFinal.push("All");
  invoiceYears_noDuplicates.map((item) => {
    invoiceYears_preFinal.push(item);
  });

  let invoiceYears_final: any = [];
  invoiceYears_preFinal.map((item) => {
    let obj = {
      label: item.toString() === "All" ? "Years (All)" : item.toString(),
      value: item.toString(),
    };
    invoiceYears_final.push(obj);
  });

  if (invoiceGroups) {
    let resp = {
      status: "Success",
      msg: "Fetched assigned coupons",
      payload: {
        invoiceGroups: invoiceGroups,
        invoiceYears: invoiceYears_final,
      },
    };
    return res.json(resp);
  } else {
    let resp = {
      status: "Failed",
      msg: "Could not retrieve InvoiceGroups",
    };
    return res.json(resp);
  }
});

export default router;
