import { Router } from "express";
import { isUserLogged, getUserID } from "../../middleware";
import { invoiceGroupModel } from "../../models";
import { getImageData } from "../../helpers";
import PDFDocument from "pdfkit";

const router = Router();

router.get(
  "/download-invoice/:invoiceNo?",
  isUserLogged,
  getUserID,
  async (req, res) => {
    let invoiceNo = req.params.invoiceNo;

    let invoiceGroupObj: any = await invoiceGroupModel.findOne({
      "invoices.invoiceNo": invoiceNo,
    });

    let invoiceObj: any;
    let invoices: any = invoiceGroupObj.invoices;
    invoices.map((invoice: any) => {
      if (invoice.invoiceNo === invoiceNo) {
        invoiceObj = invoice;
      }
    });

    /*---------------------
    Lo<PERSON> & Signatories
    ---------------------*/
    let primaryLogo_Url: string = invoiceObj.branding.primaryLogoUrl;
    let secondaryLogo_Url: string = invoiceObj.branding.secondaryLogoUrl;
    let primarySignatory_Name: string = invoiceObj.signatory.primary.name;
    let primarySignatory_Designation: string =
      invoiceObj.signatory.primary.designation;
    let primarySignatory_Signature_Url: string =
      invoiceObj.signatory.primary.signatureUrl;
    let primaryLogo_ImageData: any;
    let secondaryLogo_ImageDate: any;
    let primarySignatory_Signature_ImageData: any;

    if (primaryLogo_Url) {
      primaryLogo_ImageData = await getImageData(primaryLogo_Url);
    }
    if (secondaryLogo_Url) {
      secondaryLogo_ImageDate = await getImageData(secondaryLogo_Url);
    }
    if (primarySignatory_Signature_Url) {
      primarySignatory_Signature_ImageData = await getImageData(
        primarySignatory_Signature_Url
      );
    }

    /*---------------------
    Init for PDF generation
    ---------------------*/
    let recipientGstinStatement: string = "";
    let discountStatement: string = "";
    let ticketBasePrice: any = 0;
    let processingCharge: any = 0;
    let totalTaxableValue: any = 0;
    let cgst: any = 0;
    let sgst: any = 0;
    let igst: any = 0;
    let totalPaidAmount: any = 0;

    // Generate buyer GSTIN statement
    if (invoiceObj.isGstInvoiceRequested) {
      recipientGstinStatement = `${invoiceObj.buyerGstin} (${invoiceObj.buyerCompany})`;
    } else {
      recipientGstinStatement = "N/A";
    }

    // Generate coupon discount statement
    if (invoiceObj.isCouponApplied) {
      if (invoiceObj.couponDeduction) {
        discountStatement = `Discount: ${invoiceObj.couponName} (${invoiceObj.couponDeduction})`;
      } else {
        discountStatement = `Discount: ${invoiceObj.couponName}`;
      }
    } else {
      discountStatement = "";
    }

    // Format price numbers
    ticketBasePrice = parseFloat(invoiceObj.ticketBasePrice).toFixed(2);
    processingCharge = parseFloat(invoiceObj.processingCharges).toFixed(2);
    totalTaxableValue = parseFloat(invoiceObj.totalTaxableValue).toFixed(2);
    cgst = parseFloat(invoiceObj.cgst).toFixed(2);
    sgst = parseFloat(invoiceObj.sgst).toFixed(2);
    igst = parseFloat(invoiceObj.igst).toFixed(2);
    totalPaidAmount = parseFloat(invoiceObj.totalPaidAmount).toFixed(2);

    /*-------------------
    Generate PDF document
    -------------------*/
    const doc = new PDFDocument();

    // Set document fontSize
    doc.fontSize(10);
    doc.font("_assets/roboto.ttf");

    // SET HEADER
    /* doc.image("_assets/hcipai_logo.png", 50, 50, { width: 90 }); */
    doc.image(primaryLogo_ImageData, 50, 50, { width: 90 });
    doc.fontSize(18).text("INVOICE", doc.page.width / 2 - 50, 50);

    /*doc.image("_assets/IndiaHCI2022_logo.png", doc.page.width - 175, 50, {
      width: 125,
    });*/

    if (secondaryLogo_Url) {
      doc.image(secondaryLogo_ImageDate, doc.page.width - 175, 50, {
        width: 125,
      });
    }
    doc.fontSize(10);

    // Set HCIPAI details
    doc.text(`CIN: ${invoiceObj.sellerCin}`, 50, 150);
    doc.text(`GSTIN: ${invoiceObj.sellerGstin}`, 50, 165);
    doc.text(`Invoice no: ${invoiceObj.invoiceNo}`, doc.page.width - 225, 150);
    doc.text(
      `Date of Invoice: ${invoiceObj.invoiceDate}`,
      doc.page.width - 225,
      165
    );

    // Set Recipient details
    doc.text(`Invoice To: ${invoiceObj.buyerName}`, 50, 210);
    doc.text(`GST: ${recipientGstinStatement}`, 50, 225);
    doc.text(`Location of the recipient: ${invoiceObj.buyerLocation}`, 50, 240);

    // Set Purchase details
    doc.text("PURCHASE DETAILS", 50, 285);

    // Draw lines horizontal table lines
    doc.rect(50, 300, doc.page.width - 100, 20);
    doc.fill("#ccc");
    doc.fill("black");
    doc
      .moveTo(50, 300)
      .lineTo(doc.page.width - 50, 300)
      .stroke();
    doc
      .moveTo(50, 320)
      .lineTo(doc.page.width - 50, 320)
      .stroke();
    doc
      .moveTo(50, 360)
      .lineTo(doc.page.width - 50, 360)
      .stroke();
    doc
      .moveTo(50, 380)
      .lineTo(doc.page.width - 50, 380)
      .stroke();
    doc
      .moveTo(50, 400)
      .lineTo(doc.page.width - 50, 400)
      .stroke();
    doc
      .moveTo(50, 420)
      .lineTo(doc.page.width - 50, 420)
      .stroke();
    doc
      .moveTo(50, 440)
      .lineTo(doc.page.width - 50, 440)
      .stroke();
    doc
      .moveTo(50, 460)
      .lineTo(doc.page.width - 50, 460)
      .stroke();
    doc
      .moveTo(50, 480)
      .lineTo(doc.page.width - 50, 480)
      .stroke();

    // Draw vertical horizontal table lines
    doc.moveTo(50, 300).lineTo(50, 480).stroke();
    doc
      .moveTo(doc.page.width - 50, 300)
      .lineTo(doc.page.width - 50, 480)
      .stroke();
    doc.moveTo(450, 300).lineTo(450, 480).stroke();

    // Table text fill
    doc.text("Particulars", 60, 304);
    doc.text("Amount", 490, 304);
    let valueWidth: number = 50;
    doc.text(invoiceObj.ticketNarration, 60, 328);
    doc.text(discountStatement, 60, 342);
    doc.text("₹", 470, 328);
    doc.text(ticketBasePrice, 480, 328, {
      width: valueWidth,
      align: "right",
    });
    doc.text("Processing Charges", 60, 364);
    doc.text("₹", 470, 364);
    doc.text(processingCharge, 480, 364, {
      width: valueWidth,
      align: "right",
    });
    doc.text("Total Taxable Value", 60, 384);
    doc.text("₹", 470, 384);
    doc.text(totalTaxableValue, 480, 384, {
      width: valueWidth,
      align: "right",
    });
    doc.text("CGST 9%", 60, 404);
    doc.text("₹", 470, 404);
    doc.text(cgst, 480, 404, {
      width: valueWidth,
      align: "right",
    });
    doc.text("SGST 9%", 60, 424);
    doc.text("₹", 470, 424);
    doc.text(sgst, 480, 424, {
      width: valueWidth,
      align: "right",
    });
    doc.text("IGST 18%", 60, 444);
    doc.text("₹", 470, 444);
    doc.text(igst, 480, 444, {
      width: valueWidth,
      align: "right",
    });
    doc.text("Total", 60, 464);
    doc.text("₹", 470, 464);
    doc.text(totalPaidAmount, 480, 464, {
      width: valueWidth,
      align: "right",
    });
    doc.text(
      `The above transaction was paid via ${invoiceObj.paymentMethod}`,
      50,
      484
    );

    // General chair signature
    // if (invoiceObj.eventCode === "membership") {
    //   doc.image(primarySignatory_Signature_ImageData, 60, 505, {
    //     fit: [50, 75],
    //   });
    // } else {
    //   doc.image(primarySignatory_Signature_ImageData, 50, 530, { width: 90 });
    // }
    doc.image(primarySignatory_Signature_ImageData, 60, 505, {
      fit: [50, 75],
    });
    doc.text(primarySignatory_Name, 50, 580);
    doc.text("Authorised Signatory", 50, 595);
    doc.text(primarySignatory_Designation, 50, 610);

    // Set Footer
    doc.text(
      "4, Paradise Society, Baner Road, Pune 411045, India",
      doc.page.width / 4,
      doc.page.height - 90
    );

    // Add pdf to res
    doc.pipe(res);

    // Finalize PDF file
    doc.end();
    invoiceNo = invoiceNo.replace(/\//g, "-");
    res.writeHead(200, {
      "Content-Type": "application/pdf",
      "Access-Control-Allow-Origin": "*",
      "Content-Disposition": `attachment; filename=${invoiceNo}.pdf`,
    });
  }
);

export default router;
