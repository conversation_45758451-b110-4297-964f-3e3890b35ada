import { Router } from "express";
import { userModel, couponModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
const router = Router();

router.post(
  "/getavailablecoupons",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ----------------------------
  Get user details for processing
  ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    const userEmail: string = userObj.profile.email;

    let { eventCode } = req.body;

    /* ------------
    Get all coupons
    ------------ */
    const coupons: any = await couponModel.find({
      eventCode: eventCode,
    });
    let openCoupons: Array<Object> = [];
    let emailListCoupons: Array<Object> = [];
    let availableCoupons: Array<Object> = [];

    coupons.map((coupon: any) => {
      if (coupon.meta.access === "open") {
        openCoupons.push(coupon);
      } else if (coupon.meta.access === "emaillist") {
        emailListCoupons.push(coupon);
      }
    });

    // let isOpenCouponAssigned: boolean = false;
    // openCoupons.map((openCoupon: any) => {
    //   let obj = {
    //     name: openCoupon.name,
    //     code: openCoupon.meta.code,
    //   };
    //   let redeemerList: any = openCoupon.redeemerList;
    //   redeemerList.map((redeemer: any) => {
    //     if (userEmail != redeemer.email) {
    //       isOpenCouponAssigned = true;
    //     } else {
    //       isOpenCouponAssigned = false;
    //     }
    //   });

    //   if (isOpenCouponAssigned === true) {
    //     availableCoupons.push(obj);
    //     isOpenCouponAssigned = false;
    //   }
    // });

    emailListCoupons.map((emailListCoupon: any) => {
      let obj = {
        name: emailListCoupon.name,
        code: "",
      };
      let redeemerList: any = emailListCoupon.redeemerList;

      redeemerList.map((redeemer: any) => {
        if (userEmail === redeemer.email && redeemer.isUsed === false) {
          obj.code = redeemer.code;
          availableCoupons.push(obj);
        }
      });
    });

    let resp = {
      status: "Success",
      msg: "Fetched assigned coupons",
      payload: availableCoupons,
    };
    return res.json(resp);
  }
);

export default router;
