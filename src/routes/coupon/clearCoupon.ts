// @ts-nocheck

import { Router } from "express";
import { userModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";

const router = Router();

router.post("/clearcoupon", isUserLogged, getUserID, async (req, res) => {
  /* ----------------------
  Check if user is LoggedIn
  ---------------------- */
  if (!res.locals.isUserLogged) {
    let resp = {
      status: "Failed",
      msg: "You are not logged in",
    };
    return res.status(400).json(resp);
  }

  /* ------------------------
  Extract userId from headers
  ------------------------ */
  const _userID = res.locals.userID;

  /* --------------------
  Clear coupons in the DB
  -------------------- */
  let filter = {
    _userID: _userID,
  };
  let update = {
    "coupon.isApplied": false,
    "coupon.id": "",
  };
  await userModel.findOneAndUpdate(filter, update, (error, success) => {
    if (error) {
      let resp = {
        status: "Failed",
        msg: "Could not clear coupon",
      };
      return res.json(resp);
    } else {
      let resp = {
        status: "Success",
        msg: "Coupon cleared",
      };
      return res.json(resp);
    }
  });
});

export default router;
