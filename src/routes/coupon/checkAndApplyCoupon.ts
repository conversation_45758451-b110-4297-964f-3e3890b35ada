// @ts-nocheck

import { Router } from "express";
import { userModel, couponModel, ticketModel } from "../../models";
import { isUserLogged, getUserID } from "../../middleware";
import { validateCheckAndApplyCouponInputs } from "../../validation";
import moment from "moment";

const router = Router();

router.post(
  "/checkandapplycoupon",
  isUserLogged,
  getUserID,
  async (req, res) => {
    /* ----------------------
    Check if user is LoggedIn
    ---------------------- */
    if (!res.locals.isUserLogged) {
      let resp = {
        status: "Failed",
        msg: "You are not logged in",
      };
      return res.status(400).json(resp);
    }

    /* ---------------------------------
    Extract ticket details from req.body
    --------------------------------- */
    let { couponCode, cartTotal } = req.body;

    /* --------------------
    Check if inputs are valid
    ---------------------- */
    let { error } = validateCheckAndApplyCouponInputs(req.body);
    if (error) {
      let resp = {
        status: "Failed",
        msg: error.message,
      };
      return res.status(400).send(resp);
    }

    /* ----------------------------
    Get user details for processing
    ---------------------------- */
    const _userID = res.locals.userID;
    const userObj: any = await userModel.findOne({
      _userID,
    });
    const userEmail: string = userObj.profile.email;
    let persona = userObj.professional.occupation;

    /* --------------------------
    Get cart details for processing
    ---------------------------- */
    let cartItemIDs: any = [];
    userObj?.cart.forEach((item: any) => {
      cartItemIDs.push(item.ticketID);
    });

    let cartItemDetails = await ticketModel
      .find()
      .where("ticketID")
      .in(cartItemIDs)
      .exec();

    /* ------------
    Get all coupons
    ------------ */
    const coupons: any = await couponModel.find({});
    let openCoupons: Array<Object> = [];
    let emailListCoupons: Array<Object> = [];
    let isCouponFound: boolean = false;
    let isCouponUsed: boolean = false;
    let couponObj: any;
    let couponType: string;

    /* -------------------------
    Find if coupon is already used
    --------------------------- */
    coupons.map((coupon: any) => {
      if (coupon.meta.access === "open") {
        if (couponCode === coupon.meta.code) {
          let redeemerList: any = coupon.redeemerList;
          redeemerList.map((redeemer: any) => {
            if (userEmail === redeemer.email) {
              if (redeemer.isUsed) {
                isCouponUsed = true;
              }
            }
          });
        }
      } else if (coupon.meta.access === "emaillist") {
        let redeemerList: any = coupon.redeemerList;
        redeemerList.map((redeemer: any) => {
          if (userEmail === redeemer.email && redeemer.code === couponCode) {
            if (redeemer.isUsed) {
              isCouponUsed = true;
            }
          }
        });
      }
    });

    if (isCouponUsed) {
      let resp = {
        status: "Failed",
        msg: "Coupon is already used",
      };
      return res.json(resp);
    }

    coupons.map((coupon: any) => {
      if (coupon.meta.access === "open") {
        openCoupons.push(coupon);
      } else if (coupon.meta.access === "emaillist") {
        emailListCoupons.push(coupon);
      }
    });

    openCoupons.map((openCoupon: any) => {
      if (openCoupon.meta.code === couponCode) {
        isCouponFound = true;
        couponObj = openCoupon;
        couponType = "open";
      }
    });

    emailListCoupons.map((emailListCoupon: any) => {
      let redeemerList: any = emailListCoupon.redeemerList;
      redeemerList.map((redeemer: any) => {
        if (redeemer.code === couponCode && redeemer.email === userEmail) {
          isCouponFound = true;
          couponObj = emailListCoupon;
          couponType = "emaillist";
        }
      });
    });

    if (!isCouponFound) {
      let resp = {
        status: "Failed",
        msg: "Could not find coupon",
      };
      return res.json(resp);
    }

    let couponId: string = couponObj.id;
    let couponDeductionType: string = couponObj.deduction.type;
    let couponDeductionValue: number = couponObj.deduction.value;
    let couponTicketSubTypes: any = couponObj.deduction.ticketSubTypes;
    let newCartTotal: number = 0;

    if (couponDeductionType === "fixed") {
      newCartTotal = cartTotal - couponDeductionValue;
    } else if (couponDeductionType === "percentage") {
      let percentageDeductionValue = (couponDeductionValue / 100) * cartTotal;
      newCartTotal = cartTotal - percentageDeductionValue;
    } else if (couponDeductionType === "ticketType") {
      let subTypesArr: any = [];
      couponTicketSubTypes.map((ticket: any) => {
        subTypesArr.push(ticket.subType);
      });
      let ticketDetailsArr = await ticketModel
        .find()
        .where("subType")
        .in(subTypesArr)
        .exec();

      let finalProcessingArr: any = [];
      cartItemDetails.map((cartItemDetail: any) => {
        ticketDetailsArr.map((ticketDetail: any) => {
          if (cartItemDetail.subType === ticketDetail.subType) {
            finalProcessingArr.push(cartItemDetail);
          }
        });
      });
      let ticketDeductionValue: number = 0;
      finalProcessingArr.map((ticket: any) => {
        let price = 0;
        let priceArr = ticket.price;
        let subType = ticket.subType;
        priceArr.forEach((item: any) => {
          let startTx = item.startDate;
          let endTx = item.endDate;
          let startDate = new Date(parseInt(startTx) * 1000).toISOString();
          let endDate = new Date(parseInt(endTx) * 1000).toISOString();
          let nowDate = moment().toISOString();
          let isBetween = moment(nowDate).isBetween(startDate, endDate);
          if (isBetween) {
            if (subType === "course") {
              if (persona === item.name.toLowerCase()) {
                price = item.price.inr;
              }
            } else {
              price = item.price.inr;
            }
          }
          if (price > ticketDeductionValue) {
            ticketDeductionValue = price;
          }
        });
      });
      newCartTotal = cartTotal - ticketDeductionValue;
    }

    if (cartTotal <= 0) {
      let resp = {
        status: "Failed",
        msg: "Cart total cannot be less than 0",
      };
      return res.json(resp);
    }

    if (newCartTotal < 0) {
      let resp = {
        status: "Failed",
        msg: "Invalid coupon",
      };
      return res.json(resp);
    }

    let filter = {
      _userID: _userID,
    };
    let update = {
      "coupon.isApplied": true,
      "coupon.id": couponId,
    };
    await userModel.findOneAndUpdate(filter, update, (error, success) => {
      if (error) {
        let resp = {
          status: "Failed",
          msg: "Could check coupon code",
        };
        return res.json(resp);
      } else {
        let resp = {
          status: "Success",
          msg: "Coupon added",
        };
        return res.json(resp);
      }
    });
  }
);

export default router;
