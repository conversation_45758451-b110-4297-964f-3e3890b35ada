import express from "express";
import session from "express-session";
import connectRedis from "connect-redis";
import Redis from "ioredis";
import mongoose from "mongoose";
import cors from "cors";
import * as path from "path";

import {
  accountsetup,
  accountsetupv2,
  accountsetupv2student,
  addpagetoeventcode,
  addmembertolegacydb,
  addMembershipToLegacyCart,
  addSessionToTrackTicket,
  addToCart,
  attendeelist,
  attendeeListV3,
  bankpayment,
  bankPayment_v2,
  banktxverification,
  cancelIdVerificationAlert,
  cart,
  checkandapplycoupon,
  checkAndApplyCoupon_v2,
  clearcoupon,
  confirmnewpassword,
  convertAccountToPro,
  createcoupon,
  createcouponcodeforemail,
  createevent,
  createFullTicket,
  createBasicTicket,
  createTrackTicket,
  createticket,
  createuser,
  deletecouponcode,
  deletepagebypageid,
  downloadcouponemails,
  downloadmembers,
  downloadpurchases,
  downloadSales,
  downloadSalesV3,
  downloadSalesV4,
  downloadSalesV5,
  downloadtrackregistrants,
  downloadInvoice,
  downloadInvoiceCustomEvent,
  downloadDataForInvoice,
  downloadAccounts,
  downloadAccount_V2,
  editeventassets,
  editeventbasics,
  editeventinvoicing,
  editeventmanagers,
  editeventschedule,
  freepurchaseconfirm,
  freePurchaseConfirm_v2,
  generictest,
  getAllAccounts,
  getInvoiceGroups,
  getCart,
  getCheckoutTotal,
  geteventauth,
  getevents,
  geteventbycode,
  getaccesslist,
  getaccesslist19nov,
  getalerts,
  getavailablecoupons,
  getcarttotal,
  getcouponbyid,
  getcoupons,
  getgoogleprofile,
  getlinkedinprofile,
  getmembers,
  getmembership,
  getmemberticketdetails,
  getMembershipCheckoutTotal,
  getpagesbyeventcode,
  getpurchases,
  getPurchasesV2,
  getredeemerlist,
  getregsysbyeventcode,
  getSessionDetails,
  getticketsbypageidandeventcode,
  getticketdetails,
  gettickettypes,
  getTicketTypes_v2,
  gettrackregistrants,
  getverifications,
  home,
  initMemberCollection,
  ismember,
  legacymembers,
  linkaccountwithlinkedin,
  linkaccountwithgoogle,
  login,
  logout,
  membershipBankPayment,
  membershipRazorpayPrep,
  membershipRazorpayPayment,
  membershipRazorpayWebhook,
  overview,
  overviewv2,
  overviewv3,
  paywithrazorpay,
  payWithRazorPay_v2,
  profile,
  purchases,
  purchasesSummary,
  purchaseSummary_v2,
  razorpayconfirm,
  razorpayConfirm_v2,
  razorpaymenthook,
  removeFromCart,
  register,
  resendpasswordresetcode,
  resetpassword,
  resolvealerts,
  rzppaymentfailed,
  sendAllInvoices,
  sendemailverificationcode,
  sendpasswordresetcode,
  sendticketconfirmation,
  signup,
  signupwithlinkedin,
  signupwithgoogle,
  singleuser,
  ticket,
  track,
  trackoverview,
  user,
  updateeventpublishing,
  updatepagebyid,
  verifyemail,
  verifypasswordresetcode,
  uploadInvoiceGroup,
  uploadidcard,
} from "./routes";

import {
  APP_OPTIONS,
  REDIS_OPTIONS,
  SESSION_OPTIONS,
  MONGO_URI,
  MONGO_OPTIONS,
} from "./config";

(async () => {
  try {
    await mongoose.connect(MONGO_URI, MONGO_OPTIONS, () => {
      console.log(`Connected to Mongo`);
    });
  } catch (error) {
    console.log(error);
    process.exit(1);
  }

  const redisStore = connectRedis(session);
  const client = new Redis(REDIS_OPTIONS);
  const app = express();
  app.use(express.json());
  app.use(
    session({
      ...SESSION_OPTIONS,
      store: new redisStore({ client }),
    })
  );
  const corsOptions = {
    origin: [
      "http://localhost:3333",
      "https://account.indiahci.org",
      "https://hcipai.layerpark.com",
    ],
    credentials: true,
  };
  app.use(cors(corsOptions));
  app.use(express.static(__dirname + "/www"));
  app.use(accountsetup);
  app.use(accountsetupv2);
  app.use(accountsetupv2student);
  app.use(addpagetoeventcode);
  app.use(addToCart);
  app.use(addmembertolegacydb);
  app.use(addMembershipToLegacyCart);
  app.use(addSessionToTrackTicket);
  app.use(attendeelist);
  app.use(attendeeListV3);
  app.use(bankpayment);
  app.use(bankPayment_v2);
  app.use(banktxverification);
  app.use(cancelIdVerificationAlert);
  app.use(cart);
  app.use(checkandapplycoupon);
  app.use(checkAndApplyCoupon_v2);
  app.use(confirmnewpassword);
  app.use(convertAccountToPro);
  app.use(createticket);
  app.use(createcoupon);
  app.use(createcouponcodeforemail);
  app.use(createFullTicket);
  app.use(createBasicTicket);
  app.use(createTrackTicket);
  app.use(createuser);
  app.use(clearcoupon);
  app.use(createevent);
  app.use(deletecouponcode);
  app.use(deletepagebypageid);
  app.use(downloadInvoice);
  app.use(downloadInvoiceCustomEvent);
  app.use(downloadDataForInvoice);
  app.use(downloadcouponemails);
  app.use(downloadmembers);
  app.use(downloadpurchases);
  app.use(downloadSales);
  app.use(downloadSalesV3);
  app.use(downloadSalesV4);
  app.use(downloadSalesV5);
  app.use(downloadtrackregistrants);
  app.use(downloadAccounts);
  app.use(downloadAccount_V2);
  app.use(editeventassets);
  app.use(editeventbasics);
  app.use(editeventinvoicing);
  app.use(editeventmanagers);
  app.use(editeventschedule);
  app.use(freepurchaseconfirm);
  app.use(freePurchaseConfirm_v2);
  app.use(generictest);
  app.use(getAllAccounts);
  app.use(getalerts);
  app.use(getInvoiceGroups);
  app.use(getCart);
  app.use(getCheckoutTotal);
  app.use(geteventauth);
  app.use(getevents);
  app.use(geteventbycode);
  app.use(getaccesslist);
  app.use(getaccesslist19nov);
  app.use(getavailablecoupons);
  app.use(getcarttotal);
  app.use(getcoupons);
  app.use(getcouponbyid);
  app.use(getlinkedinprofile);
  app.use(getgoogleprofile);
  app.use(getmembers);
  app.use(getmembership);
  app.use(getmemberticketdetails);
  app.use(getMembershipCheckoutTotal);
  app.use(getpagesbyeventcode);
  app.use(getSessionDetails);
  app.use(getpurchases);
  app.use(getPurchasesV2);
  app.use(getredeemerlist);
  app.use(getregsysbyeventcode);
  app.use(getticketsbypageidandeventcode);
  app.use(getticketdetails);
  app.use(gettickettypes);
  app.use(getTicketTypes_v2);
  app.use(gettrackregistrants);
  app.use(home);
  app.use(ismember);
  app.use(initMemberCollection);
  app.use(legacymembers);
  app.use(linkaccountwithlinkedin);
  app.use(linkaccountwithgoogle);
  app.use(login);
  app.use(membershipBankPayment);
  app.use(membershipRazorpayPayment);
  app.use(membershipRazorpayPrep);
  app.use(membershipRazorpayWebhook);
  app.use(logout);
  app.use(overview);
  app.use(overviewv2);
  app.use(overviewv3);
  app.use(paywithrazorpay);
  app.use(payWithRazorPay_v2);
  app.use(profile);
  app.use(purchases);
  app.use(purchasesSummary);
  app.use(purchaseSummary_v2);
  app.use(razorpayconfirm);
  app.use(razorpayConfirm_v2);
  app.use(razorpaymenthook);
  app.use(register);
  app.use(removeFromCart);
  app.use(resendpasswordresetcode);
  app.use(resetpassword);
  app.use(resolvealerts);
  app.use(rzppaymentfailed);
  app.use(ticket);
  app.use(track);
  app.use(trackoverview);
  app.use(sendAllInvoices);
  app.use(singleuser);
  app.use(signup);
  app.use(signupwithlinkedin);
  app.use(signupwithgoogle);
  app.use(sendemailverificationcode);
  app.use(sendpasswordresetcode);
  app.use(sendticketconfirmation);
  app.use(updateeventpublishing);
  app.use(updatepagebyid);
  app.use(verifyemail);
  app.use(getverifications);
  app.use(verifypasswordresetcode);
  app.use(user);
  app.use(uploadInvoiceGroup);
  app.use(uploadidcard);

  app.get("/*", (req, res) => {
    res.sendFile(path.join(__dirname, "/www/index.html"), function (err) {
      if (err) {
        res.status(500).send(err);
      }
    });
  });

  app.listen(APP_OPTIONS.port, () => {
    console.log(`Server is running on port: ${APP_OPTIONS.port}`);
  });
})();
