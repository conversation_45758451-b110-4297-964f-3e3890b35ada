import state from "../state";
export const textInputHelper = (name: string, value: string) => {
  if (!name) return;
  if (!value) return;

  state.isLoginError = false;
  state.isSignupError = false;

  if (name === "email") {
    state.email = value.toLowerCase();
  } else if (name === "password") {
    state.password = value;
  } else if (name === "firstName") {
    state.firstName = value;
  } else if (name === "lastName") {
    state.lastName = value;
  }
};
