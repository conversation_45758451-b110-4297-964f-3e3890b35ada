import axios from "axios";
import state from "../../../../state";

export const linkAccountWithGoogleHelper = async (
  linkAccountWithGooglePayload: any
) => {
  state.isLinkAccountWithGoogleInAction = true;

  let isAccountLinked: boolean = false;
  let message: string = "";

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/link-account-with-google`,
    data: linkAccountWithGooglePayload,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isAccountLinked = false;
      } else if (response.data.status === "Success") {
        isAccountLinked = true;
      }
      message = response.data.msg;
    })
    .catch((error) => {
      isAccountLinked = false;
      message = error;
    });

  state.isLinkAccountWithGoogleInAction = false;

  return { isAccountLinked: isAccountLinked, message: message };
};
