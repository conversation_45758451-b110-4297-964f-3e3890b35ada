import axios from "axios";
import state from "../../../../state";

export const getGoogleProfile = async (googleJwt: string) => {
  let isGoogleProfileFetched: boolean = false;
  let firstName: string = "";
  let lastName: string = "";
  let email: string = "";
  let isEmailVerified: boolean = false;
  let dpUrl: string = "";
  let isUserExists: boolean = false;
  let isGoogleConnected: boolean = false;

  let data = {
    googleJwt: googleJwt,
  };

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/getgoogleprofile`,
    data: data,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isGoogleProfileFetched = false;
      } else if (response.data.status === "Success") {
        isGoogleProfileFetched = true;
        firstName = response.data.payload.firstName;
        lastName = response.data.payload.lastName;
        email = response.data.payload.email.toLowerCase();
        isEmailVerified = response.data.payload.isEmailVerified;
        dpUrl = response.data.payload.dpUrl;
        isUserExists = response.data.payload.isUserExists;
        isGoogleConnected = response.data.payload.isGoogleConnected;
      }
    })
    .catch((error) => {
      console.log(error);
    });

  return {
    isGoogleProfileFetched: isGoogleProfileFetched,
    firstName: firstName,
    lastName: lastName,
    email: email,
    isEmailVerified: isEmailVerified,
    dpUrl: dpUrl,
    isUserExists: isUserExists,
    isGoogleConnected: isGoogleConnected,
  };
};
