import axios from "axios";
import state from "../../../../state";

export const googleSignupHelper = async (googleSignupPayload: any) => {
  state.isGoogleCreateAccountInAction = true;

  let isUserSignedUp: boolean = false;
  let signupMessage: string = "";

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/signup-with-google`,
    data: googleSignupPayload,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isUserSignedUp = false;
      } else if (response.data.status === "Success") {
        isUserSignedUp = true;
      }
      signupMessage = response.data.msg;
    })
    .catch((error) => {
      isUserSignedUp = false;
      signupMessage = error;
    });

  state.isGoogleCreateAccountInAction = false;

  return { isUserSignedUp: isUserSignedUp, signupMessage: signupMessage };
};
