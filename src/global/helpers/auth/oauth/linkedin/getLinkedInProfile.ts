import axios from "axios";
import state from "../../../../state";

export const getLinkedInProfile = async (authCode: string) => {
  let isLinkedInProfileFetched: boolean = false;
  let remarks: string = "";
  let firstName: string = "";
  let lastName: string = "";
  let email: string = "";
  let dpUrl: string = "";
  let isUserExists: boolean = false;
  let isLinkedInConnected: boolean = false;

  let data = {
    authCode: authCode,
  };

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/getlinkedinprofile`,
    data: data,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isLinkedInProfileFetched = false;
      } else if (response.data.status === "Success") {
        isLinkedInProfileFetched = true;
        firstName = response.data.payload.firstName;
        lastName = response.data.payload.lastName;
        email = response.data.payload.email.toLowerCase();
        dpUrl = response.data.payload.dpUrl;
        isUserExists = response.data.payload.isUserExists;
        isLinkedInConnected = response.data.payload.isLinkedInConnected;
      }
      remarks = response.data.payload.msg;
    })
    .catch((error) => {
      console.log(error);
    });

  return {
    isLinkedInProfileFetched: isLinkedInProfileFetched,
    firstName: firstName,
    lastName: lastName,
    email: email,
    dpUrl: dpUrl,
    isUserExists: isUserExists,
    isLinkedInConnected: isLinkedInConnected,
    remarks: remarks,
  };
};
