import axios from "axios";
import state from "../../../../state";

export const linkedInSignupHelper = async (linkedInSignupPayload: any) => {
  state.isLinkedinCreateAccountInAction = true;

  let isUserSignedUp: boolean = false;
  let signupMessage: string = "";

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/signup-with-linkedin`,
    data: linkedInSignupPayload,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isUserSignedUp = false;
      } else if (response.data.status === "Success") {
        isUserSignedUp = true;
      }
      signupMessage = response.data.msg;
    })
    .catch((error) => {
      isUserSignedUp = false;
      signupMessage = error;
    });

  state.isLinkedinCreateAccountInAction = false;

  return { isUserSignedUp: isUserSignedUp, signupMessage: signupMessage };
};
