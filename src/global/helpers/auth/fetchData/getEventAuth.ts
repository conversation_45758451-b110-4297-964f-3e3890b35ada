import axios from "axios";
import state from "../../../state";

export const getEventAuthHelper = async (eventCode: string) => {
  let getEventDataPayload: any = {
    eventCode: eventCode,
  };

  let isFetchSuccessful: boolean = false;
  let eventObj: any;
  let error: any;

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/geteventauth`,
    data: getEventDataPayload,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isFetchSuccessful = false;
      } else if (response.data.status === "Success") {
        isFetchSuccessful = true;
        eventObj = response.data.payload;
      }
    })
    .catch((error) => {
      isFetchSuccessful = false;
      error = error;
    });

  return {
    isFetchSuccessful: isFetchSuccessful,
    eventObj: eventObj,
    error: error,
  };
};
