import axios from "axios";
import { LocalSignupPayloadInterface } from "../../../interfaces";
import state from "../../../state";

export const localSignupHelper = async (
  localSignupPayload: LocalSignupPayloadInterface
) => {
  state.isSignupButtonInAction = true;

  let isUserSignedUp: boolean = false;
  let signupMessage: string = "";

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/signup`,
    data: localSignupPayload,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isUserSignedUp = false;
      } else if (response.data.status === "Success") {
        isUserSignedUp = true;
      }
      signupMessage = response.data.msg;
      state.isSignupButtonInAction = false;
    })
    .catch((error) => {
      isUserSignedUp = false;
      signupMessage = error;
      state.isSignupButtonInAction = false;
    });

  return { isUserSignedUp: isUserSignedUp, signupMessage: signupMessage };
};
