import axios from "axios";
import { LocalLoginPayloadInterface } from "../../../interfaces";
import state from "../../../state";

export const localLoginHelper = async (
  localLoginPayload: LocalLoginPayloadInterface
) => {
  state.isLoginButtonInAction = true;

  let isUserLoggedIn: boolean = false;
  let loginMessage: string = "";

  await axios({
    method: "POST",
    baseURL: `${state.baseUrl}/login`,
    data: localLoginPayload,
    withCredentials: true,
    responseType: "json",
  })
    .then((response) => {
      if (response.data.status === "Failed") {
        isUserLoggedIn = false;
      } else if (response.data.status === "Success") {
        isUserLoggedIn = true;
      }
      loginMessage = response.data.msg;
    })
    .catch((error) => {
      isUserLoggedIn = false;
      loginMessage = error;
    });

  state.isLoginButtonInAction = false;

  return { isUserLoggedIn: isUserLoggedIn, loginMessage: loginMessage };
};
