import { createStore } from "@stencil/store";

const { state } = createStore({
  appliedCouponDeductionType: "",
  appliedCouponDeductionValue: 0,
  appliedCouponName: "",
  baseUrl:
    document.domain === "localhost"
      ? "http://localhost:1916"
      : `https://${document.domain}`,
  cartItems: [],
  cartTotal: 0,
  cartTotalAfterDiscount: 0,
  couponAccessType: "open",
  couponDeductionType: "fixed",
  couponDeductionValue: 0,
  couponFixedDeductionValue: 0,
  couponName: "",
  couponPercentageDeductionValue: 0,
  couponTicketDeductionLogic: "deductOneTicket",
  couponTicketsForDeduction: "",
  couponTicketsDeductionValue: 0,
  configureActivePageId: "",
  configureActivePageLabel: "",
  configureActivePageIcon: "",
  configureActiveTicketType: "fullTicket",
  currency: "",
  deductedAmount: 0,
  dpUrl: "",
  editEventName: "",
  editEventCode: "",
  email: "",
  eventCode: "",
  eventCodeForConfiguration: "",
  eventCodeForMonitoring: "",
  eventNameForMonitoring: "",
  eventCodeForRegistration: "",
  expandedCouponId: "",
  expandedTrackTicketSoldUnits: 0,
  expandedTrackTicketTitle: "",
  expandedUserEmail: "",
  firstName: "",
  fullConferenceTicketID: "",
  fullConferenceTicketPrice: "",
  fullConferenceTicketSubTitle: "",
  gatewayFee: 0,
  grandTotal: 0,
  googleClientId:
    "************-43rmbpjru65sf3fc20u23qajubqgme3e.apps.googleusercontent.com",
  googleScriptUrl: "https://accounts.google.com/gsi/client",
  isAdmin: false,
  isAccountSetup: true,
  isCheckoutBtnDisabled: true,
  isCouponApplied: false,
  isCouponInputEnabled: false,
  isCouponsAvailable: false,
  isEmailVerified: true,
  isFreePurchase: false,
  isFullTicketInCart: false,
  isFullTicketPurchased: false,
  isPrimaryTicketInCart: false,
  isPrimaryTicketPurchased: false,
  isGoogleCreateAccountInAction: false,
  isGuest: false,
  isLoginButtonInAction: false,
  isLoginError: false,
  isMember: false,
  isMemberDiscountApplied: false,
  isMembershipSaved: false,
  isMobileMenuOpen: false,
  isNotificationActive: false,
  isPartialTicketInCart: false,
  isPartialTicketPurchased: false,
  isPaying: false,
  isPaymentBtnDisabled: false,
  isPrimaryTicketDiscounted: false,
  isRegistrationManager: false,
  isSignupButtonInAction: false,
  isSignupError: false,
  isLinkedinCreateAccountInAction: false,
  isLinkAccountWithGoogleInAction: false,
  isLinkAccountWithLinkedInInAction: false,
  isMobileDashboardOptionsVisible: false,
  isSponsor: false,
  isIdCardExists: true,
  isIdCardExpired: false,
  isIdCardVerified: true,
  isUserDataFetched: false,
  lastName: "",
  linkedinClientId: "772s5g83qpkuqn",
  linkedinClientSecret: "x51LA8VE68XtmNnx",
  linkedinRedirectUri:
    document.domain === "localhost"
      ? "http://localhost:3333/post-linkedin-oauth"
      : "https://account.indiahci.org/post-linkedin-oauth",
  linkedinState: "hcipai",
  membershipInCart: "",
  membershipId: "",
  membershipType: "",
  membershipEndDate: "",
  notificationCount: 0,
  notificationMessage: "",
  notificationPosition: "",
  notificationType: "",
  occupation: "professional",
  password: "",
  paymentGateway: "razorpay",
  registrantSearchString: "",
  submittedID: "",
});

export default state;
