* {
  margin: 0;
  padding: 0;
}

:root {
  --fontcolor: #333;
  --fontsize: 18px;
  --bg-color: #f7f9fa;
  --accent-green-darker: #176639;
  --accent-green: #2ecc71;
  --accent-green-bg-lighter: #c0f0d4;
  --accent-pink-darker: #b10151;
  --accent-pink: #fd0273;
  --accent-pink-bg-lighter: #ffcce3;
  --accent-blue-darker: #0c6ecc;
  --accent-blue: #0f89ff;
  --accent-blue-bg-lighter: #cfe7ff;
  --blue-50: #e8f1fb;
  --blue-100: #bad6f2;
  --blue-200: #8cbbe9;
  --blue-300: #5e9fe0;
  --blue-400: #1976d2;
  --blue-500: #145ea8;
  --blue-600: #0f477e;
  --blue-700: #0a2f54;
  /* --accent-golden-darker: #917609;
  --accent-golden: #f1c40f;
  --accent-golden-bg-lighter: #fbedb7; */
  --accent-golden-darker: #7f6820;
  --accent-golden: #d4ad36;
  --accent-golden-bg-lighter: #eedeaf;
  --accent-color-bg-darkest: #24143c;
  --accent-color-bg-darker: #2d194b;
  --accent-color-bg-dark: #351d5a;
  --accent-color-bg: #bdadd5;
  --accent-color-bg-light: #cdc1e0;
  --accent-color-bg-lighter: #ded6ea;
  --accent-color-bg-lightest: #eeeaf5;
  --accent-color-lightest: #8b6fb6;
  --accent-color-lighter: #7a5aab;
  --accent-color-light: #6a46a1;
  --accent-color: #593196;
  --accent-color-dark: #502c87;
  --accent-color-darker: #472778;
  --accent-color-darkest: #3e2269;
  --danger-color: rgba(231, 76, 60, 1);
  --danger-color-lightest: rgba(231, 76, 60, 0.1);
  --border-radius: 0.25em;
  --label-font-size: 0.8em;
  --label-color-grey: rgba(0, 0, 0, 0.5);

  /* ==============
  NEW CSS VARIABLES
  ============== */
  --site-font-size: 17px;
  --site-border-radius: 0.25em;
  --site-padding: 0.7em 1em;
  --site-border: 1px solid rgba(0, 0, 0, 0.1);
  --site-border-focus: 1px solid rgba(0, 0, 0, 0.3);
  --site-transition: all 0.25s ease-in;

  --violet-50: #eeeaf5;
  --violet-100: #cdc1e0;
  --violet-200: #ac98cb;
  --violet-300: #8b6fb6;
  --violet-400: #593196;
  --violet-500: #472778;
  --violet-600: #351d5a;
  --violet-700: #24143c;

  --blue-50: #e8f1fb;
  --blue-100: #bad6f2;
  --blue-200: #8cbbe9;
  --blue-300: #5e9fe0;
  --blue-400: #1976d2;
  --blue-500: #145ea8;
  --blue-600: #0f477e;
  --blue-700: #0a2f54;

  --red-50: #fdedec;
  --red-100: #f8c9c5;
  --red-200: #f3a69e;
  --red-300: #ee8277;
  --red-400: #e74c3c;
  --red-500: #b93d30;
  --red-600: #a2352a;
  --red-700: #8b2e24;

  --orange-50: #fdebd0;
  --orange-100: #fad7a0;
  --orange-200: #f8c471;
  --orange-300: #f5b041;
  --orange-400: #f39c12;
  --orange-500: #c27d0e;
  --orange-600: #925e0b;
  --orange-700: #613e07;

  --icon-size: 22px;
  --icon-left-margin: 0.1em;
}

* {
  margin: 0;
  padding: 0;
}

body {
  font-size: var(--site-font-size);
  color: var(--fontcolor);
  background: var(--bg-color);
  font-family: "Open Sans", sans-serif;
}

@media only screen and (max-width: 768px) {
  body {
    font-size: 18px;
  }
}
