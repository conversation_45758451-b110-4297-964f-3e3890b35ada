class Store {
  private email: string = "";
  private password: string = "";
  private confirmPassword: string = "";
  private isRememberMe: boolean = true;
  private firstName: string = "";
  private lastName: string = "";
  private isdCode: string = "";
  private mobileCountry: string = "";
  private mobileNumber: string = "";
  private emailVerificationCode: number = 0;
  private occupation: string = "";
  private orgInsti: string = "";
  private jobDegree: string = "";
  private country: string = "";
  private currency: string = "";
  private currencySymbol: string = "";
  private isMember: boolean = false;
  private isRegistering: boolean = false;
  private checkpointID: string = "";
  private bankTxCode: string = "";
  private paymentGateway: string = "";
  private membershipId: string = "";
  private passwordResetCode: string = "";
  private isGSTInvoicePreferred: boolean = false;
  private businessName: string = "";
  private taxID: string = "";
  private taxJurisdiction: string = "inside-tax-jurisdiction-state";
  private billingAddressLine1: string = "";
  private billingAddressLine2: string = "";
  private billingAddressLine3: string = "";

  setEmail(email) {
    this.email = email.toLowerCase();
  }
  setPassword(password) {
    this.password = password;
  }
  setConfirmPassword(password) {
    this.confirmPassword = password;
  }
  setFirstName(firstName) {
    this.firstName = firstName;
  }
  setLastName(lastName) {
    this.lastName = lastName;
  }
  setIsdCode(isdCode) {
    this.isdCode = isdCode;
  }
  setMobileCountry(mobileCountry) {
    this.mobileCountry = mobileCountry;
  }
  setMobileNumber(mobileNumber) {
    this.mobileNumber = mobileNumber;
  }
  setEmailVerificationCode(emailVerificationCode) {
    this.emailVerificationCode = emailVerificationCode;
  }
  setOccupation(occupation) {
    this.occupation = occupation;
  }
  setOrgInsti(orgInsti) {
    this.orgInsti = orgInsti;
  }
  setJobDegree(jobDegree) {
    this.jobDegree = jobDegree;
  }
  setCountry(country) {
    this.country = country;
  }
  setIsMember(isMemberFlag) {
    this.isMember = isMemberFlag;
  }
  setCurrency(currency) {
    this.currency = currency;
  }
  setCheckpointID(id) {
    this.checkpointID = id;
  }
  setCurrencySymbol(symbol) {
    this.currencySymbol = symbol;
  }
  setBankTxCode(txCode) {
    this.bankTxCode = txCode;
  }
  setPaymentGateway(gateway) {
    this.paymentGateway = gateway;
  }
  setMembershipId(id) {
    this.membershipId = id;
  }
  setPasswordResetCode(code) {
    this.passwordResetCode = code;
  }
  setGSTInvoicePreference(isGSTInvoicePreferred) {
    this.isGSTInvoicePreferred = isGSTInvoicePreferred;
  }
  setBusinessName(businessName) {
    this.businessName = businessName;
  }
  setTaxID(taxID) {
    this.taxID = taxID;
  }
  setTaxJurisdiction(taxJurisdiction) {
    this.taxJurisdiction = taxJurisdiction;
  }
  setBillingAddressLine1(billingAddressLine1) {
    this.billingAddressLine1 = billingAddressLine1;
  }
  setBillingAddressLine2(billingAddressLine2) {
    this.billingAddressLine2 = billingAddressLine2;
  }
  setBillingAddressLine3(billingAddressLine3) {
    this.billingAddressLine3 = billingAddressLine3;
  }

  getEmail() {
    return this.email;
  }
  getPassword() {
    return this.password;
  }
  getConfirmPassword() {
    return this.confirmPassword;
  }
  getFirstName() {
    return this.firstName;
  }
  getLastName() {
    return this.lastName;
  }
  getIsdCode() {
    return this.isdCode;
  }
  getMobileCountry() {
    return this.mobileCountry;
  }
  getMobileNumber() {
    return this.mobileNumber;
  }
  getOccupation() {
    return this.occupation;
  }
  getOrgInsti() {
    return this.orgInsti;
  }
  getJobDegree() {
    return this.jobDegree;
  }
  getCountry() {
    return this.country;
  }
  getCurrency() {
    return this.currency;
  }
  getCheckpointID() {
    return this.checkpointID;
  }
  getIsMember() {
    return this.isMember;
  }
  getCurrencySymbol() {
    return this.currencySymbol;
  }
  getBankTxCode() {
    return this.bankTxCode;
  }
  getPaymentGateway() {
    return this.paymentGateway;
  }
  getLoginDetails() {
    let obj = {
      email: this.email,
      password: this.password,
      isRememberMe: this.isRememberMe,
    };
    return obj;
  }
  getSignUpDetails() {
    let obj = {};
    if (this.mobileNumber.length === 0) {
      obj = {
        firstName: this.firstName,
        lastName: this.lastName,
        email: this.email,
        password: this.password,
        isdCode: "",
        mobileCountry: "",
        mobileNumber: "",
      };
    } else {
      obj = {
        firstName: this.firstName,
        lastName: this.lastName,
        email: this.email,
        password: this.password,
        isdCode: this.isdCode,
        mobileCountry: this.mobileCountry,
        mobileNumber: this.mobileNumber,
      };
    }
    return obj;
  }
  getIsRegistering() {
    return this.isRegistering;
  }
  getEmailVerificationDetails() {
    let obj = {
      checkpointID: this.checkpointID,
      emailVerificationCode: this.emailVerificationCode,
    };
    return obj;
  }
  getResendVerificationCodePayload() {
    let obj = {
      checkpointID: this.checkpointID,
    };
    return obj;
  }
  getAccountSetupDetails() {
    let obj: object;
    if (this.isGSTInvoicePreferred) {
      obj = {
        occupation: this.occupation,
        orgInsti: this.orgInsti,
        jobDegree: this.jobDegree,
        country: this.country,
        currency: this.currency,
        isHCIPAIMember: this.isMember,
        checkpointID: this.checkpointID,
        isGSTInvoicePreferred: this.isGSTInvoicePreferred,
        businessName: this.businessName,
        taxID: this.taxID,
        taxJurisdiction: this.taxJurisdiction,
        billingAddressLine1: this.billingAddressLine1,
        billingAddressLine2: this.billingAddressLine2,
        billingAddressLine3: this.billingAddressLine3,
      };
    } else {
      obj = {
        occupation: this.occupation,
        orgInsti: this.orgInsti,
        jobDegree: this.jobDegree,
        country: this.country,
        currency: this.currency,
        isHCIPAIMember: this.isMember,
        checkpointID: this.checkpointID,
        isGSTInvoicePreferred: this.isGSTInvoicePreferred,
        businessName: "",
        taxID: "",
        taxJurisdiction: this.taxJurisdiction,
        billingAddressLine1: this.billingAddressLine1,
        billingAddressLine2: this.billingAddressLine2,
        billingAddressLine3: this.billingAddressLine3,
      };
    }
    return obj;
  }
  getMembershipId() {
    let obj = {
      membershipID: this.membershipId,
    };
    return obj;
  }
  getPasswordResetCode() {
    return this.passwordResetCode;
  }
  getPasswordConfirmationDetails() {
    let obj = {
      password: this.password,
      confirmPassword: this.confirmPassword,
    };
    return obj;
  }
  toggleIsRememberMe() {
    this.isRememberMe = !this.isRememberMe;
  }
  toggleIsRegistering() {
    this.isRegistering = !this.isRegistering;
  }
  getGSTInvoicePreference() {
    return this.isGSTInvoicePreferred;
  }
  getBusinessName() {
    return this.businessName;
  }
  getTaxID() {
    return this.taxID;
  }
  getTaxJurisdiction() {
    let taxJurisdictionFinal = "";
    if (this.taxJurisdiction === "inside-tax-jurisdiction-state") {
      taxJurisdictionFinal = "Inside Maharashtra & Inside India";
    } else if (this.taxJurisdiction === "outside-tax-jurisdiction-state") {
      taxJurisdictionFinal = "Outside Maharashtra & Inside India";
    } else if (this.taxJurisdiction === "outside-tax-jurisdiction-country") {
      taxJurisdictionFinal = "Outside India";
    } else {
      taxJurisdictionFinal = "-";
    }
    return taxJurisdictionFinal;
  }
  getBillingAddressLine1() {
    return this.billingAddressLine1;
  }
  getBillingAddressLine2() {
    return this.billingAddressLine2;
  }
  getBillingAddressLine3() {
    return this.billingAddressLine3;
  }

  getBillingAddress() {
    let billingAddress = "";
    if (this.billingAddressLine1.length > 0) {
      billingAddress = this.billingAddressLine1;
    }

    if (this.billingAddressLine2.length > 0) {
      if (this.billingAddressLine1.length === 0) {
        billingAddress = this.billingAddressLine2;
      } else {
        billingAddress = billingAddress + ", " + this.billingAddressLine2;
      }
    }

    if (this.billingAddressLine3.length > 0) {
      if (
        this.billingAddressLine1.length === 0 &&
        this.billingAddressLine2.length === 0
      ) {
        billingAddress = this.billingAddressLine3;
      } else {
        billingAddress = billingAddress + ", " + this.billingAddressLine3;
      }
    }

    if (
      this.billingAddressLine1.length === 0 &&
      this.billingAddressLine2.length === 0 &&
      this.billingAddressLine3.length === 0
    ) {
      billingAddress = "-";
    }

    return billingAddress;
  }

  getInvoicePref() {
    let obj = {
      isGSTInvoicePreferred: this.isGSTInvoicePreferred,
      businessName: this.businessName,
      taxID: this.taxID,
      taxJurisdiction: this.taxJurisdiction,
      billingAddressLine1: this.billingAddressLine1,
      billingAddressLine2: this.billingAddressLine2,
      billingAddressLine3: this.billingAddressLine3,
    };
    return obj;
  }

  debug() {
    let obj = {
      email: this.email,
      password: this.password,
      isRememberMe: this.isRememberMe,
      firstName: this.firstName,
      lastName: this.lastName,
      isdCode: this.isdCode,
      mobileCountry: this.mobileCountry,
      mobileNumber: this.mobileNumber,
      emailVerificationCode: this.emailVerificationCode,
      occupation: this.occupation,
      orgInsti: this.orgInsti,
      jobDegree: this.jobDegree,
      country: this.country,
      currency: this.currency,
      isMember: this.isMember,
      isRegistering: this.isRegistering,
      checkpointID: this.checkpointID,
    };
    return obj;
  }

  reset() {
    this.email = "";
    this.password = "";
    this.confirmPassword = "";
    this.isRememberMe = true;
    this.firstName = "";
    this.lastName = "";
    this.isdCode = "";
    this.mobileCountry = "";
    this.mobileNumber = "";
    this.emailVerificationCode = 0;
    this.occupation = "";
    this.orgInsti = "";
    this.jobDegree = "";
    this.country = "";
    this.currency = "";
    this.currencySymbol = "";
    this.isMember = false;
    this.isRegistering = false;
    this.checkpointID = "";
    this.bankTxCode = "";
    this.paymentGateway = "";
    this.membershipId = "";
    this.passwordResetCode = "";
  }
}

export default new Store();
