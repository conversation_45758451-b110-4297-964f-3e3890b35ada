import * as postmark from "postmark";
import { POSTMARK_SERVER_TOKEN } from "../../../config";

export const sendInvoiceHelper = async (emailPayload: any) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isBulkInvoicesSent: boolean = false;

  await client.sendEmailBatchWithTemplates(emailPayload, (error, success) => {
    if (error) {
      isBulkInvoicesSent = false;
      console.log(error);
    } else if (success) {
      isBulkInvoicesSent = true;
      console.log(success);
    } else {
      isBulkInvoicesSent = false;
      console.log("Could not send bulk invoices");
    }
  });

  return isBulkInvoicesSent;
};
