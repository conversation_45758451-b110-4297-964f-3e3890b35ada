import * as postmark from "postmark";
import { POSTMARK_SERVER_TOKEN } from "../config";

export const sendNewUserVerificationMail = async (
  firstName: string,
  email: string,
  verificationCode: number,
  senderName: string,
  templateId: any
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isVerificationEmailSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: templateId,
      To: email,
      TemplateModel: {
        verificationCode: verificationCode.toString(),
        firstName: firstName,
        eventName: senderName,
        eventOrganiser: "HCI Professional Association of India",
        signatureName: "Registration Team",
        supportEmail: "<EMAIL>",
      },
    },
    (error, success) => {
      if (error) {
        console.log(`Error while sending email verification mail`);
        console.log(error);
        isVerificationEmailSent = false;
      } else if (success) {
        console.log(`Success while sending email verification mail`);
        console.log(success);
        isVerificationEmailSent = true;
      } else {
        console.log("Error while sending email verification mail");
        isVerificationEmailSent = false;
      }
    }
  );
  return isVerificationEmailSent;
};

export const sendBankConfirmationMail = async (
  eventCode: string,
  firstName: string,
  email: string,
  isCouponApplied: boolean,
  couponDetailsObj: object,
  cartItems: Array<Object>,
  cartSummary: Array<Object>,
  paymentDetails: Array<Object>
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;

  let templateId: any = ********;
  let eventName: string = "India HCI 2024";
  let supportEmail: string = "<EMAIL>";
  let eventOrganiser: string = "HCI Professional Association of India";

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: templateId,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: eventName,
        eventOrganiser: eventOrganiser,
        signatureName: "Registration Team",
        supportEmail: supportEmail,
        isCouponApplied: isCouponApplied,
        couponDetailsObj: couponDetailsObj,
        cartItems: cartItems,
        cartSummary: cartSummary,
        paymentDetails: paymentDetails,
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendFreePurchaseConfirmationMail = async (
  eventCode: string,
  firstName: string,
  email: string,
  isCouponApplied: boolean,
  couponDetailsObj: object,
  cartItems: Array<Object>,
  cartSummary: Array<Object>,
  paymentDetails: Array<Object>
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;

  let templateId: any = 25718332;
  let eventName: string = "India HCI 2024";
  let supportEmail: string = "<EMAIL>";
  let eventOrganiser: string = "HCI Professional Association of India";

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: templateId,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: eventName,
        eventOrganiser: eventOrganiser,
        signatureName: "Registration Team",
        supportEmail: supportEmail,
        isCouponApplied: isCouponApplied,
        couponDetailsObj: couponDetailsObj,
        cartItems: cartItems,
        cartSummary: cartSummary,
        paymentDetails: paymentDetails,
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendRazorpayConfirmationMail = async (
  eventCode: string,
  firstName: string,
  email: string,
  isCouponApplied: boolean,
  couponDetailsObj: object,
  cartItems: Array<Object>,
  cartSummary: Array<Object>,
  paymentDetails: Array<Object>
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;

  let templateId: any = 19392499;
  let eventName: string = "India HCI 2024";
  let supportEmail: string = "<EMAIL>";
  let eventOrganiser: string = "HCI Professional Association of India";

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: templateId,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: eventName,
        eventOrganiser: eventOrganiser,
        signatureName: "Registration Team",
        supportEmail: supportEmail,
        isCouponApplied: isCouponApplied,
        couponDetailsObj: couponDetailsObj,
        cartItems: cartItems,
        cartSummary: cartSummary,
        paymentDetails: paymentDetails,
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendPasswordResetCode = async (
  firstName: string,
  email: string,
  publicResetCode: number
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 19631764,
      To: email,
      TemplateModel: {
        passwordResetCode: publicResetCode.toString(),
        firstName: firstName,
        eventName: "India HCI 2024",
        eventOrganiser: "HCI Professional Association of India",
        signatureName: "Registration Team",
        supportEmail: "<EMAIL>",
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendPasswordChangeConfirmation = async (
  firstName: string,
  email: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 19634353,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: "India HCI 2024",
        eventOrganiser: "HCI Professional Association of India",
        signatureName: "Registration Team",
        supportEmail: "<EMAIL>",
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendBankTransferVerificationMail = async (
  eventCode: string,
  firstName: string,
  email: string,
  paymentID: string,
  transferredAmount: string,
  bankTxCode: string,
  paymentDate: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;

  let templateId: any = ********;
  let eventName: string = "India HCI 2024";
  let supportEmail: string = "<EMAIL>";
  let eventOrganiser: string = "HCI Professional Association of India";

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: templateId,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: eventName,
        paymentID: paymentID,
        transferredAmount: transferredAmount,
        bankTxCode: bankTxCode,
        paymentDate: paymentDate,
        eventOrganiser: eventOrganiser,
        signatureName: "Registration Team",
        supportEmail: supportEmail,
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendSponsorTicketConfirmation = async (
  firstName: string,
  email: string,
  companyName: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: ********,
      To: email,
      TemplateModel: {
        firstName: firstName,
        companyName: companyName,
        eventName: "India HCI 2024",
        eventOrganiser: "HCI Professional Association of India",
        signatureName: "Registration Team",
        supportEmail: "<EMAIL>",
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendGuestTicketConfirmation = async (
  firstName: string,
  email: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 20861873,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: "India HCI 2024",
        eventOrganiser: "HCI Professional Association of India",
        signatureName: "Registration Team",
        supportEmail: "<EMAIL>",
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendDiscountedPrimaryTicketConfirmation = async (
  firstName: string,
  email: string,
  ticketName: string,
  persona: string,
  tier: string,
  discountPerc: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 20890386,
      To: email,
      TemplateModel: {
        firstName: firstName,
        eventName: "India HCI 2024",
        eventOrganiser: "HCI Professional Association of India",
        signatureName: "Registration Team",
        supportEmail: "<EMAIL>",
        ticketName: ticketName,
        persona: persona,
        tier: tier,
        discountPerc: discountPerc,
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendMembershipConfirmationMail = async (
  email: string,
  firstName: string,
  membershipType: string,
  membershipId: string,
  membershipStartDate: string,
  membershipEndDate: string,
  paymentId: string,
  paymentGateway: string,
  paymentAmount: string,
  paymentReferenceId: string,
  paymentDate: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isConfirmationSent: boolean = false;
  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 29045222,
      To: email,
      TemplateModel: {
        firstName: firstName,
        membershipType:
          membershipType === "annualMembership" ? "Annual" : "Lifetime",
        membershipId: membershipId,
        membershipStartDate: new Date(membershipStartDate)
          .toString()
          .substring(4, 15),
        membershipEndDate:
          membershipType === "annualMembership"
            ? new Date(membershipEndDate).toString().substring(4, 15)
            : "-",
        paymentId: paymentId,
        paymentGateway: paymentGateway,
        paymentAmount: paymentAmount,
        paymentReferenceId: paymentReferenceId,
        paymentDate: new Date(paymentDate).toString(),
      },
    },
    (error, success) => {
      if (error) {
        isConfirmationSent = false;
      } else if (success) {
        isConfirmationSent = true;
      } else {
        isConfirmationSent = false;
      }
    }
  );
  return isConfirmationSent;
};

export const sendInvoiceMail = async (
  email: string,
  fullName: string,
  invoiceNo: string,
  pdfDataBase64: any
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isInvoiceSent: boolean = false;

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 29579448,
      To: email,
      TemplateModel: {
        fullName: fullName,
      },
      Attachments: [
        {
          ContentID: "",
          Name: `${invoiceNo}.pdf`,
          Content: pdfDataBase64,
          ContentType: "application/octet-stream",
        },
      ],
    },
    (error, success) => {
      if (error) {
        isInvoiceSent = false;
      } else if (success) {
        isInvoiceSent = true;
      } else {
        isInvoiceSent = false;
      }
    }
  );
  return isInvoiceSent;
};

export const sendIdVerificationMail = async (
  email: string,
  firstName: string,
  idType: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isVerificationMailSent: boolean = false;

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 33001258,
      To: email,
      TemplateModel: {
        firstName: firstName,
        idType: idType === "student" ? "student ID" : "professional ID",
      },
    },
    (error, success) => {
      if (error) {
        isVerificationMailSent = false;
      } else if (success) {
        isVerificationMailSent = true;
      } else {
        isVerificationMailSent = false;
      }
    }
  );

  return isVerificationMailSent;
};

export const cancelIdVerificationMail = async (
  email: string,
  firstName: string,
  idType: string,
  reason: string
) => {
  const token = POSTMARK_SERVER_TOKEN || "";
  const client = new postmark.Client(token);
  let isCancellationMailSent: boolean = false;

  await client.sendEmailWithTemplate(
    {
      From: `<NAME_EMAIL>`,
      TemplateId: 33102246,
      To: email,
      TemplateModel: {
        firstName: firstName,
        idType: idType === "student" ? "student ID" : "professional ID",
        reason: reason,
      },
    },
    (error, success) => {
      if (error) {
        isCancellationMailSent = false;
      } else if (success) {
        isCancellationMailSent = true;
      } else {
        isCancellationMailSent = false;
      }
    }
  );

  return isCancellationMailSent;
};
