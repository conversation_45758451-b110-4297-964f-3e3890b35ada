import { Document, model, Schema } from "mongoose";

interface ticketDocument extends Document {
  ticketID: string;
  eventCode: string;
  type: string;
  subType: string;
  title: string;
  subTitle: string;
  props: string;
  desc: string;
  quantity: number;
  isDisabled: boolean;
  isVisible: boolean;
  isPrimary: boolean;
  isPrimaryDependent: boolean;
  price: [object];
}

const ticketSchema: Schema = new Schema(
  {
    ticketID: {
      type: String,
      required: true,
      unique: true,
      min: 1,
      max: 1024,
    },
    eventCode: {
      type: String,
    },
    type: {
      type: String,
      required: true,
      min: 1,
      max: 1024,
    },
    subType: {
      type: String,
      required: true,
      min: 1,
      max: 1024,
    },
    props: {
      type: String,
      required: true,
      min: 1,
      max: 1024,
    },
    title: {
      type: String,
      required: true,
      min: 1,
      max: 1024,
    },
    subTitle: {
      type: String,
      required: true,
      min: 1,
      max: 1024,
    },
    desc: {
      type: String,
      min: 1,
    },
    quantity: {
      type: Number,
    },
    price: [
      {
        name: {
          type: String,
          min: 1,
          max: 1024,
        },
        startDate: {
          type: Number,
        },
        endDate: {
          type: Number,
        },
        price: {
          inr: {
            type: Number,
          },
          usd: {
            type: Number,
          },
          eur: {
            type: Number,
          },
        },
      },
    ],
    isDisabled: {
      type: Boolean,
    },
    isVisible: {
      type: Boolean,
    },
    isPrimary: {
      type: Boolean,
    },
    isPrimaryDependent: {
      type: Boolean,
    },
  },
  {
    timestamps: true,
  }
);

export const ticketModel = model<ticketDocument>("ticket", ticketSchema);
