import { Document, model, Schema } from "mongoose";

interface couponDocument extends Document {
  id: string;
  eventCode: string;
  name: string;
  deduction: object;
  redeemerList: Array<Object>;
  meta: object;
}

const redeemerSchema: Schema = new Schema({
  redeemerId: {
    type: String,
    min: 1,
  },
  name: {
    first: { type: String, min: 1 },
    last: { type: String, min: 1 },
  },
  email: { type: String, min: 1 },
  code: { type: String, min: 1 },
  isUsed: { type: Boolean },
  usedOn: { type: String, min: 1 },
});

const ticketSubTypeSchema: Schema = new Schema({
  title: {
    type: String,
    min: 1,
  },
  subType: {
    type: String,
    min: 1,
  },
});

const ticketTypeSchema: Schema = new Schema({
  ticketId: {
    type: String,
  },
});

const couponSchema: Schema = new Schema({
  id: {
    type: String,
    min: 1,
    required: true,
    unique: true,
  },
  eventCode: {
    type: String,
  },
  name: {
    type: String,
    required: true,
    min: 1,
  },
  deduction: {
    type: {
      type: String,
      min: 1,
      required: true,
    },
    value: {
      type: Number,
    },
    ticketSubTypes: {
      type: [ticketSubTypeSchema],
    },
    ticketTypes: {
      items: [ticketTypeSchema],
      subDeductionType: {
        type: String,
      },
      subDeductionValue: {
        type: String,
      },
    },
  },
  redeemerList: [redeemerSchema],
  meta: {
    access: { type: String, min: 1 },
    code: { type: String, min: 1 },
    issuer: {
      name: {
        first: { type: String, min: 1 },
        last: { type: String, min: 1 },
      },
      email: { type: String, min: 1 },
    },
    issuedOn: {
      type: String,
    },
  },
});

export const couponModel = model<couponDocument>("coupon", couponSchema);
