import { model, Document, Schema } from "mongoose";

interface eventDocument extends Document {
  id: string;
  code: string;
  displayPos: number;
  name: string;
  tagline: string;
  venue: object;
  website: string;
  invoice: object;
  branding: object;
  schedule: object;
  managers: Array<Object>;
  timestamp: object;
  isPublished: boolean;
}

const managerSchema: Schema = new Schema({
  email: {
    type: String,
  },
  fullName: {
    type: String,
  },
});

const eventSchema: Schema = new Schema({
  id: {
    type: String,
  },
  displayPos: {
    type: Number,
  },
  code: {
    type: String,
  },
  name: {
    type: String,
  },
  tagline: { type: String },
  venue: {
    label: {
      type: String,
    },
    url: {
      type: String,
    },
  },
  website: { type: String },
  invoice: {
    prefix: { type: String },
    count: { type: Number },
    signatory: {
      primary: {
        name: { type: String },
        designation: { type: String },
        signatureUrl: { type: String },
      },
      secondary: {
        name: { type: String },
        designation: { type: String },
        signatureUrl: { type: String },
      },
    },
  },
  branding: {
    logoUrl: {
      type: String,
    },
    logoUrl_Square: {
      type: String,
    },
    logoUrl_Rectangle: {
      type: String,
    },
    bannerUrl: {
      type: String,
    },
    posterUrl: {
      type: String,
    },
  },
  managers: [managerSchema],
  schedule: {
    event: {
      startsOn: {
        type: String,
      },
      endsOn: {
        type: String,
      },
    },
    registration: {
      startsOn: {
        type: String,
      },
      endsOn: {
        type: String,
      },
    },
  },
  timestamp: {
    createdOn: {
      type: String,
    },
    updatedOn: {
      type: String,
    },
  },
  isPublished: {
    type: Boolean,
  },
});

export const eventModel = model<eventDocument>("event", eventSchema);
