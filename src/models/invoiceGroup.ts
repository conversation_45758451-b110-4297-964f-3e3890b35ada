import { Document, model, Schema } from "mongoose";

interface invoiceGroupDocument extends Document {
  id: string;
  name: string;
  invoiceCount: number;
  bulkSendCount: number;
  invoices: Array<Object>;
  branding: object;
  signatory: object;
  createdOn: string;
}

const invoiceSchema: Schema = new Schema({
  id: {
    type: String,
  },
  eventCode: {
    type: String,
  },
  eventName: {
    type: String,
  },
  sellerCin: {
    type: String,
  },
  sellerGstin: {
    type: String,
  },
  invoiceNo: {
    type: String,
  },
  invoiceDate: {
    type: String,
  },
  isGstInvoiceRequested: {
    type: Boolean,
  },
  buyerName: {
    type: String,
  },
  buyerEmail: {
    type: String,
  },
  buyerGstin: {
    type: String,
  },
  buyerCompany: {
    type: String,
  },
  buyerLocation: {
    type: String,
  },
  isCouponApplied: {
    type: Boolean,
  },
  ticketNarration: {
    type: String,
  },
  couponName: {
    type: String,
  },
  couponDeduction: {
    type: String,
  },
  ticketBasePrice: {
    type: String,
  },
  processingCharges: {
    type: String,
  },
  totalTaxableValue: {
    type: String,
  },
  cgst: {
    type: String,
  },
  sgst: {
    type: String,
  },
  igst: {
    type: String,
  },
  totalPaidAmount: {
    type: String,
  },
  paymentMethod: {
    type: String,
  },
  branding: {
    primaryLogoUrl: {
      type: String,
    },
    secondaryLogoUrl: {
      type: String,
    },
  },
  signatory: {
    primary: {
      name: { type: String },
      designation: { type: String },
      signatureUrl: { type: String },
    },
    secondary: {
      name: { type: String },
      designation: { type: String },
      signatureUrl: { type: String },
    },
  },
});

const invoiceGroupSchema: Schema = new Schema({
  id: {
    type: String,
  },
  name: {
    type: String,
  },
  invoiceCount: {
    type: Number,
  },
  bulkSendCount: {
    type: Number,
  },
  invoices: [invoiceSchema],
  createdOn: {
    type: String,
  },
});

export const invoiceGroupModel = model<invoiceGroupDocument>(
  "invoiceGroup",
  invoiceGroupSchema
);
