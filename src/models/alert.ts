import { Document, model, Schema } from "mongoose";

interface alertDocument extends Document {
  id: string;
  eventCode: string;
  type: string;
  issuer: object;
  resolver: object;
  details: object;
  status: object;
  timestamp: object;
}

const alertSchema: Schema = new Schema({
  id: {
    type: String,
    min: 1,
  },
  eventCode: {
    type: String,
  },
  type: {
    type: String,
    min: 1,
  },
  issuer: {
    name: {
      first: { type: String, min: 1 },
      middle: { type: String, min: 1 },
      last: { type: String, min: 1 },
    },
    email: { type: String, min: 1 },
  },
  resolver: {
    name: {
      first: { type: String, min: 1 },
      middle: { type: String, min: 1 },
      last: { type: String, min: 1 },
    },
    email: { type: String, min: 1 },
  },
  details: {
    prop1: { type: String, min: 1 },
    prop2: { type: String, min: 1 },
    prop3: { type: String, min: 1 },
    prop4: { type: String },
    prop5: { type: String },
  },
  status: {
    isActive: { type: Boolean },
    remarks: { type: String, min: 1 },
  },
  timestamp: {
    issuedOn: { type: String, min: 1 },
    resolvedOn: { type: String, min: 1 },
  },
});

export const alertModel = model<alertDocument>("alert", alertSchema);
