import { Document, model, Schema } from "mongoose";

// interface purchasetDocument extends Document {
//   id: string;
//   eventCode: string;
//   type: string;
//   issuer: object;
//   resolver: object;
//   details: object;
//   status: object;
//   timestamp: object;
// }

interface purchasetDocument extends Document {
  userID: string;
  purchaseID: string;
  transactionID: string;
  eventCode: string;
  paymentGateway: string;
  isCouponApplied: boolean;
  coupon: boolean;
  cartTotal: number;
}

const purchaseSchema: Schema = new Schema({
  userID: {
    type: String,
    required: true,
    min: 1,
  },
  purchaseID: {
    type: String,
    required: true,
    min: 1,
  },
  transactionID: {
    type: String,
    required: true,
    min: 1,
  },
  eventCode: {
    type: String,
  },
  paymentGateway: {
    type: String,
    required: true,
    min: 1,
  },
  isCouponApplied: {
    type: Boolean,
  },
  coupon: {
    id: {
      type: String,
    },
    name: {
      type: String,
    },
    deductionType: {
      type: String,
    },
    deductionValue: {
      type: Number,
    },
    cartAmountBeforeDeduction: {
      type: Number,
    },
    deductedAmount: {
      type: Number,
    },
    cartAmountAfterDeduction: {
      type: Number,
    },
  },
  cartTotal: {
    type: Number,
    required: true,
    min: 1,
  },
  gatewayFee: {
    type: Number,
    required: true,
    min: 1,
  },
  grandTotal: {
    type: Number,
    required: true,
    min: 1,
  },
  purchasedItems: {
    type: Array,
    required: true,
  },
  paymentStatus: {
    type: String,
    required: true,
    min: 1,
  },
});

export const purchaseModel = model<purchasetDocument>(
  "purchase",
  purchaseSchema
);
