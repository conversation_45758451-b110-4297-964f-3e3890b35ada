import { Document, model, Schema } from "mongoose";

interface orderDocument extends Document {
  user: object;
  coupon: object;
  transaction: object;
  items: Array<Object>;
  timestamp: object;
}

const orderItemsSchema: Schema = new Schema({
  id: {
    type: String,
  },
  transaction: {
    priceBeforeDeduction: {
      type: Number,
    },
    priceDeductionAmount: {
      type: Number,
    },
    priceAfterDeduction: {
      type: Number,
    },
    gatewayFeeBeforeDeduction: {
      type: Number,
    },
    gatewayFeeDeductionAmount: {
      type: Number,
    },
    gatewayFeeAfterDeduction: {
      type: Number,
    },
  },
});

const orderSchema: Schema = new Schema({
  id: {
    type: String,
  },
  eventCode: {
    type: String,
  },
  user: {
    id: {
      type: String,
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
  },
  coupon: {
    isApplied: {
      type: Boolean,
    },
    id: {
      type: String,
    },
    name: {
      type: String,
    },
    deduction: {
      type: {
        type: String,
      },
      value: {
        type: String,
      },
    },
  },
  transaction: {
    id: {
      type: String,
    },
    cartTotalBeforeDeduction: {
      type: Number,
    },
    cartTotalDeductionAmount: {
      type: Number,
    },
    cartTotalAfterDeduction: {
      type: Number,
    },
    gatewayFeeBeforeDeduction: {
      type: Number,
    },
    gatewayFeeDeductionAmount: {
      type: Number,
    },
    gatewayFeeAfterDeduction: {
      type: Number,
    },
    grandTotalBeforeDeduction: {
      type: Number,
    },
    grandTotalDeductionAmount: {
      type: Number,
    },
    grandTotalAfterDeduction: {
      type: Number,
    },
    paymentGateway: {
      type: String,
    },
    status: {
      type: String,
    },
  },
  items: [orderItemsSchema],
  timestamp: {
    createdOn: {
      type: String,
    },
    updatedOn: {
      type: String,
    },
  },
});

export const orderModel = model<orderDocument>("order", orderSchema);
