// @ts-nocheck

import { Document, model, Schema } from "mongoose";
import * as argon from "argon2";

interface userDocument extends Document {
  meta: object;
  _userID: string;
  profile: object;
  professional: object;
  membership: object;
  cart: Array<Object>;
  cartv2: Array<Object>;
  coupon: object;
  purchases: Array<Object>;
  purchasesV2: object;
  settings: object;
  signature: string;
  passwordMatch: (password: string) => Promise<boolean>;
}

const cartSchema: Schema = new Schema({
  ticketID: {
    type: String,
    required: true,
    min: 1,
  },
  eventCode: {
    type: String,
  },
});

const cartSchema_v2: Schema = new Schema({
  ticketId: {
    type: String,
    required: true,
  },
  eventCode: {
    type: String,
  },
  sessionId: {
    type: String,
  },
});

const purchasedItemsSchema: Schema = new Schema(
  {
    purchaseID: {
      type: String,
      required: true,
      min: 1,
    },
    transactionID: {
      type: String,
      required: true,
      min: 1,
    },
    eventCode: {
      type: String,
    },
    paymentGateway: {
      type: String,
      required: true,
      min: 1,
    },
    isCouponApplied: {
      type: Boolean,
    },
    coupon: {
      id: {
        type: String,
      },
      name: {
        type: String,
      },
      deductionType: {
        type: String,
      },
      deductionValue: {
        type: Number,
      },
      cartAmountBeforeDeduction: {
        type: Number,
      },
      deductedAmount: {
        type: Number,
      },
      cartAmountAfterDeduction: {
        type: Number,
      },
    },
    cartTotal: {
      type: Number,
      required: true,
      min: 1,
    },
    gatewayFee: {
      type: Number,
      required: true,
      min: 1,
    },
    grandTotal: {
      type: Number,
      required: true,
      min: 1,
    },
    purchasedItems: {
      type: Array,
      required: true,
    },
    paymentStatus: {
      type: String,
      required: true,
      min: 1,
    },
  },
  { timestamps: true }
);

const userSchema: Schema = new Schema(
  {
    _userID: {
      type: String,
      required: true,
      unique: true,
      min: 5,
    },
    meta: {
      account: {
        isSetupComplete: { type: Boolean, required: true },
      },
      email: {
        isVerified: { type: Boolean, required: true },
        verificationCode: { type: String },
      },
      oauth: {
        isLinkedInConnected: { type: Boolean },
        isGoogleConnected: { type: Boolean },
      },
    },
    profile: {
      name: {
        first: {
          type: String,
          required: true,
          min: 1,
        },
        middle: {
          type: String,
          min: 1,
        },
        last: {
          type: String,
          min: 1,
        },
      },
      email: {
        type: String,
        required: true,
        unique: true,
        min: 5,
      },
      psswd: {
        type: String,
        min: 8,
      },
      mobile: {
        isdCode: {
          type: String,
          min: 1,
        },
        country: {
          type: String,
          min: 1,
        },
        number: {
          type: String,
          min: 1,
        },
      },
      country: {
        type: String,
        min: 1,
      },
      dpUrl: {
        type: String,
      },
    },
    professional: {
      occupation: {
        type: String,
        min: 1,
      },
      orgInsti: {
        type: String,
        min: 1,
      },
      jobDegree: {
        type: String,
        min: 1,
      },
      idCard: {
        type: {
          type: String,
        },
        url: {
          front: {
            type: String,
          },
          back: {
            type: String,
          },
        },
        expiry: {
          type: String,
        },
        isVerified: {
          type: Boolean,
        },
      },
    },
    membership: {
      isMember: {
        type: Boolean,
      },
      id: {
        type: String,
        min: 1,
      },
      type: {
        type: String,
        min: 1,
      },
      startDate: {
        type: String,
        min: 1,
      },
      endDate: {
        type: String,
        min: 1,
      },
      paymentMethod: {
        type: String,
        min: 1,
      },
      amountPaid: {
        type: Number,
        min: 1,
      },
      txCode: {
        type: String,
        min: 1,
      },
      txTimestamp: {
        type: String,
        min: 1,
      },
      isInCart: {
        type: Boolean,
      },
      remarks: {
        type: String,
      },
      memberFromRegistration: {
        type: Boolean,
      },
      submittedID: {
        type: String,
      },
    },
    cart: [cartSchema],
    cartv2: [cartSchema_v2],
    coupon: {
      isApplied: {
        type: Boolean,
      },
      id: {
        type: String,
      },
    },
    purchasedItems: [purchasedItemsSchema],
    settings: {
      isRememberMe: {
        type: Boolean,
      },
      isRegistrationManager: {
        type: Boolean,
      },
      isAdmin: {
        type: Boolean,
      },
      isSuperAdmin: {
        type: Boolean,
      },
      currency: {
        name: {
          type: String,
          min: 1,
        },
        symbol: {
          type: String,
          min: 1,
        },
      },
      passwordResetCode: {
        type: String,
        min: 1,
      },
      billing: {
        tax: {
          india: {
            gst: {
              isGSTInvoicePreferred: { type: Boolean },
              businessName: { type: String, min: 1 },
              taxID: { type: String, min: 1 },
              taxJurisdiction: { type: String, min: 1 },
              address: {
                billing: {
                  line1: { type: String, min: 1 },
                  line2: { type: String, min: 1 },
                  line3: { type: String, min: 1 },
                },
              },
            },
          },
        },
      },
    },
    signature: {
      type: String,
      min: 1,
    },
  },
  {
    timestamps: true,
  }
);

userSchema.methods.passwordMatch = async function (password: string) {
  if (!this.profile.psswd) {
    return false;
  }
  return await argon.verify(this.profile.psswd, password);
};

export const userModel = model<userDocument>("user", userSchema);
