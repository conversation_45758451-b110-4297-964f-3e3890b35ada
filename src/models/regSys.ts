import { Document, model, Schema } from "mongoose";

interface regSysDocument extends Document {
  id: string;
  eventCode: string;
  pages: Array<Object>;
}

const pageSchema: Schema = new Schema({
  meta: {
    schedule: {
      isScheduled: { type: Boolean },
      enableOn: {
        type: String,
      },
      disableOn: {
        type: String,
      },
    },
    isDisabled: { type: Boolean },
    isPublished: { type: Boolean },
  },
  id: {
    type: String,
  },
  label: {
    type: String,
  },
  icon: {
    type: String,
  },
});

const regSysSchema: Schema = new Schema({
  id: {
    type: String,
  },
  eventCode: {
    type: String,
  },
  pages: [pageSchema],
});

export const regSysModel = model<regSysDocument>("regSys", regSysSchema);
