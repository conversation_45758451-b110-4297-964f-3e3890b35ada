import { Document, model, Schema } from "mongoose";

interface ticketDocument extends Document {
  ticketId: string;
  eventCode: string;
  pageId: string;
  ticketType: string;
  fullTicketDetails: object;
  basicTicketDetails: object;
  trackTicketDetails: object;
  isTicketVisible: boolean;
  isTicketPrimary: boolean;
  isTicketPrimaryDependent: boolean;
}

const tierSchema: Schema = new Schema({
  tierId: {
    type: String,
  },
  tierName: {
    type: String,
  },
  tierStartDate: {
    type: String,
  },
  tierEndDate: {
    type: String,
  },
  studentPrice: { type: Number },
  professionalPrice: { type: Number },
});

const trackSessionSchema: Schema = new Schema({
  id: {
    type: String,
  },
  title: {
    type: String,
  },
  type: {
    type: String,
  },
  quantity: {
    type: Number,
  },
  startsOn: { type: String },
  endsOn: { type: String },
  studentPrice: { type: Number },
  professionalPrice: { type: Number },
  url: {
    type: String,
  },
  instructors: {
    type: String,
  },
  isVisible: { type: Boolean },
  isDisabled: { type: Boolean },
});

const ticketSchema_v2: Schema = new Schema({
  ticketId: {
    type: String,
    required: true,
    unique: true,
  },
  eventCode: {
    type: String,
    required: true,
  },
  pageId: {
    type: String,
    required: true,
  },
  fullTicketDetails: {
    title: {
      type: String,
    },
    tiers: [tierSchema],
  },
  basicTicketDetails: {
    title: {
      type: String,
    },
    tiers: [tierSchema],
  },
  trackTicketDetails: {
    sessions: [trackSessionSchema],
  },
  ticketType: {
    type: String,
    required: true,
  },
  isTicketVisible: {
    type: Boolean,
    required: true,
  },
  isTicketPrimary: {
    type: Boolean,
    required: true,
  },
  isTicketPrimaryDependent: {
    type: Boolean,
    required: true,
  },
});

export const ticketModel_v2 = model<ticketDocument>(
  "ticket_v2",
  ticketSchema_v2
);
