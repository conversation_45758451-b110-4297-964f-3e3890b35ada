import Joi from "@hapi/joi";
import { Request } from "express";

const validateLinkAccountWithOauth = (reqBody: Request) => {
  const userSchema = Joi.object({
    dpUrl: Joi.string().min(1).trim(),
    email: Joi.string()
      .email({
        tlds: {
          allow: false,
        },
      })
      .min(5)
      .max(128)
      .trim()
      .required(),
  });
  return userSchema.validate(reqBody);
};

export default validateLinkAccountWithOauth;
