import Joi from "@hapi/joi";
import { Request } from "express";

const validateCreateTicketInputs = (reqBody: Request) => {
  const schema = Joi.object({
    eventCode: Joi.string().required(),
    pageId: Joi.string().required(),
    ticketTitle: Joi.string().required(),
    ticketType: Joi.string().required(),
    tiers: Joi.array(),
  });
  return schema.validate(reqBody);
};

export default validateCreateTicketInputs;
