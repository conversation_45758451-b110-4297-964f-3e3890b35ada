import Joi from "@hapi/joi";
import { Request } from "express";

const validateAccountSetupInputs_v2 = (reqBody: Request) => {
  const schema = Joi.object({
    orgInsti: Joi.string().min(1).required(),
    jobDegree: Joi.string().min(1).required(),
    country: Joi.string().min(1).required(),
    idCardExpiry: Joi.string().allow(""),
    mobileIsdCode: Joi.string().min(1).allow(""),
    mobileCountry: Joi.string().min(1).allow(""),
    mobileNumber: Joi.string().min(1).allow(""),
    isGSTInvoicePreferred: Joi.boolean().required(),
    businessName: Joi.string().allow(""),
    taxID: Joi.string().allow(""),
    taxJurisdiction: Joi.string().min(1).required(),
    billingAddressLine1: Joi.string().min(1).allow(""),
    billingAddressLine2: Joi.string().min(1).allow(""),
    billingAddressLine3: Joi.string().min(1).allow(""),
  });
  return schema.validate(reqBody);
};

export default validateAccountSetupInputs_v2;
