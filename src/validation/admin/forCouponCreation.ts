import Joi from "@hapi/joi";
import { Request } from "express";

const validateCouponInputs = (reqBody: Request) => {
  const schema = Joi.object({
    couponName: Joi.string().min(1).max(1024).required(),
    couponDeductionType: Joi.string().min(1).max(1024).required(),
    couponFixedDeductionValue: Joi.number().required(),
    couponPercentageDeductionValue: Joi.number().required(),
    couponTicketsForDeduction: Joi.array(),
    couponTicketDeductionLogic: Joi.string(),
    couponTicketsDeductionValue: Joi.number(),
    couponAccessType: Joi.string().min(1).max(1024).required(),
    eventCode: Joi.string().min(1).max(1024).required(),
    emailList: Joi.array(),
  });
  return schema.validate(reqBody);
};

export default validateCouponInputs;
