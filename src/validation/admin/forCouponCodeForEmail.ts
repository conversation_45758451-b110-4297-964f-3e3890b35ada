import Joi from "@hapi/joi";
import { Request } from "express";

const validateCouponCodeInputs = (reqBody: Request) => {
  const schema = Joi.object({
    couponId: Joi.string().min(1).max(1024).required(),
    emailForNewCouponCode: Joi.string()
      .email({ tlds: { allow: false } })
      .min(5)
      .max(128)
      .lowercase()
      .trim()
      .required(),
  });
  return schema.validate(reqBody);
};

export default validateCouponCodeInputs;
