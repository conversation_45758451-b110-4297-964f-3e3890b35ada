import Joi from "@hapi/joi";
import { Request } from "express";

const validateCreateEventInputs = (reqBody: Request) => {
  const schema = Joi.object({
    eventName: Joi.string().required(),
    eventTagline: Joi.string().allow(""),
    eventVenueLabel: Joi.string().allow(""),
    eventVenueUrl: Joi.string().allow(""),
    eventWebsite: Joi.string().allow(""),
    invoicePrefix: Joi.string().required(),
    eventStartDate: Joi.string().required(),
    eventStartTime: Joi.string().required(),
    eventEndDate: Joi.string().required(),
    eventEndTime: Joi.string().required(),
    registrationStartDate: Joi.string().required(),
    registrationStartTime: Joi.string().required(),
    registrationEndDate: Joi.string().required(),
    registrationEndTime: Joi.string().required(),
    managers: Joi.array(),
    isPublished: Joi.boolean().required(),
  });
  return schema.validate(reqBody);
};

export default validateCreateEventInputs;
