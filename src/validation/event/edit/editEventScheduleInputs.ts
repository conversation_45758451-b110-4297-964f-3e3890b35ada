import Joi from "@hapi/joi";
import { Request } from "express";

const validateEditEventScheduleInputs = (reqBody: Request) => {
  const schema = Joi.object({
    eventCode: Joi.string().required(),
    eventStartDate: Joi.string().required(),
    eventStartTime: Joi.string().required(),
    eventEndDate: Joi.string().required(),
    eventEndTime: Joi.string().required(),
    registrationStartDate: Joi.string().required(),
    registrationStartTime: Joi.string().required(),
    registrationEndDate: Joi.string().required(),
    registrationEndTime: Joi.string().required(),
  });
  return schema.validate(reqBody);
};

export default validateEditEventScheduleInputs;
