import Joi from "@hapi/joi";
import { Request } from "express";

const validateEditEventBasicsInputs = (reqBody: Request) => {
  const schema = Joi.object({
    eventCode: Joi.string().required(),
    eventName: Joi.string().required(),
    eventTagline: Joi.string().allow(""),
    eventVenueLabel: Joi.string().allow(""),
    eventVenueUrl: Joi.string().allow(""),
    eventWebsite: Joi.string().allow(""),
  });
  return schema.validate(reqBody);
};

export default validateEditEventBasicsInputs;
