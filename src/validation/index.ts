export { default as validateLoginInputs } from "./forLogin";
export { default as validateLocalSignupInputs } from "./auth/signup";
export { default as validateRegistrationInputs } from "./forRegistration";
export { default as validateEmailVerificationInputs } from "./forEmailVerification";
export { default as validateEmailVerificationCodeResetInputs } from "./forEmailVerificationCodeReset";
export { default as validateAccountSetupInputs } from "./forAccountSetup";
export { default as validateAccountSetupInputs_v2 } from "./account/accountSetup";

export { default as isMember } from "./forMembers/isMember";

export { default as validateGetTicketDetails } from "./forTicket/getTicket";

export { default as validateAddToCartInputs } from "./forCart/addToCartDetails";
export { default as validateCartTotalInputs } from "./forCart/getCartTotalDetails";

export { default as validateBankPaymentDetails } from "./forPayment/bankPaymentDetails";
export { default as validateBankPaymentDetails_v2 } from "./forPayment/bankPaymentDetails_v2";
export { default as validateRazorpayPaymentDetails } from "./forPayment/razorpayPaymentDetails";
export { default as validateRazorpayPaymentDetails_v2 } from "./forPayment/razorpayPaymentDetails_v2";

/* Email */
export { default as validateEmail } from "./forGeneral/isValidEmail";

/* Password Reset Code */
export { default as validatePasswordResetCode } from "./forGeneral/isPasswordResetCodeValid";
export { default as validateResendPasswordResetCode } from "./forPasswordResetCodeResend";
export { default as validateNewPassword } from "./forGeneral/isNewPasswordValid";
export { default as validatePasswordConfirmation } from "./forNewPasswordConfirmation";

/* Profile */
export { default as isNameValid } from "./forProfile/isNameValid";
export { default as isEmailValid } from "./forProfile/isEmailValid";
export { default as isPasswordValid } from "./forProfile/isPasswordValid";
export { default as isMobileValid } from "./forProfile/isMobileValid";
export { default as isCountryValid } from "./forProfile/isCountryValid";
export { default as isProfileInfoStringValid } from "./forProfile/isProfileInfoStringValid";
export { default as isProfileUpdateInputsValid } from "./forProfile/isProfileUpdateInputsValid";

/* User */
export { default as isGetAllUserInputsValid } from "./forUsers/getAllUsers";

/*------------------
Validation for Admin
------------------*/
export { default as validateBankTxInputs } from "./admin/bankTxVerification";
export { default as isSettingsInputValid } from "./admin/isSettingsInputValid";
export { default as validateCouponInputs } from "./admin/forCouponCreation";
export { default as isInvoicePrefValid } from "./isInvoicePrefValid";
export { default as isGetCouponsInputValid } from "./admin/isGetCouponsInputValid";
export { default as isGetRedeemerListInputValid } from "./admin/isGetRedeemerListInputValid";
export { default as validateCouponCodeInputs } from "./admin/forCouponCodeForEmail";

/*--------
For Coupon
--------*/
export { default as validateCheckAndApplyCouponInputs } from "./forCoupon/checkAndApplyCoupon";

/* ---
OAuth
--- */
export { default as validateOauthSignupInputs } from "./oauth/oauthSignup";
export { default as validateLinkAccountWithOauth } from "./oauth/linkAccountWithOauth";

/*----
Events
----*/
export { default as validateCreateEventInputs } from "./event/createEventInputs";
export { default as validateGetEventsInputs } from "./event/getEventsInputs";
export { default as validateGetEventByCodeInputs } from "./event/getEventByCodeInputs";
export { default as validateUpdateEventPublishingInputs } from "./event/updateEventPublishing";
export { default as validateEditEventBasicsInputs } from "./event/edit/editEventBasicsInputs";
export { default as validateEditEventAssetsInputs } from "./event/edit/editEventAssetsInputs";
export { default as validateEditEventInvoicingInputs } from "./event/edit/editEventInvoicingInputs";
export { default as validateEditEventManagersInputs } from "./event/edit/editEventManagersInputs";
export { default as validateEditEventScheduleInputs } from "./event/edit/editEventScheduleInputs";

/*----
RegSys
----*/
export { default as validateGetTicketsByPageIdAndEventCodeInputs } from "./regSys/ticketsByPageIdAndEventCodeInputs";
export { default as validateCreatePageInputs } from "./regSys/createPage_Inputs";
export { default as validateGetPagesInputs } from "./regSys/getPages_Inputs";
export { default as validateDeletePageInputs } from "./regSys/deletePage_Inputs";
export { default as validateUpdatePageInputs } from "./regSys/updatePage_Inputs";

/*-----
Tickets
-----*/
export { default as validateCreateTicketInputs } from "./forTicket/createTicketInputs";

/*--
Cart
--*/
export { default as validateAddToCartInputs_v2 } from "./forCart/addToCartDetails_v2";
export { default as validateCheckoutInputs } from "./forCart/checkoutInputs";
