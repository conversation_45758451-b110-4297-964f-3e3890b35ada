/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
import { MatchResults, RouterHistory } from "@stencil/router";
export namespace Components {
    interface AppRoot {
        "history": RouterHistory;
    }
    interface CAccountControl {
    }
    interface CAccountDetailsModal {
    }
    interface CAlert {
        "alertId": string;
        "alertType": string;
        "emailIssuer": string;
        "emailResolver": string;
        "firstNameIssuer": string;
        "firstNameResolver": string;
        "isActive": boolean;
        "issuedOn": string;
        "lastNameIssuer": string;
        "lastNameResolver": string;
        "propOne": string;
        "propThree": string;
        "propTwo": string;
        "resolvedOn": string;
    }
    interface CAlertsFilter {
        "alertCount": string;
    }
    interface CBadge {
        "color": string;
        "label": string;
    }
    interface CBanner {
        "isVisible": boolean;
        "position": string;
        "theme": string;
        "variant": string;
    }
    interface CBtn {
        "actionLabel": string;
        "icon": boolean;
        "iconUrl": string;
        "isDisabled": boolean;
        "isInAction": boolean;
        "label": string;
        "name": string;
        "type": string;
        "value": string;
    }
    interface CBtnLight {
        "actionLabel": string;
        "isDisabled": boolean;
        "isInAction": boolean;
        "label": string;
        "name": string;
    }
    interface CButton {
        "iconName": string;
        "iconUrl": string;
        "isDisabled": boolean;
        "isInAction": boolean;
        "isInActionLabel": string;
        "isInActiveState": boolean;
        "label": string;
        "name": string;
        "theme": string;
        "type": string;
        "value": string;
    }
    interface CBuynowBtn {
        "ticketId": string;
    }
    interface CCard {
        "eventBannerUrl": string;
        "eventCode": string;
        "eventEndsOn": string;
        "eventId": string;
        "eventIsActive": boolean;
        "eventIsArchived": boolean;
        "eventIsManager": boolean;
        "eventIsPublished": boolean;
        "eventIsRegistrationOpen": boolean;
        "eventLogoUrl": string;
        "eventName": string;
        "eventPosterUrl": string;
        "eventRegEndsOn": string;
        "eventRegStartsOn": string;
        "eventStartsOn": string;
        "eventTagline": string;
        "eventVenueLabel": string;
        "eventVenueUrl": string;
        "eventWebsiteUrl": string;
        "type": string;
    }
    interface CCartItem {
        "currency": string;
        "heading": string;
        "price": string;
        "subTitle": string;
    }
    interface CCartList {
    }
    interface CCartPreview {
    }
    interface CCartTotal {
    }
    interface CCheckMembership {
    }
    interface CCheckbox {
        "isChecked": boolean;
        "item": string;
        "label": string;
        "name": string;
    }
    interface CCheckoutOptions {
    }
    interface CControlBar {
        "count": number;
        "invoiceYearsString": any;
        "name": string;
    }
    interface CCoupon {
        "access": string;
        "couponId": string;
        "deductionType": string;
        "deductionValue": string;
        "issuedBy": string;
        "issuedCount": string;
        "issuedOn": string;
        "name": string;
        "openCode": string;
        "usedCount": string;
    }
    interface CCouponApplied {
    }
    interface CCouponDetailsList {
        "access": string;
        "couponId": string;
        "issuedCount": number;
        "redeemerListString": string;
        "usedCount": number;
    }
    interface CCouponDetailsModal {
    }
    interface CCouponInput {
    }
    interface CCouponItem {
        "couponCode": string;
        "couponName": string;
    }
    interface CCouponList {
    }
    interface CCreateCouponModal {
    }
    interface CCreateInvoiceModal {
    }
    interface CDatePicker {
        "date": string;
        "name": string;
        "pickTime": boolean;
        "time": string;
    }
    interface CDivider {
        "type": string;
    }
    interface CDropdown {
        "isDisabled": boolean;
        "name": string;
        "optionStr": string;
        "value": string;
    }
    interface CEditButton {
    }
    interface CHeader {
    }
    interface CImg {
        "alt": string;
        "src": string;
        "type": string;
    }
    interface CInjectedticket {
    }
    interface CInputbox {
        "isAutofocus": boolean;
        "isDisabled": boolean;
        "name": string;
        "placeholder": string;
        "type": string;
        "value": string;
    }
    interface CInputphone {
        "dropdownValue": string;
        "inputboxValue": string;
        "isDisabled": boolean;
        "numpadPlaceholder": string;
        "selectValue": string;
    }
    interface CInvoiceDetailsModal {
        "invoiceGroup": string;
    }
    interface CLink {
        "iconName": string;
        "name": string;
        "target": string;
        "type": string;
        "url": string;
    }
    interface CList {
        "listItemsAsString": string;
        "name": string;
        "subType": string;
        "type": string;
    }
    interface CListItem {
        "isClickable": boolean;
        "type": string;
    }
    interface CMember {
        "email": string;
        "expiresIn": string;
        "memberId": string;
        "membershipEndDate": string;
        "membershipStartDate": string;
        "membershipType": string;
        "name": string;
    }
    interface CMemberFilter {
        "memberCount": number;
    }
    interface CMobileCart {
    }
    interface CMobileMenu {
    }
    interface CModal {
        "data": any;
        "isActive": boolean;
        "name": string;
    }
    interface CNotification {
        "isActive": boolean;
    }
    interface COauth {
        "history": RouterHistory;
    }
    interface COverviewInfo {
        "count": string;
        "highlight": string;
        "isBtn": boolean;
        "label": string;
        "layout": string;
        "total": string;
        "width": number;
    }
    interface CPage {
        "type": string;
    }
    interface CPaidItems {
        "cartAmountAfterDeduction": number;
        "cartAmountBeforeDeduction": number;
        "cartTotal": string;
        "couponDeductionType": string;
        "couponDeductionValue": number;
        "couponName": string;
        "currency": string;
        "deductedAmount": number;
        "gatewayFee": string;
        "grandTotal": string;
        "isCouponApplied": boolean;
        "paymentMethod": string;
        "purchaseDate": string;
        "purchaseId": string;
        "purchaseStatus": string;
        "purchasedItems": string;
        "transactionId": string;
    }
    interface CPaidItemsList {
        "ownPurchases": boolean;
    }
    interface CPaymentOptions {
    }
    interface CPaymentSummaryItems {
        "cartAmountAfterDeduction": number;
        "cartAmountBeforeDeduction": number;
        "cartTotal": string;
        "couponDeductionType": string;
        "couponDeductionValue": number;
        "couponName": string;
        "currency": string;
        "deductedAmount": number;
        "email": string;
        "gatewayFee": string;
        "grandTotal": string;
        "isCouponApplied": boolean;
        "name": string;
        "paymentMethod": string;
        "purchaseDate": string;
        "purchaseId": string;
        "purchaseStatus": string;
        "purchasedItems": string;
        "transactionId": string;
    }
    interface CPriceband {
        "currency": string;
        "heading": string;
        "isDiscount": boolean;
        "price": number;
        "subheading": string;
    }
    interface CProfileItem {
        "isEditable": boolean;
        "label": string;
        "name": string;
        "value": string;
    }
    interface CProfileItemEdit {
        "name": string;
    }
    interface CProfileItemsList {
    }
    interface CPurchaseFilter {
        "btnLabel": string;
        "purchaseCount": string;
    }
    interface CPurchaseList {
        "history": RouterHistory;
    }
    interface CPurchasedItem {
        "currency": string;
        "heading": string;
        "price": string;
        "subtitle": string;
        "ticketId": string;
        "tier": string;
    }
    interface CRadio {
        "isChecked": boolean;
        "isLabel2Link": boolean;
        "label1": string;
        "label2": string;
        "label3": string;
        "name": string;
        "url": string;
        "val": string;
        "variant": string;
    }
    interface CRegistrant {
    }
    interface CRmAccounts {
    }
    interface CRmAlerts {
    }
    interface CRmCoupons {
    }
    interface CRmInvoices {
    }
    interface CRmMembers {
    }
    interface CRmOverview {
    }
    interface CRmOverview2 {
    }
    interface CRmPurchases {
    }
    interface CRmSales {
    }
    interface CRmVerification {
    }
    interface CRow {
        "type": string;
    }
    interface CSection {
    }
    interface CSectionBuyMembership {
    }
    interface CSectionGetMemberDiscount {
    }
    interface CSectionList {
    }
    interface CSectionTicket {
        "type": string;
    }
    interface CSessionInfo {
        "data": any;
    }
    interface CSidebar {
        "type": string;
    }
    interface CSiteLogo {
        "link": string;
        "src": string;
    }
    interface CSkel {
        "variant": string;
    }
    interface CSkelCard {
        "container": boolean;
    }
    interface CSkelLine {
        "color": string;
        "width": number;
    }
    interface CSpinner {
    }
    interface CSpinnerDark {
    }
    interface CStatus {
        "type": string;
    }
    interface CTaxPrefModal {
    }
    interface CText {
        "iconName": string;
        "isMandatory": boolean;
        "theme": string;
        "type": string;
    }
    interface CTextLink {
        "label": string;
        "url": string;
    }
    interface CTextarea {
        "cols": number;
        "name": string;
        "placeholder": string;
        "rows": number;
    }
    interface CTextbox {
        "inputType": string;
        "isDisabled": boolean;
        "isError": boolean;
        "isInFocus": boolean;
        "isTextboxFilled": boolean;
        "name": string;
        "placeholder": string;
        "type": string;
        "value": string;
    }
    interface CTicketBasicTicket {
        "isConfigMode": boolean;
        "isDisabled": boolean;
        "isPrimary": boolean;
        "isPrimaryDependent": boolean;
        "isTicketInCart": boolean;
        "isTicketPurchased": boolean;
        "isVisible": boolean;
        "mode": string;
        "persona": string;
        "purchaseStatus": string;
        "ticketId": string;
        "ticketTitle": string;
        "tierString": string;
        "type": string;
    }
    interface CTicketDetailModal {
    }
    interface CTicketEligibilityItem {
        "isMultiDate": boolean;
        "itemDate": string;
        "itemDateRange": string;
        "itemId": string;
    }
    interface CTicketFullTicket {
        "isConfigMode": boolean;
        "isDisabled": boolean;
        "isPrimary": boolean;
        "isPrimaryDependent": boolean;
        "isTicketInCart": boolean;
        "isTicketPurchased": boolean;
        "isVisible": boolean;
        "mode": string;
        "persona": string;
        "purchaseStatus": string;
        "ticketId": string;
        "ticketTitle": string;
        "tierString": string;
        "type": string;
    }
    interface CTicketPartialAccessDateItem {
        "dateId": string;
        "endDate": string;
        "isMultiDay": boolean;
        "name": string;
        "startDate": string;
    }
    interface CTicketPartialAccessDates {
        "accessDatesString": string;
    }
    interface CTicketPriceInputFull {
        "pricingTierString": string;
    }
    interface CTicketPriceInputPartial {
        "accessDatesPriceListString": string;
        "accessDatesString": string;
    }
    interface CTicketPriceInputTrack {
        "subTicketsString": string;
    }
    interface CTicketPriceItemFull {
        "price_Professional": number;
        "price_Student": number;
        "tierEnd": string;
        "tierId": string;
        "tierName": string;
        "tierRange": string;
        "tierStart": string;
    }
    interface CTicketPriceItemPartial {
        "accessId": string;
        "price_Professional": number;
        "price_Student": number;
        "tierEnd": string;
        "tierId": string;
        "tierName": string;
        "tierRange": string;
        "tierStart": string;
    }
    interface CTicketPriceItemTrack {
        "name": string;
        "price_Professional": number;
        "price_Student": number;
        "subTicketId": string;
    }
    interface CTicketTrack {
        "date": string;
        "desc": string;
        "end": string;
        "isDisabled": boolean;
        "people": string;
        "price": string;
        "quantity": string;
        "start": string;
        "subTitle": string;
        "subType": string;
        "ticketBtnStatus": string;
        "ticketID": string;
        "ticketTitle": string;
        "type": string;
        "url": string;
    }
    interface CTicketTrackDays {
        "trackDaysString": string;
    }
    interface CTicketTrackSession {
        "endsOn": string;
        "instructors": string;
        "isDisabled": boolean;
        "isSoldOut": boolean;
        "isTicketInCart": boolean;
        "isTicketPurchased": boolean;
        "isVisible": boolean;
        "persona": string;
        "professionalPrice": number;
        "purchaseStatus": string;
        "quantity": number;
        "sessionId": string;
        "startsOn": string;
        "studentPrice": number;
        "ticketId": string;
        "ticketTitle": string;
        "type": string;
        "url": string;
    }
    interface CTicketTrackSessions {
        "accessDatesString": string;
        "subTicketsString": string;
        "trackDaysString": string;
    }
    interface CTicketTrackTicket {
        "isDisabled": boolean;
        "isPrimary": boolean;
        "isPrimaryDependent": boolean;
        "isVisible": boolean;
        "persona": string;
        "sessionString": string;
        "ticketId": string;
        "type": string;
    }
    interface CToast {
        "duration": number;
        "label": string;
        "type": string;
    }
    interface CTopbar {
        "logoType": string;
    }
    interface CUploader {
        "fileSizeLimitInKB": number;
        "fileType": string;
        "name": string;
        "resetUploader": boolean;
    }
    interface CUserInfoModal {
    }
    interface CUserMenu {
    }
    interface CUserVerificationCard {
        "bankTxCode": string;
        "email": string;
        "firstName": string;
        "lastName": string;
        "orderID": string;
        "transferredAmount": string;
        "verificationType": string;
    }
    interface CVnav {
        "activeOption": string;
        "isConfigMode": boolean;
        "isDisabled": boolean;
        "name": string;
        "navOptsStr": string;
    }
    interface CVnavItem {
        "configure": boolean;
        "icon": string;
        "isConfigMode": boolean;
        "isDisabled": boolean;
        "label": string;
        "name": string;
        "navLen": number;
        "navName": string;
        "route": string;
        "state": string;
        "subText": string;
    }
    interface CWizardAccountSetup {
    }
    interface CWizardAddPage {
    }
    interface CWizardAddTicket {
    }
    interface CWizardCreateEvent {
    }
    interface CWizardEditEvent {
    }
    interface CWizardGetId {
    }
    interface PAccesslist {
    }
    interface PAttendeeList {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PAuth {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PBankThanks {
        "history": RouterHistory;
    }
    interface PCatchAll {
        "history": RouterHistory;
    }
    interface PCheckout {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PConfigure {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PConfirm {
        "history": RouterHistory;
    }
    interface PDashboard {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PFreePurchaseThanks {
        "history": RouterHistory;
    }
    interface PHome {
        "history": RouterHistory;
    }
    interface PInstructor {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PMembershipThanks {
        "history": RouterHistory;
    }
    interface PPostGoogleOauth {
        "history": RouterHistory;
    }
    interface PPostLinkedinOauth {
        "history": RouterHistory;
    }
    interface PProfile {
        "history": RouterHistory;
    }
    interface PPurchases {
        "history": RouterHistory;
    }
    interface PRegform {
        "history": RouterHistory;
    }
    interface PRegform2 {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PRzpThanks {
        "history": RouterHistory;
    }
    interface PTest {
        "history": RouterHistory;
        "match": MatchResults;
    }
    interface PTrackChair {
        "width": string;
    }
    interface PTrackOverview {
        "history": RouterHistory;
        "match": MatchResults;
    }
}
declare global {
    interface HTMLAppRootElement extends Components.AppRoot, HTMLStencilElement {
    }
    var HTMLAppRootElement: {
        prototype: HTMLAppRootElement;
        new (): HTMLAppRootElement;
    };
    interface HTMLCAccountControlElement extends Components.CAccountControl, HTMLStencilElement {
    }
    var HTMLCAccountControlElement: {
        prototype: HTMLCAccountControlElement;
        new (): HTMLCAccountControlElement;
    };
    interface HTMLCAccountDetailsModalElement extends Components.CAccountDetailsModal, HTMLStencilElement {
    }
    var HTMLCAccountDetailsModalElement: {
        prototype: HTMLCAccountDetailsModalElement;
        new (): HTMLCAccountDetailsModalElement;
    };
    interface HTMLCAlertElement extends Components.CAlert, HTMLStencilElement {
    }
    var HTMLCAlertElement: {
        prototype: HTMLCAlertElement;
        new (): HTMLCAlertElement;
    };
    interface HTMLCAlertsFilterElement extends Components.CAlertsFilter, HTMLStencilElement {
    }
    var HTMLCAlertsFilterElement: {
        prototype: HTMLCAlertsFilterElement;
        new (): HTMLCAlertsFilterElement;
    };
    interface HTMLCBadgeElement extends Components.CBadge, HTMLStencilElement {
    }
    var HTMLCBadgeElement: {
        prototype: HTMLCBadgeElement;
        new (): HTMLCBadgeElement;
    };
    interface HTMLCBannerElement extends Components.CBanner, HTMLStencilElement {
    }
    var HTMLCBannerElement: {
        prototype: HTMLCBannerElement;
        new (): HTMLCBannerElement;
    };
    interface HTMLCBtnElement extends Components.CBtn, HTMLStencilElement {
    }
    var HTMLCBtnElement: {
        prototype: HTMLCBtnElement;
        new (): HTMLCBtnElement;
    };
    interface HTMLCBtnLightElement extends Components.CBtnLight, HTMLStencilElement {
    }
    var HTMLCBtnLightElement: {
        prototype: HTMLCBtnLightElement;
        new (): HTMLCBtnLightElement;
    };
    interface HTMLCButtonElement extends Components.CButton, HTMLStencilElement {
    }
    var HTMLCButtonElement: {
        prototype: HTMLCButtonElement;
        new (): HTMLCButtonElement;
    };
    interface HTMLCBuynowBtnElement extends Components.CBuynowBtn, HTMLStencilElement {
    }
    var HTMLCBuynowBtnElement: {
        prototype: HTMLCBuynowBtnElement;
        new (): HTMLCBuynowBtnElement;
    };
    interface HTMLCCardElement extends Components.CCard, HTMLStencilElement {
    }
    var HTMLCCardElement: {
        prototype: HTMLCCardElement;
        new (): HTMLCCardElement;
    };
    interface HTMLCCartItemElement extends Components.CCartItem, HTMLStencilElement {
    }
    var HTMLCCartItemElement: {
        prototype: HTMLCCartItemElement;
        new (): HTMLCCartItemElement;
    };
    interface HTMLCCartListElement extends Components.CCartList, HTMLStencilElement {
    }
    var HTMLCCartListElement: {
        prototype: HTMLCCartListElement;
        new (): HTMLCCartListElement;
    };
    interface HTMLCCartPreviewElement extends Components.CCartPreview, HTMLStencilElement {
    }
    var HTMLCCartPreviewElement: {
        prototype: HTMLCCartPreviewElement;
        new (): HTMLCCartPreviewElement;
    };
    interface HTMLCCartTotalElement extends Components.CCartTotal, HTMLStencilElement {
    }
    var HTMLCCartTotalElement: {
        prototype: HTMLCCartTotalElement;
        new (): HTMLCCartTotalElement;
    };
    interface HTMLCCheckMembershipElement extends Components.CCheckMembership, HTMLStencilElement {
    }
    var HTMLCCheckMembershipElement: {
        prototype: HTMLCCheckMembershipElement;
        new (): HTMLCCheckMembershipElement;
    };
    interface HTMLCCheckboxElement extends Components.CCheckbox, HTMLStencilElement {
    }
    var HTMLCCheckboxElement: {
        prototype: HTMLCCheckboxElement;
        new (): HTMLCCheckboxElement;
    };
    interface HTMLCCheckoutOptionsElement extends Components.CCheckoutOptions, HTMLStencilElement {
    }
    var HTMLCCheckoutOptionsElement: {
        prototype: HTMLCCheckoutOptionsElement;
        new (): HTMLCCheckoutOptionsElement;
    };
    interface HTMLCControlBarElement extends Components.CControlBar, HTMLStencilElement {
    }
    var HTMLCControlBarElement: {
        prototype: HTMLCControlBarElement;
        new (): HTMLCControlBarElement;
    };
    interface HTMLCCouponElement extends Components.CCoupon, HTMLStencilElement {
    }
    var HTMLCCouponElement: {
        prototype: HTMLCCouponElement;
        new (): HTMLCCouponElement;
    };
    interface HTMLCCouponAppliedElement extends Components.CCouponApplied, HTMLStencilElement {
    }
    var HTMLCCouponAppliedElement: {
        prototype: HTMLCCouponAppliedElement;
        new (): HTMLCCouponAppliedElement;
    };
    interface HTMLCCouponDetailsListElement extends Components.CCouponDetailsList, HTMLStencilElement {
    }
    var HTMLCCouponDetailsListElement: {
        prototype: HTMLCCouponDetailsListElement;
        new (): HTMLCCouponDetailsListElement;
    };
    interface HTMLCCouponDetailsModalElement extends Components.CCouponDetailsModal, HTMLStencilElement {
    }
    var HTMLCCouponDetailsModalElement: {
        prototype: HTMLCCouponDetailsModalElement;
        new (): HTMLCCouponDetailsModalElement;
    };
    interface HTMLCCouponInputElement extends Components.CCouponInput, HTMLStencilElement {
    }
    var HTMLCCouponInputElement: {
        prototype: HTMLCCouponInputElement;
        new (): HTMLCCouponInputElement;
    };
    interface HTMLCCouponItemElement extends Components.CCouponItem, HTMLStencilElement {
    }
    var HTMLCCouponItemElement: {
        prototype: HTMLCCouponItemElement;
        new (): HTMLCCouponItemElement;
    };
    interface HTMLCCouponListElement extends Components.CCouponList, HTMLStencilElement {
    }
    var HTMLCCouponListElement: {
        prototype: HTMLCCouponListElement;
        new (): HTMLCCouponListElement;
    };
    interface HTMLCCreateCouponModalElement extends Components.CCreateCouponModal, HTMLStencilElement {
    }
    var HTMLCCreateCouponModalElement: {
        prototype: HTMLCCreateCouponModalElement;
        new (): HTMLCCreateCouponModalElement;
    };
    interface HTMLCCreateInvoiceModalElement extends Components.CCreateInvoiceModal, HTMLStencilElement {
    }
    var HTMLCCreateInvoiceModalElement: {
        prototype: HTMLCCreateInvoiceModalElement;
        new (): HTMLCCreateInvoiceModalElement;
    };
    interface HTMLCDatePickerElement extends Components.CDatePicker, HTMLStencilElement {
    }
    var HTMLCDatePickerElement: {
        prototype: HTMLCDatePickerElement;
        new (): HTMLCDatePickerElement;
    };
    interface HTMLCDividerElement extends Components.CDivider, HTMLStencilElement {
    }
    var HTMLCDividerElement: {
        prototype: HTMLCDividerElement;
        new (): HTMLCDividerElement;
    };
    interface HTMLCDropdownElement extends Components.CDropdown, HTMLStencilElement {
    }
    var HTMLCDropdownElement: {
        prototype: HTMLCDropdownElement;
        new (): HTMLCDropdownElement;
    };
    interface HTMLCEditButtonElement extends Components.CEditButton, HTMLStencilElement {
    }
    var HTMLCEditButtonElement: {
        prototype: HTMLCEditButtonElement;
        new (): HTMLCEditButtonElement;
    };
    interface HTMLCHeaderElement extends Components.CHeader, HTMLStencilElement {
    }
    var HTMLCHeaderElement: {
        prototype: HTMLCHeaderElement;
        new (): HTMLCHeaderElement;
    };
    interface HTMLCImgElement extends Components.CImg, HTMLStencilElement {
    }
    var HTMLCImgElement: {
        prototype: HTMLCImgElement;
        new (): HTMLCImgElement;
    };
    interface HTMLCInjectedticketElement extends Components.CInjectedticket, HTMLStencilElement {
    }
    var HTMLCInjectedticketElement: {
        prototype: HTMLCInjectedticketElement;
        new (): HTMLCInjectedticketElement;
    };
    interface HTMLCInputboxElement extends Components.CInputbox, HTMLStencilElement {
    }
    var HTMLCInputboxElement: {
        prototype: HTMLCInputboxElement;
        new (): HTMLCInputboxElement;
    };
    interface HTMLCInputphoneElement extends Components.CInputphone, HTMLStencilElement {
    }
    var HTMLCInputphoneElement: {
        prototype: HTMLCInputphoneElement;
        new (): HTMLCInputphoneElement;
    };
    interface HTMLCInvoiceDetailsModalElement extends Components.CInvoiceDetailsModal, HTMLStencilElement {
    }
    var HTMLCInvoiceDetailsModalElement: {
        prototype: HTMLCInvoiceDetailsModalElement;
        new (): HTMLCInvoiceDetailsModalElement;
    };
    interface HTMLCLinkElement extends Components.CLink, HTMLStencilElement {
    }
    var HTMLCLinkElement: {
        prototype: HTMLCLinkElement;
        new (): HTMLCLinkElement;
    };
    interface HTMLCListElement extends Components.CList, HTMLStencilElement {
    }
    var HTMLCListElement: {
        prototype: HTMLCListElement;
        new (): HTMLCListElement;
    };
    interface HTMLCListItemElement extends Components.CListItem, HTMLStencilElement {
    }
    var HTMLCListItemElement: {
        prototype: HTMLCListItemElement;
        new (): HTMLCListItemElement;
    };
    interface HTMLCMemberElement extends Components.CMember, HTMLStencilElement {
    }
    var HTMLCMemberElement: {
        prototype: HTMLCMemberElement;
        new (): HTMLCMemberElement;
    };
    interface HTMLCMemberFilterElement extends Components.CMemberFilter, HTMLStencilElement {
    }
    var HTMLCMemberFilterElement: {
        prototype: HTMLCMemberFilterElement;
        new (): HTMLCMemberFilterElement;
    };
    interface HTMLCMobileCartElement extends Components.CMobileCart, HTMLStencilElement {
    }
    var HTMLCMobileCartElement: {
        prototype: HTMLCMobileCartElement;
        new (): HTMLCMobileCartElement;
    };
    interface HTMLCMobileMenuElement extends Components.CMobileMenu, HTMLStencilElement {
    }
    var HTMLCMobileMenuElement: {
        prototype: HTMLCMobileMenuElement;
        new (): HTMLCMobileMenuElement;
    };
    interface HTMLCModalElement extends Components.CModal, HTMLStencilElement {
    }
    var HTMLCModalElement: {
        prototype: HTMLCModalElement;
        new (): HTMLCModalElement;
    };
    interface HTMLCNotificationElement extends Components.CNotification, HTMLStencilElement {
    }
    var HTMLCNotificationElement: {
        prototype: HTMLCNotificationElement;
        new (): HTMLCNotificationElement;
    };
    interface HTMLCOauthElement extends Components.COauth, HTMLStencilElement {
    }
    var HTMLCOauthElement: {
        prototype: HTMLCOauthElement;
        new (): HTMLCOauthElement;
    };
    interface HTMLCOverviewInfoElement extends Components.COverviewInfo, HTMLStencilElement {
    }
    var HTMLCOverviewInfoElement: {
        prototype: HTMLCOverviewInfoElement;
        new (): HTMLCOverviewInfoElement;
    };
    interface HTMLCPageElement extends Components.CPage, HTMLStencilElement {
    }
    var HTMLCPageElement: {
        prototype: HTMLCPageElement;
        new (): HTMLCPageElement;
    };
    interface HTMLCPaidItemsElement extends Components.CPaidItems, HTMLStencilElement {
    }
    var HTMLCPaidItemsElement: {
        prototype: HTMLCPaidItemsElement;
        new (): HTMLCPaidItemsElement;
    };
    interface HTMLCPaidItemsListElement extends Components.CPaidItemsList, HTMLStencilElement {
    }
    var HTMLCPaidItemsListElement: {
        prototype: HTMLCPaidItemsListElement;
        new (): HTMLCPaidItemsListElement;
    };
    interface HTMLCPaymentOptionsElement extends Components.CPaymentOptions, HTMLStencilElement {
    }
    var HTMLCPaymentOptionsElement: {
        prototype: HTMLCPaymentOptionsElement;
        new (): HTMLCPaymentOptionsElement;
    };
    interface HTMLCPaymentSummaryItemsElement extends Components.CPaymentSummaryItems, HTMLStencilElement {
    }
    var HTMLCPaymentSummaryItemsElement: {
        prototype: HTMLCPaymentSummaryItemsElement;
        new (): HTMLCPaymentSummaryItemsElement;
    };
    interface HTMLCPricebandElement extends Components.CPriceband, HTMLStencilElement {
    }
    var HTMLCPricebandElement: {
        prototype: HTMLCPricebandElement;
        new (): HTMLCPricebandElement;
    };
    interface HTMLCProfileItemElement extends Components.CProfileItem, HTMLStencilElement {
    }
    var HTMLCProfileItemElement: {
        prototype: HTMLCProfileItemElement;
        new (): HTMLCProfileItemElement;
    };
    interface HTMLCProfileItemEditElement extends Components.CProfileItemEdit, HTMLStencilElement {
    }
    var HTMLCProfileItemEditElement: {
        prototype: HTMLCProfileItemEditElement;
        new (): HTMLCProfileItemEditElement;
    };
    interface HTMLCProfileItemsListElement extends Components.CProfileItemsList, HTMLStencilElement {
    }
    var HTMLCProfileItemsListElement: {
        prototype: HTMLCProfileItemsListElement;
        new (): HTMLCProfileItemsListElement;
    };
    interface HTMLCPurchaseFilterElement extends Components.CPurchaseFilter, HTMLStencilElement {
    }
    var HTMLCPurchaseFilterElement: {
        prototype: HTMLCPurchaseFilterElement;
        new (): HTMLCPurchaseFilterElement;
    };
    interface HTMLCPurchaseListElement extends Components.CPurchaseList, HTMLStencilElement {
    }
    var HTMLCPurchaseListElement: {
        prototype: HTMLCPurchaseListElement;
        new (): HTMLCPurchaseListElement;
    };
    interface HTMLCPurchasedItemElement extends Components.CPurchasedItem, HTMLStencilElement {
    }
    var HTMLCPurchasedItemElement: {
        prototype: HTMLCPurchasedItemElement;
        new (): HTMLCPurchasedItemElement;
    };
    interface HTMLCRadioElement extends Components.CRadio, HTMLStencilElement {
    }
    var HTMLCRadioElement: {
        prototype: HTMLCRadioElement;
        new (): HTMLCRadioElement;
    };
    interface HTMLCRegistrantElement extends Components.CRegistrant, HTMLStencilElement {
    }
    var HTMLCRegistrantElement: {
        prototype: HTMLCRegistrantElement;
        new (): HTMLCRegistrantElement;
    };
    interface HTMLCRmAccountsElement extends Components.CRmAccounts, HTMLStencilElement {
    }
    var HTMLCRmAccountsElement: {
        prototype: HTMLCRmAccountsElement;
        new (): HTMLCRmAccountsElement;
    };
    interface HTMLCRmAlertsElement extends Components.CRmAlerts, HTMLStencilElement {
    }
    var HTMLCRmAlertsElement: {
        prototype: HTMLCRmAlertsElement;
        new (): HTMLCRmAlertsElement;
    };
    interface HTMLCRmCouponsElement extends Components.CRmCoupons, HTMLStencilElement {
    }
    var HTMLCRmCouponsElement: {
        prototype: HTMLCRmCouponsElement;
        new (): HTMLCRmCouponsElement;
    };
    interface HTMLCRmInvoicesElement extends Components.CRmInvoices, HTMLStencilElement {
    }
    var HTMLCRmInvoicesElement: {
        prototype: HTMLCRmInvoicesElement;
        new (): HTMLCRmInvoicesElement;
    };
    interface HTMLCRmMembersElement extends Components.CRmMembers, HTMLStencilElement {
    }
    var HTMLCRmMembersElement: {
        prototype: HTMLCRmMembersElement;
        new (): HTMLCRmMembersElement;
    };
    interface HTMLCRmOverviewElement extends Components.CRmOverview, HTMLStencilElement {
    }
    var HTMLCRmOverviewElement: {
        prototype: HTMLCRmOverviewElement;
        new (): HTMLCRmOverviewElement;
    };
    interface HTMLCRmOverview2Element extends Components.CRmOverview2, HTMLStencilElement {
    }
    var HTMLCRmOverview2Element: {
        prototype: HTMLCRmOverview2Element;
        new (): HTMLCRmOverview2Element;
    };
    interface HTMLCRmPurchasesElement extends Components.CRmPurchases, HTMLStencilElement {
    }
    var HTMLCRmPurchasesElement: {
        prototype: HTMLCRmPurchasesElement;
        new (): HTMLCRmPurchasesElement;
    };
    interface HTMLCRmSalesElement extends Components.CRmSales, HTMLStencilElement {
    }
    var HTMLCRmSalesElement: {
        prototype: HTMLCRmSalesElement;
        new (): HTMLCRmSalesElement;
    };
    interface HTMLCRmVerificationElement extends Components.CRmVerification, HTMLStencilElement {
    }
    var HTMLCRmVerificationElement: {
        prototype: HTMLCRmVerificationElement;
        new (): HTMLCRmVerificationElement;
    };
    interface HTMLCRowElement extends Components.CRow, HTMLStencilElement {
    }
    var HTMLCRowElement: {
        prototype: HTMLCRowElement;
        new (): HTMLCRowElement;
    };
    interface HTMLCSectionElement extends Components.CSection, HTMLStencilElement {
    }
    var HTMLCSectionElement: {
        prototype: HTMLCSectionElement;
        new (): HTMLCSectionElement;
    };
    interface HTMLCSectionBuyMembershipElement extends Components.CSectionBuyMembership, HTMLStencilElement {
    }
    var HTMLCSectionBuyMembershipElement: {
        prototype: HTMLCSectionBuyMembershipElement;
        new (): HTMLCSectionBuyMembershipElement;
    };
    interface HTMLCSectionGetMemberDiscountElement extends Components.CSectionGetMemberDiscount, HTMLStencilElement {
    }
    var HTMLCSectionGetMemberDiscountElement: {
        prototype: HTMLCSectionGetMemberDiscountElement;
        new (): HTMLCSectionGetMemberDiscountElement;
    };
    interface HTMLCSectionListElement extends Components.CSectionList, HTMLStencilElement {
    }
    var HTMLCSectionListElement: {
        prototype: HTMLCSectionListElement;
        new (): HTMLCSectionListElement;
    };
    interface HTMLCSectionTicketElement extends Components.CSectionTicket, HTMLStencilElement {
    }
    var HTMLCSectionTicketElement: {
        prototype: HTMLCSectionTicketElement;
        new (): HTMLCSectionTicketElement;
    };
    interface HTMLCSessionInfoElement extends Components.CSessionInfo, HTMLStencilElement {
    }
    var HTMLCSessionInfoElement: {
        prototype: HTMLCSessionInfoElement;
        new (): HTMLCSessionInfoElement;
    };
    interface HTMLCSidebarElement extends Components.CSidebar, HTMLStencilElement {
    }
    var HTMLCSidebarElement: {
        prototype: HTMLCSidebarElement;
        new (): HTMLCSidebarElement;
    };
    interface HTMLCSiteLogoElement extends Components.CSiteLogo, HTMLStencilElement {
    }
    var HTMLCSiteLogoElement: {
        prototype: HTMLCSiteLogoElement;
        new (): HTMLCSiteLogoElement;
    };
    interface HTMLCSkelElement extends Components.CSkel, HTMLStencilElement {
    }
    var HTMLCSkelElement: {
        prototype: HTMLCSkelElement;
        new (): HTMLCSkelElement;
    };
    interface HTMLCSkelCardElement extends Components.CSkelCard, HTMLStencilElement {
    }
    var HTMLCSkelCardElement: {
        prototype: HTMLCSkelCardElement;
        new (): HTMLCSkelCardElement;
    };
    interface HTMLCSkelLineElement extends Components.CSkelLine, HTMLStencilElement {
    }
    var HTMLCSkelLineElement: {
        prototype: HTMLCSkelLineElement;
        new (): HTMLCSkelLineElement;
    };
    interface HTMLCSpinnerElement extends Components.CSpinner, HTMLStencilElement {
    }
    var HTMLCSpinnerElement: {
        prototype: HTMLCSpinnerElement;
        new (): HTMLCSpinnerElement;
    };
    interface HTMLCSpinnerDarkElement extends Components.CSpinnerDark, HTMLStencilElement {
    }
    var HTMLCSpinnerDarkElement: {
        prototype: HTMLCSpinnerDarkElement;
        new (): HTMLCSpinnerDarkElement;
    };
    interface HTMLCStatusElement extends Components.CStatus, HTMLStencilElement {
    }
    var HTMLCStatusElement: {
        prototype: HTMLCStatusElement;
        new (): HTMLCStatusElement;
    };
    interface HTMLCTaxPrefModalElement extends Components.CTaxPrefModal, HTMLStencilElement {
    }
    var HTMLCTaxPrefModalElement: {
        prototype: HTMLCTaxPrefModalElement;
        new (): HTMLCTaxPrefModalElement;
    };
    interface HTMLCTextElement extends Components.CText, HTMLStencilElement {
    }
    var HTMLCTextElement: {
        prototype: HTMLCTextElement;
        new (): HTMLCTextElement;
    };
    interface HTMLCTextLinkElement extends Components.CTextLink, HTMLStencilElement {
    }
    var HTMLCTextLinkElement: {
        prototype: HTMLCTextLinkElement;
        new (): HTMLCTextLinkElement;
    };
    interface HTMLCTextareaElement extends Components.CTextarea, HTMLStencilElement {
    }
    var HTMLCTextareaElement: {
        prototype: HTMLCTextareaElement;
        new (): HTMLCTextareaElement;
    };
    interface HTMLCTextboxElement extends Components.CTextbox, HTMLStencilElement {
    }
    var HTMLCTextboxElement: {
        prototype: HTMLCTextboxElement;
        new (): HTMLCTextboxElement;
    };
    interface HTMLCTicketBasicTicketElement extends Components.CTicketBasicTicket, HTMLStencilElement {
    }
    var HTMLCTicketBasicTicketElement: {
        prototype: HTMLCTicketBasicTicketElement;
        new (): HTMLCTicketBasicTicketElement;
    };
    interface HTMLCTicketDetailModalElement extends Components.CTicketDetailModal, HTMLStencilElement {
    }
    var HTMLCTicketDetailModalElement: {
        prototype: HTMLCTicketDetailModalElement;
        new (): HTMLCTicketDetailModalElement;
    };
    interface HTMLCTicketEligibilityItemElement extends Components.CTicketEligibilityItem, HTMLStencilElement {
    }
    var HTMLCTicketEligibilityItemElement: {
        prototype: HTMLCTicketEligibilityItemElement;
        new (): HTMLCTicketEligibilityItemElement;
    };
    interface HTMLCTicketFullTicketElement extends Components.CTicketFullTicket, HTMLStencilElement {
    }
    var HTMLCTicketFullTicketElement: {
        prototype: HTMLCTicketFullTicketElement;
        new (): HTMLCTicketFullTicketElement;
    };
    interface HTMLCTicketPartialAccessDateItemElement extends Components.CTicketPartialAccessDateItem, HTMLStencilElement {
    }
    var HTMLCTicketPartialAccessDateItemElement: {
        prototype: HTMLCTicketPartialAccessDateItemElement;
        new (): HTMLCTicketPartialAccessDateItemElement;
    };
    interface HTMLCTicketPartialAccessDatesElement extends Components.CTicketPartialAccessDates, HTMLStencilElement {
    }
    var HTMLCTicketPartialAccessDatesElement: {
        prototype: HTMLCTicketPartialAccessDatesElement;
        new (): HTMLCTicketPartialAccessDatesElement;
    };
    interface HTMLCTicketPriceInputFullElement extends Components.CTicketPriceInputFull, HTMLStencilElement {
    }
    var HTMLCTicketPriceInputFullElement: {
        prototype: HTMLCTicketPriceInputFullElement;
        new (): HTMLCTicketPriceInputFullElement;
    };
    interface HTMLCTicketPriceInputPartialElement extends Components.CTicketPriceInputPartial, HTMLStencilElement {
    }
    var HTMLCTicketPriceInputPartialElement: {
        prototype: HTMLCTicketPriceInputPartialElement;
        new (): HTMLCTicketPriceInputPartialElement;
    };
    interface HTMLCTicketPriceInputTrackElement extends Components.CTicketPriceInputTrack, HTMLStencilElement {
    }
    var HTMLCTicketPriceInputTrackElement: {
        prototype: HTMLCTicketPriceInputTrackElement;
        new (): HTMLCTicketPriceInputTrackElement;
    };
    interface HTMLCTicketPriceItemFullElement extends Components.CTicketPriceItemFull, HTMLStencilElement {
    }
    var HTMLCTicketPriceItemFullElement: {
        prototype: HTMLCTicketPriceItemFullElement;
        new (): HTMLCTicketPriceItemFullElement;
    };
    interface HTMLCTicketPriceItemPartialElement extends Components.CTicketPriceItemPartial, HTMLStencilElement {
    }
    var HTMLCTicketPriceItemPartialElement: {
        prototype: HTMLCTicketPriceItemPartialElement;
        new (): HTMLCTicketPriceItemPartialElement;
    };
    interface HTMLCTicketPriceItemTrackElement extends Components.CTicketPriceItemTrack, HTMLStencilElement {
    }
    var HTMLCTicketPriceItemTrackElement: {
        prototype: HTMLCTicketPriceItemTrackElement;
        new (): HTMLCTicketPriceItemTrackElement;
    };
    interface HTMLCTicketTrackElement extends Components.CTicketTrack, HTMLStencilElement {
    }
    var HTMLCTicketTrackElement: {
        prototype: HTMLCTicketTrackElement;
        new (): HTMLCTicketTrackElement;
    };
    interface HTMLCTicketTrackDaysElement extends Components.CTicketTrackDays, HTMLStencilElement {
    }
    var HTMLCTicketTrackDaysElement: {
        prototype: HTMLCTicketTrackDaysElement;
        new (): HTMLCTicketTrackDaysElement;
    };
    interface HTMLCTicketTrackSessionElement extends Components.CTicketTrackSession, HTMLStencilElement {
    }
    var HTMLCTicketTrackSessionElement: {
        prototype: HTMLCTicketTrackSessionElement;
        new (): HTMLCTicketTrackSessionElement;
    };
    interface HTMLCTicketTrackSessionsElement extends Components.CTicketTrackSessions, HTMLStencilElement {
    }
    var HTMLCTicketTrackSessionsElement: {
        prototype: HTMLCTicketTrackSessionsElement;
        new (): HTMLCTicketTrackSessionsElement;
    };
    interface HTMLCTicketTrackTicketElement extends Components.CTicketTrackTicket, HTMLStencilElement {
    }
    var HTMLCTicketTrackTicketElement: {
        prototype: HTMLCTicketTrackTicketElement;
        new (): HTMLCTicketTrackTicketElement;
    };
    interface HTMLCToastElement extends Components.CToast, HTMLStencilElement {
    }
    var HTMLCToastElement: {
        prototype: HTMLCToastElement;
        new (): HTMLCToastElement;
    };
    interface HTMLCTopbarElement extends Components.CTopbar, HTMLStencilElement {
    }
    var HTMLCTopbarElement: {
        prototype: HTMLCTopbarElement;
        new (): HTMLCTopbarElement;
    };
    interface HTMLCUploaderElement extends Components.CUploader, HTMLStencilElement {
    }
    var HTMLCUploaderElement: {
        prototype: HTMLCUploaderElement;
        new (): HTMLCUploaderElement;
    };
    interface HTMLCUserInfoModalElement extends Components.CUserInfoModal, HTMLStencilElement {
    }
    var HTMLCUserInfoModalElement: {
        prototype: HTMLCUserInfoModalElement;
        new (): HTMLCUserInfoModalElement;
    };
    interface HTMLCUserMenuElement extends Components.CUserMenu, HTMLStencilElement {
    }
    var HTMLCUserMenuElement: {
        prototype: HTMLCUserMenuElement;
        new (): HTMLCUserMenuElement;
    };
    interface HTMLCUserVerificationCardElement extends Components.CUserVerificationCard, HTMLStencilElement {
    }
    var HTMLCUserVerificationCardElement: {
        prototype: HTMLCUserVerificationCardElement;
        new (): HTMLCUserVerificationCardElement;
    };
    interface HTMLCVnavElement extends Components.CVnav, HTMLStencilElement {
    }
    var HTMLCVnavElement: {
        prototype: HTMLCVnavElement;
        new (): HTMLCVnavElement;
    };
    interface HTMLCVnavItemElement extends Components.CVnavItem, HTMLStencilElement {
    }
    var HTMLCVnavItemElement: {
        prototype: HTMLCVnavItemElement;
        new (): HTMLCVnavItemElement;
    };
    interface HTMLCWizardAccountSetupElement extends Components.CWizardAccountSetup, HTMLStencilElement {
    }
    var HTMLCWizardAccountSetupElement: {
        prototype: HTMLCWizardAccountSetupElement;
        new (): HTMLCWizardAccountSetupElement;
    };
    interface HTMLCWizardAddPageElement extends Components.CWizardAddPage, HTMLStencilElement {
    }
    var HTMLCWizardAddPageElement: {
        prototype: HTMLCWizardAddPageElement;
        new (): HTMLCWizardAddPageElement;
    };
    interface HTMLCWizardAddTicketElement extends Components.CWizardAddTicket, HTMLStencilElement {
    }
    var HTMLCWizardAddTicketElement: {
        prototype: HTMLCWizardAddTicketElement;
        new (): HTMLCWizardAddTicketElement;
    };
    interface HTMLCWizardCreateEventElement extends Components.CWizardCreateEvent, HTMLStencilElement {
    }
    var HTMLCWizardCreateEventElement: {
        prototype: HTMLCWizardCreateEventElement;
        new (): HTMLCWizardCreateEventElement;
    };
    interface HTMLCWizardEditEventElement extends Components.CWizardEditEvent, HTMLStencilElement {
    }
    var HTMLCWizardEditEventElement: {
        prototype: HTMLCWizardEditEventElement;
        new (): HTMLCWizardEditEventElement;
    };
    interface HTMLCWizardGetIdElement extends Components.CWizardGetId, HTMLStencilElement {
    }
    var HTMLCWizardGetIdElement: {
        prototype: HTMLCWizardGetIdElement;
        new (): HTMLCWizardGetIdElement;
    };
    interface HTMLPAccesslistElement extends Components.PAccesslist, HTMLStencilElement {
    }
    var HTMLPAccesslistElement: {
        prototype: HTMLPAccesslistElement;
        new (): HTMLPAccesslistElement;
    };
    interface HTMLPAttendeeListElement extends Components.PAttendeeList, HTMLStencilElement {
    }
    var HTMLPAttendeeListElement: {
        prototype: HTMLPAttendeeListElement;
        new (): HTMLPAttendeeListElement;
    };
    interface HTMLPAuthElement extends Components.PAuth, HTMLStencilElement {
    }
    var HTMLPAuthElement: {
        prototype: HTMLPAuthElement;
        new (): HTMLPAuthElement;
    };
    interface HTMLPBankThanksElement extends Components.PBankThanks, HTMLStencilElement {
    }
    var HTMLPBankThanksElement: {
        prototype: HTMLPBankThanksElement;
        new (): HTMLPBankThanksElement;
    };
    interface HTMLPCatchAllElement extends Components.PCatchAll, HTMLStencilElement {
    }
    var HTMLPCatchAllElement: {
        prototype: HTMLPCatchAllElement;
        new (): HTMLPCatchAllElement;
    };
    interface HTMLPCheckoutElement extends Components.PCheckout, HTMLStencilElement {
    }
    var HTMLPCheckoutElement: {
        prototype: HTMLPCheckoutElement;
        new (): HTMLPCheckoutElement;
    };
    interface HTMLPConfigureElement extends Components.PConfigure, HTMLStencilElement {
    }
    var HTMLPConfigureElement: {
        prototype: HTMLPConfigureElement;
        new (): HTMLPConfigureElement;
    };
    interface HTMLPConfirmElement extends Components.PConfirm, HTMLStencilElement {
    }
    var HTMLPConfirmElement: {
        prototype: HTMLPConfirmElement;
        new (): HTMLPConfirmElement;
    };
    interface HTMLPDashboardElement extends Components.PDashboard, HTMLStencilElement {
    }
    var HTMLPDashboardElement: {
        prototype: HTMLPDashboardElement;
        new (): HTMLPDashboardElement;
    };
    interface HTMLPFreePurchaseThanksElement extends Components.PFreePurchaseThanks, HTMLStencilElement {
    }
    var HTMLPFreePurchaseThanksElement: {
        prototype: HTMLPFreePurchaseThanksElement;
        new (): HTMLPFreePurchaseThanksElement;
    };
    interface HTMLPHomeElement extends Components.PHome, HTMLStencilElement {
    }
    var HTMLPHomeElement: {
        prototype: HTMLPHomeElement;
        new (): HTMLPHomeElement;
    };
    interface HTMLPInstructorElement extends Components.PInstructor, HTMLStencilElement {
    }
    var HTMLPInstructorElement: {
        prototype: HTMLPInstructorElement;
        new (): HTMLPInstructorElement;
    };
    interface HTMLPMembershipThanksElement extends Components.PMembershipThanks, HTMLStencilElement {
    }
    var HTMLPMembershipThanksElement: {
        prototype: HTMLPMembershipThanksElement;
        new (): HTMLPMembershipThanksElement;
    };
    interface HTMLPPostGoogleOauthElement extends Components.PPostGoogleOauth, HTMLStencilElement {
    }
    var HTMLPPostGoogleOauthElement: {
        prototype: HTMLPPostGoogleOauthElement;
        new (): HTMLPPostGoogleOauthElement;
    };
    interface HTMLPPostLinkedinOauthElement extends Components.PPostLinkedinOauth, HTMLStencilElement {
    }
    var HTMLPPostLinkedinOauthElement: {
        prototype: HTMLPPostLinkedinOauthElement;
        new (): HTMLPPostLinkedinOauthElement;
    };
    interface HTMLPProfileElement extends Components.PProfile, HTMLStencilElement {
    }
    var HTMLPProfileElement: {
        prototype: HTMLPProfileElement;
        new (): HTMLPProfileElement;
    };
    interface HTMLPPurchasesElement extends Components.PPurchases, HTMLStencilElement {
    }
    var HTMLPPurchasesElement: {
        prototype: HTMLPPurchasesElement;
        new (): HTMLPPurchasesElement;
    };
    interface HTMLPRegformElement extends Components.PRegform, HTMLStencilElement {
    }
    var HTMLPRegformElement: {
        prototype: HTMLPRegformElement;
        new (): HTMLPRegformElement;
    };
    interface HTMLPRegform2Element extends Components.PRegform2, HTMLStencilElement {
    }
    var HTMLPRegform2Element: {
        prototype: HTMLPRegform2Element;
        new (): HTMLPRegform2Element;
    };
    interface HTMLPRzpThanksElement extends Components.PRzpThanks, HTMLStencilElement {
    }
    var HTMLPRzpThanksElement: {
        prototype: HTMLPRzpThanksElement;
        new (): HTMLPRzpThanksElement;
    };
    interface HTMLPTestElement extends Components.PTest, HTMLStencilElement {
    }
    var HTMLPTestElement: {
        prototype: HTMLPTestElement;
        new (): HTMLPTestElement;
    };
    interface HTMLPTrackChairElement extends Components.PTrackChair, HTMLStencilElement {
    }
    var HTMLPTrackChairElement: {
        prototype: HTMLPTrackChairElement;
        new (): HTMLPTrackChairElement;
    };
    interface HTMLPTrackOverviewElement extends Components.PTrackOverview, HTMLStencilElement {
    }
    var HTMLPTrackOverviewElement: {
        prototype: HTMLPTrackOverviewElement;
        new (): HTMLPTrackOverviewElement;
    };
    interface HTMLElementTagNameMap {
        "app-root": HTMLAppRootElement;
        "c-account-control": HTMLCAccountControlElement;
        "c-account-details-modal": HTMLCAccountDetailsModalElement;
        "c-alert": HTMLCAlertElement;
        "c-alerts-filter": HTMLCAlertsFilterElement;
        "c-badge": HTMLCBadgeElement;
        "c-banner": HTMLCBannerElement;
        "c-btn": HTMLCBtnElement;
        "c-btn-light": HTMLCBtnLightElement;
        "c-button": HTMLCButtonElement;
        "c-buynow-btn": HTMLCBuynowBtnElement;
        "c-card": HTMLCCardElement;
        "c-cart-item": HTMLCCartItemElement;
        "c-cart-list": HTMLCCartListElement;
        "c-cart-preview": HTMLCCartPreviewElement;
        "c-cart-total": HTMLCCartTotalElement;
        "c-check-membership": HTMLCCheckMembershipElement;
        "c-checkbox": HTMLCCheckboxElement;
        "c-checkout-options": HTMLCCheckoutOptionsElement;
        "c-control-bar": HTMLCControlBarElement;
        "c-coupon": HTMLCCouponElement;
        "c-coupon-applied": HTMLCCouponAppliedElement;
        "c-coupon-details-list": HTMLCCouponDetailsListElement;
        "c-coupon-details-modal": HTMLCCouponDetailsModalElement;
        "c-coupon-input": HTMLCCouponInputElement;
        "c-coupon-item": HTMLCCouponItemElement;
        "c-coupon-list": HTMLCCouponListElement;
        "c-create-coupon-modal": HTMLCCreateCouponModalElement;
        "c-create-invoice-modal": HTMLCCreateInvoiceModalElement;
        "c-date-picker": HTMLCDatePickerElement;
        "c-divider": HTMLCDividerElement;
        "c-dropdown": HTMLCDropdownElement;
        "c-edit-button": HTMLCEditButtonElement;
        "c-header": HTMLCHeaderElement;
        "c-img": HTMLCImgElement;
        "c-injectedticket": HTMLCInjectedticketElement;
        "c-inputbox": HTMLCInputboxElement;
        "c-inputphone": HTMLCInputphoneElement;
        "c-invoice-details-modal": HTMLCInvoiceDetailsModalElement;
        "c-link": HTMLCLinkElement;
        "c-list": HTMLCListElement;
        "c-list-item": HTMLCListItemElement;
        "c-member": HTMLCMemberElement;
        "c-member-filter": HTMLCMemberFilterElement;
        "c-mobile-cart": HTMLCMobileCartElement;
        "c-mobile-menu": HTMLCMobileMenuElement;
        "c-modal": HTMLCModalElement;
        "c-notification": HTMLCNotificationElement;
        "c-oauth": HTMLCOauthElement;
        "c-overview-info": HTMLCOverviewInfoElement;
        "c-page": HTMLCPageElement;
        "c-paid-items": HTMLCPaidItemsElement;
        "c-paid-items-list": HTMLCPaidItemsListElement;
        "c-payment-options": HTMLCPaymentOptionsElement;
        "c-payment-summary-items": HTMLCPaymentSummaryItemsElement;
        "c-priceband": HTMLCPricebandElement;
        "c-profile-item": HTMLCProfileItemElement;
        "c-profile-item-edit": HTMLCProfileItemEditElement;
        "c-profile-items-list": HTMLCProfileItemsListElement;
        "c-purchase-filter": HTMLCPurchaseFilterElement;
        "c-purchase-list": HTMLCPurchaseListElement;
        "c-purchased-item": HTMLCPurchasedItemElement;
        "c-radio": HTMLCRadioElement;
        "c-registrant": HTMLCRegistrantElement;
        "c-rm-accounts": HTMLCRmAccountsElement;
        "c-rm-alerts": HTMLCRmAlertsElement;
        "c-rm-coupons": HTMLCRmCouponsElement;
        "c-rm-invoices": HTMLCRmInvoicesElement;
        "c-rm-members": HTMLCRmMembersElement;
        "c-rm-overview": HTMLCRmOverviewElement;
        "c-rm-overview-2": HTMLCRmOverview2Element;
        "c-rm-purchases": HTMLCRmPurchasesElement;
        "c-rm-sales": HTMLCRmSalesElement;
        "c-rm-verification": HTMLCRmVerificationElement;
        "c-row": HTMLCRowElement;
        "c-section": HTMLCSectionElement;
        "c-section-buy-membership": HTMLCSectionBuyMembershipElement;
        "c-section-get-member-discount": HTMLCSectionGetMemberDiscountElement;
        "c-section-list": HTMLCSectionListElement;
        "c-section-ticket": HTMLCSectionTicketElement;
        "c-session-info": HTMLCSessionInfoElement;
        "c-sidebar": HTMLCSidebarElement;
        "c-site-logo": HTMLCSiteLogoElement;
        "c-skel": HTMLCSkelElement;
        "c-skel-card": HTMLCSkelCardElement;
        "c-skel-line": HTMLCSkelLineElement;
        "c-spinner": HTMLCSpinnerElement;
        "c-spinner-dark": HTMLCSpinnerDarkElement;
        "c-status": HTMLCStatusElement;
        "c-tax-pref-modal": HTMLCTaxPrefModalElement;
        "c-text": HTMLCTextElement;
        "c-text-link": HTMLCTextLinkElement;
        "c-textarea": HTMLCTextareaElement;
        "c-textbox": HTMLCTextboxElement;
        "c-ticket-basic-ticket": HTMLCTicketBasicTicketElement;
        "c-ticket-detail-modal": HTMLCTicketDetailModalElement;
        "c-ticket-eligibility-item": HTMLCTicketEligibilityItemElement;
        "c-ticket-full-ticket": HTMLCTicketFullTicketElement;
        "c-ticket-partial-access-date-item": HTMLCTicketPartialAccessDateItemElement;
        "c-ticket-partial-access-dates": HTMLCTicketPartialAccessDatesElement;
        "c-ticket-price-input-full": HTMLCTicketPriceInputFullElement;
        "c-ticket-price-input-partial": HTMLCTicketPriceInputPartialElement;
        "c-ticket-price-input-track": HTMLCTicketPriceInputTrackElement;
        "c-ticket-price-item-full": HTMLCTicketPriceItemFullElement;
        "c-ticket-price-item-partial": HTMLCTicketPriceItemPartialElement;
        "c-ticket-price-item-track": HTMLCTicketPriceItemTrackElement;
        "c-ticket-track": HTMLCTicketTrackElement;
        "c-ticket-track-days": HTMLCTicketTrackDaysElement;
        "c-ticket-track-session": HTMLCTicketTrackSessionElement;
        "c-ticket-track-sessions": HTMLCTicketTrackSessionsElement;
        "c-ticket-track-ticket": HTMLCTicketTrackTicketElement;
        "c-toast": HTMLCToastElement;
        "c-topbar": HTMLCTopbarElement;
        "c-uploader": HTMLCUploaderElement;
        "c-user-info-modal": HTMLCUserInfoModalElement;
        "c-user-menu": HTMLCUserMenuElement;
        "c-user-verification-card": HTMLCUserVerificationCardElement;
        "c-vnav": HTMLCVnavElement;
        "c-vnav-item": HTMLCVnavItemElement;
        "c-wizard-account-setup": HTMLCWizardAccountSetupElement;
        "c-wizard-add-page": HTMLCWizardAddPageElement;
        "c-wizard-add-ticket": HTMLCWizardAddTicketElement;
        "c-wizard-create-event": HTMLCWizardCreateEventElement;
        "c-wizard-edit-event": HTMLCWizardEditEventElement;
        "c-wizard-get-id": HTMLCWizardGetIdElement;
        "p-accesslist": HTMLPAccesslistElement;
        "p-attendee-list": HTMLPAttendeeListElement;
        "p-auth": HTMLPAuthElement;
        "p-bank-thanks": HTMLPBankThanksElement;
        "p-catch-all": HTMLPCatchAllElement;
        "p-checkout": HTMLPCheckoutElement;
        "p-configure": HTMLPConfigureElement;
        "p-confirm": HTMLPConfirmElement;
        "p-dashboard": HTMLPDashboardElement;
        "p-free-purchase-thanks": HTMLPFreePurchaseThanksElement;
        "p-home": HTMLPHomeElement;
        "p-instructor": HTMLPInstructorElement;
        "p-membership-thanks": HTMLPMembershipThanksElement;
        "p-post-google-oauth": HTMLPPostGoogleOauthElement;
        "p-post-linkedin-oauth": HTMLPPostLinkedinOauthElement;
        "p-profile": HTMLPProfileElement;
        "p-purchases": HTMLPPurchasesElement;
        "p-regform": HTMLPRegformElement;
        "p-regform-2": HTMLPRegform2Element;
        "p-rzp-thanks": HTMLPRzpThanksElement;
        "p-test": HTMLPTestElement;
        "p-track-chair": HTMLPTrackChairElement;
        "p-track-overview": HTMLPTrackOverviewElement;
    }
}
declare namespace LocalJSX {
    interface AppRoot {
        "history"?: RouterHistory;
    }
    interface CAccountControl {
    }
    interface CAccountDetailsModal {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface CAlert {
        "alertId"?: string;
        "alertType"?: string;
        "emailIssuer"?: string;
        "emailResolver"?: string;
        "firstNameIssuer"?: string;
        "firstNameResolver"?: string;
        "isActive"?: boolean;
        "issuedOn"?: string;
        "lastNameIssuer"?: string;
        "lastNameResolver"?: string;
        "propOne"?: string;
        "propThree"?: string;
        "propTwo"?: string;
        "resolvedOn"?: string;
    }
    interface CAlertsFilter {
        "alertCount"?: string;
    }
    interface CBadge {
        "color"?: string;
        "label"?: string;
    }
    interface CBanner {
        "isVisible"?: boolean;
        "position"?: string;
        "theme"?: string;
        "variant"?: string;
    }
    interface CBtn {
        "actionLabel"?: string;
        "icon"?: boolean;
        "iconUrl"?: string;
        "isDisabled"?: boolean;
        "isInAction"?: boolean;
        "label"?: string;
        "name"?: string;
        "onAccount-setup-btn-click-event"?: (event: CustomEvent<any>) => void;
        "onApply-member-discount"?: (event: CustomEvent<any>) => void;
        "onBack-to-account"?: (event: CustomEvent<any>) => void;
        "onBack-to-login"?: (event: CustomEvent<any>) => void;
        "onBank-transactions-btn-click"?: (event: CustomEvent<any>) => void;
        "onButton-click"?: (event: CustomEvent<any>) => void;
        "onCheckout-btn-click-event"?: (event: CustomEvent<any>) => void;
        "onCreate-coupon"?: (event: CustomEvent<any>) => void;
        "onDownload-legacy-member-data"?: (event: CustomEvent<any>) => void;
        "onDownload-purchase-data"?: (event: CustomEvent<any>) => void;
        "onDownload-track-registrants"?: (event: CustomEvent<any>) => void;
        "onDownloadAccounts"?: (event: CustomEvent<any>) => void;
        "onHide-modal"?: (event: CustomEvent<any>) => void;
        "onHide-overlay"?: (event: CustomEvent<any>) => void;
        "onLogin-btn-click-event"?: (event: CustomEvent<any>) => void;
        "onLogout-btn-click-event"?: (event: CustomEvent<any>) => void;
        "onPay-btn-click-event"?: (event: CustomEvent<any>) => void;
        "onReset-password"?: (event: CustomEvent<any>) => void;
        "onResolve-alert"?: (event: CustomEvent<any>) => void;
        "onSave-invoice-preferences"?: (event: CustomEvent<any>) => void;
        "onSave-profile-edits"?: (event: CustomEvent<any>) => void;
        "onSend-password-reset-code"?: (event: CustomEvent<any>) => void;
        "onShow-modal"?: (event: CustomEvent<any>) => void;
        "onSignup-btn-click-event"?: (event: CustomEvent<any>) => void;
        "onStart-invoice-preference-survey"?: (event: CustomEvent<any>) => void;
        "onUploadInvoiceGroup"?: (event: CustomEvent<any>) => void;
        "onVerify-email-btn-click"?: (event: CustomEvent<any>) => void;
        "onVerify-password-reset-code"?: (event: CustomEvent<any>) => void;
        "type"?: string;
        "value"?: string;
    }
    interface CBtnLight {
        "actionLabel"?: string;
        "isDisabled"?: boolean;
        "isInAction"?: boolean;
        "label"?: string;
        "name"?: string;
        "onBack"?: (event: CustomEvent<any>) => void;
        "onProfile-edit-close"?: (event: CustomEvent<any>) => void;
        "onResend-password-reset-code-event"?: (event: CustomEvent<any>) => void;
        "onResend-verification-code-event"?: (event: CustomEvent<any>) => void;
    }
    interface CButton {
        "iconName"?: string;
        "iconUrl"?: string;
        "isDisabled"?: boolean;
        "isInAction"?: boolean;
        "isInActionLabel"?: string;
        "isInActiveState"?: boolean;
        "label"?: string;
        "name"?: string;
        "onButtonClick"?: (event: CustomEvent<any>) => void;
        "theme"?: string;
        "type"?: string;
        "value"?: string;
    }
    interface CBuynowBtn {
        "onBuy-now"?: (event: CustomEvent<any>) => void;
        "ticketId"?: string;
    }
    interface CCard {
        "eventBannerUrl"?: string;
        "eventCode"?: string;
        "eventEndsOn"?: string;
        "eventId"?: string;
        "eventIsActive"?: boolean;
        "eventIsArchived"?: boolean;
        "eventIsManager"?: boolean;
        "eventIsPublished"?: boolean;
        "eventIsRegistrationOpen"?: boolean;
        "eventLogoUrl"?: string;
        "eventName"?: string;
        "eventPosterUrl"?: string;
        "eventRegEndsOn"?: string;
        "eventRegStartsOn"?: string;
        "eventStartsOn"?: string;
        "eventTagline"?: string;
        "eventVenueLabel"?: string;
        "eventVenueUrl"?: string;
        "eventWebsiteUrl"?: string;
        "type"?: string;
    }
    interface CCartItem {
        "currency"?: string;
        "heading"?: string;
        "price"?: string;
        "subTitle"?: string;
    }
    interface CCartList {
    }
    interface CCartPreview {
    }
    interface CCartTotal {
    }
    interface CCheckMembership {
    }
    interface CCheckbox {
        "isChecked"?: boolean;
        "item"?: string;
        "label"?: string;
        "name"?: string;
        "onCheckbox-input-event"?: (event: CustomEvent<any>) => void;
    }
    interface CCheckoutOptions {
    }
    interface CControlBar {
        "count"?: number;
        "invoiceYearsString"?: any;
        "name"?: string;
    }
    interface CCoupon {
        "access"?: string;
        "couponId"?: string;
        "deductionType"?: string;
        "deductionValue"?: string;
        "issuedBy"?: string;
        "issuedCount"?: string;
        "issuedOn"?: string;
        "name"?: string;
        "openCode"?: string;
        "usedCount"?: string;
    }
    interface CCouponApplied {
        "onGet-cart"?: (event: CustomEvent<any>) => void;
    }
    interface CCouponDetailsList {
        "access"?: string;
        "couponId"?: string;
        "issuedCount"?: number;
        "redeemerListString"?: string;
        "usedCount"?: number;
    }
    interface CCouponDetailsModal {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface CCouponInput {
        "onGet-cart"?: (event: CustomEvent<any>) => void;
    }
    interface CCouponItem {
        "couponCode"?: string;
        "couponName"?: string;
    }
    interface CCouponList {
        "onGet-cart"?: (event: CustomEvent<any>) => void;
    }
    interface CCreateCouponModal {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
        "onCoupon-creation-response"?: (event: CustomEvent<any>) => void;
    }
    interface CCreateInvoiceModal {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
        "onInvoiceGroupUploadFailed"?: (event: CustomEvent<any>) => void;
        "onInvoiceGroupUploadSuccess"?: (event: CustomEvent<any>) => void;
    }
    interface CDatePicker {
        "date"?: string;
        "name"?: string;
        "onDateInput"?: (event: CustomEvent<any>) => void;
        "onTimeInput"?: (event: CustomEvent<any>) => void;
        "pickTime"?: boolean;
        "time"?: string;
    }
    interface CDivider {
        "type"?: string;
    }
    interface CDropdown {
        "isDisabled"?: boolean;
        "name"?: string;
        "onDropdown-input-event"?: (event: CustomEvent<any>) => void;
        "optionStr"?: string;
        "value"?: string;
    }
    interface CEditButton {
        "onProfile-edit-btn-click"?: (event: CustomEvent<any>) => void;
    }
    interface CHeader {
    }
    interface CImg {
        "alt"?: string;
        "src"?: string;
        "type"?: string;
    }
    interface CInjectedticket {
    }
    interface CInputbox {
        "isAutofocus"?: boolean;
        "isDisabled"?: boolean;
        "name"?: string;
        "onInput-event"?: (event: CustomEvent<any>) => void;
        "placeholder"?: string;
        "type"?: string;
        "value"?: string;
    }
    interface CInputphone {
        "dropdownValue"?: string;
        "inputboxValue"?: string;
        "isDisabled"?: boolean;
        "numpadPlaceholder"?: string;
        "onPhoneInputEvent"?: (event: CustomEvent<any>) => void;
        "selectValue"?: string;
    }
    interface CInvoiceDetailsModal {
        "invoiceGroup"?: string;
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface CLink {
        "iconName"?: string;
        "name"?: string;
        "onLinkClick"?: (event: CustomEvent<any>) => void;
        "target"?: string;
        "type"?: string;
        "url"?: string;
    }
    interface CList {
        "listItemsAsString"?: string;
        "name"?: string;
        "onExpand-coupon-details"?: (event: CustomEvent<any>) => void;
        "onExpand-user-details"?: (event: CustomEvent<any>) => void;
        "onExpand-wc-details"?: (event: CustomEvent<any>) => void;
        "onExpandInvoiceGroupDetails"?: (event: CustomEvent<any>) => void;
        "onManagerSelected"?: (event: CustomEvent<any>) => void;
        "subType"?: string;
        "type"?: string;
    }
    interface CListItem {
        "isClickable"?: boolean;
        "type"?: string;
    }
    interface CMember {
        "email"?: string;
        "expiresIn"?: string;
        "memberId"?: string;
        "membershipEndDate"?: string;
        "membershipStartDate"?: string;
        "membershipType"?: string;
        "name"?: string;
    }
    interface CMemberFilter {
        "memberCount"?: number;
    }
    interface CMobileCart {
    }
    interface CMobileMenu {
    }
    interface CModal {
        "data"?: any;
        "isActive"?: boolean;
        "name"?: string;
        "onModalClosed"?: (event: CustomEvent<any>) => void;
    }
    interface CNotification {
        "isActive"?: boolean;
    }
    interface COauth {
        "history"?: RouterHistory;
    }
    interface COverviewInfo {
        "count"?: string;
        "highlight"?: string;
        "isBtn"?: boolean;
        "label"?: string;
        "layout"?: string;
        "onShow-ticket-details"?: (event: CustomEvent<any>) => void;
        "total"?: string;
        "width"?: number;
    }
    interface CPage {
        "type"?: string;
    }
    interface CPaidItems {
        "cartAmountAfterDeduction"?: number;
        "cartAmountBeforeDeduction"?: number;
        "cartTotal"?: string;
        "couponDeductionType"?: string;
        "couponDeductionValue"?: number;
        "couponName"?: string;
        "currency"?: string;
        "deductedAmount"?: number;
        "gatewayFee"?: string;
        "grandTotal"?: string;
        "isCouponApplied"?: boolean;
        "paymentMethod"?: string;
        "purchaseDate"?: string;
        "purchaseId"?: string;
        "purchaseStatus"?: string;
        "purchasedItems"?: string;
        "transactionId"?: string;
    }
    interface CPaidItemsList {
        "ownPurchases"?: boolean;
    }
    interface CPaymentOptions {
        "onCalculateMembershipCheckoutTotal"?: (event: CustomEvent<any>) => void;
    }
    interface CPaymentSummaryItems {
        "cartAmountAfterDeduction"?: number;
        "cartAmountBeforeDeduction"?: number;
        "cartTotal"?: string;
        "couponDeductionType"?: string;
        "couponDeductionValue"?: number;
        "couponName"?: string;
        "currency"?: string;
        "deductedAmount"?: number;
        "email"?: string;
        "gatewayFee"?: string;
        "grandTotal"?: string;
        "isCouponApplied"?: boolean;
        "name"?: string;
        "paymentMethod"?: string;
        "purchaseDate"?: string;
        "purchaseId"?: string;
        "purchaseStatus"?: string;
        "purchasedItems"?: string;
        "transactionId"?: string;
    }
    interface CPriceband {
        "currency"?: string;
        "heading"?: string;
        "isDiscount"?: boolean;
        "price"?: number;
        "subheading"?: string;
    }
    interface CProfileItem {
        "isEditable"?: boolean;
        "label"?: string;
        "name"?: string;
        "value"?: string;
    }
    interface CProfileItemEdit {
        "name"?: string;
        "onProfile-item-saved"?: (event: CustomEvent<any>) => void;
    }
    interface CProfileItemsList {
    }
    interface CPurchaseFilter {
        "btnLabel"?: string;
        "purchaseCount"?: string;
    }
    interface CPurchaseList {
        "history"?: RouterHistory;
    }
    interface CPurchasedItem {
        "currency"?: string;
        "heading"?: string;
        "onEmpty-purchase-list"?: (event: CustomEvent<any>) => void;
        "price"?: string;
        "subtitle"?: string;
        "ticketId"?: string;
        "tier"?: string;
    }
    interface CRadio {
        "isChecked"?: boolean;
        "isLabel2Link"?: boolean;
        "label1"?: string;
        "label2"?: string;
        "label3"?: string;
        "name"?: string;
        "onRadio-change-event"?: (event: CustomEvent<any>) => void;
        "url"?: string;
        "val"?: string;
        "variant"?: string;
    }
    interface CRegistrant {
    }
    interface CRmAccounts {
    }
    interface CRmAlerts {
    }
    interface CRmCoupons {
    }
    interface CRmInvoices {
    }
    interface CRmMembers {
    }
    interface CRmOverview {
    }
    interface CRmOverview2 {
    }
    interface CRmPurchases {
    }
    interface CRmSales {
    }
    interface CRmVerification {
    }
    interface CRow {
        "type"?: string;
    }
    interface CSection {
    }
    interface CSectionBuyMembership {
        "onGoToMembershipCheckout"?: (event: CustomEvent<any>) => void;
    }
    interface CSectionGetMemberDiscount {
        "onMember-discount-applied"?: (event: CustomEvent<any>) => void;
    }
    interface CSectionList {
    }
    interface CSectionTicket {
        "type"?: string;
    }
    interface CSessionInfo {
        "data"?: any;
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface CSidebar {
        "type"?: string;
    }
    interface CSiteLogo {
        "link"?: string;
        "src"?: string;
    }
    interface CSkel {
        "variant"?: string;
    }
    interface CSkelCard {
        "container"?: boolean;
    }
    interface CSkelLine {
        "color"?: string;
        "width"?: number;
    }
    interface CSpinner {
    }
    interface CSpinnerDark {
    }
    interface CStatus {
        "type"?: string;
    }
    interface CTaxPrefModal {
        "onClose-save-invoice-pref-modal"?: (event: CustomEvent<any>) => void;
    }
    interface CText {
        "iconName"?: string;
        "isMandatory"?: boolean;
        "theme"?: string;
        "type"?: string;
    }
    interface CTextLink {
        "label"?: string;
        "url"?: string;
    }
    interface CTextarea {
        "cols"?: number;
        "name"?: string;
        "onTextarea-input-event"?: (event: CustomEvent<any>) => void;
        "placeholder"?: string;
        "rows"?: number;
    }
    interface CTextbox {
        "inputType"?: string;
        "isDisabled"?: boolean;
        "isError"?: boolean;
        "isInFocus"?: boolean;
        "isTextboxFilled"?: boolean;
        "name"?: string;
        "onTextInput"?: (event: CustomEvent<any>) => void;
        "placeholder"?: string;
        "type"?: string;
        "value"?: string;
    }
    interface CTicketBasicTicket {
        "isConfigMode"?: boolean;
        "isDisabled"?: boolean;
        "isPrimary"?: boolean;
        "isPrimaryDependent"?: boolean;
        "isTicketInCart"?: boolean;
        "isTicketPurchased"?: boolean;
        "isVisible"?: boolean;
        "mode"?: string;
        "persona"?: string;
        "purchaseStatus"?: string;
        "ticketId"?: string;
        "ticketTitle"?: string;
        "tierString"?: string;
        "type"?: string;
    }
    interface CTicketDetailModal {
    }
    interface CTicketEligibilityItem {
        "isMultiDate"?: boolean;
        "itemDate"?: string;
        "itemDateRange"?: string;
        "itemId"?: string;
        "onTicketEligibilityClicked"?: (event: CustomEvent<any>) => void;
    }
    interface CTicketFullTicket {
        "isConfigMode"?: boolean;
        "isDisabled"?: boolean;
        "isPrimary"?: boolean;
        "isPrimaryDependent"?: boolean;
        "isTicketInCart"?: boolean;
        "isTicketPurchased"?: boolean;
        "isVisible"?: boolean;
        "mode"?: string;
        "persona"?: string;
        "purchaseStatus"?: string;
        "ticketId"?: string;
        "ticketTitle"?: string;
        "tierString"?: string;
        "type"?: string;
    }
    interface CTicketPartialAccessDateItem {
        "dateId"?: string;
        "endDate"?: string;
        "isMultiDay"?: boolean;
        "name"?: string;
        "onAccessDateClicked"?: (event: CustomEvent<any>) => void;
        "startDate"?: string;
    }
    interface CTicketPartialAccessDates {
        "accessDatesString"?: string;
        "onAddAccessDates_partialTicket"?: (event: CustomEvent<any>) => void;
    }
    interface CTicketPriceInputFull {
        "onAddPricingTier_fullTicket"?: (event: CustomEvent<any>) => void;
        "onTierChange"?: (event: CustomEvent<any>) => void;
        "pricingTierString"?: string;
    }
    interface CTicketPriceInputPartial {
        "accessDatesPriceListString"?: string;
        "accessDatesString"?: string;
        "onTierChange"?: (event: CustomEvent<any>) => void;
    }
    interface CTicketPriceInputTrack {
        "onAddSubTicket_trackTicket"?: (event: CustomEvent<any>) => void;
        "subTicketsString"?: string;
    }
    interface CTicketPriceItemFull {
        "onTicketTierClicked"?: (event: CustomEvent<any>) => void;
        "price_Professional"?: number;
        "price_Student"?: number;
        "tierEnd"?: string;
        "tierId"?: string;
        "tierName"?: string;
        "tierRange"?: string;
        "tierStart"?: string;
    }
    interface CTicketPriceItemPartial {
        "accessId"?: string;
        "onAccessDateClicked"?: (event: CustomEvent<any>) => void;
        "price_Professional"?: number;
        "price_Student"?: number;
        "tierEnd"?: string;
        "tierId"?: string;
        "tierName"?: string;
        "tierRange"?: string;
        "tierStart"?: string;
    }
    interface CTicketPriceItemTrack {
        "name"?: string;
        "onTicketTierClicked"?: (event: CustomEvent<any>) => void;
        "price_Professional"?: number;
        "price_Student"?: number;
        "subTicketId"?: string;
    }
    interface CTicketTrack {
        "date"?: string;
        "desc"?: string;
        "end"?: string;
        "isDisabled"?: boolean;
        "people"?: string;
        "price"?: string;
        "quantity"?: string;
        "start"?: string;
        "subTitle"?: string;
        "subType"?: string;
        "ticketBtnStatus"?: string;
        "ticketID"?: string;
        "ticketTitle"?: string;
        "type"?: string;
        "url"?: string;
    }
    interface CTicketTrackDays {
        "onAddTrackDay_TrackTicket"?: (event: CustomEvent<any>) => void;
        "trackDaysString"?: string;
    }
    interface CTicketTrackSession {
        "endsOn"?: string;
        "instructors"?: string;
        "isDisabled"?: boolean;
        "isSoldOut"?: boolean;
        "isTicketInCart"?: boolean;
        "isTicketPurchased"?: boolean;
        "isVisible"?: boolean;
        "persona"?: string;
        "professionalPrice"?: number;
        "purchaseStatus"?: string;
        "quantity"?: number;
        "sessionId"?: string;
        "startsOn"?: string;
        "studentPrice"?: number;
        "ticketId"?: string;
        "ticketTitle"?: string;
        "type"?: string;
        "url"?: string;
    }
    interface CTicketTrackSessions {
        "accessDatesString"?: string;
        "onAddAccessDates_partialTicket"?: (event: CustomEvent<any>) => void;
        "subTicketsString"?: string;
        "trackDaysString"?: string;
    }
    interface CTicketTrackTicket {
        "isDisabled"?: boolean;
        "isPrimary"?: boolean;
        "isPrimaryDependent"?: boolean;
        "isVisible"?: boolean;
        "persona"?: string;
        "sessionString"?: string;
        "ticketId"?: string;
        "type"?: string;
    }
    interface CToast {
        "duration"?: number;
        "label"?: string;
        "onHide-toast"?: (event: CustomEvent<any>) => void;
        "type"?: string;
    }
    interface CTopbar {
        "logoType"?: string;
    }
    interface CUploader {
        "fileSizeLimitInKB"?: number;
        "fileType"?: string;
        "name"?: string;
        "onFileChangeEvent"?: (event: CustomEvent<any>) => void;
        "onFileRemovedEvent"?: (event: CustomEvent<any>) => void;
        "resetUploader"?: boolean;
    }
    interface CUserInfoModal {
    }
    interface CUserMenu {
    }
    interface CUserVerificationCard {
        "bankTxCode"?: string;
        "email"?: string;
        "firstName"?: string;
        "lastName"?: string;
        "onVerify-bank-transaction"?: (event: CustomEvent<any>) => void;
        "orderID"?: string;
        "transferredAmount"?: string;
        "verificationType"?: string;
    }
    interface CVnav {
        "activeOption"?: string;
        "isConfigMode"?: boolean;
        "isDisabled"?: boolean;
        "name"?: string;
        "navOptsStr"?: string;
    }
    interface CVnavItem {
        "configure"?: boolean;
        "icon"?: string;
        "isConfigMode"?: boolean;
        "isDisabled"?: boolean;
        "label"?: string;
        "name"?: string;
        "navLen"?: number;
        "navName"?: string;
        "onPageEdit"?: (event: CustomEvent<any>) => void;
        "onVnav-route-change"?: (event: CustomEvent<any>) => void;
        "route"?: string;
        "state"?: string;
        "subText"?: string;
    }
    interface CWizardAccountSetup {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface CWizardAddPage {
    }
    interface CWizardAddTicket {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface CWizardCreateEvent {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
        "onFetchNewData"?: (event: CustomEvent<any>) => void;
    }
    interface CWizardEditEvent {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
        "onEventEdited"?: (event: CustomEvent<any>) => void;
        "onFetchNewData"?: (event: CustomEvent<any>) => void;
    }
    interface CWizardGetId {
        "onCloseModal"?: (event: CustomEvent<any>) => void;
    }
    interface PAccesslist {
    }
    interface PAttendeeList {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PAuth {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PBankThanks {
        "history"?: RouterHistory;
    }
    interface PCatchAll {
        "history"?: RouterHistory;
    }
    interface PCheckout {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PConfigure {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PConfirm {
        "history"?: RouterHistory;
    }
    interface PDashboard {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PFreePurchaseThanks {
        "history"?: RouterHistory;
    }
    interface PHome {
        "history"?: RouterHistory;
        "onGetAccountInfoEvent"?: (event: CustomEvent<any>) => void;
    }
    interface PInstructor {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PMembershipThanks {
        "history"?: RouterHistory;
    }
    interface PPostGoogleOauth {
        "history"?: RouterHistory;
    }
    interface PPostLinkedinOauth {
        "history"?: RouterHistory;
    }
    interface PProfile {
        "history"?: RouterHistory;
    }
    interface PPurchases {
        "history"?: RouterHistory;
    }
    interface PRegform {
        "history"?: RouterHistory;
    }
    interface PRegform2 {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PRzpThanks {
        "history"?: RouterHistory;
    }
    interface PTest {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface PTrackChair {
        "width"?: string;
    }
    interface PTrackOverview {
        "history"?: RouterHistory;
        "match"?: MatchResults;
    }
    interface IntrinsicElements {
        "app-root": AppRoot;
        "c-account-control": CAccountControl;
        "c-account-details-modal": CAccountDetailsModal;
        "c-alert": CAlert;
        "c-alerts-filter": CAlertsFilter;
        "c-badge": CBadge;
        "c-banner": CBanner;
        "c-btn": CBtn;
        "c-btn-light": CBtnLight;
        "c-button": CButton;
        "c-buynow-btn": CBuynowBtn;
        "c-card": CCard;
        "c-cart-item": CCartItem;
        "c-cart-list": CCartList;
        "c-cart-preview": CCartPreview;
        "c-cart-total": CCartTotal;
        "c-check-membership": CCheckMembership;
        "c-checkbox": CCheckbox;
        "c-checkout-options": CCheckoutOptions;
        "c-control-bar": CControlBar;
        "c-coupon": CCoupon;
        "c-coupon-applied": CCouponApplied;
        "c-coupon-details-list": CCouponDetailsList;
        "c-coupon-details-modal": CCouponDetailsModal;
        "c-coupon-input": CCouponInput;
        "c-coupon-item": CCouponItem;
        "c-coupon-list": CCouponList;
        "c-create-coupon-modal": CCreateCouponModal;
        "c-create-invoice-modal": CCreateInvoiceModal;
        "c-date-picker": CDatePicker;
        "c-divider": CDivider;
        "c-dropdown": CDropdown;
        "c-edit-button": CEditButton;
        "c-header": CHeader;
        "c-img": CImg;
        "c-injectedticket": CInjectedticket;
        "c-inputbox": CInputbox;
        "c-inputphone": CInputphone;
        "c-invoice-details-modal": CInvoiceDetailsModal;
        "c-link": CLink;
        "c-list": CList;
        "c-list-item": CListItem;
        "c-member": CMember;
        "c-member-filter": CMemberFilter;
        "c-mobile-cart": CMobileCart;
        "c-mobile-menu": CMobileMenu;
        "c-modal": CModal;
        "c-notification": CNotification;
        "c-oauth": COauth;
        "c-overview-info": COverviewInfo;
        "c-page": CPage;
        "c-paid-items": CPaidItems;
        "c-paid-items-list": CPaidItemsList;
        "c-payment-options": CPaymentOptions;
        "c-payment-summary-items": CPaymentSummaryItems;
        "c-priceband": CPriceband;
        "c-profile-item": CProfileItem;
        "c-profile-item-edit": CProfileItemEdit;
        "c-profile-items-list": CProfileItemsList;
        "c-purchase-filter": CPurchaseFilter;
        "c-purchase-list": CPurchaseList;
        "c-purchased-item": CPurchasedItem;
        "c-radio": CRadio;
        "c-registrant": CRegistrant;
        "c-rm-accounts": CRmAccounts;
        "c-rm-alerts": CRmAlerts;
        "c-rm-coupons": CRmCoupons;
        "c-rm-invoices": CRmInvoices;
        "c-rm-members": CRmMembers;
        "c-rm-overview": CRmOverview;
        "c-rm-overview-2": CRmOverview2;
        "c-rm-purchases": CRmPurchases;
        "c-rm-sales": CRmSales;
        "c-rm-verification": CRmVerification;
        "c-row": CRow;
        "c-section": CSection;
        "c-section-buy-membership": CSectionBuyMembership;
        "c-section-get-member-discount": CSectionGetMemberDiscount;
        "c-section-list": CSectionList;
        "c-section-ticket": CSectionTicket;
        "c-session-info": CSessionInfo;
        "c-sidebar": CSidebar;
        "c-site-logo": CSiteLogo;
        "c-skel": CSkel;
        "c-skel-card": CSkelCard;
        "c-skel-line": CSkelLine;
        "c-spinner": CSpinner;
        "c-spinner-dark": CSpinnerDark;
        "c-status": CStatus;
        "c-tax-pref-modal": CTaxPrefModal;
        "c-text": CText;
        "c-text-link": CTextLink;
        "c-textarea": CTextarea;
        "c-textbox": CTextbox;
        "c-ticket-basic-ticket": CTicketBasicTicket;
        "c-ticket-detail-modal": CTicketDetailModal;
        "c-ticket-eligibility-item": CTicketEligibilityItem;
        "c-ticket-full-ticket": CTicketFullTicket;
        "c-ticket-partial-access-date-item": CTicketPartialAccessDateItem;
        "c-ticket-partial-access-dates": CTicketPartialAccessDates;
        "c-ticket-price-input-full": CTicketPriceInputFull;
        "c-ticket-price-input-partial": CTicketPriceInputPartial;
        "c-ticket-price-input-track": CTicketPriceInputTrack;
        "c-ticket-price-item-full": CTicketPriceItemFull;
        "c-ticket-price-item-partial": CTicketPriceItemPartial;
        "c-ticket-price-item-track": CTicketPriceItemTrack;
        "c-ticket-track": CTicketTrack;
        "c-ticket-track-days": CTicketTrackDays;
        "c-ticket-track-session": CTicketTrackSession;
        "c-ticket-track-sessions": CTicketTrackSessions;
        "c-ticket-track-ticket": CTicketTrackTicket;
        "c-toast": CToast;
        "c-topbar": CTopbar;
        "c-uploader": CUploader;
        "c-user-info-modal": CUserInfoModal;
        "c-user-menu": CUserMenu;
        "c-user-verification-card": CUserVerificationCard;
        "c-vnav": CVnav;
        "c-vnav-item": CVnavItem;
        "c-wizard-account-setup": CWizardAccountSetup;
        "c-wizard-add-page": CWizardAddPage;
        "c-wizard-add-ticket": CWizardAddTicket;
        "c-wizard-create-event": CWizardCreateEvent;
        "c-wizard-edit-event": CWizardEditEvent;
        "c-wizard-get-id": CWizardGetId;
        "p-accesslist": PAccesslist;
        "p-attendee-list": PAttendeeList;
        "p-auth": PAuth;
        "p-bank-thanks": PBankThanks;
        "p-catch-all": PCatchAll;
        "p-checkout": PCheckout;
        "p-configure": PConfigure;
        "p-confirm": PConfirm;
        "p-dashboard": PDashboard;
        "p-free-purchase-thanks": PFreePurchaseThanks;
        "p-home": PHome;
        "p-instructor": PInstructor;
        "p-membership-thanks": PMembershipThanks;
        "p-post-google-oauth": PPostGoogleOauth;
        "p-post-linkedin-oauth": PPostLinkedinOauth;
        "p-profile": PProfile;
        "p-purchases": PPurchases;
        "p-regform": PRegform;
        "p-regform-2": PRegform2;
        "p-rzp-thanks": PRzpThanks;
        "p-test": PTest;
        "p-track-chair": PTrackChair;
        "p-track-overview": PTrackOverview;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "app-root": LocalJSX.AppRoot & JSXBase.HTMLAttributes<HTMLAppRootElement>;
            "c-account-control": LocalJSX.CAccountControl & JSXBase.HTMLAttributes<HTMLCAccountControlElement>;
            "c-account-details-modal": LocalJSX.CAccountDetailsModal & JSXBase.HTMLAttributes<HTMLCAccountDetailsModalElement>;
            "c-alert": LocalJSX.CAlert & JSXBase.HTMLAttributes<HTMLCAlertElement>;
            "c-alerts-filter": LocalJSX.CAlertsFilter & JSXBase.HTMLAttributes<HTMLCAlertsFilterElement>;
            "c-badge": LocalJSX.CBadge & JSXBase.HTMLAttributes<HTMLCBadgeElement>;
            "c-banner": LocalJSX.CBanner & JSXBase.HTMLAttributes<HTMLCBannerElement>;
            "c-btn": LocalJSX.CBtn & JSXBase.HTMLAttributes<HTMLCBtnElement>;
            "c-btn-light": LocalJSX.CBtnLight & JSXBase.HTMLAttributes<HTMLCBtnLightElement>;
            "c-button": LocalJSX.CButton & JSXBase.HTMLAttributes<HTMLCButtonElement>;
            "c-buynow-btn": LocalJSX.CBuynowBtn & JSXBase.HTMLAttributes<HTMLCBuynowBtnElement>;
            "c-card": LocalJSX.CCard & JSXBase.HTMLAttributes<HTMLCCardElement>;
            "c-cart-item": LocalJSX.CCartItem & JSXBase.HTMLAttributes<HTMLCCartItemElement>;
            "c-cart-list": LocalJSX.CCartList & JSXBase.HTMLAttributes<HTMLCCartListElement>;
            "c-cart-preview": LocalJSX.CCartPreview & JSXBase.HTMLAttributes<HTMLCCartPreviewElement>;
            "c-cart-total": LocalJSX.CCartTotal & JSXBase.HTMLAttributes<HTMLCCartTotalElement>;
            "c-check-membership": LocalJSX.CCheckMembership & JSXBase.HTMLAttributes<HTMLCCheckMembershipElement>;
            "c-checkbox": LocalJSX.CCheckbox & JSXBase.HTMLAttributes<HTMLCCheckboxElement>;
            "c-checkout-options": LocalJSX.CCheckoutOptions & JSXBase.HTMLAttributes<HTMLCCheckoutOptionsElement>;
            "c-control-bar": LocalJSX.CControlBar & JSXBase.HTMLAttributes<HTMLCControlBarElement>;
            "c-coupon": LocalJSX.CCoupon & JSXBase.HTMLAttributes<HTMLCCouponElement>;
            "c-coupon-applied": LocalJSX.CCouponApplied & JSXBase.HTMLAttributes<HTMLCCouponAppliedElement>;
            "c-coupon-details-list": LocalJSX.CCouponDetailsList & JSXBase.HTMLAttributes<HTMLCCouponDetailsListElement>;
            "c-coupon-details-modal": LocalJSX.CCouponDetailsModal & JSXBase.HTMLAttributes<HTMLCCouponDetailsModalElement>;
            "c-coupon-input": LocalJSX.CCouponInput & JSXBase.HTMLAttributes<HTMLCCouponInputElement>;
            "c-coupon-item": LocalJSX.CCouponItem & JSXBase.HTMLAttributes<HTMLCCouponItemElement>;
            "c-coupon-list": LocalJSX.CCouponList & JSXBase.HTMLAttributes<HTMLCCouponListElement>;
            "c-create-coupon-modal": LocalJSX.CCreateCouponModal & JSXBase.HTMLAttributes<HTMLCCreateCouponModalElement>;
            "c-create-invoice-modal": LocalJSX.CCreateInvoiceModal & JSXBase.HTMLAttributes<HTMLCCreateInvoiceModalElement>;
            "c-date-picker": LocalJSX.CDatePicker & JSXBase.HTMLAttributes<HTMLCDatePickerElement>;
            "c-divider": LocalJSX.CDivider & JSXBase.HTMLAttributes<HTMLCDividerElement>;
            "c-dropdown": LocalJSX.CDropdown & JSXBase.HTMLAttributes<HTMLCDropdownElement>;
            "c-edit-button": LocalJSX.CEditButton & JSXBase.HTMLAttributes<HTMLCEditButtonElement>;
            "c-header": LocalJSX.CHeader & JSXBase.HTMLAttributes<HTMLCHeaderElement>;
            "c-img": LocalJSX.CImg & JSXBase.HTMLAttributes<HTMLCImgElement>;
            "c-injectedticket": LocalJSX.CInjectedticket & JSXBase.HTMLAttributes<HTMLCInjectedticketElement>;
            "c-inputbox": LocalJSX.CInputbox & JSXBase.HTMLAttributes<HTMLCInputboxElement>;
            "c-inputphone": LocalJSX.CInputphone & JSXBase.HTMLAttributes<HTMLCInputphoneElement>;
            "c-invoice-details-modal": LocalJSX.CInvoiceDetailsModal & JSXBase.HTMLAttributes<HTMLCInvoiceDetailsModalElement>;
            "c-link": LocalJSX.CLink & JSXBase.HTMLAttributes<HTMLCLinkElement>;
            "c-list": LocalJSX.CList & JSXBase.HTMLAttributes<HTMLCListElement>;
            "c-list-item": LocalJSX.CListItem & JSXBase.HTMLAttributes<HTMLCListItemElement>;
            "c-member": LocalJSX.CMember & JSXBase.HTMLAttributes<HTMLCMemberElement>;
            "c-member-filter": LocalJSX.CMemberFilter & JSXBase.HTMLAttributes<HTMLCMemberFilterElement>;
            "c-mobile-cart": LocalJSX.CMobileCart & JSXBase.HTMLAttributes<HTMLCMobileCartElement>;
            "c-mobile-menu": LocalJSX.CMobileMenu & JSXBase.HTMLAttributes<HTMLCMobileMenuElement>;
            "c-modal": LocalJSX.CModal & JSXBase.HTMLAttributes<HTMLCModalElement>;
            "c-notification": LocalJSX.CNotification & JSXBase.HTMLAttributes<HTMLCNotificationElement>;
            "c-oauth": LocalJSX.COauth & JSXBase.HTMLAttributes<HTMLCOauthElement>;
            "c-overview-info": LocalJSX.COverviewInfo & JSXBase.HTMLAttributes<HTMLCOverviewInfoElement>;
            "c-page": LocalJSX.CPage & JSXBase.HTMLAttributes<HTMLCPageElement>;
            "c-paid-items": LocalJSX.CPaidItems & JSXBase.HTMLAttributes<HTMLCPaidItemsElement>;
            "c-paid-items-list": LocalJSX.CPaidItemsList & JSXBase.HTMLAttributes<HTMLCPaidItemsListElement>;
            "c-payment-options": LocalJSX.CPaymentOptions & JSXBase.HTMLAttributes<HTMLCPaymentOptionsElement>;
            "c-payment-summary-items": LocalJSX.CPaymentSummaryItems & JSXBase.HTMLAttributes<HTMLCPaymentSummaryItemsElement>;
            "c-priceband": LocalJSX.CPriceband & JSXBase.HTMLAttributes<HTMLCPricebandElement>;
            "c-profile-item": LocalJSX.CProfileItem & JSXBase.HTMLAttributes<HTMLCProfileItemElement>;
            "c-profile-item-edit": LocalJSX.CProfileItemEdit & JSXBase.HTMLAttributes<HTMLCProfileItemEditElement>;
            "c-profile-items-list": LocalJSX.CProfileItemsList & JSXBase.HTMLAttributes<HTMLCProfileItemsListElement>;
            "c-purchase-filter": LocalJSX.CPurchaseFilter & JSXBase.HTMLAttributes<HTMLCPurchaseFilterElement>;
            "c-purchase-list": LocalJSX.CPurchaseList & JSXBase.HTMLAttributes<HTMLCPurchaseListElement>;
            "c-purchased-item": LocalJSX.CPurchasedItem & JSXBase.HTMLAttributes<HTMLCPurchasedItemElement>;
            "c-radio": LocalJSX.CRadio & JSXBase.HTMLAttributes<HTMLCRadioElement>;
            "c-registrant": LocalJSX.CRegistrant & JSXBase.HTMLAttributes<HTMLCRegistrantElement>;
            "c-rm-accounts": LocalJSX.CRmAccounts & JSXBase.HTMLAttributes<HTMLCRmAccountsElement>;
            "c-rm-alerts": LocalJSX.CRmAlerts & JSXBase.HTMLAttributes<HTMLCRmAlertsElement>;
            "c-rm-coupons": LocalJSX.CRmCoupons & JSXBase.HTMLAttributes<HTMLCRmCouponsElement>;
            "c-rm-invoices": LocalJSX.CRmInvoices & JSXBase.HTMLAttributes<HTMLCRmInvoicesElement>;
            "c-rm-members": LocalJSX.CRmMembers & JSXBase.HTMLAttributes<HTMLCRmMembersElement>;
            "c-rm-overview": LocalJSX.CRmOverview & JSXBase.HTMLAttributes<HTMLCRmOverviewElement>;
            "c-rm-overview-2": LocalJSX.CRmOverview2 & JSXBase.HTMLAttributes<HTMLCRmOverview2Element>;
            "c-rm-purchases": LocalJSX.CRmPurchases & JSXBase.HTMLAttributes<HTMLCRmPurchasesElement>;
            "c-rm-sales": LocalJSX.CRmSales & JSXBase.HTMLAttributes<HTMLCRmSalesElement>;
            "c-rm-verification": LocalJSX.CRmVerification & JSXBase.HTMLAttributes<HTMLCRmVerificationElement>;
            "c-row": LocalJSX.CRow & JSXBase.HTMLAttributes<HTMLCRowElement>;
            "c-section": LocalJSX.CSection & JSXBase.HTMLAttributes<HTMLCSectionElement>;
            "c-section-buy-membership": LocalJSX.CSectionBuyMembership & JSXBase.HTMLAttributes<HTMLCSectionBuyMembershipElement>;
            "c-section-get-member-discount": LocalJSX.CSectionGetMemberDiscount & JSXBase.HTMLAttributes<HTMLCSectionGetMemberDiscountElement>;
            "c-section-list": LocalJSX.CSectionList & JSXBase.HTMLAttributes<HTMLCSectionListElement>;
            "c-section-ticket": LocalJSX.CSectionTicket & JSXBase.HTMLAttributes<HTMLCSectionTicketElement>;
            "c-session-info": LocalJSX.CSessionInfo & JSXBase.HTMLAttributes<HTMLCSessionInfoElement>;
            "c-sidebar": LocalJSX.CSidebar & JSXBase.HTMLAttributes<HTMLCSidebarElement>;
            "c-site-logo": LocalJSX.CSiteLogo & JSXBase.HTMLAttributes<HTMLCSiteLogoElement>;
            "c-skel": LocalJSX.CSkel & JSXBase.HTMLAttributes<HTMLCSkelElement>;
            "c-skel-card": LocalJSX.CSkelCard & JSXBase.HTMLAttributes<HTMLCSkelCardElement>;
            "c-skel-line": LocalJSX.CSkelLine & JSXBase.HTMLAttributes<HTMLCSkelLineElement>;
            "c-spinner": LocalJSX.CSpinner & JSXBase.HTMLAttributes<HTMLCSpinnerElement>;
            "c-spinner-dark": LocalJSX.CSpinnerDark & JSXBase.HTMLAttributes<HTMLCSpinnerDarkElement>;
            "c-status": LocalJSX.CStatus & JSXBase.HTMLAttributes<HTMLCStatusElement>;
            "c-tax-pref-modal": LocalJSX.CTaxPrefModal & JSXBase.HTMLAttributes<HTMLCTaxPrefModalElement>;
            "c-text": LocalJSX.CText & JSXBase.HTMLAttributes<HTMLCTextElement>;
            "c-text-link": LocalJSX.CTextLink & JSXBase.HTMLAttributes<HTMLCTextLinkElement>;
            "c-textarea": LocalJSX.CTextarea & JSXBase.HTMLAttributes<HTMLCTextareaElement>;
            "c-textbox": LocalJSX.CTextbox & JSXBase.HTMLAttributes<HTMLCTextboxElement>;
            "c-ticket-basic-ticket": LocalJSX.CTicketBasicTicket & JSXBase.HTMLAttributes<HTMLCTicketBasicTicketElement>;
            "c-ticket-detail-modal": LocalJSX.CTicketDetailModal & JSXBase.HTMLAttributes<HTMLCTicketDetailModalElement>;
            "c-ticket-eligibility-item": LocalJSX.CTicketEligibilityItem & JSXBase.HTMLAttributes<HTMLCTicketEligibilityItemElement>;
            "c-ticket-full-ticket": LocalJSX.CTicketFullTicket & JSXBase.HTMLAttributes<HTMLCTicketFullTicketElement>;
            "c-ticket-partial-access-date-item": LocalJSX.CTicketPartialAccessDateItem & JSXBase.HTMLAttributes<HTMLCTicketPartialAccessDateItemElement>;
            "c-ticket-partial-access-dates": LocalJSX.CTicketPartialAccessDates & JSXBase.HTMLAttributes<HTMLCTicketPartialAccessDatesElement>;
            "c-ticket-price-input-full": LocalJSX.CTicketPriceInputFull & JSXBase.HTMLAttributes<HTMLCTicketPriceInputFullElement>;
            "c-ticket-price-input-partial": LocalJSX.CTicketPriceInputPartial & JSXBase.HTMLAttributes<HTMLCTicketPriceInputPartialElement>;
            "c-ticket-price-input-track": LocalJSX.CTicketPriceInputTrack & JSXBase.HTMLAttributes<HTMLCTicketPriceInputTrackElement>;
            "c-ticket-price-item-full": LocalJSX.CTicketPriceItemFull & JSXBase.HTMLAttributes<HTMLCTicketPriceItemFullElement>;
            "c-ticket-price-item-partial": LocalJSX.CTicketPriceItemPartial & JSXBase.HTMLAttributes<HTMLCTicketPriceItemPartialElement>;
            "c-ticket-price-item-track": LocalJSX.CTicketPriceItemTrack & JSXBase.HTMLAttributes<HTMLCTicketPriceItemTrackElement>;
            "c-ticket-track": LocalJSX.CTicketTrack & JSXBase.HTMLAttributes<HTMLCTicketTrackElement>;
            "c-ticket-track-days": LocalJSX.CTicketTrackDays & JSXBase.HTMLAttributes<HTMLCTicketTrackDaysElement>;
            "c-ticket-track-session": LocalJSX.CTicketTrackSession & JSXBase.HTMLAttributes<HTMLCTicketTrackSessionElement>;
            "c-ticket-track-sessions": LocalJSX.CTicketTrackSessions & JSXBase.HTMLAttributes<HTMLCTicketTrackSessionsElement>;
            "c-ticket-track-ticket": LocalJSX.CTicketTrackTicket & JSXBase.HTMLAttributes<HTMLCTicketTrackTicketElement>;
            "c-toast": LocalJSX.CToast & JSXBase.HTMLAttributes<HTMLCToastElement>;
            "c-topbar": LocalJSX.CTopbar & JSXBase.HTMLAttributes<HTMLCTopbarElement>;
            "c-uploader": LocalJSX.CUploader & JSXBase.HTMLAttributes<HTMLCUploaderElement>;
            "c-user-info-modal": LocalJSX.CUserInfoModal & JSXBase.HTMLAttributes<HTMLCUserInfoModalElement>;
            "c-user-menu": LocalJSX.CUserMenu & JSXBase.HTMLAttributes<HTMLCUserMenuElement>;
            "c-user-verification-card": LocalJSX.CUserVerificationCard & JSXBase.HTMLAttributes<HTMLCUserVerificationCardElement>;
            "c-vnav": LocalJSX.CVnav & JSXBase.HTMLAttributes<HTMLCVnavElement>;
            "c-vnav-item": LocalJSX.CVnavItem & JSXBase.HTMLAttributes<HTMLCVnavItemElement>;
            "c-wizard-account-setup": LocalJSX.CWizardAccountSetup & JSXBase.HTMLAttributes<HTMLCWizardAccountSetupElement>;
            "c-wizard-add-page": LocalJSX.CWizardAddPage & JSXBase.HTMLAttributes<HTMLCWizardAddPageElement>;
            "c-wizard-add-ticket": LocalJSX.CWizardAddTicket & JSXBase.HTMLAttributes<HTMLCWizardAddTicketElement>;
            "c-wizard-create-event": LocalJSX.CWizardCreateEvent & JSXBase.HTMLAttributes<HTMLCWizardCreateEventElement>;
            "c-wizard-edit-event": LocalJSX.CWizardEditEvent & JSXBase.HTMLAttributes<HTMLCWizardEditEventElement>;
            "c-wizard-get-id": LocalJSX.CWizardGetId & JSXBase.HTMLAttributes<HTMLCWizardGetIdElement>;
            "p-accesslist": LocalJSX.PAccesslist & JSXBase.HTMLAttributes<HTMLPAccesslistElement>;
            "p-attendee-list": LocalJSX.PAttendeeList & JSXBase.HTMLAttributes<HTMLPAttendeeListElement>;
            "p-auth": LocalJSX.PAuth & JSXBase.HTMLAttributes<HTMLPAuthElement>;
            "p-bank-thanks": LocalJSX.PBankThanks & JSXBase.HTMLAttributes<HTMLPBankThanksElement>;
            "p-catch-all": LocalJSX.PCatchAll & JSXBase.HTMLAttributes<HTMLPCatchAllElement>;
            "p-checkout": LocalJSX.PCheckout & JSXBase.HTMLAttributes<HTMLPCheckoutElement>;
            "p-configure": LocalJSX.PConfigure & JSXBase.HTMLAttributes<HTMLPConfigureElement>;
            "p-confirm": LocalJSX.PConfirm & JSXBase.HTMLAttributes<HTMLPConfirmElement>;
            "p-dashboard": LocalJSX.PDashboard & JSXBase.HTMLAttributes<HTMLPDashboardElement>;
            "p-free-purchase-thanks": LocalJSX.PFreePurchaseThanks & JSXBase.HTMLAttributes<HTMLPFreePurchaseThanksElement>;
            "p-home": LocalJSX.PHome & JSXBase.HTMLAttributes<HTMLPHomeElement>;
            "p-instructor": LocalJSX.PInstructor & JSXBase.HTMLAttributes<HTMLPInstructorElement>;
            "p-membership-thanks": LocalJSX.PMembershipThanks & JSXBase.HTMLAttributes<HTMLPMembershipThanksElement>;
            "p-post-google-oauth": LocalJSX.PPostGoogleOauth & JSXBase.HTMLAttributes<HTMLPPostGoogleOauthElement>;
            "p-post-linkedin-oauth": LocalJSX.PPostLinkedinOauth & JSXBase.HTMLAttributes<HTMLPPostLinkedinOauthElement>;
            "p-profile": LocalJSX.PProfile & JSXBase.HTMLAttributes<HTMLPProfileElement>;
            "p-purchases": LocalJSX.PPurchases & JSXBase.HTMLAttributes<HTMLPPurchasesElement>;
            "p-regform": LocalJSX.PRegform & JSXBase.HTMLAttributes<HTMLPRegformElement>;
            "p-regform-2": LocalJSX.PRegform2 & JSXBase.HTMLAttributes<HTMLPRegform2Element>;
            "p-rzp-thanks": LocalJSX.PRzpThanks & JSXBase.HTMLAttributes<HTMLPRzpThanksElement>;
            "p-test": LocalJSX.PTest & JSXBase.HTMLAttributes<HTMLPTestElement>;
            "p-track-chair": LocalJSX.PTrackChair & JSXBase.HTMLAttributes<HTMLPTrackChairElement>;
            "p-track-overview": LocalJSX.PTrackOverview & JSXBase.HTMLAttributes<HTMLPTrackOverviewElement>;
        }
    }
}
