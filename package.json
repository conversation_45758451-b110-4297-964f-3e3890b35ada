{"dependencies": {"@hapi/joi": "^17.1.1", "@types/hapi__joi": "^17.1.2", "@types/jest": "^25.2.3", "@types/qs": "^6.9.7", "@types/babel__traverse": "7.20.0", "axios": "^0.26.1", "gsap": "^3.10.2", "qs": "^6.10.3"}, "description": "<PERSON><PERSON><PERSON><PERSON>", "devDependencies": {"@stencil/core": "^1.12.2", "@stencil/router": "^1.0.1", "@stencil/store": "^1.3.0", "@types/puppeteer": "2.0.1", "@types/prettier": "2.6.0", "jest": "^27.5.1", "jest-cli": "^27.5.1", "puppeteer": "2.1.1"}, "license": "MIT", "name": "layerpark-event-reg-frontend", "private": true, "scripts": {"bnm": "npm run build && mv www ../deploy", "bnmp": "npm run prerender && mv www ../deploy", "build": "stencil build", "generate": "stencil generate", "prerender": "stencil build --prerender", "start": "stencil build --dev --watch --serve", "sync": "rsync -av --delete www/ ../backend", "test": "stencil test --spec --e2e", "test.watch": "stencil test --spec --e2e --watchAll"}, "version": "0.0.1"}