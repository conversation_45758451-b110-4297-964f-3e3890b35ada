{"name": "layerpark-events-api", "version": "1.0.0", "description": "Backend of layerpark events", "main": "index.js", "scripts": {"prebuild": "rm -rf dist", "build": "tsc", "dev": "ts-node-dev --transpile-only --no-notify src", "bnm": "npm run build && mv dest/* ../deploy && rm -rf dest && rm -rf ../deploy/_assets && rm -rf ../deploy/uploads && cp .env ../deploy && cp package.json ../deploy && cp -R _assets ../deploy && mkdir ../deploy/uploads"}, "repository": {"type": "git", "url": "git+https://github.com/projckt/mosentra-app-api-server.git"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/projckt/mosentra-app-api-server/issues"}, "homepage": "https://github.com/projckt/mosentra-app-api-server#readme", "dependencies": {"@hapi/joi": "^17.1.0", "@luxuryescapes/lib-voucher-code": "^2.0.0", "@types/cors": "^2.8.6", "@types/dotenv": "^8.2.0", "@types/json2csv": "^5.0.1", "@types/jsonwebtoken": "^8.5.8", "@types/multer": "^1.4.7", "@types/pdfkit": "^0.12.6", "@types/qs": "^6.9.7", "@types/streamifier": "^0.1.0", "argon2": "^0.26.1", "axios": "^1.2.1", "cloudinary": "^1.29.1", "connect-redis": "^4.0.4", "cors": "^2.8.5", "crypto-js": "^4.0.0", "csvtojson": "^2.0.10", "dotenv": "^8.2.0", "express": "^4.17.1", "express-session": "^1.17.0", "ioredis": "^4.16.0", "json2csv": "^5.0.1", "jsonwebtoken": "^8.5.1", "moment": "^2.27.0", "mongoose": "^5.9.3", "multer": "^1.4.4", "pdfkit": "^0.13.0", "postmark": "^2.5.1", "qs": "^6.10.3", "razorpay": "^2.0.6", "streamifier": "^0.1.1", "uuid": "^7.0.2"}, "devDependencies": {"@types/connect-redis": "0.0.13", "@types/crypto-js": "^3.1.43", "@types/express": "^4.17.3", "@types/express-session": "^1.17.0", "@types/hapi__joi": "^16.0.12", "@types/mongodb": "^3.5.2", "@types/node": "^13.9.0", "@types/uuid": "^7.0.0", "ts-node-dev": "^1.0.0-pre.44", "typescript": "^4.2.4"}}